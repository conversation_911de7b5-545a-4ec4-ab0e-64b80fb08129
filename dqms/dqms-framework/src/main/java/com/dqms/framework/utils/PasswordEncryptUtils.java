package com.dqms.framework.utils;

import com.alibaba.druid.filter.config.ConfigTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 数据源密码加密解密工具类
 * 基于Druid的非对称加密算法
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
public class PasswordEncryptUtils {

    /**
     * 默认的私钥，用于解密
     */
    private static final String DEFAULT_PRIVATE_KEY = "MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEAznIZLVso6c6c72bQbdcH7FDVFzYT9UuTcsCVdtaKVqyzgDp7TypJbzol4UysIBaOeNBzYJDt/5bKg0AJf33zDwIDAQABAkEAqbWsMtf0qzfZVQiNIS6a6c6NTjZ7c7O8M6JTfEzjjlmRhlE9UpydykOlNcsFz6eIgCUA5BemIuYyKuojbQ3HiQIhAPcOWCZw55v5aqD6oA203BzNaLP9QzW5NCTDND3gKERdAiEA1etjIQ+JSpFK6PrqZLEGZImKvncyUfjdJtmEyY8p3lsCIQCXeoUT9NAds1zsnR67qLP5OPzEtzmZXBgbnlCOy70ExQIgMdiaja6YGCcI5IbrLTFOwwiFquINeKpbpdzToz/XyrcCIF09Kp9OvRG8l2ZuxYx/gPsbpcrCaoLJu8rGxBjS3CO1";

    /**
     * 默认的公钥，用于加密
     */
    private static final String DEFAULT_PUBLIC_KEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAM5yGS1bKOnOnO9m0G3XB+xQ1Rc2E/VLk3LAlXbWilass4A6e08qSW86JeFMrCAWjnjQc2CQ7f+WyoNACX998w8CAwEAAQ==";

    static {
        try {
            // 初始化时生成密钥对（可选，这里使用固定密钥对）
            log.info("密码加密工具类初始化完成");
        } catch (Exception e) {
            log.error("密码加密工具类初始化失败", e);
        }
    }

    /**
     * 生成密钥对
     *
     * @return 密钥对数组，[0]为私钥，[1]为公钥
     */
    public static String[] generateKeyPair() {
        try {
            String[] keyPair = ConfigTools.genKeyPair(512);
            log.info("生成新的密钥对成功");
            return keyPair;
        } catch (Exception e) {
            log.error("生成密钥对失败", e);
            throw new RuntimeException("生成密钥对失败", e);
        }
    }

    /**
     * 使用默认私钥加密密码
     *
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String encrypt(String password) {
        return encrypt(DEFAULT_PRIVATE_KEY, password);
    }

    /**
     * 使用指定私钥加密密码
     * 注意：Druid ConfigTools中加密使用私钥，解密使用公钥
     *
     * @param privateKey 私钥
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String encrypt(String privateKey, String password) {
        if (StringUtils.isEmpty(password)) {
            return password;
        }

        try {
            return ConfigTools.encrypt(privateKey, password);
        } catch (Exception e) {
            log.error("密码加密失败，明文密码长度：{}", password.length(), e);
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 使用默认公钥解密密码
     *
     * @param encryptedPassword 加密后的密码
     * @return 解密后的明文密码
     */
    public static String decrypt(String encryptedPassword) {
        return decrypt(DEFAULT_PUBLIC_KEY, encryptedPassword);
    }

    /**
     * 使用指定公钥解密密码
     * 注意：Druid ConfigTools中加密使用私钥，解密使用公钥
     *
     * @param publicKey 公钥
     * @param encryptedPassword 加密后的密码
     * @return 解密后的明文密码
     * @throws RuntimeException 如果解密失败
     */
    public static String decrypt(String publicKey, String encryptedPassword) {
        if (StringUtils.isEmpty(encryptedPassword)) {
            return encryptedPassword;
        }

        try {
            return ConfigTools.decrypt(publicKey, encryptedPassword);
        } catch (Exception e) {
            String errorMsg = String.format("密码解密失败，可能原因：1.密码不是用对应私钥加密的；2.密码格式不正确；3.密钥不匹配。" +
                            "加密密码长度：%d，密码前10字符：%s",
                    encryptedPassword.length(),
                    encryptedPassword.length() > 10 ? encryptedPassword.substring(0, 10) + "..." : encryptedPassword);
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 判断密码是否已加密
     * 判断逻辑：
     * 1. 长度检查：RSA加密后的Base64字符串通常长度在80-200之间
     * 2. 字符检查：只包含Base64字符（A-Z, a-z, 0-9, +, /, =）
     * 3. 尝试解密验证（可选，避免异常）
     *
     * @param password 密码
     * @return true-已加密，false-未加密
     */
    public static boolean isEncrypted(String password) {
        if (StringUtils.isEmpty(password)) {
            return false;
        }

        // 1. 长度检查：RSA 512位加密后的Base64字符串通常在80-200字符之间
        if (password.length() < 50 || password.length() > 300) {
            return false;
        }

        // 2. Base64字符检查：只包含Base64有效字符
        if (!password.matches("^[A-Za-z0-9+/]*={0,2}$")) {
            return false;
        }

        // 3. 尝试解密验证（静默处理异常，避免抛出异常影响判断）
        try {
            String decrypted = decrypt(password);
            // 如果解密成功且结果不为空，认为是已加密的密码
            return !StringUtils.isEmpty(decrypted);
        } catch (Exception e) {
            // 解密失败，记录debug日志但不抛异常
            log.debug("尝试解密密码失败，可能不是用当前密钥加密的密码，密码长度：{}", password.length());
            return false;
        }
    }

    /**
     * 获取用于数据库连接的解密密码
     * 如果密码已加密则解密返回，如果未加密则直接返回
     *
     * @param password 密码（可能已加密或未加密）
     * @return 明文密码
     */
    public static String getDecryptedPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return password;
        }

        if (isEncrypted(password)) {
            return decrypt(password);
        } else {
            return password;
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        try {
            // 首先生成新的密钥对

            String originalPassword = "test123456";
            System.out.println("\n=== 使用生成的密钥对进行密码加密测试 ===");
            System.out.println("原始密码: " + originalPassword);

            // 使用默认密钥对进行加密解密
            String encryptedPassword = encrypt(DEFAULT_PRIVATE_KEY, originalPassword);
            System.out.println("加密后密码: " + encryptedPassword);
            System.out.println("加密后密码长度: " + encryptedPassword.length());

            String decryptedPassword = decrypt(DEFAULT_PUBLIC_KEY, encryptedPassword);
            System.out.println("解密后密码: " + decryptedPassword);

            System.out.println("密码匹配: " + originalPassword.equals(decryptedPassword));

            System.out.println("\n=== 测试isEncrypted方法 ===");
            System.out.println("是否已加密（原始密码）: " + isEncrypted(originalPassword));
            System.out.println("是否已加密（加密后密码）: " + isEncrypted(encryptedPassword));


            System.out.println("\n=== 测试getDecryptedPassword方法 ===");
            System.out.println("原始密码处理结果: " + getDecryptedPassword(originalPassword));
            System.out.println("加密密码处理结果: " + getDecryptedPassword(encryptedPassword));

        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
