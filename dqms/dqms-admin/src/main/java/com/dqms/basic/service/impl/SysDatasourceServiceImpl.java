package com.dqms.basic.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dqms.common.enums.TableInfo;
import com.dqms.common.exception.CustomException;
import com.dqms.utils.PasswordEncryptUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.basic.service.ISysDatasourceService;
import com.dqms.common.annotation.DataScope;
import com.dqms.common.enums.DsType;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.SftpUtil;
import com.dqms.common.utils.http.HttpUtils;
import com.dqms.mdm.util.MetaDataContext;
import com.dqms.utils.JdbcTemplateUtils;
import com.dqms.utils.SqlCheckUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据源管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-09
 */
@Service
@Slf4j
public class SysDatasourceServiceImpl implements ISysDatasourceService
{
    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;

    @Autowired
    private MetaDataContext metaDataContext;

    /**
     * 查询数据源管理
     *
     * @param datasourceId 数据源管理ID
     * @return 数据源管理
     */
    @Override
    public SysDatasource selectSysDatasourceById(Long datasourceId)
    {
        return sysDatasourceMapper.selectSysDatasourceById(datasourceId);
    }

    /**
     * 查询数据源管理
     *
     * @param datasourceName 数据源管理Name
     * @return 数据源管理
     */
    @Override
    public SysDatasource selectSysDatasourceByName(String datasourceName)
    {
        return sysDatasourceMapper.selectSysDatasourceByName(datasourceName);
    }

    /**
     * 查询数据源管理列表
     *
     * @param sysDatasource 数据源管理
     * @return 数据源管理
     */
    @Override
    @DataScope(systemAlias = "d")
    public List<SysDatasource> selectSysDatasourceList(SysDatasource sysDatasource)
    {
        return sysDatasourceMapper.selectSysDatasourceList(sysDatasource);
    }

    /**
     * 新增数据源管理
     *
     * @param sysDatasource 数据源管理
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false)
    public int insertSysDatasource(SysDatasource sysDatasource)
    {
        sysDatasource.setCreateTime(DateUtils.getNowDate());
        sysDatasource.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        sysDatasource.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        sysDatasource.setUpdateTime(DateUtils.getNowDate());
        sysDatasource.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        sysDatasource.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        
        // 在业务层处理密码加密
        if (sysDatasource.getPassword() != null && !PasswordEncryptUtils.isEncrypted(sysDatasource.getPassword())) {
            sysDatasource.setEncryptedPassword(sysDatasource.getPassword());
        }
        
        List<SysDatasource> tName=sysDatasourceMapper.listSysDatasourceByName(sysDatasource);
        if(tName!=null&&tName.size()!=0) {
            throw new CustomException("数据源名称已经存在！");
        }
        List<SysDatasource> tCode=sysDatasourceMapper.selectSysDatasourceByCode(sysDatasource);
        if(tCode!=null&&tCode.size()!=0) {
            throw new CustomException("数据源代码已经存在！");
        }
        int flag = sysDatasourceMapper.insertSysDatasource(sysDatasource);
//        if(flag==1){
//            JdbcTemplateUtils.addDataSourceMap(sysDatasource);
//        }
        return flag;
    }

    /**
     * 修改数据源管理
     *
     * @param sysDatasource 数据源管理
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false)
    public int updateSysDatasource(SysDatasource sysDatasource)
    {
        sysDatasource.setUpdateTime(DateUtils.getNowDate());
        sysDatasource.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        sysDatasource.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        
        // 密码处理：如果密码为空，则保留原密码
        if (sysDatasource.getPassword() == null || sysDatasource.getPassword().trim().isEmpty()) {
            // 获取原数据源信息保留密码
            SysDatasource originalDatasource = sysDatasourceMapper.selectSysDatasourceById(sysDatasource.getDatasourceId());
            if (originalDatasource != null) {
                // 直接设置原有密码，避免重复加密
                sysDatasource.setPassword(originalDatasource.getPassword());
            }
        } else {
            // 新密码需要加密，但只有明文时才加密
            if (!PasswordEncryptUtils.isEncrypted(sysDatasource.getPassword())) {
                sysDatasource.setPlainPassword(sysDatasource.getPassword());
            }
        }
        
        List<SysDatasource>tName=sysDatasourceMapper.listSysDatasourceByName(sysDatasource);
        if(tName.size()>1) {
            throw new CustomException("数据源名称已经存在！");
        }else if(tName.size()==1){
            if(!tName.get(0).getDatasourceId().equals(sysDatasource.getDatasourceId())){
                throw new RuntimeException("数据源名称已经存在！");
            }
        }
        List<SysDatasource> tCode=sysDatasourceMapper.selectSysDatasourceByCode(sysDatasource);
        if(tCode.size()>1) {
            throw new CustomException("数据源代码已经存在！");
        }else if(tCode.size()==1){
            if(!tCode.get(0).getDatasourceId().equals(sysDatasource.getDatasourceId())){
                throw new RuntimeException("数据源名称已经存在！");
            }
        }
        int flag = sysDatasourceMapper.updateSysDatasource(sysDatasource);
        if(flag==1){
            JdbcTemplateUtils.removeDataSourceMap(sysDatasource.getDatasourceId());
        }
        return flag;
    }

    /**
     * 批量删除数据源管理
     *
     * @param datasourceIds 需要删除的数据源管理ID
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false)
    public int deleteSysDatasourceByIds(Long[] datasourceIds)
    {
        int flag = sysDatasourceMapper.deleteSysDatasourceByIds(datasourceIds);
        if(flag!=0){
            JdbcTemplateUtils.removeDataSourceMapAll(datasourceIds);
        }
        return flag;
    }

    /**
     * 删除数据源管理信息
     *
     * @param datasourceId 数据源管理ID
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false)
    public int deleteSysDatasourceById(Long datasourceId)
    {
        int flag = sysDatasourceMapper.deleteSysDatasourceById(datasourceId);
        if(flag==1){
            JdbcTemplateUtils.removeDataSourceMap(datasourceId);
        }
        return flag;
    }

    @Override
    public Boolean testConnection(SysDatasource sysDatasource) {
        if(sysDatasource.getDsType().equals(DsType.DATABASE.getCode())){
            JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        }else if(sysDatasource.getDsType().equals(DsType.FTP.getCode())){
            Map map = HttpUtils.splitUrl(sysDatasource.getUrl());
            // getPassword()方法已经自动返回解密后的密码
            SftpUtil sftpUtil= SftpUtil.builder()
                    .username(sysDatasource.getUsername())
                    .password(sysDatasource.getPassword())
                    .host(map.get("host").toString())
                    .port((int) map.get("port"))
                    .build();
            sftpUtil.login();
            sftpUtil.logout();
        }
        return true;
    }




    @Override
    public List<SysDatasource> selectSysDatasourceAll() {
        return sysDatasourceMapper.selectSysDatasourceAll();
    }

    @Override
    public List<SysDatasource> selectSysDatasourceTreeAll() {
        return sysDatasourceMapper.selectSysDatasourceTreeAll();
    }

    @Override
    public List<Map<String, Object>> findTablesSelect(Long datasourceId) {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(datasourceId);
        return metaDataContext.getTablesAndView(sysDatasource);
    }

    @Override
    public JSONObject findTablesByDS(Long datasourceId) {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(datasourceId);
        List<Map<String, Object>> tables = metaDataContext.getTables(sysDatasource);
        for(Map<String, Object> tableMap : tables){
            tableMap.put("label", tableMap.get("tableName"));
        }
        Map<Object, List<Map<String, Object>>> map = tables.stream().collect(Collectors.groupingBy(e -> e.get(TableInfo.TABLE_SCHEMA.getCode())));
        JSONArray jsonArray = new JSONArray();
        int i = 1;
        for (Map.Entry<Object, List<Map<String, Object>>> list : map.entrySet()) {
            Object key = list.getKey();
            List<Map<String, Object>> value = list.getValue();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("label",key);
            jsonObject.put("id",i); i++;
            jsonObject.put("children",JSONArray.toJSON(value));
            jsonObject.put("icon", "iconTwo");
            jsonArray.add(jsonObject);
        }

        JSONObject jObject = new JSONObject();
        jObject.put("list",jsonArray);

        //List<Map<String,Object>> 转 Map<String,List<String>>
        Map<Object, List<String>> resultMap = tables.stream().collect(Collectors.toMap(m -> {return
                        m.get("schema");},m ->{
                    List<String> getNameList = new ArrayList<>();
                    getNameList.add(m.get("tableName").toString());
                    return getNameList;
                },
                (List<String> value1, List<String> value2) -> {
                    value1.addAll(value2);
                    return value1;
                } ));

        jObject.put("tables",resultMap);
        return jObject;
    }

    @Override
    public Map<Object, List<String>> findLexicon(Long datasourceId) {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(datasourceId);
        List<Map<String, Object>> list = metaDataContext.getTables(sysDatasource);
        //List<Map<String,Object>> 转 Map<String,List<String>>
        Map<Object, List<String>> resultMap = list.stream().collect(Collectors.toMap(map -> {return
                        map.get("schema");},m ->{
                    List<String> getNameList = new ArrayList<>();
                    getNameList.add(m.get("tableName").toString());
                    return getNameList;
                },
                (List<String> value1, List<String> value2) -> {
                    value1.addAll(value2);
                    return value1;
                } ));
        return resultMap;
    }

    @Override
    public Map excuteSql(Long datasourceId, String sqlText,int pageSize) {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(datasourceId);
        return metaDataContext.excuteByLimit(sysDatasource,sqlText,pageSize);
    }
    
    @Override
    public Map checkSql(Long datasourceId, String sqlText,int pageSize) {
    	Map resultMap = new HashMap();
    	List<Map<String, Object>>  infoList =  new ArrayList<Map<String,Object>>();
    	List<SysDatasource> list = sysDatasourceMapper.selectCheckRuleList();
    	Map<String, Object> mapS = new HashMap<String, Object>();
    	infoList=SqlCheckUtils.exec(sqlText);
    	resultMap.put("info",infoList);
    	resultMap.put("data",new ArrayList<>());
        return resultMap;
    }

    @Override
    public List<SysDatasource> findDataTypeAll() {
        return sysDatasourceMapper.findDataTypeAll();
    }
}
