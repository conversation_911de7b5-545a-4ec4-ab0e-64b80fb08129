package com.dqms.basic.domain;

import com.dqms.common.annotation.Excel;
import com.dqms.common.annotation.Excels;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.common.core.domain.entity.SysSystem;

import com.dqms.utils.PasswordEncryptUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 数据源管理对象 sys_datasource
 *
 * <AUTHOR>
 * @date 2021-03-09
 */
public class SysDatasource extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long datasourceId;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 代码 */
    @Excel(name = "代码")
    private String code;

    /** 数据库类型 */
    private Long dbType;

    @Excels({
            @Excel(name = "数据库类型", targetAttr = "datasourceTypeName", type = Excel.Type.EXPORT),
    })
    private SysDatasourceType sysDatasourceType;

    /** 所属系统 */
    private Long systemId;

    @Excels({
            @Excel(name = "所属系统", targetAttr = "name", type = Excel.Type.EXPORT),
    })
    private SysSystem sysSystem;

    /** url地址 */
    @Excel(name = "url地址")
    private String url;


    /** 认证方式 */
    private String authType;

    /** 用户名 */
    private String username;

    /** 密码 */
    private String password;

    /** kerberos认证主机名/别名 */
    private String kerberosUser;

    /** kerberos文件路径 */
    private String kerberosPath;

    /** 最小空闲连接 */
    private int minimumIdle;

    /** 最大连接数 */
    private int maximumPoolSize;

    /** 空闲连接超时时间 */
    private int idleTimeout;

    /** 连接最大存活时间 */
    private int maxLifetime;

    /** 连接超时时间 */
    private int connectionTimeout;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    private String dsType;

    private String icon;

    private Long id ;

    private String label;

    private String createTableSuffix;

    public String getCreateTableSuffix() {
        return createTableSuffix;
    }

    public void setCreateTableSuffix(String createTableSuffix) {
        this.createTableSuffix = createTableSuffix;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }


    public String getDsType() {
        return dsType;
    }

    public void setDsType(String dsType) {
        this.dsType = dsType;
    }

    public SysDatasourceType getSysDatasourceType() {
        return sysDatasourceType;
    }

    public void setSysDatasourceType(SysDatasourceType sysDatasourceType) {
        this.sysDatasourceType = sysDatasourceType;
    }

    public SysSystem getSysSystem() {
        return sysSystem;
    }

    public void setSysSystem(SysSystem sysSystem) {
        this.sysSystem = sysSystem;
    }

    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }
    public void setDbType(Long dbType)
    {
        this.dbType = dbType;
    }

    public Long getDbType()
    {
        return dbType;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setUrl(String url)
    {
        this.url = url;
    }

    public String getUrl()
    {
        return url;
    }
    public void setAuthType(String authType)
    {
        this.authType = authType;
    }

    public String getAuthType()
    {
        return authType;
    }
    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getUsername()
    {
        return username;
    }
    public void setPassword(String password)
    {
        // 直接设置密码，不在setter中加密
        // 加密逻辑移到业务层处理
        this.password = password;
    }

    /**
     * 获取解密后的密码用于数据库连接等实际使用
     * @return 解密后的明文密码
     */
    public String getPassword()
    {
        return PasswordEncryptUtils.getDecryptedPassword(this.password);
    }
    
    /**
     * 获取原始加密密码（用于数据库存储）
     * @return 加密后的密码
     */
    public String getEncryptedPassword()
    {
        return this.password;
    }
    
    /**
     * 直接设置加密密码（用于从数据库读取时）
     * @param encryptedPassword 已加密的密码
     */
    public void setEncryptedPassword(String encryptedPassword)
    {
        this.password = encryptedPassword;
    }
    
    /**
     * 设置明文密码并自动加密（用于业务逻辑）
     * @param plainPassword 明文密码
     */
    public void setPlainPassword(String plainPassword)
    {
        if (StringUtils.isNotEmpty(plainPassword) && !PasswordEncryptUtils.isEncrypted(plainPassword)) {
            this.password = PasswordEncryptUtils.encrypt(plainPassword);
        } else {
            this.password = plainPassword;
        }
    }

    public String getKerberosUser() {
        return kerberosUser;
    }

    public void setKerberosUser(String kerberosUser) {
        this.kerberosUser = kerberosUser;
    }

    public String getKerberosPath() {
        return kerberosPath;
    }

    public void setKerberosPath(String kerberosPath) {
        this.kerberosPath = kerberosPath;
    }

    public void setMinimumIdle(int minimumIdle)
    {
        this.minimumIdle = minimumIdle;
    }

    public int getMinimumIdle()
    {
        return minimumIdle;
    }

    public int getMaximumPoolSize() {
        return maximumPoolSize;
    }

    public void setMaximumPoolSize(int maximumPoolSize) {
        this.maximumPoolSize = maximumPoolSize;
    }

    public int getIdleTimeout() {
        return idleTimeout;
    }

    public void setIdleTimeout(int idleTimeout) {
        this.idleTimeout = idleTimeout;
    }

    public int getMaxLifetime() {
        return maxLifetime;
    }

    public void setMaxLifetime(int maxLifetime) {
        this.maxLifetime = maxLifetime;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("datasourceId", getDatasourceId())
            .append("name", getName())
            .append("code", getCode())
            .append("dbType", getDbType())
            .append("systemId", getSystemId())
            .append("url", getUrl())
            .append("authType", getAuthType())
            .append("username", getUsername())
            .append("password", getPassword())
            .append("kerberosUser", getKerberosUser())
            .append("kerberosPath", getKerberosPath())
            .append("minimumIdle", getMinimumIdle())
            .append("maximumPoolSize", getMaximumPoolSize())
            .append("idleTimeout", getIdleTimeout())
            .append("maxLifetime", getMaxLifetime())
            .append("connectionTimeout", getConnectionTimeout())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
