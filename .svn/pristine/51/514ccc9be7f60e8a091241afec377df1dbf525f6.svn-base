<template>
  <div class="app-container" style="height:100%;">
    <el-row type="flex" justify="start" align="top" style="height:calc(100vh - 140px);">
		<el-col :span="4" :xs="24" style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);" v-show="false">
			  <el-card shadow="hover">内容</el-card>
			  <el-checkbox-group v-model="checkList">
			    <el-checkbox v-for="item in dicManualDataInstallList" @change="checked=>changeProp(checked,item)" :key="item.propName" :label="item.propName" :value="item.propName" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #909399;background-color:#909399;" border ></el-checkbox>
			  </el-checkbox-group>
		</el-col>
		<el-col :span="24" :xs="24" border style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);padding-top:20px;padding-right:20px;">
	       		<el-form size="small" label-width="120px" ref="formO" :model="formO" :rules="rules">
			       <div v-for="(item, key) in propMap">
			       		<el-col :span="item[1].width" :xs="item[1].width">
			       		<div>
				       		<el-form-item :label="item[1].propComment" :prop="item[1].propName">
				       			<el-input v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='input'">
				       				<template slot="prepend" v-if="item[1].prefix!=null && item[1].prefix!=''">{{item[1].prefix}}{{formO.user_id}}</template>
				       				<template slot="append" v-if="item[1].postfix!=null && item[1].postfix!=''">{{item[1].postfix}}</template>
				       			</el-input>
				       		    <el-select v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" style="width:100%" :clearable="item[1].clearable=='Y'" v-if="item[1].type==='select'">
						         <el-option
						           v-for="dict in item[1].options"
						           :key="dict.detailCode"
						           :label="dict.detailName"
						           :value="dict.detailCode"
						         />
						        </el-select>
								<el-radio-group v-model="formO[item[1].propName]" v-if="item[1].type==='radio'">
								  <el-radio :label="dict.detailCode" v-for="dict in item[1].options" >{{dict.detailName}}</el-radio>
								</el-radio-group>
								<el-input v-model="formO[item[1].propName]" placeholder="禁用" :disabled="true" size="small" v-if="item[1].type==='auto'||item[1].type==='uuid'"/>
								<el-input type="textarea" v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" size="small" v-if="item[1].type==='textarea'"/>
								<el-input-number v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" :precision="2" :step="0.1" size="small" v-if="item[1].type==='number2'" style="width:100%"/>
								<el-input-number v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='number'" style="width:100%"/>
								<el-date-picker type="date" v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='timePicker'" style="width:100%"/>
								<el-date-picker type="datetime" v-model="formO[item[1].propName]"  :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='datePicker'" style="width:100%"/>
				       		</el-form-item>
				       	</div>	
				       	</el-col>	
			       </div>
	       		</el-form>
		</el-col>
	</el-row>
	<el-col :span="24" :xs="24" border style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);text-align:center;">
	   <el-button type="primary" round style="width:100px" @click="submitForm">保存</el-button>
	   <el-button type="warning" round style="width:100px" @click="resetForm">重置</el-button>
	   <el-button type="primary" round style="width:100px" @click="closeForm">关闭</el-button>
	 </el-col>	
  </div>
</template>

<script>
import { listDicManualDataDefine, getDicManualDataDefine, delDicManualDataDefine, addDicManualDataDefine, updateDicManualDataDefine, exportDicManualDataDefine,getDicManualDataDefineVo,validateRules,installDicManualDataDefine ,saveDicManualData,updateDicManualData} from "@/api/dic/dicManualDataDefine";
import { listDicManualDataInstall, getDicManualDataInstall, delDicManualDataInstall, addDicManualDataInstall, updateDicManualDataInstall, exportDicManualDataInstall } from "@/api/dic/dicManualDataInstall";
import { listDsmMasterDataRule} from "@/api/dsm/dsmMasterDataRule";
import { listDsmDimension } from "@/api/dsm/dsmDimension";
import { listDsmDimensionDetail } from "@/api/dsm/dsmDimensionDetail";
import { getToken } from "@/utils/auth";
import draggable from 'vuedraggable';
import { isArray } from 'util';

export default {
  name: "DicManualDataEnter",
  components: {
	  draggable
  },
  data() {
    return {
    	manualDataId:null,
    	form:{},
    	formO:{},
    	dicManualDataInstallList:[],
    	dsmMasterDataRuleList:[],
    	checkList:[],
    	ruleList:[],
    	propMap:new Map(),
    	prop:"",
    	relOptions:[],
    	typeList:[{
    		label: '输入型组件',
            options: [{
              value: 'input',
              label: '单行文本'
            }, {
              value: 'textarea',
              label: '多行文本'
            }, {
              value: 'number',
              label: '计数器'
            }, {
              value: 'password',
              label: '密码'
            }]
    	},{
    		label: '选择型组件',
            options: [{
              value: 'select',
              label: '下拉选择'
            }, {
              value: 'radio',
              label: '单选'
            }, {
              value: 'checkbox',
              label: '多选'
            }, {
              value: 'switch',
              label: '开关'
            }, {
              value: '滑块 ',
              label: 'slider'
            }, {
              value: 'timePicker',
              label: '时间选择'
            }, {
              value: 'timePickerArea',
              label: '时间范围'
            }, {
              value: 'datePicker',
              label: '日期选择'
            }, {
              value: 'datePickerArea',
              label: '日期范围'
            }, {
              value: 'upload',
              label: '上传'
            }]
    	}],
    	rules: {
    	}
    };
  },
  created() {
	  this.manualDataId = this.$route.query.manualDataId;
	  this.getList();
	  const detailParams = {pageNum: 1,pageSize: 10000};
      listDsmMasterDataRule(detailParams).then(response => {
        this.dsmMasterDataRuleList = response.rows;
      });
  },
  methods: {
      
    getList() {
    	getDicManualDataDefineVo(this.manualDataId).then(response => {
            this.form = response.data;
            this.dicManualDataInstallList = response.data.dicManualDataInstallList;
            this.initForm();
          })
    },
    submitForm(){
    	this.$refs["formO"].validate(valid => {
            if (valid) {
		    	this.formO.manualDataId=this.manualDataId;
		    	let list =[];
		    	const that=this;
		    	this.propMap.forEach(function(value,key){
		    		list.push(value);
		    	});
		    	let forms={
		    		manualDataId:this.manualDataId,
		    		dicManualDataInstallList:list
		    	};
		    	if(this.$route.query.xtype=="UPDATE"){
		    		updateDicManualData(this.formO).then(response => {
			           this.msgSuccess("修改成功,等待审核");
			           this.resetForm();
			        });
		    	}else{
		    		saveDicManualData(this.formO).then(response => {
			           this.msgSuccess("新增成功,等待审核");
			           this.resetForm();
			        });	  
		    	}
		    	 
            }
          });
    	
    },
    resetForm(){
    	this.formO={};
    	this.propMap =new Map();
        this.initForm();
    },
    closeForm(){
    	this.$store.dispatch("tagsView/delView", this.$route);
        this.$router.push({ path: '/dic/dicManualDataManager/'+this.manualDataId})
    },
    initForm(){
    	for(let i=0;i<this.dicManualDataInstallList.length;i++){
    		
        	if(this.dicManualDataInstallList[i].manualDataInstallId!=null){
        		if(this.$route.query[this.dicManualDataInstallList[i].propName]){
        			this.$set(this.formO, this.dicManualDataInstallList[i].propName, this.$route.query[this.dicManualDataInstallList[i].propName]);
        		}
        		//this.$set(this.rules, this.dicManualDataInstallList[i].propName , this.dicManualDataInstallList[i].dsmMasterDataRules); 
            	let roles=[];
            	if(this.dicManualDataInstallList[i].dsmMasterDataRules!=null){
            		for(var j=0;j<this.dicManualDataInstallList[i].dsmMasterDataRules.length;j++){
            			var item=this.dicManualDataInstallList[i].dsmMasterDataRules[j];
            			if(item.type=="Q"){
            				roles.push({ masterDataRuleId:item.masterDataRuleId,reg: item.rule, message: "数据不满足'"+item.name+"'", trigger: "blur" ,validator:validateRules,name:item.name});
            			}
            		}
            	}
            	if(this.dicManualDataInstallList[i].nullable=='N'){
            		if(this.dicManualDataInstallList[i].rules==null){this.dicManualDataInstallList[i].rules=[];}
            		roles.push({ masterDataRuleId:'nullable',required: true, message: this.dicManualDataInstallList[i].propComment+"不能为空", trigger: "blur" });
            	}
            	if(this.dicManualDataInstallList[i].columnSize!=null&&this.dicManualDataInstallList[i].columnSize>0&&this.dicManualDataInstallList[i].type!='number2'&&this.dicManualDataInstallList[i].type!='number'){
            		if(this.dicManualDataInstallList[i].rules==null){this.dicManualDataInstallList[i].rules=[];}
            		roles.push({ masterDataRuleId:'columnSize',max: this.dicManualDataInstallList[i].columnSize, message: this.dicManualDataInstallList[i].propComment+"不能超过"+this.dicManualDataInstallList[i].columnSize, trigger: "blur" });
            	}
            	if(this.dicManualDataInstallList[i].rules==null){this.dicManualDataInstallList[i].rules=[];}
        		this.dicManualDataInstallList[i].rules.push(roles);
        		this.$set(this.rules, this.dicManualDataInstallList[i].propName , roles); 
            	this.propMap.set(this.dicManualDataInstallList[i].propName,this.dicManualDataInstallList[i]);
            	this.checkList.push(this.dicManualDataInstallList[i].propName+"("+this.dicManualDataInstallList[i].propComment+")");
        	}
        }
    	this.$forceUpdate();
    }
    
  },
  computed: {

  }
};
</script>
<style>
	html,body{
	  height: 100%;
	}
</style>