import request from '@/utils/request'

// 查询任务调度计划列表
export function listTaskSchedule(query) {
  return request({
    url: '/task/taskSchedule/list',
    method: 'get',
    params: query
  })
}
export function listTaskScheduleByPage(query) {
  return request({
    url: '/task/taskSchedule/listByPage',
    method: 'get',
    params: query
  })
}
// 查询任务调度计划详细
export function getTaskSchedule(taskScheduleId) {
  return request({
    url: '/task/taskSchedule/' + taskScheduleId,
    method: 'get'
  })
}

// 新增任务调度计划
export function addTaskSchedule(data) {
  return request({
    url: '/task/taskSchedule',
    method: 'post',
    data: data
  })
}

// 修改任务调度计划
export function updateTaskSchedule(data) {
  return request({
    url: '/task/taskSchedule',
    method: 'put',
    data: data
  })
}

// 删除任务调度计划
export function delTaskSchedule(taskScheduleId) {
  return request({
    url: '/task/taskSchedule/' + taskScheduleId,
    method: 'delete'
  })
}

// 导出任务调度计划
export function exportTaskSchedule(query) {
  return request({
    url: '/task/taskSchedule/export',
    method: 'get',
    params: query
  })
}

export function runJob(jobId) {
	  const data = {
		jobId
	  }
	  return request({
	    url: '/task/taskSchedule/run',
	    method: 'put',
	    data: data
	  })
}

export function exportdemo(taskID,data) {
  return request({
    url: '/task/taskSchedule/exportdemo',
    method: 'get'

  })
}
