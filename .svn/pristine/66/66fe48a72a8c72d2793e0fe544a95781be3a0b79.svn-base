package com.dqms.utils;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.Vector;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.common.core.domain.entity.SysUser;
import com.dqms.utils.DateUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.system.domain.SysNotice;
import com.dqms.system.service.ISysConfigService;
import com.dqms.system.service.ISysNoticeService;
import com.dqms.system.service.ISysUserService;
import com.dqms.task.domain.EtlTaskSubscribe;
import com.dqms.task.domain.SysSystemSubscribe;
import com.dqms.task.enums.EtlConstants;
import com.dqms.task.enums.EtlTaskInstanceStatus;
import com.dqms.task.mapper.EtlTaskSubscribeMapper;
import com.dqms.task.mapper.SysSystemSubscribeMapper;
import com.wisdom.sms.net.http.Response;
import com.wisdom.sms.net.http.SmsServiceServiceLocator;
import com.wisdom.sms.net.http.SmsServiceSoapBindingStub;
import com.wisdom.sms.net.messages.SmsMessage;
import com.wisdom.sms.net.messages.SmsMessages;



public class NoticeByTaskRunThread implements Runnable {
	private static final Logger log = LoggerFactory.getLogger(NoticeByTaskRunThread.class);
	Long taskId=null;
	Long systemId=null;
	String status=null;
	String title=null;
	String content=null;
	public NoticeByTaskRunThread(Long taskId,Long systemId,String status,String title,String content) {
		this.taskId = taskId;
		this.systemId = systemId;
		this.status = status;
		this.title = title;
		this.content = content;
	}
	@Override
	public void run() {
    	//根据任务订阅进行推送
    	EtlTaskSubscribe etlTaskSubscribe = new EtlTaskSubscribe();
    	etlTaskSubscribe.setTaskId(taskId);
    	if(status.equals(EtlTaskInstanceStatus.SUCCESS.name())) {
    		etlTaskSubscribe.setSuccessFlag(EtlConstants.YES);
    	}else {
    		etlTaskSubscribe.setErrorFlag(EtlConstants.YES);
    	}
    	List<EtlTaskSubscribe> list = SpringUtils.getBean(EtlTaskSubscribeMapper.class).selectEtlTaskSubscribeList(etlTaskSubscribe);
    	if(list!=null&&list.size()>0) {
    		for(EtlTaskSubscribe s : list) {
    			SysUser user = SpringUtils.getBean(ISysUserService.class).selectUserById(s.getUserId());
				if(user==null){
					return;
				}
        		if(s.getNoticeFlag().equals(EtlConstants.YES)) {
        			sendNotice(s.getUserId().toString(), user.getUserName(), title, content+"(来源：任务订阅)");
        		}

        		if(s.getEmailFlag().equals(EtlConstants.YES)) {
        			try {
						sendMail(user.getEmail(),null,  title,content+"(来源：任务订阅)",null );
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
        		}
				if(s.getSmsFlag().equals(EtlConstants.YES)) {
					try {
						sendSMS(user.getPhonenumber(),content+"(来源：任务订阅)" );
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
        	}
    	}
    	//根据系统订阅进行推送
    	if(systemId!=null) {
    		SysSystemSubscribe sysSystemSubscribe = new SysSystemSubscribe();
    		sysSystemSubscribe.setSystemId(systemId);;
        	if(status.equals(EtlTaskInstanceStatus.SUCCESS.name())) {
        		sysSystemSubscribe.setSuccessFlag(EtlConstants.YES);
        	}else {
        		sysSystemSubscribe.setErrorFlag(EtlConstants.YES);
        	}
        	List<SysSystemSubscribe> listS = SpringUtils.getBean(SysSystemSubscribeMapper.class).selectSysSystemSubscribeList(sysSystemSubscribe);
        	if(listS!=null&&listS.size()>0) {
        		for(SysSystemSubscribe s : listS) {
        			SysUser user = SpringUtils.getBean(ISysUserService.class).selectUserById(s.getUserId());
					if(user==null){
						return;
					}
            		if(s.getNoticeFlag().equals(EtlConstants.YES)) {
            			sendNotice(s.getUserId().toString(), user.getUserName(), title, content+"(来源：系统订阅)");
            		}

            		if(s.getEmailFlag().equals(EtlConstants.YES)) {
            			try {
    						sendMail(user.getEmail(),null,  title,content+"(来源：系统订阅)",null );
    					} catch (Exception e) {
    						// TODO Auto-generated catch block
    						e.printStackTrace();
    					}
            		}
					if(s.getSmsFlag().equals(EtlConstants.YES)) {
						try {
							sendSMS(user.getPhonenumber(),content+"(来源：任务订阅)" );
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
            	}
        	}
    	}
	}
	public static boolean sendMail(String toAddr, String ccAddr, String subject, String content, Vector<File> attFiles) throws Exception {
		String fromHost = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.mail.fromHost");
		String fromNickName = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.mail.fromNickName");
		String fromAddr = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.mail.fromAddr");
		String fromPwd = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.mail.fromPwd");
		fromPwd = PasswordEncryptUtils.decrypt(fromPwd,PasswordEncryptUtils.key);
//		0 false 1 true
		boolean useSSL = "0".equals(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.mail.useSSL"))?false:true;
		String sslPort = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.mail.sslPort");
		log.info(String.format("邮箱发送开始===接收地址={},fromHost={},fromAddr={}",toAddr,fromHost,fromAddr));
		return send(fromHost, fromNickName, fromAddr, fromPwd, useSSL, sslPort, toAddr, ccAddr,  "", subject, content, attFiles);
	}
	/****
	 * 发送站内通知
	 * @param userNo
	 * @param userName
	 * @param title
	 * @param content
	 */
    public static void sendNotice(String userNo, String userName, String title,String content) {
		SysNotice notice = new SysNotice();
		notice.setNoticeTitle(title);
		notice.setNoticeType(EtlConstants.SYSTEM_NOTICE_TYPE_XX);
		notice.setStatus(EtlConstants.SYSTEM_NOTICE_STATUS_ZC);
		notice.setReadStatus(EtlConstants.NO);
		notice.setReceiverId(userNo);
		notice.setReceiverName(userName);
		notice.setNoticeContent(content);
		SpringUtils.getBean(ISysNoticeService.class).insertNotice(notice);
	}




	public static void sendSMS(String mobile, String content) {
		String url = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.url");
		String organizationId = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.organization-id");
		String needUseTemplateFlag = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.needUseTemplateFlag");
		String extension = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.extension");
		String serviceType = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.service-type");
		String username = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.username");
		String password = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sms.password");
		sendSMSLCZQ(url, organizationId, needUseTemplateFlag, extension, serviceType, username, password, mobile, content);
	}	
	/**
	 * 发送邮件
	 *
	 * @param fromHost
	 *            发件人Host
	 * @param fromNickNm
	 *            发件人昵称
	 * @param fromAddr
	 *            发件人地址
	 * @param fromPwd
	 *            发件人密码
	 * @param useSSL
	 *            是否使用SSL
	 * @param sslPort
	 *            SSL端口
	 * @param toAddr
	 *            收件人地址（多个用逗号隔开）
	 * @param ccAddr
	 *            抄送人地址（多个用逗号隔开）
	 * @param bccAddr
	 *            密送人地址（多个用逗号隔开）
	 * @param subject
	 *            主题
	 * @param content
	 *            正文
	 * @param attFiles
	 *            附件
	 */
	public static boolean send(String fromHost, String fromNickNm, String fromAddr, String fromPwd, boolean useSSL,
							   String sslPort, String toAddr, String ccAddr, String bccAddr, String subject, String content,
							   Vector<File> attFiles) {
		log.info("邮箱发送进行中===");
		Properties prop = new Properties();
		prop.setProperty("mail.transport.protocol", "smtp");
		prop.setProperty("mail.smtp.auth", "true");
		// "mail.smtp.host" or "mail.host"
		prop.setProperty("mail.smtp.host", fromHost);

		if (useSSL) {
			prop.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
			// "mail.smtp.socketFactory.port" or "mail.smtp.port"
			prop.setProperty("mail.smtp.socketFactory.port", sslPort);
			prop.setProperty("mail.smtp.port", sslPort);
		}

		Transport transport = null;
		try {
			// 1，创建session
			Session session = Session.getInstance(prop);
			// 若开启 debug 模式则可以看到程序发送Email的运行状态
			session.setDebug(false);

			// 2，通过session得到transport对象
			transport = session.getTransport("smtp");

			// 3，连接邮件服务器
			// host 可以没有，session中已经设置过了
			// transport.connect(host, user, password);
			transport.connect(fromAddr, fromPwd);

			// 4，创建邮件对象
			MimeMessage message = new MimeMessage(session);
			// 发件人
			// message.setFrom(new InternetAddress("<EMAIL>"));
			// message.setFrom(new InternetAddress("nickname<<EMAIL>>"));
			// message.setFrom(new InternetAddress(new
			// String(fromNickNm.getBytes("UTF-8"), "GB2312") + "<" +
			// fromAddr + ">"));
			message.setFrom(new InternetAddress(fromAddr, fromNickNm, "UTF-8"));

			// 收件人
			if (!StringUtils.isEmpty(toAddr)) {
				String[] toAddrSplits = toAddr.split(",");
				InternetAddress[] toAddrs = new InternetAddress[toAddrSplits.length];
				for (int i = 0; i < toAddrSplits.length; i++) {
					toAddrs[i] = new InternetAddress(toAddrSplits[i]);
				}
				message.setRecipients(MimeMessage.RecipientType.TO, toAddrs);
			}else {
				message.setRecipients(MimeMessage.RecipientType.TO,toAddr);
			}
			// 抄送人
			if (!StringUtils.isEmpty(ccAddr)) {
				String[] ccAddrSplits = ccAddr.split(",");
				InternetAddress[] ccAddrs = new InternetAddress[ccAddrSplits.length];
				for (int i = 0; i < ccAddrSplits.length; i++) {
					ccAddrs[i] = new InternetAddress(ccAddrSplits[i]);
				}
				message.setRecipients(MimeMessage.RecipientType.CC, ccAddrs);
			}else {
				message.setRecipients(MimeMessage.RecipientType.CC, ccAddr);
			}

			// 密送人
//			message.setRecipients(MimeMessage.RecipientType.BCC, bccAddr);

			// 标题
			message.setSubject(subject);
			// message.setText(text);// 简单正文

			// 创建邮件主体，用于存放正文和附件
			MimeMultipart multipart = new MimeMultipart();
			multipart.setSubType("mixed");
			log.info("邮箱发送===创建邮件主体");
			// 向主体添加正文
			MimeBodyPart contentBodyPart = new MimeBodyPart();
			contentBodyPart.setContent(content, "text/html;charset=UTF-8");// 为避免正文乱码
			multipart.addBodyPart(contentBodyPart);

			// 向主体添加附件
			if (attFiles != null && !attFiles.isEmpty()) {
				for (File attFile : attFiles) {
					MimeBodyPart attBodyPart = new MimeBodyPart();
					// DataHandler dataHandler = new DataHandler(new
					// FileDataSource(attFile.getAbsolutePath()));
					DataHandler dataHandler = new DataHandler(new FileDataSource(attFile));
					attBodyPart.setDataHandler(dataHandler);
					String fileName = dataHandler.getName();
					fileName = MimeUtility.encodeText(fileName);
					attBodyPart.setFileName(fileName);
					multipart.addBodyPart(attBodyPart);
				}
			}

			// 向邮件对象添加主体
			message.setContent(multipart);
			message.saveChanges();

			// 设置发送时间
			// message.setSentDate(new Date());

			// 5，发送邮件
			transport.sendMessage(message, message.getAllRecipients());
		} catch (UnsupportedEncodingException | MessagingException e) {
			log.error(e.getMessage(), e);
			return false;
		} finally {
			try {
				if (transport != null)
					transport.close();
			} catch (Exception e2) {
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 直接调用短信接口
	 * @param url 短信接口
	 * @param organizationId 机构代码
	 * @param needUseTemplateFlag 是否需要使用接口模板
	 * @param extension
	 * @param serviceType 服务种类代码
	 * @param username 短信用户
	 * @param password 短信密码
	 * @param mobile 电话
	 * @param content 内容
	 * @return
	 */
	public static boolean sendSMSLCZQ(String url, String organizationId, String needUseTemplateFlag, String extension, String serviceType, String username, String password, String mobile, String content) {
		SmsServiceSoapBindingStub binding = null;
		try {
			log.debug(">>>>调用联储短信发送方法>>>>");
			//创建binding对象
			SmsServiceServiceLocator smsServiceServiceLocator = new SmsServiceServiceLocator();
			smsServiceServiceLocator.setSmsServiceEndpointAddress(url);
			binding = (SmsServiceSoapBindingStub) smsServiceServiceLocator.getSmsService();
		} catch (Exception jre) {
			log.error("发送短信异常", jre);
			return false;
		}
		try {
			// binding.setTimeout(60000);
			//声明Response对象，该对象将在提交短信后包含提交的结果。
			Response value = null;
			//创建SmsMessages对象，该对象对应于上文下行短信格式中的Messages元素
			SmsMessages msgs = new com.wisdom.sms.net.messages.SmsMessages();
			//设置该批短信的OrganizationId，定义同上文下行短信格式中的Organization元素，
			msgs.setOrganizationId(organizationId);
			//设置是否调用的接口模板 true:使用 false:不使用
			msgs.setNeedUseTemplateFlag(needUseTemplateFlag);
			//请确保此值在每次通过接口发送时唯一
			msgs.setTaskId(UUID.randomUUID().toString());
			//taskid对应的备注字段  不传值
			//msgs.setTaskValue("admin123");
			//设置该批短信的Extension，定义同上文下行短信格式中的Extension元素
			msgs.setExtension(extension);
			//设置该批短信的ServiceType，定义同上文下行短信格式中的ServiceType元素
			msgs.setServiceType(serviceType);
			//设置该批短信的StartDate，定义同上文下行短信格式中的StartDate元素
			Date currDate = new Date();
			String dateStr = DateUtils.format(currDate, "yyyy-MM-dd");
			String startTime = DateUtils.format(currDate, "HH:mm");
			//String endTime = DateUtil.format(DateUtils.timeCal(currDate, Calendar.HOUR, 1), "HH:mm");
			//TODO 经过沟通，先给指定值，23:59,如果startTime为23:59，endTime给00:00
			String endTime = "23:59";
			if (endTime.equals(startTime)) {
				endTime = "00:00";
			}
			msgs.setStartDate(dateStr);
			//设置该批短信的StartTime，定义同上文下行短信格式中的StartTime元素
			msgs.setStartTime(startTime);
			//设置该批短信的EndDate，定义同上文下行短信格式中的EndDate元素
			msgs.setEndDate(dateStr);
			//设置该批短信的EndTime，定义同上文下行短信格式中的EndTime元素
			msgs.setEndTime(endTime);

			msgs.setPacketLength(500);

			Vector vec = new Vector();
			//创建SmsMessage对象，定义同上文下行短信格式中的Message元素
			SmsMessage msg = new SmsMessage();
			//设置该条短信的Receiver，定义同上文下行短信格式中的Receiver元素
			msg.setReceiver(mobile);
			//设置该条短信的Contents，定义同上文下行短信格式中的Contents元素
			msg.setContents(content);
			//设置该条短信的发送机构orgCode，定义同上文下行短信格式中的orgCode元素
//            msg.setOrgCode("100000");

			vec.add(msg);
			//设置该批短信的每一条短信，一批短信可以包含多条短信
			msgs.setMessages((SmsMessage[]) vec.toArray(new SmsMessage[vec.size()]));

			//提交该批短信，UserName是短信服务平台管理员分配的用户名， Password则是其对应的密码，用户名和密码用于验证发送者，只有验证通过才可能提交短信，msgs即为刚才创建的短信对象。
			value = binding.sendSMS(username, password, msgs);

			if (value == null) {
				log.error("发送信息返回结果为空");
				return false;
			}

			String status = value.getStatus();
			String message = value.getMessage();

			log.info("短信发送结果：status=[" + status + "],message=[" + message + "]");

			if ("OK".equalsIgnoreCase(status)) {
				return true;
			} else {
				return false;
			}
		} catch (Exception ex) {
			log.error("发送信息异常", ex);
		}
		return false;
	}
}
