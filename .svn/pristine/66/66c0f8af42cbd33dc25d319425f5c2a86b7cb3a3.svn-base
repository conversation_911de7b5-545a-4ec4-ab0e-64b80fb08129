<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscDesensitizationMapper">
    
    <resultMap type="DscDesensitization" id="DscDesensitizationResult">
        <result property="desensitizationId"    column="desensitization_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="desensitizationType"    column="desensitization_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tableId"    column="table_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />         
    </resultMap>

    <resultMap type="DscDesensitizationVo" id="DscDesensitizationVoResult">
        <result property="desensitizationId"    column="desensitization_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="desensitizationType"    column="desensitization_type"    />
        <result property="desensitizationTypeKey"    column="desensitization_type_key"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tableId"    column="table_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />
    </resultMap>

    <sql id="selectDscDesensitizationVo">
	    SELECT e.entity_id AS table_id ,e.table_name,ds.datasource_id ,ds.name AS datasource_name,ss.system_id ,ss.name AS system_name,
		t.desensitization_id, t.entity_id, t.desensitization_type, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time
		FROM mdm_data_entity e 
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
		LEFT JOIN sys_system ss ON r.system_id=ss.system_id
		LEFT JOIN dsc_desensitization t ON e.entity_id=t.entity_id
    </sql>

    <sql id="getDscDesensitizationVoList">
	    SELECT
			e.entity_id AS table_id,
			e.table_name,
			ds.datasource_id,
			ds.NAME AS datasource_name,
			ss.system_id,
			ss.NAME AS system_name,
			t.desensitization_id,
			t.entity_id,
			sd.dict_label AS desensitization_type,
			t.desensitization_type AS desensitization_type_key,
			t.create_by,
			t.update_by,
			t.create_id,
			t.update_id,
			t.create_time,
			t.update_time
		FROM
			dsc_desensitization t
			LEFT JOIN mdm_data_entity e ON e.entity_id = t.entity_id
			LEFT JOIN mdm_registry r ON e.registry_id = r.reg_id
			LEFT JOIN sys_datasource ds ON r.datasource_id = ds.datasource_id
			LEFT JOIN sys_system ss ON r.system_id = ss.system_id
			LEFT JOIN sys_dict_data sd ON t.desensitization_type = sd.dict_value AND sd.dict_type = 'dsc_desensitization_type'
    </sql>

    <select id="selectDscDesensitizationList" parameterType="DscDesensitization" resultMap="DscDesensitizationResult">
        <include refid="selectDscDesensitizationVo"/>
        <where>
            <if test="systemId != null and systemId != ''"> and ss.system_id = #{systemId}</if>
            <if test="datasourceId != null and datasourceId != ''"> and ds.datasource_id = #{datasourceId}</if>
            <if test="entityId != null "> and t.entity_id = #{entityId}</if>
            <if test="desensitizationType != null  and desensitizationType != ''"> and t.desensitization_type = #{desensitizationTypeKey}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
            and r.meta_type IN(1,2)
            and r.del_flag != '2'
        </where>
        order by t.create_time desc
    </select>

    <select id="getDscDesensitizationVoList" parameterType="DscDesensitizationVo" resultMap="DscDesensitizationVoResult">
        <include refid="getDscDesensitizationVoList"/>
        <where>
            <if test="systemId != null and systemId != ''"> and ss.system_id = #{systemId}</if>
            <if test="datasourceId != null and datasourceId != ''"> and ds.datasource_id = #{datasourceId}</if>
            <if test="entityId != null "> and t.entity_id = #{entityId}</if>
            <if test="desensitizationType != null  and desensitizationType != ''"> and t.desensitization_type = #{desensitizationType}</if>
            and r.del_flag != '2'
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.create_time desc
    </select>


    
    <select id="selectDscDesensitizationById" parameterType="Long" resultMap="DscDesensitizationResult">
        <include refid="selectDscDesensitizationVo"/>
        where t.desensitization_id = #{desensitizationId}
    </select>
        
    <insert id="insertDscDesensitization" parameterType="DscDesensitization" useGeneratedKeys="true" keyProperty="desensitizationId">
        insert into dsc_desensitization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityId != null">entity_id,</if>
            <if test="desensitizationType != null">desensitization_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityId != null">#{entityId},</if>
            <if test="desensitizationType != null">#{desensitizationType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDscDesensitization" parameterType="DscDesensitization">
        update dsc_desensitization
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="desensitizationType != null">desensitization_type = #{desensitizationType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where desensitization_id = #{desensitizationId}
    </update>

    <delete id="deleteDscDesensitizationById" parameterType="Long">
        delete from dsc_desensitization where desensitization_id = #{desensitizationId}
    </delete>

    <delete id="deleteDscDesensitizationByIds" parameterType="String">
        delete from dsc_desensitization where desensitization_id in 
        <foreach item="desensitizationId" collection="array" open="(" separator="," close=")">
            #{desensitizationId}
        </foreach>
    </delete>
</mapper>