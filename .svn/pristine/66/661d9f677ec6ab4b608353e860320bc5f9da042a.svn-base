package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 配置规则对象 dsm_master_data_install_rule
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public class DsmMasterDataInstallRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置规则 */
    private Long masterDataInstallRuleId;

    /** 主数据ID */
    @Excel(name = "主数据ID")
    private Long masterDataId;

    /** 配置ID */
    @Excel(name = "配置ID")
    private Long masterDataInstallId;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long masterDataRuleId;

    public void setMasterDataInstallRuleId(Long masterDataInstallRuleId)
    {
        this.masterDataInstallRuleId = masterDataInstallRuleId;
    }

    public Long getMasterDataInstallRuleId()
    {
        return masterDataInstallRuleId;
    }
    public void setMasterDataId(Long masterDataId)
    {
        this.masterDataId = masterDataId;
    }

    public Long getMasterDataId()
    {
        return masterDataId;
    }
    public void setMasterDataInstallId(Long masterDataInstallId)
    {
        this.masterDataInstallId = masterDataInstallId;
    }

    public Long getMasterDataInstallId()
    {
        return masterDataInstallId;
    }
    public void setMasterDataRuleId(Long masterDataRuleId)
    {
        this.masterDataRuleId = masterDataRuleId;
    }

    public Long getMasterDataRuleId()
    {
        return masterDataRuleId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("masterDataInstallRuleId", getMasterDataInstallRuleId())
            .append("masterDataId", getMasterDataId())
            .append("masterDataInstallId", getMasterDataInstallId())
            .append("masterDataRuleId", getMasterDataRuleId())
            .toString();
    }
}
