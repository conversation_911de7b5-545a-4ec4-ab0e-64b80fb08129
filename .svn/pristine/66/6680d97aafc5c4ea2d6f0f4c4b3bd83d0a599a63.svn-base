package com.dqms.task.job.executor;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.dqm.domain.DqmValidationRuleCate;
import com.dqms.dqm.enums.DqmConstants;
import com.dqms.dqm.service.IDqmValidationRuleCateService;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.service.IMdmRegistryService;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.service.IEtlTaskInstanceService;
import com.dqms.task.service.ITaskExecutor;

public class ETLDqmExecutor implements ITaskExecutor {
	private static final Logger log = LoggerFactory.getLogger(ETLDqmExecutor.class);
	
	public String execute(EtlTaskInstance ti) {
		int successNum=0;
		int errorNum=0;
		int allNum=0;
		int page =1;
		EtlTaskInstance etlTaskInstance = new EtlTaskInstance();
		etlTaskInstance.setTaskInstanceId(ti.getTaskInstanceId());
		etlTaskInstance.setBatchId(ti.getBatchId());
		List<DqmValidationRuleCate> list = getList(ti, page++);
		if(list!=null&&list.size()>0) {
			while(list!=null&&list.size()>0) {
				allNum=allNum+list.size();
				etlTaskInstance.setMsg("第"+(page-1)+"批次共"+list.size()+"个质量检查准备执行！");
				log.info("第"+(page-1)+"批次共"+list.size()+"个质量检查准备执行！");
				SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
				for(DqmValidationRuleCate cate : list) {
					try {
						log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+cate.getValidationName()+"开始执行");
						etlTaskInstance.setMsg(DateUtils.getTime()+":"+cate.getValidationName()+"开始执行");
						SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
						
						SpringUtils.getBean(IDqmValidationRuleCateService.class).executorDqmValidationRuleCate(cate,DqmConstants.DQMS_EXECUTION_STYLE_DD,ti.getLoadDate());
						
						log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+cate.getValidationName()+"执行成功");
						etlTaskInstance.setMsg(DateUtils.getTime()+":"+cate.getValidationName()+"执行成功");
						SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
						successNum=successNum+1;
					} catch (Exception e) {
						log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+cate.getValidationName()+"执行失败");
						etlTaskInstance.setMsg(DateUtils.getTime()+":"+cate.getValidationName()+"执行失败");
						SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
						errorNum=errorNum+1;
					}
				}
				list=getList(ti,page++);
			}
			log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+"已执行"+allNum+"个规则，其中：成功"+successNum+"个，失败"+errorNum+"个！");
			etlTaskInstance.setMsg("已执行"+allNum+"个数据质量检查，其中：成功"+successNum+"个，失败"+errorNum+"个！");
			SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
		}else {
			log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+"无可执行子任务！");
			etlTaskInstance.setMsg("无可执行子任务！");
			SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
		}
		return 	null;
	}
	
	
	public List<DqmValidationRuleCate> getList(EtlTaskInstance ti,int page) {
		List<DqmValidationRuleCate> list = null;
		if(ti.getEtlTask().getDatasourceId()==null) {
			if(page!=1) {
				return list;
			}
			list = SpringUtils.getBean(IDqmValidationRuleCateService.class).selectDqmValidationRuleCateListByTaskId(ti.getTaskId());
		}else {
			DqmValidationRuleCate dqmValidationRuleCate = new DqmValidationRuleCate();
			dqmValidationRuleCate.setDatasourceId(ti.getEtlTask().getDatasourceId());
			list = SpringUtils.getBean(IDqmValidationRuleCateService.class).selectDqmValidationRuleCateListByPage(ti.getEtlTask().getDatasourceId(),(page-1)*100,100);
		}
		return list;
	}
}
