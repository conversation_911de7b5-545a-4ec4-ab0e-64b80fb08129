package com.dqms.dqm.controller;

import java.util.List;

import com.dqms.common.utils.DateTimeUtils;
import com.dqms.dsm.domain.vo.DsmIndexVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dqm.domain.DqmValidationMould;
import com.dqms.dqm.domain.DqmValidationRuleCate;
import com.dqms.dqm.domain.DqmValidationSubscription;
import com.dqms.dqm.domain.vo.DqmValidationRuleCateVo;
import com.dqms.dqm.enums.DqmConstants;
import com.dqms.dqm.service.IDqmValidationMouldParameterService;
import com.dqms.dqm.service.IDqmValidationMouldService;
import com.dqms.dqm.service.IDqmValidationRuleCateService;
import com.dqms.dqm.service.IDqmValidationSubscriptionService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 检核规则Controller
 *
 * <AUTHOR>
 * @date 2021-07-21
 */
@RestController
@RequestMapping("/dqm/dqmValidationRuleCate")
public class DqmValidationRuleCateController extends BaseController
{
    @Autowired
    private IDqmValidationRuleCateService dqmValidationRuleCateService;
    @Autowired
    private IDqmValidationSubscriptionService dqmValidationSubscriptionService;
    @Autowired
    private IDqmValidationMouldService dqmValidationMouldService;
    @Autowired
    private IDqmValidationMouldParameterService dqmValidationMouldParameterService;

    /**
     * 查询检核规则列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:list')")
    @GetMapping("/list")
    public TableDataInfo list(DqmValidationRuleCate dqmValidationRuleCate)
    {
        startPage();
        List<DqmValidationRuleCate> list = dqmValidationRuleCateService.selectDqmValidationRuleCateList(dqmValidationRuleCate);
        return getDataTable(list);
    }
    
    @GetMapping("/listByUser")
    public TableDataInfo listByUser(DqmValidationRuleCate dqmValidationRuleCate)
    {
        startPage();
        List<DqmValidationRuleCate> list = dqmValidationRuleCateService.selectDqmValidationRuleCateListByUser(dqmValidationRuleCate);
        return getDataTable(list);
    }
    /**
     * 导出检核规则列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:export')")
    @Log(title = "检核规则", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmValidationRuleCate dqmValidationRuleCate)
    {
        List<DqmValidationRuleCateVo> list = dqmValidationRuleCateService.exportDqmValidationRuleCateVoList(dqmValidationRuleCate);
        ExcelUtil<DqmValidationRuleCateVo> util = new ExcelUtil<DqmValidationRuleCateVo>(DqmValidationRuleCateVo.class);
        return util.exportExcel(list, "dqmValidationRuleCate");
    }

    @GetMapping("/exportDemo")
    public AjaxResult exportDemo()
    {
        ExcelUtil<DqmValidationRuleCateVo> util = new ExcelUtil<>(DqmValidationRuleCateVo.class);
        return util.importTemplateExcel("数据质量检查检核规则模板");
    }

    @Log(title = "导入数据质量检查检核规则", businessType = BusinessType.IMPORT )
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DqmValidationRuleCateVo> util = new ExcelUtil<>(DqmValidationRuleCateVo.class);
        List<DqmValidationRuleCateVo> dqmValidationRuleCateVoList = util.importExcel(file.getInputStream());
        String message = dqmValidationRuleCateService.importDqmValidationRuleCate(dqmValidationRuleCateVoList, updateSupport);
        return AjaxResult.success(message);
    }


    /**
     * 获取检核规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:query')")
    @GetMapping(value = "/{validationRuleCateId}")
    public AjaxResult getInfo(@PathVariable("validationRuleCateId") Long validationRuleCateId)
    {
        return AjaxResult.success(dqmValidationRuleCateService.selectDqmValidationRuleCateById(validationRuleCateId));
    }

    /**
     * 新增检核规则
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:add')")
    @Log(title = "检核规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DqmValidationRuleCate dqmValidationRuleCate)
    {
    	if(dqmValidationRuleCate.getThresholdDatasourceId()==null||dqmValidationRuleCate.getThresholdDatasourceId().equals("")) {
    		dqmValidationRuleCate.setThresholdDatasourceId(0L);
    	}
        return toAjax(dqmValidationRuleCateService.insertDqmValidationRuleCate(dqmValidationRuleCate));
    }

    /**
     * 修改检核规则
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:edit')")
    @Log(title = "检核规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmValidationRuleCate dqmValidationRuleCate)
    {
    	if(dqmValidationRuleCate.getThresholdDatasourceId()==null||dqmValidationRuleCate.getThresholdDatasourceId().equals("")) {
    		dqmValidationRuleCate.setThresholdDatasourceId(0L);
    	}
        return toAjax(dqmValidationRuleCateService.updateDqmValidationRuleCate(dqmValidationRuleCate));
    }

    /**
     * 删除检核规则
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationRuleCate:remove')")
    @Log(title = "检核规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{validationRuleCateIds}")
    public AjaxResult remove(@PathVariable Long[] validationRuleCateIds)
    {
        return toAjax(dqmValidationRuleCateService.deleteDqmValidationRuleCateByIds(validationRuleCateIds));
    }
    /**
     * 新增订阅
     */
    @Log(title = "订阅检查规则任务", businessType = BusinessType.INSERT)
    @DeleteMapping("addSubscription/{validationRuleCateIds}")
    public AjaxResult insertDqmValidationSubscription(@PathVariable Long[] validationRuleCateIds)
    {
        return toAjax(dqmValidationSubscriptionService.insertDqmValidationSubscription(validationRuleCateIds));
    }

    /**
     * 订阅
     */
    @Log(title = "订阅检查规则任务", businessType = BusinessType.INSERT)
    @PutMapping("subscriptionChange")
    public AjaxResult subscriptionChange(@RequestBody DqmValidationSubscription dqmValidationSubscription)
    {
       return toAjax(dqmValidationSubscriptionService.subscriptionChange(dqmValidationSubscription));
        
    }
    /**
     * 执行规则任务
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationCate:executorDqmRuleCate')")
    @Log(title = "执行任务", businessType = BusinessType.OTHER)
    @PostMapping("executorDqmRuleCate")
    public AjaxResult executorDqmValidationRuleCate(@RequestBody DqmValidationRuleCate dqmValidationRuleCate)
    {
        String loadDate=DateTimeUtils.now2StrDate().replaceAll("-", "");
        return toAjax(dqmValidationRuleCateService.executorDqmValidationRuleCate(dqmValidationRuleCate,DqmConstants.DQMS_EXECUTION_STYLE_SD,loadDate));
    }

    /**
     * 获取模板信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationCate:getMould')")
    @GetMapping("/getMould")
    public AjaxResult getMould(DqmValidationMould dqmValidationMould)
    {
        return AjaxResult.success(dqmValidationMouldService.selectMould(dqmValidationMould));
    }

    /**
     * 获取条件信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationCate:getConDitionById')")
    @GetMapping("/getConditionById/{validationMouldId}")
    public AjaxResult getConditionById(@PathVariable Integer validationMouldId)
    {
        return AjaxResult.success(dqmValidationMouldParameterService.getConditionById(validationMouldId));
    }

    @GetMapping("/dqmlist")
    public TableDataInfo dqmlist(DqmValidationRuleCate dqmValidationRuleCate)
    {
        startPage();
        List<DqmValidationRuleCate> list = dqmValidationRuleCateService.selectDqmValidationRuleCateListByTaskId(dqmValidationRuleCate.getTaskId());
        return getDataTable(list);
    }

    @GetMapping("/unDqmlist")
    public TableDataInfo unDqmlist(DqmValidationRuleCate dqmValidationRuleCate)
    {
        startPage();
        List<DqmValidationRuleCate> list = dqmValidationRuleCateService.selectUnDqmValidationRuleCateListByTaskId(dqmValidationRuleCate);
        return getDataTable(list);
    }
    /**
     * 获取模板信息
     */
    @GetMapping("/getRuleCateSystem")
    public AjaxResult getRuleCateSystem(DqmValidationRuleCate dqmValidationRuleCate)
    {
        return AjaxResult.success(dqmValidationRuleCateService.getRuleCateSystem(dqmValidationRuleCate));
    }
    
    @GetMapping("/getRuleCateStatus")
    public AjaxResult getRuleCateStatus(DqmValidationRuleCate dqmValidationRuleCate)
    {
        return AjaxResult.success(dqmValidationRuleCateService.getRuleCateStatus(dqmValidationRuleCate));
    }
    /**
     * 获取模板信息
     */
    @GetMapping("/getRuleCateType")
    public AjaxResult getRuleCateType(DqmValidationRuleCate dqmValidationRuleCate)
    {
        return AjaxResult.success(dqmValidationRuleCateService.getRuleCateType(dqmValidationRuleCate));
    }
}
