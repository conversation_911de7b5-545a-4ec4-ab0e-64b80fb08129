package com.dqms.dsm.service;

import java.util.List;

import com.dqms.dsm.domain.DsmIndex;
import com.dqms.dsm.domain.vo.DsmIndexRelVo;
import com.dqms.dsm.domain.vo.DsmIndexVo;

/**
 * 指标管理Service接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface IDsmIndexService
{
    /**
     * 查询指标管理
     *
     * @param indexId 指标管理ID
     * @return 指标管理
     */
    public DsmIndex selectDsmIndexById(Long indexId);
    public DsmIndexVo selectDsmIndexVoById(Long indexId);

    /**
     * 查询指标管理列表
     *
     * @param dsmIndex 指标管理
     * @return 指标管理集合
     */
    public List<DsmIndex> selectDsmIndexList(DsmIndex dsmIndex);
    public List<DsmIndex> selectDsmIndexListByPage(DsmIndex dsmIndex);
    public List<DsmIndex> selectDsmIndexListForShow(DsmIndex dsmIndex);
    public List<DsmIndexVo> selectDsmIndexVoList(DsmIndex dsmIndex);
    public List<DsmIndex> selectDsmIndexListByNo(DsmIndex dsmIndex);
    public List<DsmIndex> selectDsmIndexListMap(DsmIndex dsmIndex);
    public List<DsmIndexRelVo> selectDsmIndexListRel(DsmIndex dsmIndex);

    /**
     * 新增指标管理
     *
     * @param dsmIndex 指标管理
     * @return 结果
     */
    public int insertDsmIndex(DsmIndex dsmIndex,String oldId);

    /**
     * 修改指标管理
     *
     * @param dsmIndex 指标管理
     * @return 结果
     */
    public int updateDsmIndex(DsmIndex dsmIndex);

    public int updateDsmIndexStatus(DsmIndex dsmIndex);

    /**
     * 批量删除指标管理
     *
     * @param indexIds 需要删除的指标管理ID
     * @return 结果
     */
    public int deleteDsmIndexByIds(Long[] indexIds);

    /**
     * 删除指标管理信息
     *
     * @param indexId 指标管理ID
     * @return 结果
     */
    public int deleteDsmIndexById(Long indexId);

    /**
     * 导入指标管理
     * @param etlTaskList
     * @param isUpdateSupport
     * @return
     */
    public String importDsmIndex(List<DsmIndexVo> dsmIndexList, Boolean isUpdateSupport);
}
