import request from '@/utils/request'

// 查询主题管理列表
export function listMdmTheme(query) {
  return request({
    url: '/mdm/mdmTheme/list',
    method: 'get',
    params: query
  })
}

// 查询主题管理下拉列表
export function listMdmThemeAll(query) {
  return request({
    url: '/mdm/mdmTheme/findAll',
    method: 'get',
    params: query
  })
}

// 查询主题管理详细
export function getMdmTheme(themeId) {
  return request({
    url: '/mdm/mdmTheme/' + themeId,
    method: 'get'
  })
}

// 新增主题管理
export function addMdmTheme(data) {
  return request({
    url: '/mdm/mdmTheme',
    method: 'post',
    data: data
  })
}

// 修改主题管理
export function updateMdmTheme(data) {
  return request({
    url: '/mdm/mdmTheme',
    method: 'put',
    data: data
  })
}

// 删除主题管理
export function delMdmTheme(themeId) {
  return request({
    url: '/mdm/mdmTheme/' + themeId,
    method: 'delete'
  })
}

// 导出主题管理
export function exportMdmTheme(query) {
  return request({
    url: '/mdm/mdmTheme/export',
    method: 'get',
    params: query
  })
}

// 下载主题导入模板
export function importTemplate() {
  return request({
    url: '/mdm/mdmTheme/importTemplate',
    method: 'get'
  })
}
