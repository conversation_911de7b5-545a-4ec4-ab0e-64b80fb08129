package com.dqms.dic.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dic.domain.DicFileDefine;
import com.dqms.dic.service.IDicFileDefineService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 文件补录配置Controller
 *
 * <AUTHOR>
 * @date 2022-04-14
 */
@RestController
@RequestMapping("/dic/dicFileDefine")
public class DicFileDefineController extends BaseController
{
    @Autowired
    private IDicFileDefineService dicFileDefineService;

    /**
     * 查询文件补录配置列表
     */
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:list')")
    @GetMapping("/list")
    public TableDataInfo list(DicFileDefine dicFileDefine)
    {
        startPage();
        List<DicFileDefine> list = dicFileDefineService.selectDicFileDefineList(dicFileDefine);
        return getDataTable(list);
    }
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:list')")
    @GetMapping("/listAll")
    public TableDataInfo listAll(DicFileDefine dicFileDefine)
    {
        List<DicFileDefine> list = dicFileDefineService.selectDicFileDefineList(dicFileDefine);
        return getDataTable(list);
    }
    /**
     * 导出文件补录配置列表
     */
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:export')")
    @Log(title = "文件补录配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DicFileDefine dicFileDefine)
    {
        List<DicFileDefine> list = dicFileDefineService.selectDicFileDefineList(dicFileDefine);
        ExcelUtil<DicFileDefine> util = new ExcelUtil<DicFileDefine>(DicFileDefine.class);
        return util.exportExcel(list, "dicFileDefine");
    }

    /**
     * 获取文件补录配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:query')")
    @GetMapping(value = "/{fileDefineId}")
    public AjaxResult getInfo(@PathVariable("fileDefineId") Long fileDefineId)
    {
        return AjaxResult.success(dicFileDefineService.selectDicFileDefineById(fileDefineId));
    }

    /**
     * 新增文件补录配置
     */
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:add')")
    @Log(title = "文件补录配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DicFileDefine dicFileDefine)
    {
        return toAjax(dicFileDefineService.insertDicFileDefine(dicFileDefine));
    }

    /**
     * 修改文件补录配置
     */
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:edit')")
    @Log(title = "文件补录配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DicFileDefine dicFileDefine)
    {
        return toAjax(dicFileDefineService.updateDicFileDefine(dicFileDefine));
    }

    /**
     * 删除文件补录配置
     */
    @PreAuthorize("@ss.hasPermi('dic:dicFileDefine:remove')")
    @Log(title = "文件补录配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{fileDefineIds}")
    public AjaxResult remove(@PathVariable Long[] fileDefineIds)
    {
        return toAjax(dicFileDefineService.deleteDicFileDefineByIds(fileDefineIds));
    }
}
