package com.dqms.dam.controller;

import java.util.List;

import com.dqms.common.core.page.PageDomain;
import com.dqms.common.core.page.TableSupport;
import com.dqms.dam.domain.vo.DamAssetsVo;
import com.dqms.dsc.domain.vo.DscEntityPropVo;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.service.IDamAssetsService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据资产Controller
 *
 * <AUTHOR>
 * @date 2021-06-08
 */
@RestController
@RequestMapping("/dam/damAssets")
public class DamAssetsController extends BaseController
{
    @Autowired
    private IDamAssetsService damAssetsService;

    /**
     * 查询数据资产列表
     */
    @PreAuthorize("@ss.hasPermi('dam:damAssets:list')")
    @GetMapping("/list")
    public TableDataInfo list(DamAssets damAssets)
    {
        startPage();
        List<DamAssets> list = damAssetsService.selectDamAssetsList(damAssets);
        return getDataTable(list);
    }

    @GetMapping("/mylist")
    public TableDataInfo mylist(DamAssets damAssets)
    {
//        startPage();
        return damAssetsService.selectMyDamAssetsList(damAssets);
    }

    @GetMapping("/listAnaLyse")
    public TableDataInfo listAnaLyse(DamAssets damAssets)
    {
        startPage();
        List<DamAssets> list = damAssetsService.selectDamAssetsAnaLyseList(damAssets);
        return getDataTable(list);
    }
    
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<DamAssetsVo> util = new ExcelUtil<>(DamAssetsVo.class);
        return util.importTemplateExcel("数据资产");
    }

    /**
     * 导入指标分级列表
     */
    @Log(title = "导入数据资产", businessType = BusinessType.IMPORT )
    @PreAuthorize("@ss.hasPermi('dsc:damAssets:import')")
    @PostMapping("/import")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DamAssetsVo> util = new ExcelUtil<>(DamAssetsVo.class);
        List<DamAssetsVo> damAssetsVoList = util.importExcel(file.getInputStream());
        String message = damAssetsService.importDamAssets(damAssetsVoList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导出数据资产列表
     */
    @PreAuthorize("@ss.hasPermi('dam:damAssets:export')")
    @Log(title = "数据资产", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DamAssetsVo damAssetsVo)
    {
        List<DamAssetsVo> list = damAssetsService.selectDamAssetsVoList(damAssetsVo);
        ExcelUtil<DamAssetsVo> util = new ExcelUtil<DamAssetsVo>(DamAssetsVo.class);
        return util.exportExcel(list, "damAssetsVo");
    }

    /**
     * 获取数据资产详细信息
     */
    @PreAuthorize("@ss.hasPermi('dam:damAssets:query')")
    @GetMapping(value = "/{damAssetsId}")
    public AjaxResult getInfo(@PathVariable("damAssetsId") Long damAssetsId)
    {
        return AjaxResult.success(damAssetsService.selectDamAssetsById(damAssetsId));
    }

    @GetMapping(value = "/getInfoVo/{damAssetsId}")
    public AjaxResult getInfoVo(@PathVariable("damAssetsId") Long damAssetsId)
    {
        return AjaxResult.success(damAssetsService.selectDamAssetsVoById(damAssetsId));
    }
    /**
     * 新增数据资产
     */
    @PreAuthorize("@ss.hasPermi('dam:damAssets:add')")
    @Log(title = "数据资产", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DamAssets damAssets)
    {
        return toAjax(damAssetsService.insertDamAssets(damAssets));
    }

    /**
     * 修改数据资产
     */
    @PreAuthorize("@ss.hasPermi('dam:damAssets:edit')")
    @Log(title = "数据资产", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DamAssets damAssets)
    {
        return toAjax(damAssetsService.updateDamAssets(damAssets));
    }

    /**
     * 删除数据资产
     */
    @PreAuthorize("@ss.hasPermi('dam:damAssets:remove')")
    @Log(title = "数据资产", businessType = BusinessType.DELETE)
	@DeleteMapping("/{damAssetsIds}")
    public AjaxResult remove(@PathVariable Long[] damAssetsIds)
    {
        return toAjax(damAssetsService.deleteDamAssetsByIds(damAssetsIds));
    }
}
