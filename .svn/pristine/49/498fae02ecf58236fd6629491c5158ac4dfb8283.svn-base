package com.dqms.dic.mapper;

import java.util.List;
import com.dqms.dic.domain.DicManualDataDefine;

/**
 * 手工数据配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-06-28
 */
public interface DicManualDataDefineMapper 
{
    /**
     * 查询手工数据配置
     * 
     * @param manualDataId 手工数据配置ID
     * @return 手工数据配置
     */
    public DicManualDataDefine selectDicManualDataDefineById(Long manualDataId);

    /**
     * 查询手工数据配置列表
     * 
     * @param dicManualDataDefine 手工数据配置
     * @return 手工数据配置集合
     */
    public List<DicManualDataDefine> selectDicManualDataDefineList(DicManualDataDefine dicManualDataDefine);
    
    public List<DicManualDataDefine> selectDicManualDataDefineListByUse(DicManualDataDefine dicManualDataDefine);
    
    public List<DicManualDataDefine> selectDicManualDataDefineListByApply(DicManualDataDefine dicManualDataDefine);

    /**
     * 新增手工数据配置
     * 
     * @param dicManualDataDefine 手工数据配置
     * @return 结果
     */
    public int insertDicManualDataDefine(DicManualDataDefine dicManualDataDefine);

    /**
     * 修改手工数据配置
     * 
     * @param dicManualDataDefine 手工数据配置
     * @return 结果
     */
    public int updateDicManualDataDefine(DicManualDataDefine dicManualDataDefine);

    /**
     * 删除手工数据配置
     * 
     * @param manualDataId 手工数据配置ID
     * @return 结果
     */
    public int deleteDicManualDataDefineById(Long manualDataId);

    /**
     * 批量删除手工数据配置
     * 
     * @param manualDataIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDicManualDataDefineByIds(Long[] manualDataIds);
}
