package com.dqms.task.job;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.service.ISysDatasourceService;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.core.redis.RedisCache;
import com.dqms.common.exception.regException;
import com.dqms.common.utils.DateTimeUtils;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.mdm.domain.MdmCollectHis;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.service.IMdmRegistryService;
import com.dqms.system.service.ISysConfigService;
import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.enums.EtlConstants;
import com.dqms.task.enums.EtlTaskInstanceStatus;
import com.dqms.task.job.executor.ETLDlcExecutor;
import com.dqms.task.job.executor.ETLDqmExecutor;
import com.dqms.task.job.executor.ETLMdmExecutor;
import com.dqms.task.job.executor.ETLTaskExecutor;
import com.dqms.task.service.IEtlTaskInstanceService;
import com.dqms.task.service.IEtlTaskService;
import com.dqms.task.service.ITaskExecutor;
import com.dqms.utils.JdbcTemplateUtils;
import com.dqms.utils.NoticeUtils;
import com.dqms.utils.ThreadPoolUtils;



public class MdmRunThread implements Runnable {
	private static final Logger log = LoggerFactory.getLogger(MdmRunThread.class);
	
	private MdmRegistry mdmRegistry;
	
	private EtlTaskInstance etlTaskInstance;
	
	public MdmRunThread(EtlTaskInstance etlTaskInstance ,MdmRegistry mdmRegistry) {
		this.mdmRegistry = mdmRegistry;
		EtlTaskInstance eti = new EtlTaskInstance();
		eti.setTaskInstanceId(etlTaskInstance.getTaskInstanceId());
		eti.setBatchId(etlTaskInstance.getBatchId());
		this.etlTaskInstance = eti;
	}
	@Override
	public void run() {
		try {
			etlTaskInstance.setMsg(DateUtils.getTime()+":"+mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName()+"开始执行");
			SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);

            MdmCollectHis mdmCollectHis = new MdmCollectHis();
            try{
					SpringUtils.getBean(IMdmRegistryService.class).collectData(mdmRegistry, "2", mdmCollectHis);
            }catch (Exception e){
                mdmCollectHis.setAcqResultState(MdmConstants.ACQ_FAIL);
                StringWriter stringWriter=new StringWriter();
                e.printStackTrace(new PrintWriter(stringWriter,true));
                mdmCollectHis.setErrorMsg(stringWriter.getBuffer().toString());
                e.printStackTrace();
                throw new regException(e.getMessage());
            }finally {
                SpringUtils.getBean(IMdmRegistryService.class).insertCollectLog(mdmCollectHis,mdmRegistry,"2");
            }

			log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName()+"执行成功");
			etlTaskInstance.setMsg("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName()+"执行成功");
			SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
		} catch (Exception e) {
			etlTaskInstance.setMsg("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName()+"执行失败");
			log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+DateUtils.getTime()+":"+mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName()+"执行失败");
			SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
		}finally {
			ETLMdmExecutor.flag.decrementAndGet();
		}
	}

}
