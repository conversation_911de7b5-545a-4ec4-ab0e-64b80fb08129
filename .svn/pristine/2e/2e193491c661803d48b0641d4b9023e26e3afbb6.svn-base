package com.dqms.dsc.service;

import java.util.List;
import com.dqms.dsc.domain.DscEntityPropSystem;

/**
 * 字段应用系统Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscEntityPropSystemService 
{
    /**
     * 查询字段应用系统
     * 
     * @param entityPropId 字段应用系统ID
     * @return 字段应用系统
     */
    public DscEntityPropSystem selectDscEntityPropSystemById(Long entityPropId);

    /**
     * 查询字段应用系统列表
     * 
     * @param dscEntityPropSystem 字段应用系统
     * @return 字段应用系统集合
     */
    public List<DscEntityPropSystem> selectDscEntityPropSystemList(DscEntityPropSystem dscEntityPropSystem);

    /**
     * 新增字段应用系统
     * 
     * @param dscEntityPropSystem 字段应用系统
     * @return 结果
     */
    public int insertDscEntityPropSystem(DscEntityPropSystem dscEntityPropSystem);

    /**
     * 修改字段应用系统
     * 
     * @param dscEntityPropSystem 字段应用系统
     * @return 结果
     */
    public int updateDscEntityPropSystem(DscEntityPropSystem dscEntityPropSystem);

    /**
     * 批量删除字段应用系统
     * 
     * @param entityPropIds 需要删除的字段应用系统ID
     * @return 结果
     */
    public int deleteDscEntityPropSystemByIds(Long[] entityPropIds);

    /**
     * 删除字段应用系统信息
     * 
     * @param entityPropId 字段应用系统ID
     * @return 结果
     */
    public int deleteDscEntityPropSystemById(Long entityPropId);
}
