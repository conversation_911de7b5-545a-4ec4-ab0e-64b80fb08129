<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="所属维度" prop="dimensionId">
        <el-input
          v-model="queryParams.dimensionId"
          placeholder="请输入所属维度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="名称" prop="detailName">
        <el-input
          v-model="queryParams.detailName"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="编码" prop="detailCode">
        <el-input
          v-model="queryParams.detailCode"
          placeholder="请输入编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dsm:dsmDimensionDetaiTemp:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dsm:dsmDimensionDetaiTemp:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dsm:dsmDimensionDetaiTemp:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dsm:dsmDimensionDetaiTemp:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dsmDimensionDetaiTempList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="维度明细ID" align="center" prop="dimensionDetaiTempId" />
      <el-table-column label="所属维度" align="center" prop="dimensionId" />
      <el-table-column label="名称" align="center" prop="detailName" />
      <el-table-column label="编码" align="center" prop="detailCode" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsm:dsmDimensionDetaiTemp:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsm:dsmDimensionDetaiTemp:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改维度明细临时对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属维度" prop="dimensionId">
          <el-input v-model="form.dimensionId" placeholder="请输入所属维度" clearable/>
        </el-form-item>
        <el-form-item label="名称" prop="detailName">
          <el-input v-model="form.detailName" placeholder="请输入名称" clearable/>
        </el-form-item>
        <el-form-item label="编码" prop="detailCode">
          <el-input v-model="form.detailCode" placeholder="请输入编码" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDsmDimensionDetaiTemp, getDsmDimensionDetaiTemp, delDsmDimensionDetaiTemp, addDsmDimensionDetaiTemp, updateDsmDimensionDetaiTemp, exportDsmDimensionDetaiTemp } from "@/api/dsm/dsmDimensionDetaiTemp";

export default {
  name: "DsmDimensionDetaiTemp",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 维度明细临时表格数据
      dsmDimensionDetaiTempList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dimensionId: null,
        detailName: null,
        detailCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dimensionId: [
          { required: true, message: "所属维度不能为空", trigger: "blur" }
        ],
        detailName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        detailCode: [
          { required: true, message: "编码不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询维度明细临时列表 */
    getList() {
      this.loading = true;
      listDsmDimensionDetaiTemp(this.queryParams).then(response => {
        this.dsmDimensionDetaiTempList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dimensionDetaiTempId: null,
        dimensionId: null,
        detailName: null,
        detailCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dimensionDetaiTempId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加维度明细临时";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dimensionDetaiTempId = row.dimensionDetaiTempId || this.ids
      getDsmDimensionDetaiTemp(dimensionDetaiTempId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改维度明细临时";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dimensionDetaiTempId != null) {
            updateDsmDimensionDetaiTemp(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDsmDimensionDetaiTemp(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dimensionDetaiTempIds = row.dimensionDetaiTempId || this.ids;
      this.$confirm('是否确认删除维度明细临时编号为"' + dimensionDetaiTempIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDsmDimensionDetaiTemp(dimensionDetaiTempIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有维度明细临时数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDsmDimensionDetaiTemp(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
