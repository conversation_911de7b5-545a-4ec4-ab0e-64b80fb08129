<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="字典名称" prop="dimensionName">
        <el-input
          v-model="queryParams.dimensionName"
          placeholder="请输入字典名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="字典类型" prop="dimensionCode">
        <el-input
          v-model="queryParams.dimensionCode"
          placeholder="请输入字典类型"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="明细名称" prop="detailName">
        <el-input
          v-model="queryParams.detailName"
          placeholder="请输入明细名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="明细编码" prop="detailCode">
        <el-input
          v-model="queryParams.detailCode"
          placeholder="请输入明细编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="录入方式" prop="operateType">
        <el-select v-model="queryParams.operateType" placeholder="请选择录入方式" clearable size="small">
          <el-option
            v-for="dict in operateTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dsm:dsmDimensionDetail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dsm:dsmDimensionDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dsm:dsmDimensionDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dsm:dsmDimensionDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="dsmDimensionDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="字典名称" align="center" prop="dimensionName" />
      <el-table-column label="字典类型" align="center" prop="dimensionCode" />
      <el-table-column label="明细名称" align="center" prop="detailName" />
      <el-table-column label="明细编码" align="center" prop="detailCode" />
      <el-table-column label="录入方式" align="center" prop="operateType" :formatter="operateTypeFormat" />
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsm:dsmDimensionDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsm:dsmDimensionDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改维度明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
         <el-form-item label="字典类型" prop="dimensionId">
            <el-select
          v-model="form.dimensionId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入字典类型"
          :remote-method="remoteMethod"
          :loading="loading"  style="width:100%">
          <el-option
            v-for="item in dimensionOptions"
            :key="item.dimensionId"
            :label="item.dimensionCode"
            :value="item.dimensionId">
          </el-option>
        </el-select>
        </el-form-item>


        <el-form-item label="明细名称" prop="detailName">
          <el-input v-model="form.detailName" placeholder="明细名称" clearable/>
        </el-form-item>
        <el-form-item label="明细编码" prop="detailCode">
          <el-input v-model="form.detailCode" placeholder="明细编码" clearable/>
        </el-form-item>
        <el-form-item label="录入方式" prop="operateType">
          <el-select v-model="form.operateType" placeholder="请选择录入方式" clearable>
            <el-option
              v-for="dict in operateTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDsmDimensionDetail, getDsmDimensionDetail, delDsmDimensionDetail, addDsmDimensionDetail, updateDsmDimensionDetail, exportDsmDimensionDetail } from "@/api/dsm/dsmDimensionDetail";
import { listDsmDimension,listDsmDimensionForDetail} from "@/api/dsm/dsmDimension";
import { getToken } from "@/utils/auth";
export default {
  name: "DsmDimensionDetail",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 维度明细表格数据
      dsmDimensionDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 录入方式字典
      operateTypeOptions: [],
      // 状态字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        dimensionName:null,
        dimensionCode:null,
        pageNum: 1,
        pageSize: 10,
        detailName: null,
        detailCode: null,
        operateType: null,
        status: null
      },
      dimensionOptions: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dimensionName: [
          { required: true, message: "所属维度不能为空", trigger: "blur" }
        ],
        detailName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        detailCode: [
          { required: true, message: "编码不能为空", trigger: "blur" }
        ],
        operateType: [
          { required: true, message: "录入方式不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("etl_task_trigger_type").then(response => {
      this.operateTypeOptions = response.data;
    });
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    /** 查询维度明细列表 */
    getList() {
      this.loading = true;
      listDsmDimensionDetail(this.queryParams).then(response => {
        this.dsmDimensionDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 录入方式字典翻译
    operateTypeFormat(row, column) {
      return this.selectDictLabel(this.operateTypeOptions, row.operateType);
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dimensionDetailId: null,
        dimensionId: null,
        dimensionName: null,
        dimensionCode: null,
        detailName: null,
        detailCode: null,
        operateType: null,
        status: null,
        createBy: null,
        createId: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dimensionDetailId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加维度明细";
    },
    /** 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const dimensionDetailId = row.dimensionDetailId || this.ids
      getDsmDimensionDetail(dimensionDetailId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改维度明细";
      });
    },*/
    /** 修改按钮操作*/
    handleUpdate(row) {
      this.reset();
      const dimensionDetailId = row.dimensionDetailId || this.ids[0]
      var dimensionCode = this.dsmDimensionDetailList.filter(el=>el.dimensionDetailId==dimensionDetailId)
      getDsmDimensionDetail(dimensionDetailId).then(response => {
        this.dimensionOptions =[{
          dimensionCode: dimensionCode[0].dsmDimension.dimensionCode,
          dimensionId: response.data.dimensionId
        }]

        this.form = response.data;
        this.open = true;
        this.title = "修改维度明细";
      });
    },


    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dimensionDetailId != null) {
            updateDsmDimensionDetail(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDsmDimensionDetail(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dimensionDetailIds = row.dimensionDetailId || this.ids;
      this.$confirm('是否确认删除维度明细编号为"' + dimensionDetailIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDsmDimensionDetail(dimensionDetailIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
   remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
            let dimensions = {dimensionCode:query}
            listDsmDimensionForDetail(dimensions).then(response => {
            this.dimensionOptions = response.rows;
          });
          }, 200);
        } else {
          this.dimensionOptions = [];
        }
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有维度明细数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDsmDimensionDetail(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
