import request from "@/utils/request";

// 查询数据资产列表
export function listDamAssets(query) {
  return request({
    url: "/dam/damAssets/list",
    method: "get",
    params: query
  });
}

// 下载数据分级导入模板
export function importTemplate() {
  return request({
    url: "/dam/damAssets/importTemplate",
    method: "get"
  });
}

export function listDamAssetsAnaLyse(query) {
  return request({
    url: "/dam/damAssets/listAnaLyse",
    method: "get",
    params: query
  });
}


export function listMyDamAssets(query) {
  return request({
    url: "/dam/damAssets/mylist",
    method: "get",
    params: query
  });
}

// 查询数据资产详细
export function getDamAssets(damAssetsId) {
  return request({
    url: "/dam/damAssets/" + damAssetsId,
    method: "get"
  });
}

// 新增数据资产
export function addDamAssets(data) {
  return request({
    url: "/dam/damAssets",
    method: "post",
    data: data
  });
}

// 修改数据资产
export function updateDamAssets(data) {
  return request({
    url: "/dam/damAssets",
    method: "put",
    data: data
  });
}

// 删除数据资产
export function delDamAssets(damAssetsId) {
  return request({
    url: "/dam/damAssets/" + damAssetsId,
    method: "delete"
  });
}

// 导出数据资产
export function exportDamAssets(query) {
  return request({
    url: "/dam/damAssets/export",
    method: "get",
    params: query
  });
}

// 查询数据资产详细
export function getDamAssetsVo(damAssetsId) {
  return request({
    url: "/dam/damAssets/getInfoVo/" + damAssetsId,
    method: "get"
  });
}
