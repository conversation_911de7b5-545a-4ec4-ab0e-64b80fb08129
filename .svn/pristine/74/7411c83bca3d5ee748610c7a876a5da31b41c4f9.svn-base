package com.dqms.mdm.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dqms.mdm.domain.vo.MdmDataEntityShipVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.dqms.common.annotation.Log;
import com.dqms.common.config.DqmsConfig;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmDataEntityShip;
import com.dqms.mdm.domain.vo.MdmDataEntityVo;
import com.dqms.mdm.service.IMdmDataEntityShipService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据实体关系Controller
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@RestController
@RequestMapping("/mdm/mdmDataEntityShip")
public class MdmDataEntityShipController extends BaseController
{
    @Autowired
    private IMdmDataEntityShipService mdmDataEntityShipService;

    /**
     * 查询数据实体关系列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmDataEntityShip mdmDataEntityShip)
    {
        startPage();
        List<MdmDataEntityShip> list = mdmDataEntityShipService.selectMdmDataEntityShipList(mdmDataEntityShip);
        return getDataTable(list);
    }
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<MdmDataEntityShipVo> util = new ExcelUtil<>(MdmDataEntityShipVo.class);
        return util.importTemplateExcel("数据实体关系");
    }

    /**
     * 导入数据实体关系列表
     */
    @Log(title = "导入数据实体关系", businessType = BusinessType.IMPORT )
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:import')")
    @PostMapping("/import")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<MdmDataEntityShipVo> util = new ExcelUtil<>(MdmDataEntityShipVo.class);
        List<MdmDataEntityShipVo> mdmDataEntityShipVoList = util.importExcel(file.getInputStream());
        String message = mdmDataEntityShipService.importMdmDataEntityShipVo(mdmDataEntityShipVoList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导出数据实体关系列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:export')")
    @Log(title = "数据实体关系", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmDataEntityShip mdmDataEntityShip)
    {
        List<MdmDataEntityShip> list = mdmDataEntityShipService.selectMdmDataEntityShipList(mdmDataEntityShip);
        ExcelUtil<MdmDataEntityShip> util = new ExcelUtil<MdmDataEntityShip>(MdmDataEntityShip.class);
        return util.exportExcel(list, "mdmDataEntityShip");
    }

    /**
     * 获取数据实体关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:query')")
    @GetMapping(value = "/{shipId}")
    public AjaxResult getInfo(@PathVariable("shipId") Long shipId)
    {
        return AjaxResult.success(mdmDataEntityShipService.selectMdmDataEntityShipById(shipId));
    }
    /**
     * 导出数据实体关系列表
     */
    @PostMapping("/getInfoByRel")
    public AjaxResult getInfoByRel(@RequestBody MdmDataEntityShip mdmDataEntityShip)
    {
    	return AjaxResult.success(mdmDataEntityShipService.selectMdmDataEntityShipByRel(mdmDataEntityShip));
    	
    }

    /**
     * 新增数据实体关系
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:add')")
    @Log(title = "数据实体关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmDataEntityShip mdmDataEntityShip)
    {
        return toAjax(mdmDataEntityShipService.insertMdmDataEntityShip(mdmDataEntityShip));
    }

    /**
     * 修改数据实体关系
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:edit')")
    @Log(title = "数据实体关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmDataEntityShip mdmDataEntityShip)
    {
        return toAjax(mdmDataEntityShipService.updateMdmDataEntityShip(mdmDataEntityShip));
    }

    /**
     * 删除数据实体关系
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:remove')")
    @Log(title = "数据实体关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shipIds}")
    public AjaxResult remove(@PathVariable Long[] shipIds)
    {
        return toAjax(mdmDataEntityShipService.deleteMdmDataEntityShipByIds(shipIds));
    }

    @GetMapping("/findAllEntity")
    public AjaxResult findAllEntity()
    {
        return AjaxResult.success(mdmDataEntityShipService.selectEntityAll());
    }

    @GetMapping("/findAllEntityTable")
    public TableDataInfo findAllEntityTable(MdmDataEntity mdmDataEntity)
    {
    	startPage();
    	List<MdmDataEntityVo>  list = mdmDataEntityShipService.selectEntityTableAll(mdmDataEntity);
        return getDataTable(list);
    }
    
    @GetMapping("/findAllEntityUnTable")
    public TableDataInfo findAllEntityUnTable(MdmDataEntity mdmDataEntity)
    {
    	startPage();
    	List<MdmDataEntityVo>  list = mdmDataEntityShipService.selectEntityUnTableAll(mdmDataEntity);
        return getDataTable(list);
    }

    @GetMapping("/findAllEntityProp")
    public TableDataInfo findAllEntityProp(MdmDataEntityProp mdmDataEntityProp)
    {	startPage();
    	List<MdmDataEntityProp>  list = mdmDataEntityShipService.selectAllByEntityId(mdmDataEntityProp);
    	return getDataTable(list);
    }


    @GetMapping("/findAllEntityPropVo")
    public TableDataInfo findAllEntityPropVo(MdmDataEntityProp mdmDataEntityProp)
    {	startPage();
    	List<MdmDataEntityProp>  list = mdmDataEntityShipService.selectPropVoAllByEntityId(mdmDataEntityProp);
    	return getDataTable(list);
    }
    
    @GetMapping("/findEntityAndSystem")
    public AjaxResult findEntityAndSystem(String name)
    {
        return AjaxResult.success(mdmDataEntityShipService.selectEntityAndSystem(name));
    }

    @GetMapping("/findAllPropAndEntityAndSystem")
    public AjaxResult findAllPropAndEntityAndSystem(String name)
    {
        return AjaxResult.success(mdmDataEntityShipService.selectAllPropAndEntityAndSystem(name));
    }

    @PutMapping("/findShipList")
    public AjaxResult findShipListByMdmDataEntityShip(@RequestBody MdmDataEntityVo mdmDataEntityVo)
    {
        return AjaxResult.success(mdmDataEntityShipService.findShipListByMdmDataEntityShip(mdmDataEntityVo));
    }
    
    @PutMapping("/findShipAllList")
    public AjaxResult findShipAllListByMdmDataEntityShip(@RequestBody MdmDataEntityVo mdmDataEntityVo)
    {
        return AjaxResult.success(mdmDataEntityShipService.findShipAllListByMdmDataEntityShip(mdmDataEntityVo));
    }
    
    @PutMapping("/findShipListByTheme")
    public AjaxResult findShipListByTheme(@RequestBody MdmDataEntityVo mdmDataEntityVo)
    {
        return AjaxResult.success(mdmDataEntityShipService.findShipListByTheme(mdmDataEntityVo));
    }
    
    @PutMapping("/getData")
    public AjaxResult getData(@RequestBody MdmDataEntityVo mdmDataEntityVo)
    {
    	Map<String,Object> map = mdmDataEntityShipService.analyse(mdmDataEntityVo.getSqlScripts(),mdmDataEntityVo.getSysDatasourceType());

        return AjaxResult.success(map);
    }

    /**
     * 解除来源
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShip:removeSource')")
    @Log(title = "数据实体关系", businessType = BusinessType.DELETE)
    @PutMapping("/removeSource")
    public AjaxResult removeSource(@RequestBody MdmDataEntityShip mdmDataEntityShip)
    {
        return toAjax(mdmDataEntityShipService.removeSource(mdmDataEntityShip));
    }
}
