package com.dqms.task.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.domain.entity.SysDept;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.enums.BusinessType;
import com.dqms.task.domain.EtlTaskClass;
import com.dqms.task.service.IEtlTaskClassService;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.framework.web.service.TokenService;

/**
 * 任务分类Controller
 *
 * <AUTHOR>
 * @date 2021-03-11
 */
@RestController
@RequestMapping("/task/taskClass")
public class EtlTaskClassController extends BaseController
{
    @Autowired
    private IEtlTaskClassService etlTaskClassService;
    
    /**
     * 查询任务分类列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskClass:list')")
    @GetMapping("/list")
    public AjaxResult list(EtlTaskClass etlTaskClass)
    {
        List<EtlTaskClass> list = etlTaskClassService.selectEtlTaskClassList(etlTaskClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出任务分类列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskClass:export')")
    @Log(title = "任务分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EtlTaskClass etlTaskClass)
    {
        List<EtlTaskClass> list = etlTaskClassService.selectEtlTaskClassList(etlTaskClass);
        ExcelUtil<EtlTaskClass> util = new ExcelUtil<EtlTaskClass>(EtlTaskClass.class);
        return util.exportExcel(list, "taskClass");
    }

    /**
     * 获取任务分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('task:taskClass:query')")
    @GetMapping(value = "/{taskClassId}")
    public AjaxResult getInfo(@PathVariable("taskClassId") Long taskClassId)
    {
        return AjaxResult.success(etlTaskClassService.selectEtlTaskClassById(taskClassId));
    }

    /**
     * 新增任务分类
     */
    @PreAuthorize("@ss.hasPermi('task:taskClass:add')")
    @Log(title = "任务分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EtlTaskClass etlTaskClass)
    {
        return toAjax(etlTaskClassService.insertEtlTaskClass(etlTaskClass));
    }

    /**
     * 修改任务分类
     */
    @PreAuthorize("@ss.hasPermi('task:taskClass:edit')")
    @Log(title = "任务分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EtlTaskClass etlTaskClass)
    {
        return toAjax(etlTaskClassService.updateEtlTaskClass(etlTaskClass));
    }

    /**
     * 删除任务分类
     */
    @PreAuthorize("@ss.hasPermi('task:taskClass:remove')")
    @Log(title = "任务分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskClassIds}")
    public AjaxResult remove(@PathVariable Long[] taskClassIds)
    {
        return toAjax(etlTaskClassService.deleteEtlTaskClassByIds(taskClassIds));
    }
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(EtlTaskClass etlTaskClass)
    {
        List<EtlTaskClass> etlTaskClasss = etlTaskClassService.selectEtlTaskClassList(etlTaskClass);
        return AjaxResult.success(etlTaskClassService.buildEtlTaskClassTreeSelect(etlTaskClasss));
    }
}
