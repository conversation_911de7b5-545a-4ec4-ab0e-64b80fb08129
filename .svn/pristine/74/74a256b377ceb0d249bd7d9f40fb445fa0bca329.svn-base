# 项目相关配置
dqms:
  # 文件路径 示例（ Windows配置D:/gbicc/uploadPath，Linux配置 /home/<USER>/uploadPath）
  # 自行修改为本地环境
  profile: /usr/local/tomcat/uploadPath
  #api 接口
  address: http://localhost:8080
  #dqms-kafka 地址
#  kafkaAddr: http://***********:9099/dk/msg/massfetch?topic={topic}&maxcount={maxcount}


spring:
  # elasticsearch 配置
  elasticsearch:
    rest:
      uris: http://localhost:9200  #es服务器地址（默认）
#      uris:  http://***********:9200  #es服务器地址（默认）
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # 密码
    password: jbicc@20220112
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # 数据源配置
  datasource:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      druid:
          # 主库数据源
          master:
#              url: jdbc:mysql://***********:3306/dqms?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
#              username: root
#              password: LCDQMS@2021
              url: ********************************************************************************************************************************************************************************
              username: dqms_jbicc
              password: jbicc@20220112
          # 从库数据源
          slave:
              # 从数据源开关/默认关闭
              enabled: false
              url:
              username:
              password:
          # 初始连接数
          initialSize: 5
          # 最小连接池数量
          minIdle: 10
          # 最大连接池数量
          maxActive: 20
          # 配置获取连接等待超时的时间
          maxWait: 60000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          timeBetweenEvictionRunsMillis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          minEvictableIdleTimeMillis: 300000
          # 配置一个连接在池中最大生存的时间，单位是毫秒
          maxEvictableIdleTimeMillis: 900000
          # 配置检测连接是否有效
          validationQuery: SELECT 1 FROM DUAL
          testWhileIdle: true
          testOnBorrow: false
          testOnReturn: false
          webStatFilter:
              enabled: true
          statViewServlet:
              enabled: true
              # 设置白名单，不填则允许所有访问
              allow:
              url-pattern: /druid/*
              # 控制台管理用户名和密码
              login-username:
              login-password:
          filter:
              stat:
                  enabled: true
                  # 慢SQL记录
                  log-slow-sql: true
                  slow-sql-millis: 1000
                  merge-sql: true
              wall:
                  config:
                      multi-statement-allow: true

  # Kafka配置
#  kafka:
#      bootstrap-servers: 120.199.93.37:9092
#      producer:
#        # 发生错误后，消息重发的次数。
#        retries: 0
#        #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
#        batch-size: 16384
#        # 设置生产者内存缓冲区的大小。
#        buffer-memory: 33554432
#        # 键的序列化方式
#        key-serializer: org.apache.kafka.common.serialization.StringSerializer
#        # 值的序列化方式
#        value-serializer: org.apache.kafka.common.serialization.StringSerializer
#        # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
#        # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
#        # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
#        acks: 1
#      consumer:
#        group-id: dqms-consumer-group
#        # 自动提交的时间间隔 在spring boot 2.X 版本中这里采用的是值的类型为Duration 需要符合特定的格式，如1S,1M,2H,5D
#        auto-commit-interval: 1S
#        # 该属性指定了消费者在读取一个没有偏移量的分区或者偏移量无效的情况下该作何处理：
#        # latest（默认值）在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
#        # earliest ：在偏移量无效的情况下，消费者将从起始位置读取分区的记录
#        auto-offset-reset: earliest
#        # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
#        enable-auto-commit: false
#        # 键的反序列化方式
#        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#        # 值的反序列化方式
#        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      listener:
#        # 在侦听器容器中运行的线程数。
#        concurrency: 5
#        #listner负责ack，每调用一次，就立即commit
#        ack-mode: manual_immediate
#        missing-topics-fatal: false





