package com.dqms.dsm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsm.domain.DsmIndexClass;
import com.dqms.dsm.service.IDsmIndexClassService;

/**
 * 指标分类Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmIndexClass")
public class DsmIndexClassController extends BaseController
{
    @Autowired
    private IDsmIndexClassService dsmIndexClassService;

    /**
     * 查询指标分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexClass:list')")
    @GetMapping("/list")
    public AjaxResult list(DsmIndexClass dsmIndexClass)
    {
        List<DsmIndexClass> list = dsmIndexClassService.selectDsmIndexClassList(dsmIndexClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出指标分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexClass:export')")
    @Log(title = "指标分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmIndexClass dsmIndexClass)
    {
        List<DsmIndexClass> list = dsmIndexClassService.selectDsmIndexClassList(dsmIndexClass);
        ExcelUtil<DsmIndexClass> util = new ExcelUtil<DsmIndexClass>(DsmIndexClass.class);
        return util.exportExcel(list, "dsmIndexClass");
    }

    /**
     * 获取指标分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexClass:query')")
    @GetMapping(value = "/{indexClassId}")
    public AjaxResult getInfo(@PathVariable("indexClassId") Long indexClassId)
    {
        return AjaxResult.success(dsmIndexClassService.selectDsmIndexClassById(indexClassId));
    }

    /**
     * 新增指标分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexClass:add')")
    @Log(title = "指标分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmIndexClass dsmIndexClass)
    {
        return toAjax(dsmIndexClassService.insertDsmIndexClass(dsmIndexClass));
    }

    /**
     * 修改指标分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexClass:edit')")
    @Log(title = "指标分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmIndexClass dsmIndexClass)
    {
        return toAjax(dsmIndexClassService.updateDsmIndexClass(dsmIndexClass));
    }

    /**
     * 删除指标分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexClass:remove')")
    @Log(title = "指标分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{indexClassIds}")
    public AjaxResult remove(@PathVariable Long[] indexClassIds)
    {
        return toAjax(dsmIndexClassService.deleteDsmIndexClassByIds(indexClassIds));
    }
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DsmIndexClass dsmIndexClass)
    {
        List<DsmIndexClass> dsmIndexClasss = dsmIndexClassService.selectDsmIndexClassList(dsmIndexClass);
        return AjaxResult.success(dsmIndexClassService.buildDsmIndexClassTreeSelect(dsmIndexClasss));
    }
}
