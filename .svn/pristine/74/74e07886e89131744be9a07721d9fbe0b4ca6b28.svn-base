<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="需求名称" prop="needsName">
        <el-input
          v-model="queryParams.needsName"
          placeholder="请输入需求名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="需求类型" prop="needsType">
        <el-select v-model="queryParams.needsType" placeholder="请选择需求类型" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="变更类型" prop="changeType">
        <el-select v-model="queryParams.changeType" placeholder="请选择变更类型" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="使用频率" prop="rate">
        <el-select v-model="queryParams.rate" placeholder="请选择使用频率" clearable size="small">
          <el-option
            v-for="dict in rateOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    </el-row>

    <el-table v-loading="loading" :data="needsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="需求名称" align="center" prop="needsName" />
      <el-table-column label="需求类型" align="center" prop="needsType"  :formatter="needsTypeFormat"/>
      <el-table-column label="变更类型" align="center" prop="changeType"  :formatter="changeTypeFormat"/>
      <el-table-column label="展示路径" align="center" prop="needsPath" />
      <el-table-column label="使用频率" align="center" prop="rate" :formatter="rateFormat" />
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column label="紧急级别" align="center" prop="level" :formatter="levelFormat" />
      <el-table-column label="完成率%" align="center" prop="scale" />
      <el-table-column label="计划时间" align="center" prop="planDate" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="360">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="examineAndApprove (scope.row)"
            v-if="scope.row.taskId && scope.row.taskId!='' && scope.row.status=='READY'"
          >审批
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['needs:needs:edit']"
            v-if="scope.row.status=='DEVELOP'"
          >处理 </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="historyFory(scope.row)"
          >审批详情
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="checkTheSchedule(scope.row)"
            v-if="scope.row.status=='READY' "
          >查看进度
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            :loading="scope.row.downLoading"
            @click="handleDown(scope.row,scope.$index)"
            v-if="scope.row.attachment && scope.row.attachment!=''"
          >附件</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改需求反馈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px"  append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      	<div style="height:600px;overflow-y:auto;">
      	<el-col :span="14">
		        <el-form-item label="需求名称" prop="needsName">
		          <el-input v-model="form.needsName" placeholder="请输入需求名称" clearable/>
		        </el-form-item>
		        <el-form-item label="需求类型" prop="needsType" style="width:100%">
		          <el-radio-group v-model="form.needsType" style="width:100%">
		            <el-radio
		              v-for="dict in needsTypeOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue"
		            >{{dict.dictLabel}}</el-radio>
		          </el-radio-group>
		        </el-form-item>
		        <el-form-item label="变更类型" prop="changeType">
		          <el-radio-group v-model="form.changeType" style="width:100%">
		            <el-radio
		              v-for="dict in changeTypeOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue"
		            >{{dict.dictLabel}}</el-radio>
		          </el-radio-group>
		        </el-form-item>
		        <el-form-item label="展示路径" prop="needsPath">
		          <el-input v-model="form.needsPath" placeholder="请输入展示路径" clearable/>
		        </el-form-item>
		        <el-form-item label="使用频率">
		          <el-radio-group v-model="form.rate">
		            <el-radio
		              v-for="dict in rateOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue"
		            >{{dict.dictLabel}}</el-radio>
		          </el-radio-group>
		        </el-form-item>
		        <el-form-item label="关联维度" prop="dimensions">
		          <el-input v-model="form.dimensions" placeholder="请输入关联维度" clearable/>
		        </el-form-item>
		        <el-form-item label="关联指标" prop="indexs">
		          <el-input v-model="form.indexs" placeholder="请输入关联指标" clearable/>
		        </el-form-item>
		        <el-form-item label="需求说明" prop="remark">
		          <editor v-model="form.remark" type="textarea" placeholder="请输入内容" :min-height="150"/>
		        </el-form-item>
		</el-col>
		<el-col :span="10">
			<el-form-item label="应用部门">
	          <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
	          <el-tree
	            class="tree-border"
	            :data="deptOptions"
	            show-checkbox
	            default-expand-all
	            ref="dept"
	            node-key="id"
	            :check-strictly="!form.deptCheckStrictly"
	            empty-text="加载中，请稍后"
	            :props="defaultProps"
	          ></el-tree>
       		</el-form-item>
	    </el-col>
        <el-table v-loading="loading" :data="needsHandleList" border>
	      <el-table-column label="处理说明" align="center" prop="remark" />
	      <el-table-column label="完成比例" align="center" prop="scale" />
	      <el-table-column label="创建人" align="center" prop="createBy" />
	      <el-table-column label="计划时间" align="center" prop="planDate" />
	      <el-table-column label="开始时间" align="center" prop="createTime" width="180">
	        <template slot-scope="scope">
	          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
	        </template>
	      </el-table-column>
	      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
		      <template slot-scope="scope">
		      <el-button
	            size="mini"
	            type="text"
	            icon="el-icon-download"
	            :loading="scope.row.downLoading"
	            @click="handleDownHandle(scope.row,scope.$index)"
	          >附件</el-button>
	          </template>
	      </el-table-column>
    	</el-table>

	    <pagination
	      v-show="handleTotal>0"
	      :total="handleTotal"
	      :page.sync="handleQueryParams.pageNum"
	      :limit.sync="handleQueryParams.pageSize"
	      @pagination="getHandleList"
	    />
	    </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAdd">添加记录</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

        <!-- 查看详细信息话框 -->
    <el-dialog :title="title" :visible.sync="open2" width="1200px" append-to-body>
      <needsForm :businessKey="businessKey" v-if="open2"/>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open2=!open2">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="modelVisible"
      title="进度查询"
      width="680px"
      append-to-body
    >
      <div style="position:relative;height: 100%;">
        <iframe
          id="iframe"
          :src="modelerUrl"
          frameborder="0"
          width="100%"
          height="300px"
          scrolling="auto"
        ></iframe>
      </div>
    </el-dialog>

        <!-- 添加或修改需求处理对话框 -->
    <el-dialog :title="title" :visible.sync="handleOpen" width="700px" append-to-body>
      <el-form ref="form" :model="handleForm" :rules="rules" label-width="80px">
        <el-form-item label="需求ID" prop="needsId" v-show="false">
          <el-input v-model="handleForm.needsId" placeholder="请输入需求ID" clearable/>
        </el-form-item>
        <el-form-item label="处理说明" prop="remark">
          <el-input v-model="handleForm.remark" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="完成比例" prop="scale">
        <el-slider v-model="handleForm.scale" :step="10" show-stops placeholder="请输入完成比例" :marks="marks"></el-slider>
        </el-form-item>
        <el-form-item label="计划时间" prop="remark" >
          <el-date-picker
	       v-model="handleForm.planDate"
	       type="date"
	       placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width:100%">
	      </el-date-picker>
        </el-form-item>
        <el-form-item label-width="0px" :error="uploaderr">
          <div class="upload">
           <el-upload
              ref="upload"
              class="upload-demo"
              :action='uploadUrl'
              accept=".jpg,.jpeg,.txt,.zip,.xls,.xlsx,.doc,.docx,.pdf,.PDF"
              :auto-upload="true"
              :file-list="fileList"
              :on-success="handleSuccess"
              :headers="myHeaders"
              :limit="1">
              <el-button size="small" icon='el-icon-upload2' type="danger">上传附件</el-button>
           </el-upload>
          <div class="uptxt">（支持pdf、word、excel、zip、jpg，文件限制500M以内）</div>
          </div>
        </el-form-item>
        <el-form-item label="文档名称" prop="attachment"  v-show="false">
          <el-input v-model="handleForm.attachment"  maxLength='20' placeholder="请输入文档名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('DEVELOP')">确 定</el-button>
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm('REJECT')">驳回</el-button>
      </div>
    </el-dialog>

        <!-- 审批对话框 -->
    <el-dialog :title="applyTitle" :visible.sync="applyOpen" v-if="applyOpen" width="950px" append-to-body>
      <needsForm :businessKey="businessKey" v-if="'needs'==definitionKey"/>

      <el-form :model="applyForm" ref="form" label-width="100px" class="demo-dynamic">
        <el-form-item
          v-for="(domain, index) in applyForm.formData"
          :label="domain.controlLable"
          :key="index"
        >
          <el-radio-group v-model="domain.controlValue" v-if="'radio'==domain.controlType">
            <el-radio v-for="(defaults,indexd) in domain.controlDefault.split('--__--')"
                      :label=indexd
                      :key="indexd"
                      >{{defaults}}

            </el-radio>

          </el-radio-group>
          <el-input type="textarea" v-model="domain.controlValue" v-if="'textarea'==domain.controlType"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="applySubmitForm">确 定</el-button>
        <el-button @click="applyCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNeeds, getNeeds, delNeeds, addNeeds, updateNeeds, exportNeeds ,needDeptTreeselect ,singleDown} from "@/api/needs/needs";
import { listNeedsHandle, getNeedsHandle, delNeedsHandle, addNeedsHandle, updateNeedsHandle, exportNeedsHandle ,singleHandleDown} from "@/api/needs/needsHandle";
import {getDefinitionsByInstanceId} from '@/api/activiti/definition'
import needsForm from "./../needs/needsForm";
import { getToken } from "@/utils/auth";
import { treeselect as deptTreeselect } from "@/api/system/dept";
import Editor from '@/components/Editor';
import {listTask, formDataShow, formDataSave} from "@/api/activiti/task";

export default {
  name: "NeedsHandle",
  components: {
    needsForm,
    Editor
  },
  data() {
    return {
   	  modelVisible: false,
      modelerUrl: '',
      // 遮罩层
      loading: true,
      uploading: false,
      uploaderr:"",
      uploadUrl:process.env.VUE_APP_BASE_API + '/needs/needs/upload',
      myHeaders: {
          'Authorization': 'Bearer ' + getToken()
      },
      isfile: true,
      // 选中数组
      ids: [],
      needsId:null,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      handleTotal: 0,
      // 需求反馈表格数据
      needsList: [],
      needsHandleList: [],
      // 弹出层标题
      title: "",
      handleTitle: "",
      // 是否显示弹出层
      open: false,
      // 使用频率字典
      rateOptions: [],
      statusOptions: [],
      needsTypeOptions: [],
      changeTypeOptions: [],
      levelOptions: [],
      open2: false,
      applyOpen: false,
      fileList:[],
      deptOptions: [],
      deptExpand: true,
      deptNodeAll: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        needsName: null,
        needsType: null,
        changeType: null,
        needsPath: null,
        rate: null,
        dimensions: null,
        indexs: null,
        attachment: null,
        depts: null,
        instanceId: null,
        status: 'DEVELOP'
      },
      handleQueryParams: {
        pageNum: 1,
        pageSize: 10,
        needsId: null
      },
      form: {
    	  changeType:'ADD',
    	  rate:'REAL'
      },
      handleForm: {
      },
      // 表单校验
      rules: {
      },
      defaultProps: {
          children: "children",
          label: "label"
      },
      marks: {
          0: '需求分析',
          20: '设计开发',
          70: '联调测试',
          90: '实施部署'
      }  ,
      handleOpen:false,
      taskId:'',
      definitionKey: '',
      businessKey: '' ,
      applyTitle:'审批',
      // 表单参数
      applyForm: {
        formData:[]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("nes_needs_rate").then(response => {
      this.rateOptions = response.data;
    });
    this.getDicts("nes_needs_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("nes_needs_change_type").then(response => {
        this.changeTypeOptions = response.data;
      });
    this.getDicts("nes_needs_type").then(response => {
        this.needsTypeOptions = response.data;
      });
    this.getDicts("sys_important_level").then(response => {
        this.levelOptions = response.data;
    });
  },
  methods: {
    /** 查询需求反馈列表 */
    getList() {
      this.loading = true;
      listNeeds(this.queryParams).then(response => {
        this.needsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    changeTypeFormat(row, column) {
        return this.selectDictLabel(this.changeTypeOptions, row.changeType);
      },
    needsTypeFormat(row, column) {
      return this.selectDictLabel(this.needsTypeOptions, row.needsType);
    },
    rateFormat(row, column) {
      return this.selectDictLabel(this.rateOptions, row.rate);
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    levelFormat(row, column) {
        return this.selectDictLabel(this.levelOptions, row.level);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    handleCancel() {
        this.handleOpen = false;
        this.reset();
    },
    // 表单重置
    reset() {
      this.handleForm = {
        needsId: null,
        needsHandleId: null,
        scale: 0,
        attachment: null,
        remark: null
      };
      this.uploaderr = "";
      this.isfile = false;
      this.$nextTick(function () {
          this.isfile = true
      })
      this.resetForm("form");
      this.fileList=[];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.needsId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.handleTitle = "添加需求处理";
      this.handleOpen = true;
      this.handleForm.needsId=this.needsId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const needsId = row.needsId || this.ids
      this.needsId=needsId
      const needDeptTreeselect = this.getNeedDeptTreeselect(needsId);
      getNeeds(needsId).then(response => {
    	  this.fileList=[{
    		  name:response.data.needsName,
              url:response.data.attachment
            }];
        this.form = response.data;
        this.open = true;
        this.title = "修改需求反馈";
        this.$nextTick(() => {
        	needDeptTreeselect.then(res => {
              this.$refs.dept.setCheckedKeys(res.checkedKeys);
            });
        });
      });
      this.getHandleList();
    },
    /** 提交按钮 */
    submitForm(type) {
    	this.handleForm.status=type
      this.$refs["form"].validate(valid => {
        if (valid) {
        	this.uploading = true;
        	addNeedsHandle(this.handleForm).then(response => {
              this.msgSuccess("新增成功");
              this.handleOpen = false;
              this.getHandleList();
            });
        }
      });
    },
    /** 审批详情 */
    historyFory(row) {
      this.businessKey = row.needsId
      this.open2 = true
      this.title = '审批详情'

    },
    /** 进度查看 */
    checkTheSchedule(row) {
      getDefinitionsByInstanceId(row.instanceId).then(response => {
        let data = response.data
        // this.url = '/bpmnjs/index.html?type=lookBpmn&deploymentFileUUID='+data.deploymentID+'&deploymentName='+ encodeURI(data.resourceName);
        this.modelerUrl = '/bpmnjs/indexShow.html?type=lookBpmn&instanceId=' + row.instanceId + '&deploymentFileUUID=' + data.deploymentID + '&deploymentName=' + encodeURI(data.resourceName);
        this.modelVisible = true
      })
    },
    handleSuccess(res, file, fileList) { // 文件上传成功处理
      this.handleForm.attachment=res.msg;
      //成功后的业务逻辑处理
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
          let treeList = this.deptOptions;
          for (let i = 0; i < treeList.length; i++) {
            this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
          }
      },
      /** 根据需求ID查询部门树结构 */
      getNeedDeptTreeselect(needId) {
        return needDeptTreeselect(needId).then(response => {
          this.deptOptions = response.depts;
          return response;
        });
      },
      handleDown(row,index){
   	   singleDown(row.needsId).then((response) => {
   	              const blob = new Blob([response.data]);
   	              let filePath = row.attachment;
   	              let fileName = filePath.substr(filePath.lastIndexOf("/")+1,filePath.length);
   	              let brower = '';
   	              if (navigator.userAgent.indexOf('Edge') > -1) {
   	                brower = 'Edge';
   	              }
   	              if ('download' in document.createElement('a')) { // 非IE下载
   	                  if (brower == 'Edge') {
   	                    navigator.msSaveBlob(blob, fileName);
   	                    return;
   	                  }
   	                  const elink = document.createElement('a');
   	                  elink.download = fileName;
   	                  elink.style.display = 'none';
   	                  elink.href = URL.createObjectURL(blob);
   	                  document.body.appendChild(elink);
   	                  elink.click();
   	                  URL.revokeObjectURL(elink.href);// 释放URL 对象
   	                  document.body.removeChild(elink)
   	              } else { // IE10+下载
   	                  navigator.msSaveBlob(blob, fileName)
   	              }
   	              this.msgSuccess("下载成功！");
   	              this.areaList.map((item,i) => {
   	                if(i==index){
   	                  this.$set(item, 'downLoading', false)
   	                }
   	                return item
   	              })

   	      }).catch(err => {

   	      })
    },
    handleDownHandle(row,index){
    	   singleHandleDown(row.needsHandleId).then((response) => {
	              const blob = new Blob([response.data]);
	              let filePath = row.attachment;
	              let fileName = filePath.substr(filePath.lastIndexOf("/")+1,filePath.length);
	              let brower = '';
	              if (navigator.userAgent.indexOf('Edge') > -1) {
	                brower = 'Edge';
	              }
	              if ('download' in document.createElement('a')) { // 非IE下载
	                  if (brower == 'Edge') {
	                    navigator.msSaveBlob(blob, fileName);
	                    return;
	                  }
	                  const elink = document.createElement('a');
	                  elink.download = fileName;
	                  elink.style.display = 'none';
	                  elink.href = URL.createObjectURL(blob);
	                  document.body.appendChild(elink);
	                  elink.click();
	                  URL.revokeObjectURL(elink.href);// 释放URL 对象
	                  document.body.removeChild(elink)
	              } else { // IE10+下载
	                  navigator.msSaveBlob(blob, fileName)
	              }
	              this.msgSuccess("下载成功！");
	              this.areaList.map((item,i) => {
	                if(i==index){
	                  this.$set(item, 'downLoading', false)
	                }
	                return item
	              })

	      }).catch(err => {

	      })
 	},
    /** 查询需求处理列表 */
    getHandleList() {
      this.handleQueryParams.params = {};
      this.handleQueryParams.needsId = this.needsId;
      listNeedsHandle(this.handleQueryParams).then(response => {
        this.needsHandleList = response.rows;
        this.handleTotal = response.total;
      });
    },

    /** 审批按钮操作 */
    examineAndApprove(row) {
      this.applyReset();
      this.definitionKey = row.definitionKey;
      this.businessKey = row.businessKey;
      this.taskId=row.taskId;
      formDataShow(row.taskId).then(response => {
        let datas = response.data;
        let formData = []
        for (let i = 0; i < datas.length; i++) {
          let strings = datas[i].split('--__!!')
          let controlValue = null
          let controlDefault = null
          switch (strings[1]) {
            case 'radio':
              controlValue = 0;
              controlDefault = strings[4]
              break;
            // default:
          }
          formData.push({
            controlId: strings[0],
            controlType: strings[1],
            controlLable: strings[2],
            controlIsParam: strings[3],
            controlValue: controlValue,
            controlDefault: controlDefault
          })
        }
        this.applyForm.formData = formData;
        this.applyOpen = true;
        this.applyTitle = "审批";
      });
    },
    /** 提交按钮 */
    applySubmitForm() {
      formDataSave(this.id,this.form.formData).then(response => {
        this.msgSuccess("审批成功");
        this.applyOpen = false;
        this.getList();
      });
    },
    // 取消按钮
    applyCancel() {
      this.applyOpen = false;
      this.applyReset();
    },
    // 表单重置
    applyReset() {
       this.definitionKey = '',
        this.businessKey = '',
        this.applyForm = {
          formData:[],
        };
      this.resetForm("applyForm");
    },
  }
};
</script>
<style scoped>
	.upload{position:relative; padding:0 15px; height:80px;}
	.upload-demo{float:left;}
	.uptxt{ position:absolute; top:0; left:130px; line-height:32px;}
	.hotfile{padding:0 28px 20px; line-height: 30px; font-size: 40px; font-size: 14px; font-weight: bold; color:#a00847}
	.hotfile span{display: inline-block; margin-right: 15px; font-style: italic; color:#409EFF; font-weight: normal; cursor: pointer;}
	.stree >>> .vue-treeselect__control{line-height: 20px; height: 36px;}
	.qstree{width: 220px;}
	.qstree >>> .vue-treeselect__control{height: 32px;}

   .transition-box {
    margin-bottom: 10px;
    width: 300px;
    height: 150px;
    border-radius: 4px;
    background-color: #409EFF;
    text-align: center;
    color: #fff;
    padding: 40px 20px;
    box-sizing: border-box;
    margin-right: 20px;
  }
</style>
