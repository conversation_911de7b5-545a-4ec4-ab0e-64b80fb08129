<template>
  <div class="app-container">
<el-row type="flex" justify="start" align="top"  >
    	  <el-col :span="24" >
		  	<div class="container-fluid" style="margin-top:0px;">
	    	<div id="scalingToolBar" style="position:absolute;padding-top:10;right:0;height:100;width:150px;cursor:pointer;z-index: 99;">
	    	<el-col :span="24" >
	    	<el-button type="primary" size="mini" @click="handleSearch()"><i class="el-icon-search"></i>搜索</el-button>
	    	<el-button type="danger" size="mini" @click="handleAdd()"><i class="el-icon-close"></i>保存</el-button>
	    	</el-col>
			</div>
	    	<div id="rowdiv" class="row">
			<div id="container" style="position: relative;"></div>
		    </div>
	    	</div>
	    </el-col>
		</el-row>
		
		
	<el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="脑图名称" prop="mindName">
          <el-input v-model="form.mindName" placeholder="请输入脑图名称" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
        <el-row>
    <el-drawer
		  title="操作栏" :with-header="false"
		  :visible.sync="drawer"
		  :direction="direction"
		   :modal=false :wrapperClosable=true :modal-append-to-body=false size='30%'>
		      <el-table v-loading="loading" :data="damMindList" @selection-change="handleSelectionChange">
		      <el-table-column label="脑图名称" align="center" prop="mindName" />
		      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
		        <template slot-scope="scope">
		          <el-button
		            size="mini"
		            type="text"
		            icon="el-icon-edit"
		            @click="handleUpdate(scope.row)"
		            v-hasPermi="['dam:damMind:edit']"
		          >查看</el-button>
		          <el-button
		            size="mini"
		            type="text"
		            icon="el-icon-delete"
		            @click="handleDelete(scope.row)"
		            v-hasPermi="['dam:damMind:remove']"
		          >删除</el-button>
		        </template>
		      </el-table-column>
		    </el-table>
		
		    <pagination
		      v-show="total>0"
		      :total="total"
		      :page.sync="queryParams.pageNum"
		      :limit.sync="queryParams.pageSize"
		      @pagination="getList"
		    />
	</el-drawer>
    </el-row> 
  </div>
</template>

<script>
import G6 from '@antv/g6'
import { listDamMind, getDamMind, delDamMind, addDamMind, updateDamMind, exportDamMind } from "@/api/dam/damMind";
import backgroundImage from '@/assets/images/timg.jpg'
export default {
  name: "DamMind",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 我的脑图表格数据
      damMindList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawer: false,
      //关系图
      backgroundImage:"",
      g6graph:undefined,
      layout:null,
      direction: 'rtl',
      rawData:null,
      tree:null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mindName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    
  },
  mounted() {
		this.rawData = {
	    		  label: 'ROOT',
	    		  id: '0',
	    		  children: [
	    		  ],
	    		};
	  this.handleMind();
  },
  methods: {
    /** 查询我的脑图列表 */
    getList() {
      this.loading = true;
      listDamMind(this.queryParams).then(response => {
        this.damMindList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mindId: null,
        mindName: null,
        remark: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.mindId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加我的脑图";
    },
    handleSearch(){
    	this.drawer=true;
    	this.getList();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const mindId = row.mindId || this.ids
      getDamMind(mindId).then(response => {
        this.rawData = JSON.parse(response.data.remark);
        console.log(this.rawData);
        this.handleMind();
      });
    },
    /** 提交按钮 */
    submitForm() {
    	this.form.remark=JSON.stringify(this.tree.save());
          if (this.form.mindId != null) {
            updateDamMind(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDamMind(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const mindIds = row.mindId || this.ids;
      this.$confirm('是否确认删除我的脑图编号为"' + mindIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDamMind(mindIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有我的脑图数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDamMind(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    handleMind() {
    	if(this.tree!=null){
    		this.tree.clear();
    		this.tree.destroy();
    	}
    		const {Util} = G6;
    		const colorArr = [
    		  '#5B8FF9',
    		  '#5AD8A6',
    		  '#5D7092',
    		  '#F6BD16',
    		  '#6F5EF9',
    		  '#6DC8EC',
    		  '#D3EEF9',
    		  '#DECFEA',
    		  '#FFE0C7',
    		  '#1E9493',
    		  '#BBDEDE',
    		  '#FF99C3',
    		  '#FFE0ED',
    		  '#CDDDFD',
    		  '#CDF3E4',
    		  '#CED4DE',
    		  '#FCEBB9',
    		  '#D3CEFD',
    		  '#945FB9',
    		  '#FF9845',
    		];

    		G6.registerNode(
    		  'dice-mind-map-root', {
    		    jsx: (cfg) => {
    		      const width = Util.getTextSize(cfg.label, 16)[0] + 24;
    		      const stroke = cfg.style.stroke || '#096dd9';
    		      const fill = cfg.style.fill;

    		      return `
    		      <group>
    		        <rect draggable="true" style={{width: ${width}, height: 42, stroke: ${stroke}, radius: 4}} keyshape>
    		          <text style={{ fontSize: 16, marginLeft: 12, marginTop: 12 }}>${cfg.label}</text>
    		          <text style={{ marginLeft: ${
    		            width - 16
    		          }, marginTop: -20, stroke: '#66ccff', fill: '#000', cursor: 'pointer', opacity: ${
    		        cfg.hover ? 0.75 : 0
    		      } }} action="add">+</text>
    		        </rect>
    		      </group>
    		    `;
    		    },
    		    getAnchorPoints() {
    		      return [
    		        [0, 0.5],
    		        [1, 0.5],
    		      ];
    		    },
    		  },
    		  'single-node',
    		);
    		G6.registerNode(
    		  'dice-mind-map-sub', {
    		    jsx: (cfg) => {
    		      const width = Util.getTextSize(cfg.label, 14)[0] + 24;
    		      const color = cfg.color || cfg.style.stroke;

    		      return `
    		      <group>
    		        <rect draggable="true" style={{width: ${width + 24}, height: 22}} keyshape>
    		          <text draggable="true" style={{ fontSize: 14, marginLeft: 12, marginTop: 6 }}>${
    		            cfg.label
    		          }</text>
    		          <text style={{ marginLeft: ${
    		            width - 8
    		          }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${
    		        cfg.hover ? 0.75 : 0
    		      }, next: 'inline' }} action="add">+</text>
    		          <text style={{ marginLeft: ${
    		            width - 4
    		          }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${
    		        cfg.hover ? 0.75 : 0
    		      }, next: 'inline' }} action="delete">-</text>
    		        </rect>
    		        <rect style={{ fill: ${color}, width: ${width + 24}, height: 2, x: 0, y: 22 }} />
    		        
    		      </group>
    		    `;
    		    },
    		    getAnchorPoints() {
    		      return [
    		        [0, 0.965],
    		        [1, 0.965],
    		      ];
    		    },
    		  },
    		  'single-node',
    		);
    		G6.registerNode(
    		  'dice-mind-map-leaf', {
    		    jsx: (cfg) => {
    		      const width = Util.getTextSize(cfg.label, 12)[0] + 24;
    		      const color = cfg.color || cfg.style.stroke;

    		      return `
    		      <group>
    		        <rect draggable="true" style={{width: ${width + 20}, height: 26, fill: 'transparent' }}>
    		          <text style={{ fontSize: 12, marginLeft: 12, marginTop: 6 }}>${cfg.label}</text>
    		              <text style={{ marginLeft: ${
    		                width - 8
    		              }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${
    		        cfg.hover ? 0.75 : 0
    		      }, next: 'inline' }} action="add">+</text>
    		              <text style={{ marginLeft: ${
    		                width - 4
    		              }, marginTop: -10, stroke: ${color}, fill: '#000', cursor: 'pointer', opacity: ${
    		        cfg.hover ? 0.75 : 0
    		      }, next: 'inline' }} action="delete">-</text>
    		        </rect>
    		        <rect style={{ fill: ${color}, width: ${width + 24}, height: 2, x: 0, y: 32 }} />
    		        
    		      </group>
    		    `;
    		    },
    		    getAnchorPoints() {
    		      return [
    		        [0, 0.965],
    		        [1, 0.965],
    		      ];
    		    },
    		  },
    		  'single-node',
    		);
    		G6.registerBehavior('dice-mindmap', {
    		  getEvents() {
    		    return {
    		      'node:click': 'clickNode',
    		      'node:dblclick': 'editNode',
    		      'node:mouseenter': 'hoverNode',
    		      'node:mouseleave': 'hoverNodeOut',
    		    };
    		  },
    		  clickNode(evt) {
    		    const model = evt.item.get('model');
    		    const name = evt.target.get('action');
    		    switch (name) {
    		      case 'add':
    		        const newId =
    		          model.id +
    		          '-' +
    		          (((model.children || []).reduce((a, b) => {
    		              const num = Number(b.id.split('-').pop());
    		              return a < num ? num : a;
    		            }, 0) || 0) +
    		            1);
    		        evt.currentTarget.updateItem(evt.item, {
    		          children: (model.children || []).concat([{
    		            id: newId,
    		            direction: newId.charCodeAt(newId.length - 1) % 2 === 0 ? 'right' : 'left',
    		            label: 'New',
    		            type: 'dice-mind-map-leaf',
    		            color: model.color || colorArr[Math.floor(Math.random() * colorArr.length)],
    		          }, ]),
    		        });
    		        evt.currentTarget.layout(false);
    		        break;
    		      case 'delete':
    		        const parent = evt.item.get('parent');
    		        evt.currentTarget.updateItem(parent, {
    		          children: (parent.get('model').children || []).filter((e) => e.id !== model.id),
    		        });
    		        evt.currentTarget.layout(false);
    		        break;
    		      case 'edit':
    		        break;
    		      default:
    		        return;
    		    }
    		  },
    		  editNode(evt) {
    		    const item = evt.item;
    		    const model = item.get('model');
    		    const {
    		      x,
    		      y
    		    } = item.calculateBBox();
    		    const graph = evt.currentTarget;
    		    const realPosition = evt.currentTarget.getClientByPoint(x, y);
    		    const el = document.createElement('div');
    		    const fontSizeMap = {
    		      'dice-mind-map-root': 24,
    		      'dice-mind-map-sub': 18,
    		      'dice-mind-map-leaf': 16,
    		    };
    		    el.style.fontSize = fontSizeMap[model.type] + 'px';
    		    el.style.position = 'fixed';
    		    el.style.top = realPosition.y + 'px';
    		    el.style.left = realPosition.x + 'px';
    		    el.style.paddingLeft = '12px';
    		    el.style.transformOrigin = 'top left';
    		    el.style.transform = `scale(${evt.currentTarget.getZoom()})`;
    		    const input = document.createElement('input');
    		    input.style.border = 'none';
    		    input.value = model.label;
    		    input.style.width = Util.getTextSize(model.label, fontSizeMap[model.type])[0] + 'px';
    		    input.className = 'dice-input';
    		    el.className = 'dice-input';
    		    el.appendChild(input);
    		    document.body.appendChild(el);
    		    const destroyEl = () => {
    		      document.body.removeChild(el);
    		    };
    		    const clickEvt = (event) => {
    		      if (!(event.target && event.target.className && event.target.className.includes('dice-input'))) {
    		        window.removeEventListener('mousedown', clickEvt);
    		        window.removeEventListener('scroll', clickEvt);
    		        graph.updateItem(item, {
    		          label: input.value,
    		        });
    		        graph.layout(false);
    		        graph.off('wheelZoom', clickEvt);
    		        destroyEl();
    		      }
    		    };
    		    graph.on('wheelZoom', clickEvt);
    		    window.addEventListener('mousedown', clickEvt);
    		    window.addEventListener('scroll', clickEvt);
    		    input.addEventListener('keyup', (event) => {
    		      if (event.key === 'Enter') {
    		        clickEvt({
    		          target: {},
    		        });
    		      }
    		    });
    		  },
    		  hoverNode(evt) {
    		    evt.currentTarget.updateItem(evt.item, {
    		      hover: true,
    		    });
    		  },
    		  hoverNodeOut(evt) {
    		    evt.currentTarget.updateItem(evt.item, {
    		      hover: false,
    		    });
    		  },
    		});
    		G6.registerBehavior('scroll-canvas', {
    		  getEvents: function getEvents() {
    		    return {
    		      wheel: 'onWheel',
    		    };
    		  },

    		  onWheel: function onWheel(ev) {
    		    const {
    		      graph
    		    } = this;
    		    if (!graph) {
    		      return;
    		    }
    		    if (ev.ctrlKey) {
    		      const canvas = graph.get('canvas');
    		      const point = canvas.getPointByClient(ev.clientX, ev.clientY);
    		      let ratio = graph.getZoom();
    		      if (ev.wheelDelta > 0) {
    		        ratio += ratio * 0.05;
    		      } else {
    		        ratio *= ratio * 0.05;
    		      }
    		      graph.zoomTo(ratio, {
    		        x: point.x,
    		        y: point.y,
    		      });
    		    } else {
    		      const x = ev.deltaX || ev.movementX;
    		      const y = ev.deltaY || ev.movementY || (-ev.wheelDelta * 125) / 3;
    		      graph.translate(-x, -y);
    		    }
    		    ev.preventDefault();
    		  },
    		});

    		const dataTransform = (data) => {
    		  const changeData = (d, level = 0, color) => {
    		    const data = {
    		      ...d,
    		    };
    		    switch (level) {
    		      case 0:
    		        data.type = 'dice-mind-map-root';
    		        break;
    		      case 1:
    		        data.type = 'dice-mind-map-sub';
    		        break;
    		      default:
    		        data.type = 'dice-mind-map-leaf';
    		        break;
    		    }

    		    data.hover = false;

    		    if (color) {
    		      data.color = color;
    		    }

    		    if (level === 1 && !d.direction) {
    		      if (!d.direction) {
    		        data.direction = d.id.charCodeAt(d.id.length - 1) % 2 === 0 ? 'right' : 'left';
    		      }
    		    }

    		    if (d.children) {
    		      data.children = d.children.map((child) => changeData(child, level + 1, data.color));
    		    }
    		    return data;
    		  };
    		  return changeData(data);
    		};

    		const container = document.getElementById('container');
    		console.log(container);
    		const width = document.body.scrollWidth;;
    		const height = document.body.clientHeight-195;
    		const tree = new G6.TreeGraph({
    		  container: 'container',
    		  width,
    		  height,
    		  fitView: true,
    		  fitViewPadding: [10, 20],
    		  layout: {
    		    type: 'mindmap',
    		    direction: 'H',
    		    getHeight: () => {
    		      return 16;
    		    },
    		    getWidth: (node) => {
    		      return node.level === 0 ?
    		        Util.getTextSize(node.label, 16)[0] + 12 :
    		        Util.getTextSize(node.label, 12)[0];
    		    },
    		    getVGap: () => {
    		      return 10;
    		    },
    		    getHGap: () => {
    		      return 60;
    		    },
    		    getSide: (node) => {
    		      return node.data.direction;
    		    },
    		  },
    		  defaultEdge: {
    		    type: 'cubic-horizontal',
    		    style: {
    		      lineWidth: 2,
    		    },
    		  },
    		  minZoom: 0.5,
    		  modes: {
    		    default: ['drag-canvas', 'zoom-canvas', 'dice-mindmap'],
    		  },
    		});

    		tree.data(dataTransform(this.rawData));

    		tree.render();
    		
    		this.tree=tree;
    }
  }
};
</script>
<style>
	
</style>
