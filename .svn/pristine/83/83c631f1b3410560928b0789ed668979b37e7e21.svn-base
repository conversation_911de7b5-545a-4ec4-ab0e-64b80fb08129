package com.dqms.mdm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 数据实体属性历史对象 mdm_data_entity_prop_his
 *
 * <AUTHOR>
 * @date 2021-04-10
 */
public class MdmDataEntityPropHis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据实体属性历史表编号 */
    private Long propId;

    /** 所属实体ID */
    @Excel(name = "所属实体ID")
    private Long entityId;

    /** 属性名称 */
    @Excel(name = "属性名称")
    private String propName;

    /** 属性注释 */
    @Excel(name = "属性注释")
    private String propComment;

    /** 手工维护注释 */
    @Excel(name = "手工维护注释")
    private String propDesc;

    /** 是否主键 */
    @Excel(name = "是否主键")
    private String isPriKey;

    /** 可否为空 */
    @Excel(name = "可否为空")
    private String nullable;

    /** 列大小 */
    @Excel(name = "列大小")
    private int columnSize;

    /** 小数位数 */
    @Excel(name = "小数位数")
    private int decimalDigits;

    /**  java.sql.Types中的SQL类型 */
    @Excel(name = " java.sql.Types中的SQL类型")
    private String dataType;

    /** 默认值 */
    @Excel(name = "默认值")
    private String defaultValue;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operType;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNo;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long updateId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createId;

    private String modifyFields;

    private int orderId;

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public String getModifyFields() {
        return modifyFields;
    }

    public void setModifyFields(String modifyFields) {
        this.modifyFields = modifyFields;
    }

    public void setPropId(Long propId)
    {
        this.propId = propId;
    }

    public Long getPropId()
    {
        return propId;
    }
    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setPropName(String propName)
    {
        this.propName = propName;
    }

    public String getPropName()
    {
        return propName;
    }
    public void setPropComment(String propComment)
    {
        this.propComment = propComment;
    }

    public String getPropComment()
    {
        return propComment;
    }
    public void setPropDesc(String propDesc)
    {
        this.propDesc = propDesc;
    }

    public String getPropDesc()
    {
        return propDesc;
    }
    public void setIsPriKey(String isPriKey)
    {
        this.isPriKey = isPriKey;
    }

    public String getIsPriKey()
    {
        return isPriKey;
    }
    public void setNullable(String nullable)
    {
        this.nullable = nullable;
    }

    public String getNullable()
    {
        return nullable;
    }

    public int getColumnSize() {
        return columnSize;
    }

    public void setColumnSize(int columnSize) {
        this.columnSize = columnSize;
    }

    public int getDecimalDigits() {
        return decimalDigits;
    }

    public void setDecimalDigits(int decimalDigits) {
        this.decimalDigits = decimalDigits;
    }

    public void setDataType(String dataType)
    {
        this.dataType = dataType;
    }

    public String getDataType()
    {
        return dataType;
    }
    public void setDefaultValue(String defaultValue)
    {
        this.defaultValue = defaultValue;
    }

    public String getDefaultValue()
    {
        return defaultValue;
    }
    public void setOperType(String operType)
    {
        this.operType = operType;
    }

    public String getOperType()
    {
        return operType;
    }
    public void setVersionNo(String versionNo)
    {
        this.versionNo = versionNo;
    }

    public String getVersionNo()
    {
        return versionNo;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("propId", getPropId())
            .append("entityId", getEntityId())
            .append("propName", getPropName())
            .append("propComment", getPropComment())
            .append("propDesc", getPropDesc())
            .append("isPriKey", getIsPriKey())
            .append("nullable", getNullable())
            .append("columnSize", getColumnSize())
            .append("decimalDigits", getDecimalDigits())
            .append("dataType", getDataType())
            .append("defaultValue", getDefaultValue())
            .append("operType", getOperType())
            .append("versionNo", getVersionNo())
            .append("createBy", getCreateBy())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .toString();
    }
}
