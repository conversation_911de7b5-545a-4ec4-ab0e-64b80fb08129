<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.mdm.mapper.MdmDataEntityShipMapper">

    <resultMap type="MdmDataEntityShip" id="MdmDataEntityShipResult">
        <result property="shipId"    column="ship_id"    />
        <result property="srcEntityId"    column="src_entity_id"    />
        <result property="srcEntityPropId"    column="src_entity_prop_id"    />
        <result property="tarEntityId"    column="tar_entity_id"    />
        <result property="tarEntityPropId"    column="tar_entity_prop_id"    />
        <result property="shipType"    column="ship_type"    />
        <result property="transferRule"    column="transfer_rule"    />
        <result property="shipName"    column="ship_name"    />
        <result property="versionNo"    column="version_no"    />
        <result property="systemId"    column="system_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />

        <result property="srcTableName"     column="src_table_name"    />
        <result property="tarTableName"     column="tar_table_name"    />
        <result property="srcPropName"     column="src_prop_name"    />
        <result property="tarPropName"     column="tar_prop_name"    />
        <result property="srcSystemName"    column="src_system_name"    />
        <result property="tarSystemName"    column="tar_system_name"    />
        <result property="tableSchema"    column="table_schema"    />
        <result property="triggerType"    column="trigger_type"    />
        <result property="entityName"    column="entity_name"    />
        <result property="entityId"    column="entity_id"    />
        

    </resultMap>

    <sql id="selectMdmDataEntityShipVo">
        select s.ship_id, s.src_entity_id, s.src_entity_prop_id, s.tar_entity_id, s.tar_entity_prop_id, s.ship_type, s.transfer_rule, s.ship_name, s.version_no, s.system_id, s.create_by, s.update_by, s.create_id, s.update_id, s.create_time, s.update_time
        ,s.trigger_type
        ,esrc.table_name src_table_name
        ,etar.table_name tar_table_name
        ,psrc.prop_name src_prop_name
        ,ptar.prop_name tar_prop_name
        ,syssrc.name src_system_name
        ,systar.name tar_system_name
        from mdm_data_entity_ship s

        left join mdm_data_entity esrc  on s.src_entity_id = esrc.entity_id
        left join mdm_data_entity_prop psrc  on s.src_entity_prop_id = psrc.prop_id
        left join mdm_registry regsrc on esrc.registry_id = regsrc.reg_id
        left join sys_system syssrc on regsrc.system_id = syssrc.system_id

        left join mdm_data_entity etar  on s.tar_entity_id = etar.entity_id
        left join mdm_data_entity_prop ptar  on s.tar_entity_prop_id = ptar.prop_id
        left join mdm_registry regtar on etar.registry_id = regtar.reg_id
        left join sys_system systar on regtar.system_id = systar.system_id
    </sql>
    <sql id="selectMdmDataEntityShipRelVo">
        select s.ship_id, s.src_entity_id, s.src_entity_prop_id, s.tar_entity_id, s.tar_entity_prop_id, s.ship_type, s.transfer_rule, s.ship_name, s.version_no, s.system_id, s.create_by, s.update_by, s.create_id, s.update_id, s.create_time, s.update_time
        ,s.trigger_type
        ,esrc.table_name src_table_name
        ,etar.table_name tar_table_name
        ,psrc.prop_name src_prop_name
        ,ptar.prop_name tar_prop_name
        ,syssrc.name src_system_name
        ,systar.name tar_system_name
        ,erel.table_name entity_name
        ,rel.entity_id
        from mdm_data_entity_ship s
		left join mdm_data_entity_ship_analysis_rel rel on s.ship_id=rel.ship_id
		left join mdm_data_entity erel  on rel.entity_id = erel.entity_id
		
		
        left join mdm_data_entity esrc  on s.src_entity_id = esrc.entity_id
        left join mdm_data_entity_prop psrc  on s.src_entity_prop_id = psrc.prop_id
        left join mdm_registry regsrc on esrc.registry_id = regsrc.reg_id
        left join sys_system syssrc on regsrc.system_id = syssrc.system_id

        left join mdm_data_entity etar  on s.tar_entity_id = etar.entity_id
        left join mdm_data_entity_prop ptar  on s.tar_entity_prop_id = ptar.prop_id
        left join mdm_registry regtar on etar.registry_id = regtar.reg_id
        left join sys_system systar on regtar.system_id = systar.system_id
    </sql>
    <select id="selectMdmDataEntityShipList" parameterType="MdmDataEntityShip" resultMap="MdmDataEntityShipResult">
        <include refid="selectMdmDataEntityShipVo"/>
        <where>
            <if test="srcSystemId != null "> and syssrc.system_id = #{srcSystemId}</if>
            <if test="tarSystemId != null "> and systar.system_id = #{tarSystemId}</if>
            <if test="srcEntityId != null "> and esrc.entity_id = #{srcEntityId}</if>
            <if test="tarEntityId != null "> and etar.entity_id = #{tarEntityId}</if>
            <if test="srcEntityPropId != null "> and s.src_entity_prop_id = #{srcEntityPropId}</if>
            <if test="tarEntityPropId != null "> and s.tar_entity_prop_id = #{tarEntityPropId}</if>
             and regsrc.del_flag != '2'
             and regtar.del_flag != '2'
        </where>
    </select>
    
    <select id="selectMdmDataEntityShipListByRel" parameterType="MdmDataEntityShip" resultMap="MdmDataEntityShipResult">
        <include refid="selectMdmDataEntityShipRelVo"/>
        <where>
            <if test="srcSystemId != null "> and syssrc.system_id = #{srcSystemId}</if>
            <if test="tarSystemId != null "> and systar.system_id = #{tarSystemId}</if>
            <if test="srcEntityId != null "> and esrc.entity_id = #{srcEntityId}</if>
            <if test="tarEntityId != null "> and etar.entity_id = #{tarEntityId}</if>
            <if test="srcEntityPropId != null "> and s.src_entity_prop_id = #{srcEntityPropId}</if>
            <if test="tarEntityPropId != null "> and s.tar_entity_prop_id = #{tarEntityPropId}</if>
             and regsrc.del_flag != '2'
             and regtar.del_flag != '2'
        </where>
    </select>
    
    <select id="selectMdmDataEntityShipForTableRelList" parameterType="MdmDataEntityShip" resultMap="MdmDataEntityShipResult">
        <include refid="selectMdmDataEntityShipVo"/>
        <where>
            <if test="srcSystemId != null "> and syssrc.system_id = #{srcSystemId}</if>
            <if test="tarSystemId != null "> and systar.system_id = #{tarSystemId}</if>
            <if test="srcEntityId != null "> and esrc.entity_id = #{srcEntityId}</if>
            <if test="tarEntityId != null "> and etar.entity_id = #{tarEntityId}</if>
            and s.src_entity_prop_id is null
            and s.tar_entity_prop_id is null
             and regsrc.del_flag != '2'
             and regtar.del_flag != '2'
        </where>
    </select>
    <select id="selectSrcMdmDataEntityShipList" parameterType="MdmDataEntityShip" resultMap="MdmDataEntityShipResult">
        SELECT
        mde.entity_id src_entity_id,
        mde.table_name src_table_name,
        mdep.prop_id src_prop_id,
        mdep.prop_name src_prop_name,
        ss.system_id src_system_id,
        ss.NAME src_system_name
        FROM
        mdm_data_entity mde
        LEFT JOIN mdm_registry mdr ON mde.registry_id = mdr.reg_id
        LEFT JOIN sys_system ss ON mdr.system_id = ss.system_id
        LEFT JOIN mdm_data_entity_prop mdep ON mde.entity_id = mdep.entity_id
        <where>
            <if test="srcSystemName != null "> and ss.name = #{srcSystemName}</if>
            <if test="srcTableName != null "> and mde.table_name = #{srcTableName}</if>
            <if test="srcPropName != null "> and mdep.prop_name = #{srcPropName}</if>
            and mdr.del_flag != '2'
        </where>
    </select>
    <select id="selectTarMdmDataEntityShipList" parameterType="MdmDataEntityShip" resultMap="MdmDataEntityShipResult">
        SELECT
        mde.entity_id,
        mde.table_name,
        mdep.prop_id,
        mdep.prop_name,
        ss.system_id,
        ss.NAME
        FROM
        mdm_data_entity mde
        LEFT JOIN mdm_registry mdr ON mde.registry_id = mdr.reg_id
        LEFT JOIN sys_system ss ON mdr.system_id = ss.system_id
        LEFT JOIN mdm_data_entity_prop mdep ON mde.entity_id = mdep.entity_id
        <where>
            <if test="tarSystemName != null "> and ss.name = #{tarSystemName}</if>
            <if test="tarTableName != null "> and mde.table_name = #{tarTableName}</if>
            <if test="tarPropName != null "> and mdep.prop_name = #{tarPropName}</if>
            and mdr.del_flag != '2'
        </where>
    </select>

    <select id="selectMdmDataEntityShipById" parameterType="Long" resultMap="MdmDataEntityShipResult">
        <include refid="selectMdmDataEntityShipVo"/>
        where ship_id = #{shipId}
    </select>
    <select id="selectMdmDataEntityShipByRel"  parameterType="MdmDataEntityShip" resultMap="MdmDataEntityShipResult">
        <include refid="selectMdmDataEntityShipRelVo"/>
        where s.ship_id = #{shipId} and rel.entity_id=#{entityId}
    </select>

    <insert id="insertMdmDataEntityShip" parameterType="MdmDataEntityShip"  useGeneratedKeys="true" keyProperty="shipId">
        insert into mdm_data_entity_ship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shipId != null">ship_id,</if>
            <if test="srcEntityId != null">src_entity_id,</if>
            <if test="srcEntityPropId != null">src_entity_prop_id,</if>
            <if test="tarEntityId != null">tar_entity_id,</if>
            <if test="tarEntityPropId != null">tar_entity_prop_id,</if>
            <if test="shipType != null">ship_type,</if>
            <if test="transferRule != null">transfer_rule,</if>
            <if test="shipName != null">ship_name,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="systemId != null">system_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="triggerType != null">trigger_type,</if>
         </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shipId != null">#{shipId},</if>
            <if test="srcEntityId != null">#{srcEntityId},</if>
            <if test="srcEntityPropId != null">#{srcEntityPropId},</if>
            <if test="tarEntityId != null">#{tarEntityId},</if>
            <if test="tarEntityPropId != null">#{tarEntityPropId},</if>
            <if test="shipType != null">#{shipType},</if>
            <if test="transferRule != null">#{transferRule},</if>
            <if test="shipName != null">#{shipName},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="triggerType != null">#{triggerType},</if>
         </trim>
    </insert>

    <update id="updateMdmDataEntityShip" parameterType="MdmDataEntityShip">
        update mdm_data_entity_ship
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcEntityId != null">src_entity_id = #{srcEntityId},</if>
            <if test="srcEntityPropId != null">src_entity_prop_id = #{srcEntityPropId},</if>
            <if test="tarEntityId != null">tar_entity_id = #{tarEntityId},</if>
            <if test="tarEntityPropId != null">tar_entity_prop_id = #{tarEntityPropId},</if>
            <if test="shipType != null">ship_type = #{shipType},</if>
            <if test="transferRule != null">transfer_rule = #{transferRule},</if>
            <if test="shipName != null">ship_name = #{shipName},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="triggerType != null">trigger_type = #{triggerType},</if>
        </trim>
        where ship_id = #{shipId}
    </update>

    <delete id="deleteMdmDataEntityShipById" parameterType="Long">
        delete from mdm_data_entity_ship where ship_id = #{shipId}
    </delete>

    <delete id="deleteMdmDataEntityShipByIds" parameterType="String">
        delete from mdm_data_entity_ship where ship_id in
        <foreach item="shipId" collection="array" open="(" separator="," close=")">
            #{shipId}
        </foreach>
    </delete>

    <delete id="deleteMdmDataEntityShipByEntityId">
        delete from mdm_data_entity_ship where src_entity_id = #{srcEntityId} and tar_entity_id = #{tarEntityId}
    </delete>
    
   <resultMap type="MdmDataEntityVo" id="MdmDataEntityVoResult">
        <result property="entityId"    column="entity_id"    />
        <result property="registryId"    column="registry_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableComment"    column="table_comment"    />
        <result property="tableSchema"    column="table_schema"    />
        <collection  property="relations"   javaType="java.util.List"        resultMap="MdmDataEntityShipResult" />
        <collection  property="mdmDataEntityProps"   javaType="java.util.List"        resultMap="MdmDataEntityPropResult" />
    </resultMap>
    <resultMap type="MdmDataEntityProp" id="MdmDataEntityPropResult">
        <result property="propId"    column="prop_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />
        <result property="propDesc"    column="prop_desc"    />
        <result property="isPriKey"    column="is_pri_key"    />
		<result property="dataType"    column="data_type"    />
    </resultMap>
    <select id="findShipMdmDataEntityVoByTarEntityIds" resultMap="MdmDataEntityVoResult">
        SELECT se.table_schema,se.table_name,se.entity_id,se.table_comment,
			sep.prop_id,sep.prop_name ,sep.prop_comment,sep.is_pri_key,sep.data_type,
			s.ship_id,s.src_entity_prop_id,s.src_entity_id,s.tar_entity_id,s.tar_entity_prop_id,s.ship_type
	        ,psrc.prop_name src_prop_name
	        ,ptar.prop_name tar_prop_name
			FROM mdm_data_entity se
			left join mdm_data_entity_ship s ON s.tar_entity_id=se.entity_id and s.ship_type !='1'
			LEFT JOIN mdm_data_entity_prop sep ON se.entity_id=sep.entity_id
			left join mdm_data_entity_prop psrc  on s.src_entity_prop_id = psrc.prop_id
			left join mdm_data_entity_prop ptar  on s.tar_entity_prop_id = ptar.prop_id
        <where>
        	<if test="tarEntityIds != null  and tarEntityIds.length>0">
        	(se.entity_id in
	        <foreach item="tarEntityId" collection="tarEntityIds" open="(" separator="," close=")">
	            #{tarEntityId}
	        </foreach>
	        )
	        </if>
        </where>
    </select>
    <select id="findShipMdmDataEntityVoBySrcEntityIds" resultMap="MdmDataEntityVoResult">
        SELECT se.table_schema,se.table_name,se.entity_id,se.table_comment,
			sep.prop_id,sep.prop_name ,sep.prop_comment,sep.is_pri_key,sep.data_type,
			s.ship_id,s.src_entity_prop_id,s.src_entity_id,s.tar_entity_id,s.tar_entity_prop_id,s.ship_type
	        ,psrc.prop_name src_prop_name
	        ,ptar.prop_name tar_prop_name
			FROM mdm_data_entity se
			left join mdm_data_entity_ship s ON s.src_entity_id=se.entity_id and s.ship_type !='1'
			LEFT JOIN mdm_data_entity_prop sep ON se.entity_id=sep.entity_id
			left join mdm_data_entity_prop psrc  on s.src_entity_prop_id = psrc.prop_id
			left join mdm_data_entity_prop ptar  on s.tar_entity_prop_id = ptar.prop_id
        <where>
        	<if test="srcEntityIds != null  and srcEntityIds.length>0">
        	 (se.entity_id in
	        <foreach item="srcEntityId" collection="srcEntityIds" open="(" separator="," close=")">
	            #{srcEntityId}
	        </foreach>
	        )
	        </if>
        </where>
    </select>
    <select id="findShipMdmDataEntityVoByTarEntityPropIds" resultMap="MdmDataEntityVoResult">
        SELECT se.table_schema,se.table_name,se.entity_id,se.table_comment,
			sep.prop_id,sep.prop_name ,sep.prop_comment,sep.is_pri_key,sep.data_type,
			s.ship_id,s.src_entity_prop_id,s.src_entity_id,s.tar_entity_id,s.tar_entity_prop_id,s.ship_type
			,psrc.prop_name src_prop_name
			,ptar.prop_name tar_prop_name
			FROM mdm_data_entity_prop p
			LEFT JOIN mdm_data_entity se  ON se.entity_id=p.entity_id
			LEFT JOIN mdm_data_entity_prop sep ON se.entity_id=sep.entity_id
			LEFT JOIN mdm_data_entity_ship s ON s.tar_entity_prop_id=p.prop_id and s.ship_type !='1'
			LEFT JOIN mdm_data_entity_prop psrc  ON s.src_entity_prop_id = psrc.prop_id
			LEFT JOIN mdm_data_entity_prop ptar  ON s.tar_entity_prop_id = ptar.prop_id
        <where>
        	<if test="tarEntityPropIds != null  and tarEntityPropIds.length>0">
        	(p.prop_id in
	        <foreach item="tarEntityPropId" collection="tarEntityPropIds" open="(" separator="," close=")">
	            #{tarEntityPropId}
	        </foreach>
	        )
	        </if>
        </where>
    </select>
    <select id="findShipMdmDataEntityVoBySrcEntityPropIds" resultMap="MdmDataEntityVoResult">
        SELECT se.table_schema,se.table_name,se.entity_id,se.table_comment,
			sep.prop_id,sep.prop_name ,sep.prop_comment,sep.is_pri_key,sep.data_type,
			s.ship_id,s.src_entity_prop_id,s.src_entity_id,s.tar_entity_id,s.tar_entity_prop_id,s.ship_type
			,psrc.prop_name src_prop_name
			,ptar.prop_name tar_prop_name
			FROM mdm_data_entity_prop p
			LEFT JOIN mdm_data_entity se  ON se.entity_id=p.entity_id
			LEFT JOIN mdm_data_entity_prop sep ON se.entity_id=sep.entity_id
			LEFT JOIN mdm_data_entity_ship s ON s.src_entity_prop_id=p.prop_id and s.ship_type !='1'
			LEFT JOIN mdm_data_entity_prop psrc  ON s.src_entity_prop_id = psrc.prop_id
			LEFT JOIN mdm_data_entity_prop ptar  ON s.tar_entity_prop_id = ptar.prop_id
        <where>
        	<if test="srcEntityPropIds != null  and srcEntityPropIds.length>0">
        	(p.prop_id in
	        <foreach item="srcEntityPropId" collection="srcEntityPropIds" open="(" separator="," close=")">
	            #{srcEntityPropId}
	        </foreach>
	        )
	        </if>
        </where>
    </select>

    <select id="findShipListByTheme" resultMap="MdmDataEntityVoResult">
        SELECT se.table_schema,se.table_name,se.entity_id,se.table_comment,
			sep.prop_id,sep.prop_name ,sep.prop_comment,sep.is_pri_key,sep.data_type,
			s.ship_id,s.src_entity_prop_id,s.src_entity_id,s.tar_entity_id,s.tar_entity_prop_id,s.ship_type
	        ,psrc.prop_name src_prop_name
	        ,ptar.prop_name tar_prop_name
			FROM mdm_data_entity se left join mdm_registry regtar on se.registry_id = regtar.reg_id
			left join mdm_data_entity_ship s ON s.tar_entity_id=se.entity_id and s.ship_type !='1'
			LEFT JOIN mdm_data_entity_prop sep ON se.entity_id=sep.entity_id
			left join mdm_data_entity_prop psrc  on s.src_entity_prop_id = psrc.prop_id
			left join mdm_data_entity_prop ptar  on s.tar_entity_prop_id = ptar.prop_id
			where regtar.theme_id=#{themeId}
    </select>
    
    <delete id="removeSource" parameterType="MdmDataEntityShip" >
        delete from mdm_data_entity_ship_analysis_rel where ship_id = #{shipId} and entity_id= #{entityId}
    </delete>

    <insert id="insertMdmDataEntityShipList" parameterType="java.util.List" >
        insert into mdm_data_entity_ship
        (
            ship_id,
            src_entity_id,
            src_entity_prop_id,
            tar_entity_id,
            tar_entity_prop_id,
            ship_type,
            transfer_rule,
            ship_name,
            version_no,
            system_id,
            create_by,
            update_by,
            create_id,
            update_id,
            create_time,
            update_time,
            trigger_type
        ) values
    <foreach collection="list" item="item"   separator="," >
          (
            #{item.shipId},
            #{item.srcEntityId},
            #{item.srcEntityPropId},
            #{item.tarEntityId},
            #{item.tarEntityPropId},
            #{item.shipType},
            #{item.transferRule},
            #{item.shipName},
            #{item.versionNo},
            #{item.systemId},
            #{item.createBy},
            #{item.updateBy},
            #{item.createId},
            #{item.updateId},
            #{item.createTime},
            #{item.updateTime},
            #{item.triggerType}
            )
    </foreach>
    </insert>


</mapper>
