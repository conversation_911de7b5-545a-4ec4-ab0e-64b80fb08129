<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmMdmRelMapper">

    <resultMap type="DsmMdmRel" id="DsmMdmRelResult">
        <result property="indexId"    column="index_id"    />
        <result property="dimensionId"    column="dimension_id"    />
        <result property="propId"    column="prop_id"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemName"    column="system_name"    />
    </resultMap>

    <sql id="selectDsmMdmRelVo">
        SELECT m.prop_id,m.prop_name ,m.prop_comment,e.table_name,ds.name AS datasource_name,ss.name AS system_name
		FROM mdm_data_entity_prop m
		LEFT JOIN mdm_data_entity e ON m.entity_id=e.entity_id
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
		LEFT JOIN sys_system ss ON r.system_id=ss.system_id
    </sql>

    <select id="selectDsmMdmRelList" parameterType="DsmMdmRel" resultMap="DsmMdmRelResult">
        <include refid="selectDsmMdmRelVo"/>
        <where>
        	<if test="dimensionId != null  and dimensionId != ''"> and m.dimension_id = #{dimensionId}</if>
        	<if test="indexId != null  and indexId != ''"> and m.index_id = #{indexId}</if>
        	<if test="propName != null  and propName != ''">and m.prop_name like concat('%', #{propName}, '%')
        		<if test="propComment != null  and propComment != ''"> or m.prop_comment like concat('%', #{propComment}, '%') </if>
                and r.del_flag != '2'
        	</if>
        </where>
    </select>
    <select id="selectDsmMdmRelUnList" parameterType="DsmMdmRel" resultMap="DsmMdmRelResult">
        <include refid="selectDsmMdmRelVo"/>
        <where>

            r.del_flag != '2'
        	<if test="dimensionId != null  and dimensionId != ''"> and (m.dimension_id is null or m.dimension_id ='') </if>
        	<if test="indexId != null  and indexId != ''">and (m.index_id is null or m.index_id ='')</if>
        	<if test="propName != null  and propName != ''"> and (m.prop_name like concat('%', #{propName}, '%') or e.table_name like concat('%', #{propName}, '%')
        	<if test="propComment != null  and propComment != ''"> or m.prop_comment like concat('%', #{propComment}, '%') </if>
        	)</if>
		 	<if test="tableName != null  and tableName != ''"> and e.table_name like concat('%', #{tableName}, '%')</if>

        </where>
    </select>
    <select id="selectDsmMdmRelAllList" parameterType="DsmMdmRel" resultMap="DsmMdmRelResult">
        <include refid="selectDsmMdmRelVo"/>
        <where>
        	<if test="dimensionId != null  and dimensionId != ''"> and (m.dimension_id is not null and m.dimension_id!='')</if>
        	<if test="indexId != null  and indexId != ''"> and (m.index_id  is not null and m.index_id!='')</if>
            and r.del_flag != '2'
        </where>
    </select>
    <update id="updateDsmMdmRel" parameterType="DsmMdmRel">
        update mdm_data_entity_prop
        <trim prefix="SET" suffixOverrides=",">
            <if test="indexId != null">index_id = #{indexId},</if>
            <if test="dimensionId != null">dimension_id = #{dimensionId},</if>
        </trim>
        where prop_id = #{propId}
    </update>

    <update id="updateDsmMdmRelIndexUn"  parameterType="DsmMdmRel">
        update mdm_data_entity_prop
        set index_id =null
        where index_id = #{indexId} and prop_id = #{propId}
    </update>
    <update id="updateDsmMdmRelIndexUnAll"  parameterType="Long">
        update mdm_data_entity_prop
        set index_id =null
        where index_id = #{indexId}
    </update>
    <update id="updateDsmMdmRelDimensionIdUn"  parameterType="DsmMdmRel">
        update mdm_data_entity_prop
        set dimension_id =null
        where dimension_id = #{dimensionId} and prop_id = #{propId}
    </update>
    <update id="updateDsmMdmRelDimensionIdUnAll"  parameterType="Long">
        update mdm_data_entity_prop
        set dimension_id =null
        where dimension_id = #{dimensionId}
    </update>
    <update id="updateAllDsmMdmRelDimensionIdUn"   parameterType="java.util.List">
        update mdm_data_entity_prop
        set dimension_id =null
        where dimension_id in
            <foreach collection="dsmMdmRel" index="index" item="item" separator="," open="(" close=")">
                #{item.dimensionId}
            </foreach>
    </update>

    <update id="importDsmMdmRelDimensionId"  parameterType="java.util.List">
        update mdm_data_entity_prop  set dimension_id=
            <foreach collection="dsmMdmRel" item="item" index="index" separator=" " open="case prop_Id" close="end">
                when  #{item.propId} then  #{item.dimensionId}
            </foreach>
             where prop_id in
            <foreach collection="dsmMdmRel" index="index" item="item" separator="," open="(" close=")">
                #{item.propId}
            </foreach>
     </update>

    <update id="updateAllDsmIndexMdmRelDimensionIdUn" parameterType="java.util.List" >

        update mdm_data_entity_prop
        set index_id =null
        where index_id in
        <foreach item="item" index="index" collection="list" separator="," open="(" close=")">
        #{item.indexId}
        </foreach>
    </update>


    <update id="importDsmIndexMdmRelDimensionId"  parameterType="java.util.List">
        update mdm_data_entity_prop set index_id =
        <foreach collection="dsmMdmRel" item="item" index="index" separator=" " open="case prop_Id" close="end">
            when  #{item.propId} then  #{item.indexId}
        </foreach>
        where  prop_id in
        <foreach collection="dsmMdmRel" index="index" item="item" separator="," open="(" close=")">
            #{item.propId}
        </foreach>


    </update>




</mapper>
