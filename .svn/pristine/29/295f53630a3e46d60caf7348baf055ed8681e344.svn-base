<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="所属系统" prop="systemId">
        <el-select
          v-model="queryParams.systemId"
          placeholder="请选择所属系统"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据源" prop="datasourceId">
        <el-select
          v-model="queryParams.datasourceId"
          placeholder="请选择数据源"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="表名" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入表名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域类型" prop="regionType">
        <el-select
          v-model="queryParams.regionType"
          placeholder="请选择区域类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in regionTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生命周期" prop="lifeCycle">
        <el-input-number
          v-model="queryParams.lifeCycle"
          placeholder="请输入生命周期"
          clearable
          @keyup.enter.native="handleQuery"
          size="small"
          :min="0"
          controls-position="right"
          style="width:215px"
        />
      </el-form-item>
      <el-form-item label="存储格式" prop="storageFormat">
        <el-select
          v-model="queryParams.storageFormat"
          placeholder="请选择存储格式"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in storageFormatOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分区类型" prop="partitionType">
        <el-select
          v-model="queryParams.partitionType"
          placeholder="请选择分区类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in partitionTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分区生命" prop="partitionLifeCycle">
        <el-input
          v-model="queryParams.partitionLifeCycle"
          placeholder="请输入分区生命"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分区格式" prop="partitionFormat">
        <el-select
          v-model="queryParams.partitionFormat"
          placeholder="请选择分区格式"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in partitionFormatOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="归档周期" prop="fileCycle">
        <el-select
          v-model="queryParams.fileCycle"
          placeholder="请选择归档周期"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in fileCycleOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="归档方式" prop="fileType">
        <el-select
          v-model="queryParams.fileType"
          placeholder="请选择归档方式"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in fileTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dsc:dscEntity:remove']"
          >注销</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dsc:dscEntity:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['dsm:dsmStandard:export']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :refreshShow="true"
        :searchShow="true"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="dscEntityList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属系统" align="center" prop="systemName"  width="155"/>
      <el-table-column label="数据源" align="center" prop="datasourceName"  width="155"/>
      <el-table-column label="表" align="center" prop="tableName" width="260" />
      <el-table-column label="用户/库/脚本路径" align="center" prop="regDir" width="220"/>
      <el-table-column
        label="区域类型"
        align="center"
        prop="regionType"
        :formatter="regionTypeFormat"
      />
      <el-table-column label="生命周期" align="center" prop="lifeCycle" />
      <el-table-column
        label="存储格式"
        align="center"
        prop="storageFormat"
        :formatter="storageFormatFormat"
      />
      <el-table-column
        label="分区字段"
        align="center"
        prop="partitionField"
        width="220"
      />
      <el-table-column
        label="分区类型"
        align="center"
        prop="partitionType"
        :formatter="partitionTypeFormat"
      />
      <el-table-column
        label="分区生命"
        align="center"
        prop="partitionLifeCycle"
      />
      <el-table-column
        label="分区格式"
        align="center"
        prop="partitionFormat"
        :formatter="partitionFormatFormat"
      />
      <el-table-column
        label="分桶字段"
        align="center"
        prop="bucketField"
        width="220"
      />
      <el-table-column label="分桶数量" align="center" prop="bucketNum" />
      <el-table-column
        label="归档周期"
        align="center"
        prop="fileCycle"
        :formatter="fileCycleFormat"
      />
      <el-table-column
        label="归档方式"
        align="center"
        prop="fileType"
        :formatter="fileTypeFormat"
        width="220"
      />
      <el-table-column
        label="日期字段"
        align="center"
        prop="dateField"
        width="220"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsc:dscEntity:edit']"
            >设置</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsc:dscEntity:remove']"
            v-if="scope.row.dscEntityId != null && scope.row.dscEntityId != ''"
            >注销</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改生命周期对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row type="flex" justify="start" align="top" v-show="false">
          <el-col :span="12">
            <el-form-item label="实体ID" prop="entityId">
              <el-input
                v-model="form.entityId"
                placeholder="请输入实体ID"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="区域类型">
              <el-radio-group v-model="form.regionType">
                <el-radio
                  v-for="dict in regionTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生命周期" prop="lifeCycle">
              <el-input-number
                v-model="form.lifeCycle"
                placeholder="请输入生命周期，0为无期限"
                clearable
                :min="0"
                controls-position="right"
                style="width:100%"
              >
                <template slot="append">天</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="存储格式">
              <el-radio-group v-model="form.storageFormat">
                <el-radio
                  v-for="dict in storageFormatOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="分区字段" prop="partitionField">
              <el-input
                v-model="form.partitionField"
                placeholder="请输入分区字段"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分区生命" prop="partitionLifeCycle">
              <el-input-number
                v-model="form.partitionLifeCycle"
                placeholder="请输入分区生命，0为无期限"
                clearable
                :min="0"
                controls-position="right"
                style="width:100%"
              >
                <template slot="append">天</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="分区类型">
              <el-radio-group v-model="form.partitionType">
                <el-radio
                  v-for="dict in partitionTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="分区格式">
              <el-radio-group v-model="form.partitionFormat">
                <el-radio
                  v-for="dict in partitionFormatOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="分桶字段" prop="bucketField">
              <el-input
                v-model="form.bucketField"
                placeholder="请输入分桶字段"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分桶数量" prop="bucketNum">
              <el-input-number
                v-model="form.bucketNum"
                placeholder="请输入分桶数量"
                clearable
                :min="0"
                controls-position="right"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="HDFS路径" prop="hdfsPath">
              <el-input
                v-model="form.hdfsPath"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="归档周期">
              <el-radio-group v-model="form.fileCycle">
                <el-radio
                  v-for="dict in fileCycleOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="归档方式" prop="fileType">
              <el-select
                v-model="form.fileType"
                placeholder="请选择归档方式 "
                style="width:100%"
                filterable
                clearable
              >
                <el-option
                  v-for="dict in fileTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期字段" prop="dateField">
              <el-input
                v-model="form.dateField"
                placeholder="请输入日期字段"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 主题导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox
            v-model="upload.updateSupport"
          />是否更新已经存在的生命周期,以<span style="font-weight: bold"
            >名称</span
          >为主键更新
          <el-link type="info" style="font-size:12px" @click="importTemplate"
            >下载模板</el-link
          >
        </div>
        <div class="el-upload__tip" style="color:#ff0000" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDscEntity,
  getDscEntity,
  delDscEntity,
  addDscEntity,
  updateDscEntity,
  exportDscEntity,
  importTemplate
} from "@/api/dsc/dscEntity";
import { listDatasourceAll, listSystem } from "@/api/basic/datasource";
import { getToken } from "@/utils/auth";

export default {
  name: "DscEntity",
  components: {},
  data() {
    return {
      dataSourceOptions: [],
      //应用系统选项
      systemOptions: [],
      // 主题导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/dsc/dscEntity/import"
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 生命周期表格数据
      dscEntityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 区域类型字典
      regionTypeOptions: [],
      // 存储格式字典
      storageFormatOptions: [],
      // 分区类型字典
      partitionTypeOptions: [],
      // 分区格式字典
      partitionFormatOptions: [],
      // 归档周期字典
      fileCycleOptions: [],
      // 归档方式字典
      fileTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entityId: null,
        regionType: null,
        lifeCycle: undefined,
        storageFormat: null,
        partitionField: null,
        partitionType: null,
        partitionLifeCycle: undefined,
        partitionFormat: null,
        bucketField: null,
        bucketNum: undefined,
        hdfsPath: null,
        fileCycle: null,
        fileType: null,
        dateField: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entityId: [
          { required: true, message: "实体ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("dsc_entity_region_type").then(response => {
      this.regionTypeOptions = response.data;
    });
    this.getDicts("dsc_entity_storage_format").then(response => {
      this.storageFormatOptions = response.data;
    });
    this.getDicts("dsc_entity_run_cycle").then(response => {
      this.partitionTypeOptions = response.data;
    });
    this.getDicts("sys_data_type").then(response => {
      this.partitionFormatOptions = response.data;
    });
    this.getDicts("dsc_entity_run_cycle").then(response => {
      this.fileCycleOptions = response.data;
    });
    this.getDicts("dsc_entity_file_type").then(response => {
      this.fileTypeOptions = response.data;
    });
    this.getSystem();
    this.getDataSource();
  },
  methods: {
    /** 获取应用系统 */
    getSystem() {
      let systemParams = {};
      listSystem(systemParams).then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "生命周期导入";
      this.upload.open = true;
    },
    /** 查询生命周期列表 */
    getList() {
      this.loading = true;
      listDscEntity(this.queryParams).then(response => {
        this.dscEntityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 区域类型字典翻译
    regionTypeFormat(row, column) {
      return this.selectDictLabel(this.regionTypeOptions, row.regionType);
    },
    // 存储格式字典翻译
    storageFormatFormat(row, column) {
      return this.selectDictLabel(this.storageFormatOptions, row.storageFormat);
    },
    // 分区类型字典翻译
    partitionTypeFormat(row, column) {
      return this.selectDictLabel(this.partitionTypeOptions, row.partitionType);
    },
    // 分区格式字典翻译
    partitionFormatFormat(row, column) {
      return this.selectDictLabel(
        this.partitionFormatOptions,
        row.partitionFormat
      );
    },
    // 归档周期字典翻译
    fileCycleFormat(row, column) {
      return this.selectDictLabel(this.fileCycleOptions, row.fileCycle);
    },
    // 归档方式字典翻译
    fileTypeFormat(row, column) {
      return this.selectDictLabel(this.fileTypeOptions, row.fileType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dscEntityId: null,
        entityId: null,
        regionType: "N",
        lifeCycle: undefined,
        storageFormat: "ORC",
        partitionField: null,
        partitionType: "W",
        partitionLifeCycle: undefined,
        partitionFormat: null,
        bucketField: null,
        bucketNum: undefined,
        hdfsPath: null,
        fileCycle: "W",
        fileType: null,
        dateField: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dscEntityId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加生命周期";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dscEntityId = row.dscEntityId || this.ids;
      this.form.entityId = row.tableId;
      if (dscEntityId != null && dscEntityId != "") {
        getDscEntity(dscEntityId).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改生命周期";
        });
      } else {
        this.open = true;
        this.title = "添加生命周期";
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dscEntityId != null) {
            updateDscEntity(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDscEntity(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dscEntityIds = row.dscEntityId || this.ids;
      this.$confirm(
        '是否确认删除生命周期编号为"' + dscEntityIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delDscEntity(dscEntityIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有生命周期数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportDscEntity(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    }
  }
};
</script>
