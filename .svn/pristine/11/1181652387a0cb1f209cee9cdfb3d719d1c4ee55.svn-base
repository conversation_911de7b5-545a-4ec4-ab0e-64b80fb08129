package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 模型关系对象 dsm_model_entity_ship
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
public class DsmModelEntityShip extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关系ID */
    private Long modelEntityShipId;

    /** 来源实体 */
    @Excel(name = "来源实体")
    private Long srcModelEntityId;

    /** 来源字段 */
    @Excel(name = "来源字段")
    private Long srcModelEntityPropId;
    private String srcModelEntityPropName;

    /** 目标实体 */
    @Excel(name = "目标实体")
    private Long tarModelEntityId;

    /** 目标字段 */
    @Excel(name = "目标字段")
    private Long tarModelEntityPropId;
    private String tarModelEntityPropName;

    /** 版本 */
    @Excel(name = "版本")
    private String versionNo;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** 更新 */
    @Excel(name = "更新")
    private String updateExec;

    /** 删除 */
    @Excel(name = "删除")
    private String deleteExec;

    public void setModelEntityShipId(Long modelEntityShipId)
    {
        this.modelEntityShipId = modelEntityShipId;
    }

    public Long getModelEntityShipId()
    {
        return modelEntityShipId;
    }
    public void setSrcModelEntityId(Long srcModelEntityId)
    {
        this.srcModelEntityId = srcModelEntityId;
    }

    public Long getSrcModelEntityId()
    {
        return srcModelEntityId;
    }
    public void setSrcModelEntityPropId(Long srcModelEntityPropId)
    {
        this.srcModelEntityPropId = srcModelEntityPropId;
    }

    public Long getSrcModelEntityPropId()
    {
        return srcModelEntityPropId;
    }
    public void setTarModelEntityId(Long tarModelEntityId)
    {
        this.tarModelEntityId = tarModelEntityId;
    }

    public Long getTarModelEntityId()
    {
        return tarModelEntityId;
    }
    public void setTarModelEntityPropId(Long tarModelEntityPropId)
    {
        this.tarModelEntityPropId = tarModelEntityPropId;
    }

    public Long getTarModelEntityPropId()
    {
        return tarModelEntityPropId;
    }
    public void setVersionNo(String versionNo)
    {
        this.versionNo = versionNo;
    }

    public String getVersionNo()
    {
        return versionNo;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setUpdateExec(String updateExec)
    {
        this.updateExec = updateExec;
    }

    public String getUpdateExec()
    {
        return updateExec;
    }
    public void setDeleteExec(String deleteExec)
    {
        this.deleteExec = deleteExec;
    }

    public String getDeleteExec()
    {
        return deleteExec;
    }

    public String getSrcModelEntityPropName() {
		return srcModelEntityPropName;
	}

	public void setSrcModelEntityPropName(String srcModelEntityPropName) {
		this.srcModelEntityPropName = srcModelEntityPropName;
	}

	public String getTarModelEntityPropName() {
		return tarModelEntityPropName;
	}

	public void setTarModelEntityPropName(String tarModelEntityPropName) {
		this.tarModelEntityPropName = tarModelEntityPropName;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("modelEntityShipId", getModelEntityShipId())
            .append("srcModelEntityId", getSrcModelEntityId())
            .append("srcModelEntityPropId", getSrcModelEntityPropId())
            .append("tarModelEntityId", getTarModelEntityId())
            .append("tarModelEntityPropId", getTarModelEntityPropId())
            .append("versionNo", getVersionNo())
            .append("createBy", getCreateBy())
            .append("name", getName())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("updateExec", getUpdateExec())
            .append("deleteExec", getDeleteExec())
            .toString();
    }
}
