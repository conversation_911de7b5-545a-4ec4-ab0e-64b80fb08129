package com.dqms.api.service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

import com.dqms.api.domain.ApiTemplateMapping;

/**
 * 数据补录模版Service接口
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public interface IApiTemplateMappingService
{
    /**
     * 查询数据补录模版
     *
     * @param id 数据补录模版ID
     * @return 数据补录模版
     */
    public ApiTemplateMapping selectApiTemplateMappingById(Long id);

    /**
     * 查询数据补录模版列表
     *
     * @param apiTemplateMapping 数据补录模版
     * @return 数据补录模版集合
     */
    public List<ApiTemplateMapping> selectApiTemplateMappingList(ApiTemplateMapping apiTemplateMapping);

    /**
     * 新增数据补录模版
     *
     * @param apiTemplateMapping 数据补录模版
     * @return 结果
     */
    public int insertApiTemplateMapping(ApiTemplateMapping apiTemplateMapping);

    /**
     * 修改数据补录模版
     *
     * @param apiTemplateMapping 数据补录模版
     * @return 结果
     */
    public int updateApiTemplateMapping(ApiTemplateMapping apiTemplateMapping);

    /**
     * 批量删除数据补录模版
     *
     * @param ids 需要删除的数据补录模版ID
     * @return 结果
     */
    public int deleteApiTemplateMappingByIds(Long[] ids);

    /**
     * 删除数据补录模版信息
     *
     * @param id 数据补录模版ID
     * @return 结果
     */
    public int deleteApiTemplateMappingById(Long id);

    /**
     * 添加模板映射查询表名
     * @param condition
     * @return
     */
    List<Map<String,Object>> getTableNames();

    /**
     * 模板映射管理通过表名实现自动映射关系
     * @param table_name
     * @return
     */
    List getTableMapping(String table_name);

    Map selectRecord(Long templateId) throws Exception;

    /**
     * 获取临时表数据
     * @param sql
     * @return
     */
    List<Map<String,Object>> getTempTable(String sql);

    List<Map<String, Object>> getTempForTable(String table_name);
    void insertSqlWocAll(List<Map<String, Object>> list,String templateId,String templateName,String templateId2,String excelName);
    List<Map<String, Object>> currencyValuationExcel(Map<String, String> mapping, String excelName, String templateId, String fileName, InputStream in);

    public List<Map<String, Object>> ValuationExcel(String templateId2, String templateName2, String fileName, InputStream in);

    /**
     * 清除表数据
     * @param tableName
     */
    void cleanTempTable(String tableName);
    /**
     *执行存储过程
     * @param apiTemplateMapping
     */
    void updateProcedures(String procedure);

}
