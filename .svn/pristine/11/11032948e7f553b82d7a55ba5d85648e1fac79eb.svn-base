import request from '@/utils/request'

// 查询接口输出列表
export function listApiDefineColumn(query) {
  return request({
    url: '/api/apiDefineColumn/list',
    method: 'get',
    params: query
  })
}

// 查询接口输出详细
export function getApiDefineColumn(defineColumnId) {
  return request({
    url: '/api/apiDefineColumn/' + defineColumnId,
    method: 'get'
  })
}

// 新增接口输出
export function addApiDefineColumn(data) {
  return request({
    url: '/api/apiDefineColumn',
    method: 'post',
    data: data
  })
}

// 修改接口输出
export function updateApiDefineColumn(data) {
  return request({
    url: '/api/apiDefineColumn',
    method: 'put',
    data: data
  })
}

// 删除接口输出
export function delApiDefineColumn(defineColumnId) {
  return request({
    url: '/api/apiDefineColumn/' + defineColumnId,
    method: 'delete'
  })
}

// 导出接口输出
export function exportApiDefineColumn(query) {
  return request({
    url: '/api/apiDefineColumn/export',
    method: 'get',
    params: query
  })
}