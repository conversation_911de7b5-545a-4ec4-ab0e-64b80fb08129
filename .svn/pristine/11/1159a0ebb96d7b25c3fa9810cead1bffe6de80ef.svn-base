package com.dqms.dsm.domain.vo;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

public class DsmMdmRelVo extends BaseEntity {

    /** 系统 */
    @Excel(name = "维度字典系统")
    private String systemName;
    private Long systemId;

    /** 编码 */
    @Excel(name = "维度字典编码")
    private String dimensionCode;

    /** 数据源 */
    @Excel(name = "元数据数据源")
    private String datasourceName;
    private Long datasourceId;



    /** 系统 */
    @Excel(name = "元数据表")
    private String tableName;

    /** 系统 */
    @Excel(name = "元数据字段")
    private String regName;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public String getDimensionCode() {
        return dimensionCode;
    }

    public void setDimensionCode(String dimensionCode) {
        this.dimensionCode = dimensionCode;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public Long getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(Long datasourceId) {
        this.datasourceId = datasourceId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }
}
