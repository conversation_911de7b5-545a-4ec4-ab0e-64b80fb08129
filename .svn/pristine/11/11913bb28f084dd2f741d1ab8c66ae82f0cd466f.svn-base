package com.dqms.mdm.service;

import java.util.List;
import java.util.Map;

import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataMonitor;

/**
 * 元数据变更监控Service接口
 *
 * <AUTHOR>
 * @date 2021-04-13
 */
public interface IMdmDataMonitorService
{
    /**
     * 查询元数据变更监控
     *
     * @param monitorId 元数据变更监控ID
     * @return 元数据变更监控
     */
    public MdmDataMonitor selectMdmDataMonitorById(Long monitorId);

    /**
     * 查询元数据变更监控列表
     *
     * @param mdmDataMonitor 元数据变更监控
     * @return 元数据变更监控集合
     */
    public List<MdmDataMonitor> selectMdmDataMonitorList(MdmDataMonitor mdmDataMonitor);

    /**
     * 新增元数据变更监控
     *
     * @param mdmDataMonitor 元数据变更监控
     * @return 结果
     */
    public int insertMdmDataMonitor(MdmDataMonitor mdmDataMonitor);

    /**
     * 修改元数据变更监控
     *
     * @param mdmDataMonitor 元数据变更监控
     * @return 结果
     */
    public int updateMdmDataMonitor(MdmDataMonitor mdmDataMonitor);

    /**
     * 批量删除元数据变更监控
     *
     * @param monitorIds 需要删除的元数据变更监控ID
     * @return 结果
     */
    public int deleteMdmDataMonitorByIds(Long[] monitorIds);

    /**
     * 删除元数据变更监控信息
     *
     * @param monitorId 元数据变更监控ID
     * @return 结果
     */
    public int deleteMdmDataMonitorById(Long monitorId);

    void batchChange(Long[] monitorIds);

    void change(MdmDataMonitor mdmDataMonitor);

    void mtcUpdate(MdmDataEntity mdmDataEntity);

    void ingore(MdmDataMonitor mdmDataMonitor);

    Map getEntityNewAndOld(MdmDataMonitor mdmDataMonitor);
}
