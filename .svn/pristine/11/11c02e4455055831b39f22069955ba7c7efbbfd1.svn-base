package com.dqms.dqm.controller;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.dqms.dqm.domain.DqmValidationMouldPojo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dqm.domain.DqmValidationMould;
import com.dqms.dqm.service.IDqmValidationMouldService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 质量模板Controller
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@RestController
@RequestMapping("/dqm/mould")
public class DqmValidationMouldController extends BaseController
{
    @Autowired
    private IDqmValidationMouldService dqmValidationMouldService;

    /**
     * 查询质量模板列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationMould:list')")
    @GetMapping("/list")
    public TableDataInfo list(DqmValidationMould dqmValidationMould)
    {
        startPage();
        List<DqmValidationMould> list = dqmValidationMouldService.selectDqmValidationMouldList(dqmValidationMould);
        return getDataTable(list);
    }

    /**
     * 导出质量模板列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationMould:export')")
    @Log(title = "质量模板", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmValidationMould dqmValidationMould)
    {
        List<DqmValidationMould> list = dqmValidationMouldService.selectDqmValidationMouldList(dqmValidationMould);
        ExcelUtil<DqmValidationMould> util = new ExcelUtil<DqmValidationMould>(DqmValidationMould.class);
        return util.exportExcel(list, "mould");
    }

    /**
     * 获取质量模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationMould:query')")
    @GetMapping(value = "/{validationMouldId}")
    public AjaxResult getInfo(@PathVariable("validationMouldId") Integer validationMouldId)
    {
        return AjaxResult.success(dqmValidationMouldService.selectDqmValidationMouldById(validationMouldId));
    }

    /**
     * 新增质量模板
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationMould:add')")
    @Log(title = "质量模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map queryBody)
    {
        DqmValidationMouldPojo dqmValidationMouldPojo = JSONObject.parseObject(
                JSONObject.toJSONString(queryBody),DqmValidationMouldPojo.class);
        return toAjax(dqmValidationMouldService.insertDqmValidationMould(dqmValidationMouldPojo));
    }

    /**
     * 修改质量模板
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationMould:edit')")
    @Log(title = "质量模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmValidationMould dqmValidationMould)
    {
        return toAjax(dqmValidationMouldService.updateDqmValidationMould(dqmValidationMould));
    }

    /**
     * 删除质量模板
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationMould:remove')")
    @Log(title = "质量模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{validationMouldIds}")
    public AjaxResult remove(@PathVariable Integer[] validationMouldIds)
    {
         //删除模板主数据
         int MouldCode = dqmValidationMouldService.deleteDqmValidationMouldByIds(validationMouldIds);
         //删除模板参数列表
         dqmValidationMouldService.deleteDqmValidationMouldParameterByMouldIds(validationMouldIds);
        return toAjax(MouldCode);
    }

}
