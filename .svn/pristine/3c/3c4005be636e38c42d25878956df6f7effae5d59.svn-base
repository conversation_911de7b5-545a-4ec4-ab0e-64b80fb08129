<template>
  <div class="app-container" style="padding:20px;">
	<div id="scalingToolBar" style="position:absolute;padding-top:10;padding-right:50px;right:0;height:100;cursor:pointer;width:95%;z-index: 99;">
   		<el-slider v-model="zoomIndex" :step="1" @input="zoomTo()" show-stops></el-slider>
	</div>
  	<div id="mapcontainer" style="position: relative;"></div>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { findShipAllList,showShipMap} from "@/api/mdm/mdmDataEntityShip";
import request from '@/utils/request'
import G6 from '@antv/g6'
export default {
  name: "MdmDataEntityShip",
  components: {
  },
  data() {
	    return {
		  entityId:null,
		  zoomIndex:50,
	      srcg6graph:undefined,
	      srcnodes:[],
	      srcedges:[]
	    };
  },
  created() {
	  this.entityId = this.$route.params && this.$route.params.entityId;
	  if(this.entityId!=null){
		  this.getSrcList(this.entityId);
	  }
  },
  methods: {
	  zoomTo(){
      if(this.srcg6graph!=null) {
        this.srcg6graph.zoomTo(this.zoomIndex / 100);
      }
	  },
	  getSrcList(entityId){
	    	if(this.srcg6graph){
	    		this.srcg6graph.destroy();
	    	}
	    	if(entityId==null){
	    		return;
	    	}
	        this.srcnodes=[];
	        this.srcedges=[];
	        let queryParams={};
	    	queryParams.tarEntityIds=[];
	    	queryParams.tarEntityIds.push(entityId);
		    findShipAllList(queryParams).then(response => {
		          for(var i=0;i<response.data.length;i++){
		        	  if(response.data[i].entryType=='API-TABLE'){
		        		    this.srcnodes.push({id:(response.data[i].entityId).toString(),label:response.data[i].tableName,attrs:response.data[i].mdmDataEntityProps,collapsed:"collapsed",xtype:"API-TABLE"});
		          		}else{
		          			this.srcnodes.push({id:(response.data[i].entityId).toString(),label:response.data[i].tableName,attrs:response.data[i].mdmDataEntityProps});
		          		}

		    			if(response.data[i].relations!=null){
		    				for(var j=0;j<response.data[i].relations.length;j++){
		    					//this.edges.push({"source":(response.data[i].relations[j].srcEntityId).toString(),"target":(response.data[i].relations[j].tarEntityId).toString()});
		    					if(response.data[i].relations[j].shipType=='3'&&response.data[i].relations[j].srcEntityId!=null&&response.data[i].relations[j].tarEntityId!=null){
		    						this.srcedges.push({source: (response.data[i].relations[j].srcEntityId).toString(),target: (response.data[i].relations[j].tarEntityId).toString(),sourceKey: response.data[i].relations[j].srcPropName,targetKey: response.data[i].relations[j].tarPropName,label: "交换"});
		    					}else if(response.data[i].relations[j].shipType!='API-TABLE'&&response.data[i].relations[j].srcEntityId!=null&&response.data[i].relations[j].tarEntityId!=null){
		    						this.srcedges.push({source: (response.data[i].relations[j].srcEntityId).toString(),target: (response.data[i].relations[j].tarEntityId).toString(),sourceKey: response.data[i].relations[j].srcPropName,targetKey: response.data[i].relations[j].tarPropName,label: "计算"});
		    					}else if(response.data[i].relations[j].shipType=='API-TABLE'&&response.data[i].relations[j].srcTableName!=null&&response.data[i].relations[j].tarTableName!=null){
		    						this.srcedges.push({source: response.data[i].relations[j].srcTableName,target: response.data[i].relations[j].tarTableName,sourceKey: response.data[i].relations[j].srcPropName,targetKey: response.data[i].relations[j].tarPropName,label: "管控"});
		    					}

		    				}
		    			}
		    		}
		          this.srcg6graph=showShipMap("mapcontainer",this.srcnodes,this.srcedges);
		    });
	    }


  },
  destroyed () {
  	  //注意，VUE此处必须清理，否则切换界面会越来越卡
  	  if(this.srcg6graph){
  		  this.srcg6graph.clear();
  		  this.srcg6graph.destroy();
  	  }
 	  if(this.targ6graph){
	     this.targ6graph.clear();
	     this.targ6graph.destroy();
 	  }
  }
};
</script>
<style>
  .input-with-select .el-input-group__prepend {
    background-color: #fff;
  }
  .el-collapse-item__header{
  	background-color: rgb(48, 65, 86);
  	color:#FFF;
  	border-radius: 4px;
  	padding-left:10px;
  }
  .el-collapse-item{
  	border: 1px;
  }
  .el-radio--mini {
  	width:100%;
  }
  .el-radio {
	margin-left: 0px;
  }
</style>
