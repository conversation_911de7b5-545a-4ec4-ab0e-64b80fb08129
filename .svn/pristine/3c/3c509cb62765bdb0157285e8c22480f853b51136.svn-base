package com.dqms.needs.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.enums.BusinessType;
import com.dqms.needs.domain.NesNeeds;
import com.dqms.needs.domain.NesNeedsHandle;
import com.dqms.needs.service.INesNeedsHandleService;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.framework.web.service.TokenService;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 需求处理Controller
 *
 * <AUTHOR>
 * @date 2021-05-19
 */
@RestController
@RequestMapping("/needs/needsHandle")
public class NesNeedsHandleController extends BaseController
{
    @Autowired
    private INesNeedsHandleService nesNeedsHandleService;
    
    @Autowired
    private TokenService tokenService;
    
    /**
     * 查询需求处理列表
     */
    @PreAuthorize("@ss.hasPermi('needs:needsHandle:list')")
    @GetMapping("/list")
    public TableDataInfo list(NesNeedsHandle nesNeedsHandle)
    {
        startPage();
        List<NesNeedsHandle> list = nesNeedsHandleService.selectNesNeedsHandleList(nesNeedsHandle);
        return getDataTable(list);
    }

    /**
     * 导出需求处理列表
     */
    @PreAuthorize("@ss.hasPermi('needs:needsHandle:export')")
    @Log(title = "需求处理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(NesNeedsHandle nesNeedsHandle)
    {
        List<NesNeedsHandle> list = nesNeedsHandleService.selectNesNeedsHandleList(nesNeedsHandle);
        ExcelUtil<NesNeedsHandle> util = new ExcelUtil<NesNeedsHandle>(NesNeedsHandle.class);
        return util.exportExcel(list, "needsHandle");
    }

    /**
     * 获取需求处理详细信息
     */
    @PreAuthorize("@ss.hasPermi('needs:needsHandle:query')")
    @GetMapping(value = "/{needsHandleId}")
    public AjaxResult getInfo(@PathVariable("needsHandleId") Long needsHandleId)
    {
        return AjaxResult.success(nesNeedsHandleService.selectNesNeedsHandleById(needsHandleId));
    }

    /**
     * 新增需求处理
     */
    @PreAuthorize("@ss.hasPermi('needs:needsHandle:add')")
    @Log(title = "需求处理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NesNeedsHandle nesNeedsHandle)
    {
        return toAjax(nesNeedsHandleService.insertNesNeedsHandle(nesNeedsHandle));
    }

    /**
     * 修改需求处理
     */
    @PreAuthorize("@ss.hasPermi('needs:needsHandle:edit')")
    @Log(title = "需求处理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NesNeedsHandle nesNeedsHandle)
    {
        return toAjax(nesNeedsHandleService.updateNesNeedsHandle(nesNeedsHandle));
    }

    /**
     * 删除需求处理
     */
    @PreAuthorize("@ss.hasPermi('needs:needsHandle:remove')")
    @Log(title = "需求处理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{needsHandleIds}")
    public AjaxResult remove(@PathVariable Long[] needsHandleIds)
    {
        return toAjax(nesNeedsHandleService.deleteNesNeedsHandleByIds(needsHandleIds));
    }
    
    /**
     * 单个文件下载
     */
    @GetMapping("/singleDown")
    public void singleDown(Long needsHandleId, HttpServletResponse response, HttpServletRequest request)
    throws Exception{

    	NesNeedsHandle nesNeedsHandle=nesNeedsHandleService.selectNesNeedsHandleById(needsHandleId);
        String fileName=nesNeedsHandle.getAttachment().substring(nesNeedsHandle.getAttachment().lastIndexOf("/")+1);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String operName = loginUser.getUsername();
            StringBuffer logSb = new StringBuffer();
            logSb.append("工号").append(loginUser.getUser().getNickName()).append(operName).append("下载了：").append(fileName+"文件");//文档名称
        try{
        	nesNeedsHandleService.singleDown(needsHandleId,response,request);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
