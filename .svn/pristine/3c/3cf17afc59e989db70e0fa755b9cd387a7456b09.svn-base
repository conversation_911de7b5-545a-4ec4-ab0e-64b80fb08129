package com.dqms.dqm.domain;

import com.dqms.common.core.domain.BaseEntity;
import java.util.List;


/**
 * 新增模板中间实体
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
public class DqmValidationMouldPojo extends BaseEntity{
    private static final long serialVersionUID = 1L;


    /** 模板主对象 */
    private DqmValidationMould dqmValidationMould;

    /** 模板参数对象集合 */
    private List<DqmValidationMouldParameter> dqmValidationMouldParameter;


    public DqmValidationMould getDqmValidationMould() {
        return dqmValidationMould;
    }

    public void setDqmValidationMould(DqmValidationMould dqmValidationMould) {
        this.dqmValidationMould = dqmValidationMould;
    }

    public List<DqmValidationMouldParameter> getDqmValidationMouldParameter() {
        return dqmValidationMouldParameter;
    }

    public void setDqmValidationMouldParameter(List<DqmValidationMouldParameter> dqmValidationMouldParameter) {
        this.dqmValidationMouldParameter = dqmValidationMouldParameter;
    }
}
