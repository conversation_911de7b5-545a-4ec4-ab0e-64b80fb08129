package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmDimensionDetaiTemp;

/**
 * 维度明细临时Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmDimensionDetaiTempMapper 
{
    /**
     * 查询维度明细临时
     * 
     * @param dimensionDetaiTempId 维度明细临时ID
     * @return 维度明细临时
     */
    public DsmDimensionDetaiTemp selectDsmDimensionDetaiTempById(Long dimensionDetaiTempId);

    /**
     * 查询维度明细临时列表
     * 
     * @param dsmDimensionDetaiTemp 维度明细临时
     * @return 维度明细临时集合
     */
    public List<DsmDimensionDetaiTemp> selectDsmDimensionDetaiTempList(DsmDimensionDetaiTemp dsmDimensionDetaiTemp);

    /**
     * 新增维度明细临时
     * 
     * @param dsmDimensionDetaiTemp 维度明细临时
     * @return 结果
     */
    public int insertDsmDimensionDetaiTemp(DsmDimensionDetaiTemp dsmDimensionDetaiTemp);

    /**
     * 修改维度明细临时
     * 
     * @param dsmDimensionDetaiTemp 维度明细临时
     * @return 结果
     */
    public int updateDsmDimensionDetaiTemp(DsmDimensionDetaiTemp dsmDimensionDetaiTemp);

    /**
     * 删除维度明细临时
     * 
     * @param dimensionDetaiTempId 维度明细临时ID
     * @return 结果
     */
    public int deleteDsmDimensionDetaiTempById(Long dimensionDetaiTempId);

    /**
     * 批量删除维度明细临时
     * 
     * @param dimensionDetaiTempIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmDimensionDetaiTempByIds(Long[] dimensionDetaiTempIds);
    
    /**
     * 根据维度删除明细
     * 
     * @param dimensionId
     * @return 结果
     */
    public int deleteDsmDimensionDetaiTempByDimensionId(Long dimensionId);
}
