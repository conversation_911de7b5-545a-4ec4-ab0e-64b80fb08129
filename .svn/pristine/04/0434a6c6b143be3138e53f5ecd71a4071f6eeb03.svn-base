package com.dqms.needs.enums;

import lombok.Data;

/**
 * 规则执行状态码
 */
public enum   ProcessEnum {

    SUCCESS(0,"执行成功"),

    FAIL(2,"执行失败"),

    EXECUTION (1,"执行中");

    private int code;

    private String message;

    ProcessEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }


    /**
     * 通用响应信息
     */
    @Data
    public class Response {

        private int code;

        private String message;

        public Response(ProcessEnum processEnum) {
            this.code = processEnum.getCode();
            this.message = processEnum.getMessage();
        }

    }
}
