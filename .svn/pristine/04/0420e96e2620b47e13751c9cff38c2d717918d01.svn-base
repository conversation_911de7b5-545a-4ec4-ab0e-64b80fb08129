package com.dqms.dqm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 检查规则任务问题处理进度对象 dqm_problem_handling
 *
 * <AUTHOR>
 * @date 2021-07-28
 */
public class DqmProblemHandling extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long dqmProblemHandlingId;

    /** 问题id */
    private Long dqmValidationProblemId;
    /** 处理人Id */
    private Long handlingId;
    /** 处理人 */
    @Excel(name = "处理人")
    private String handlingBy;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handlingTime;

    /** 处理进度 */
    @Excel(name = "处理进度")
    private String handlingLoding;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String handlingMsg;
    
    private String knowledgeFlag;

    public void setDqmProblemHandlingId(Long dqmProblemHandlingId)
    {
        this.dqmProblemHandlingId = dqmProblemHandlingId;
    }

    public Long getDqmProblemHandlingId()
    {
        return dqmProblemHandlingId;
    }
    public void setDqmValidationProblemId(Long dqmValidationProblemId)
    {
        this.dqmValidationProblemId = dqmValidationProblemId;
    }

    public Long getDqmValidationProblemId()
    {
        return dqmValidationProblemId;
    }

    public Long getHandlingId() {
        return handlingId;
    }

    public void setHandlingId(Long handlingId) {
        this.handlingId = handlingId;
    }

    public void setHandlingBy(String handlingBy)
    {
        this.handlingBy = handlingBy;
    }

    public String getHandlingBy()
    {
        return handlingBy;
    }
    public void setHandlingTime(Date handlingTime)
    {
        this.handlingTime = handlingTime;
    }

    public Date getHandlingTime()
    {
        return handlingTime;
    }
    public void setHandlingLoding(String handlingLoding)
    {
        this.handlingLoding = handlingLoding;
    }

    public String getHandlingLoding()
    {
        return handlingLoding;
    }
    public void setHandlingMsg(String handlingMsg)
    {
        this.handlingMsg = handlingMsg;
    }

    public String getHandlingMsg()
    {
        return handlingMsg;
    }

    public String getKnowledgeFlag() {
		return knowledgeFlag;
	}

	public void setKnowledgeFlag(String knowledgeFlag) {
		this.knowledgeFlag = knowledgeFlag;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dqmProblemHandlingId", getDqmProblemHandlingId())
            .append("dqmValidationProblemId", getDqmValidationProblemId())
            .append("handlingBy", getHandlingBy())
            .append("handlingTime", getHandlingTime())
            .append("handlingLoding", getHandlingLoding())
            .append("handlingMsg", getHandlingMsg())
            .toString();
    }
}
