<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px" onSubmit="return false" >
      <el-form-item label="分类名称" prop="standardClassNameFull">
        <el-input
          v-model="queryParams.standardClassNameFull"
          placeholder="请输入分类名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
	    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dsm:dsmStandardClass:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="dsmStandardClassList"
      row-key="standardClassId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="分类名称" header-align="center" align="left" prop="standardClassName" />
      <el-table-column label="祖级路径" align="center" prop="standardClassNameFull" />
      <el-table-column label="显示顺序" align="center" prop="orderNum" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsm:dsmStandardClass:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsm:dsmStandardClass:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改数据标准分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="父分类" prop="parentId">
          <treeselect v-model="form.parentId" :options="dsmStandardClassOptions" :normalizer="normalizer" placeholder="请选择父分类" />
        </el-form-item>
        <el-form-item label="分类名称" prop="standardClassName">
          <el-input v-model="form.standardClassName" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="显示顺序" prop="field101">
                <el-input-number v-model="form.orderNum" placeholder="请输入显示顺序"></el-input-number>
              </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDsmStandardClass, getDsmStandardClass, delDsmStandardClass, addDsmStandardClass, updateDsmStandardClass, exportDsmStandardClass } from "@/api/dsm/dsmStandardClass";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "DsmStandardClass",
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 数据标准分类表格数据
      dsmStandardClassList: [],
      // 数据标准分类树选项
      dsmStandardClassOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        parentId: null,
        ancestors: null,
        standardClassName: null,
        standardClassNameFull: null,
        orderNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询数据标准分类列表 */
    getList() {
      this.loading = true;
      listDsmStandardClass(this.queryParams).then(response => {
        this.dsmStandardClassList = this.handleTree2(response.data, "standardClassId", "parentId");
        this.loading = false;
      });
    },
    /** 转换数据标准分类数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.standardClassId,
        label: node.standardClassName,
        children: node.children
      };
    },
	/** 查询部门下拉树结构 */
    getTreeselect() {
      listDsmStandardClass().then(response => {
        this.dsmStandardClassOptions = [];
        const data = { standardClassId: 0, standardClassName: '顶级节点', children: [] };
        data.children = this.handleTree2(response.data, "standardClassId", "parentId");
        this.dsmStandardClassOptions.push(data);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        standardClassId: null,
        parentId: null,
        ancestors: null,
        standardClassName: null,
        standardClassNameFull: null,
        orderNum: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
	  this.getTreeselect();
      this.open = true;
      this.title = "添加数据标准分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
	  this.getTreeselect();
      if (row != null) {
        this.form.parentId = row.standardClassId;
      }
      getDsmStandardClass(row.standardClassId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据标准分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.standardClassId != null) {
            updateDsmStandardClass(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDsmStandardClass(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除数据标准分类编号为"' + row.standardClassId + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDsmStandardClass(row.standardClassId);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    }
  }
};
</script>
