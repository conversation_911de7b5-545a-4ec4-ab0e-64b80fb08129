import request from '@/utils/request'

// 查询数据实体历史列表
export function listMdmDataEntityHis(query) {
  return request({
    url: '/mdm/mdmDataEntityHis/list',
    method: 'get',
    params: query
  })
}

// 查询数据实体历史详细
export function getMdmDataEntityHis(entityId) {
  return request({
    url: '/mdm/mdmDataEntityHis/' + entityId,
    method: 'get'
  })
}

// 新增数据实体历史
export function addMdmDataEntityHis(data) {
  return request({
    url: '/mdm/mdmDataEntityHis',
    method: 'post',
    data: data
  })
}

// 修改数据实体历史
export function updateMdmDataEntityHis(data) {
  return request({
    url: '/mdm/mdmDataEntityHis',
    method: 'put',
    data: data
  })
}

// 删除数据实体历史
export function delMdmDataEntityHis(entityId) {
  return request({
    url: '/mdm/mdmDataEntityHis/' + entityId,
    method: 'delete'
  })
}

// 导出数据实体历史
export function exportMdmDataEntityHis(query) {
  return request({
    url: '/mdm/mdmDataEntityHis/export',
    method: 'get',
    params: query
  })
}

// 元系统注册变更预览实体信息
export function getPreVesionData(data) {
  return request({
    url: '/mdm/mdmDataEntityHis/getPreVesionData',
    method: 'post',
    data: data
  })
}
