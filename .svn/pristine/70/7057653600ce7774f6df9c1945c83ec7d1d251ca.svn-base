package com.dqms.dsc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dsc.domain.DscEntity;
import com.dqms.dsc.domain.DscEntityProp;
import com.dqms.dsc.domain.vo.DscEntityVo;

/**
 * 生命周期Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface DscEntityMapper 
{
    /**
     * 查询生命周期
     * 
     * @param dscEntityId 生命周期ID
     * @return 生命周期
     */
    public DscEntity selectDscEntityById(Long dscEntityId);

    public DscEntity selectDscEntityByEntityId(Long entityId);

    /**
     * 查询生命周期列表
     * 
     * @param dscEntity 生命周期
     * @return 生命周期集合
     */
    public List<DscEntity> selectDscEntityList(DscEntity dscEntity);
    public List<DscEntityVo> selectDscEntityVoList(DscEntityVo dscEntityVo);
    public List<DscEntity> selectDscEntityListByIdsForTask(@Param("dscEntityIds") Long[] dscEntityIds);

    /**
     * 新增生命周期
     * 
     * @param dscEntity 生命周期
     * @return 结果
     */
    public int insertDscEntity(DscEntity dscEntity);

    /**
     * 修改生命周期
     * 
     * @param dscEntity 生命周期
     * @return 结果
     */
    public int updateDscEntity(DscEntity dscEntity);

    /**
     * 删除生命周期
     * 
     * @param dscEntityId 生命周期ID
     * @return 结果
     */
    public int deleteDscEntityById(Long dscEntityId);

    /**
     * 批量删除生命周期
     * 
     * @param dscEntityIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDscEntityByIds(Long[] dscEntityIds);
    
    
    /**
     * 任务中心查询绑定的任务
     *
     * @param taskId任务IDs
     * @return 结果
     */
    public List<DscEntity> selectDscEntityListByTaskId(@Param("taskId")Long taskId);
    /**
     * 任务中心查询未绑定的任务
     *
     * @param regIds 源系统注册ID
     * @return 结果
     */
    public List<DscEntity> selectUnDscEntityListByTaskId(DscEntity dscEntity);   
    /**
     * 任务中心添加绑定任务
     *
     * @param taskId 任务IDs regIds 源系统注册ID
     * @return 结果
     */
    public int addDscEntityAndTaskRel(@Param("taskId")Long taskId,@Param("ids")Long[] ids);  
    /**
     * 任务中心删除绑定任务
     *
     * @param taskId 任务IDs
     * @return 结果
     */
    public int delDscEntityAndTaskRel(@Param("taskId")Long taskId); 
    
    public DscEntityVo getDscEntityVoByRegId(@Param("regId")Long regId);
}
