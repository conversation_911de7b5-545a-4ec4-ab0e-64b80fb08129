package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmCheckMain;

/**
 * 对标主表Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-01
 */
public interface DsmCheckMainMapper 
{
    /**
     * 查询对标主表
     * 
     * @param checkMainId 对标主表ID
     * @return 对标主表
     */
    public DsmCheckMain selectDsmCheckMainById(Long checkMainId);

    /**
     * 查询对标主表列表
     * 
     * @param dsmCheckMain 对标主表
     * @return 对标主表集合
     */
    public List<DsmCheckMain> selectDsmCheckMainList(DsmCheckMain dsmCheckMain);

    /**
     * 新增对标主表
     * 
     * @param dsmCheckMain 对标主表
     * @return 结果
     */
    public int insertDsmCheckMain(DsmCheckMain dsmCheckMain);

    /**
     * 修改对标主表
     * 
     * @param dsmCheckMain 对标主表
     * @return 结果
     */
    public int updateDsmCheckMain(DsmCheckMain dsmCheckMain);

    /**
     * 删除对标主表
     * 
     * @param checkMainId 对标主表ID
     * @return 结果
     */
    public int deleteDsmCheckMainById(Long checkMainId);

    /**
     * 批量删除对标主表
     * 
     * @param checkMainIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmCheckMainByIds(Long[] checkMainId);
    
    /**
     * 全部删除对标主表
     * 
     * @param checkMainIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmCheckMainAll();
}
