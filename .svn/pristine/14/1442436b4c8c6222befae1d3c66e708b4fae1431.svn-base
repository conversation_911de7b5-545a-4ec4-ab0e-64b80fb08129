package com.dqms.dsc.controller;

import java.util.List;

import com.dqms.dsc.domain.vo.DscDesensitizationVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsc.domain.DscDesensitization;
import com.dqms.dsc.service.IDscDesensitizationService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 数据脱敏Controller
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@RestController
@RequestMapping("/dsc/dscDesensitization")
public class DscDesensitizationController extends BaseController
{
    @Autowired
    private IDscDesensitizationService dscDesensitizationService;

    /**
     * 查询数据脱敏列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscDesensitization:list')")
    @GetMapping("/list")
    public TableDataInfo list(DscDesensitization dscDesensitization)
    {
        startPage();
        List<DscDesensitization> list = dscDesensitizationService.selectDscDesensitizationList(dscDesensitization);
        return getDataTable(list);
    }

    /**
     * 导出数据脱敏列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscDesensitization:export')")
    @Log(title = "数据脱敏", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DscDesensitizationVo dscDesensitizationVo)
    {
        List<DscDesensitizationVo> list = dscDesensitizationService.getDscDesensitizationVoList(dscDesensitizationVo);
        ExcelUtil<DscDesensitizationVo> util = new ExcelUtil<DscDesensitizationVo>(DscDesensitizationVo.class);
        return util.exportExcel(list, "dscDesensitizationVo");
    }

    /**
     * 获取数据脱敏详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscDesensitization:query')")
    @GetMapping(value = "/{desensitizationId}")
    public AjaxResult getInfo(@PathVariable("desensitizationId") Long desensitizationId)
    {
        return AjaxResult.success(dscDesensitizationService.selectDscDesensitizationById(desensitizationId));
    }

    /**
     * 新增数据脱敏
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscDesensitization:add')")
    @Log(title = "数据脱敏", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DscDesensitization dscDesensitization)
    {
        return toAjax(dscDesensitizationService.insertDscDesensitization(dscDesensitization));
    }

    /**
     * 修改数据脱敏
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscDesensitization:edit')")
    @Log(title = "数据脱敏", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DscDesensitization dscDesensitization)
    {
        return toAjax(dscDesensitizationService.updateDscDesensitization(dscDesensitization));
    }

    /**
     * 删除数据脱敏
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscDesensitization:remove')")
    @Log(title = "数据脱敏", businessType = BusinessType.DELETE)
	@DeleteMapping("/{desensitizationIds}")
    public AjaxResult remove(@PathVariable Long[] desensitizationIds)
    {
        return toAjax(dscDesensitizationService.deleteDscDesensitizationByIds(desensitizationIds));
    }
}
