package com.dqms.mdm.controller;

import java.util.List;
import java.util.Random;

import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.service.IMdmDataMonitorService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.mdm.domain.MdmDataMonitor;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 元数据变更监控Controller
 *
 * <AUTHOR>
 * @date 2021-04-13
 */
@RestController
@RequestMapping("/mdm/mdmDataMonitor")
public class MdmDataMonitorController extends BaseController
{
    @Autowired
    private IMdmDataMonitorService mdmDataMonitorService;

    /**
     * 查询元数据变更监控列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmDataMonitor mdmDataMonitor)
    {
        startPage();
        List<MdmDataMonitor> list = mdmDataMonitorService.selectMdmDataMonitorList(mdmDataMonitor);
        return getDataTable(list);
    }

    /**
     * 导出元数据变更监控列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:export')")
    @Log(title = "元数据变更监控", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmDataMonitor mdmDataMonitor)
    {
        List<MdmDataMonitor> list = mdmDataMonitorService.selectMdmDataMonitorList(mdmDataMonitor);
        ExcelUtil<MdmDataMonitor> util = new ExcelUtil<MdmDataMonitor>(MdmDataMonitor.class);
        return util.exportExcel(list, "mdmDataMonitor");
    }

    /**
     * 获取元数据变更监控详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:query')")
    @GetMapping(value = "/{monitorId}")
    public AjaxResult getInfo(@PathVariable("monitorId") Long monitorId)
    {
        return AjaxResult.success(mdmDataMonitorService.selectMdmDataMonitorById(monitorId));
    }

    /**
     * 新增元数据变更监控
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:add')")
    @Log(title = "元数据变更监控", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmDataMonitor mdmDataMonitor)
    {
        return toAjax(mdmDataMonitorService.insertMdmDataMonitor(mdmDataMonitor));
    }

    /**
     * 修改元数据变更监控
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:edit')")
    @Log(title = "元数据变更监控", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmDataMonitor mdmDataMonitor)
    {
        return toAjax(mdmDataMonitorService.updateMdmDataMonitor(mdmDataMonitor));
    }

    /**
     * 删除元数据变更监控
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:remove')")
    @Log(title = "元数据变更监控", businessType = BusinessType.UPDATE)
	@DeleteMapping("/{monitorIds}")
    public AjaxResult remove(@PathVariable Long[] monitorIds)
    {
        return toAjax(mdmDataMonitorService.deleteMdmDataMonitorByIds(monitorIds));
    }



    /**
     * 元数据变更处理
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:change')")
    @Log(title = "元数据变更处理", businessType = BusinessType.UPDATE)
    @PostMapping("/change")
    public AjaxResult change(@RequestBody MdmDataMonitor mdmDataMonitor)
    {
        mdmDataMonitorService.change(mdmDataMonitor);
        return AjaxResult.success("变更成功");
    }

    /**
     * 元数据忽略变更
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:change')")
    @Log(title = "元数据忽略变更", businessType = BusinessType.UPDATE)
    @PostMapping("/ingore")
    public AjaxResult ingore(@RequestBody MdmDataMonitor mdmDataMonitor)
    {
        mdmDataMonitorService.ingore(mdmDataMonitor);
        return AjaxResult.success("忽略变更成功");
    }

    /**
     * 元数据批量变更处理
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:change')")
    @Log(title = "元数据变更处理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchChange/{monitorIds}")
    public AjaxResult batchChange(@PathVariable Long[] monitorIds)
    {
        mdmDataMonitorService.batchChange(monitorIds);
        return AjaxResult.success("批量变更成功");
    }

    /**
     * 元数据变更处理展示
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataMonitor:change')")
    @PostMapping("/getEntityNewAndOld")
    public AjaxResult getEntityNewAndOld(@RequestBody MdmDataMonitor mdmDataMonitor)
    {
        return AjaxResult.success("查询成功",mdmDataMonitorService.getEntityNewAndOld(mdmDataMonitor));
    }


    /**
     * 元数据手动改变更处理
     */
    @Log(title = "元数据变更处理", businessType = BusinessType.DELETE)
    @PostMapping("/changeMtc")
    public AjaxResult changeMtc(@Validated  @RequestBody MdmDataEntity mdmDataMonitor)
    {
        mdmDataMonitorService.mtcUpdate(mdmDataMonitor);
        return AjaxResult.success("变更成功");
    }


}
