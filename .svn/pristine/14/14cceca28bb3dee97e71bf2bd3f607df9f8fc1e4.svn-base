package com.dqms.dsm.service;

import java.util.List;

import com.dqms.dsm.domain.DsmDimensionClass;
import com.dqms.dsm.domain.DsmDimensionClassTreeSelect;
import com.dqms.dsm.domain.DsmIndexClass;

/**
 * 字典分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-05-28
 */
public interface IDsmDimensionClassService 
{
    /**
     * 查询字典分类
     * 
     * @param dimensionClassId 字典分类ID
     * @return 字典分类
     */
    public DsmDimensionClass selectDsmDimensionClassById(Long dimensionClassId);

    /**
     * 查询字典分类列表
     * 
     * @param dsmDimensionClass 字典分类
     * @return 字典分类集合
     */
    public List<DsmDimensionClass> selectDsmDimensionClassList(DsmDimensionClass dsmDimensionClass);

    /**
     * 新增字典分类
     * 
     * @param dsmDimensionClass 字典分类
     * @return 结果
     */
    public int insertDsmDimensionClass(DsmDimensionClass dsmDimensionClass);

    /**
     * 修改字典分类
     * 
     * @param dsmDimensionClass 字典分类
     * @return 结果
     */
    public int updateDsmDimensionClass(DsmDimensionClass dsmDimensionClass);

    /**
     * 批量删除字典分类
     * 
     * @param dimensionClassIds 需要删除的字典分类ID
     * @return 结果
     */
    public int deleteDsmDimensionClassByIds(Long[] dimensionClassIds);

    /**
     * 删除字典分类信息
     * 
     * @param dimensionClassId 字典分类ID
     * @return 结果
     */
    public int deleteDsmDimensionClassById(Long dimensionClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param dsmDimensionClass 任务分类列表
     * @return 树结构列表
     */
    public List<DsmDimensionClass> buildDsmDimensionClassTree(List<DsmDimensionClass> dsmDimensionClass);
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param dsmDimensionClass 任务分类列表
     * @return 下拉树结构列表
     */
    public List<DsmDimensionClassTreeSelect> buildDsmDimensionClassTreeSelect(List<DsmDimensionClass> dsmDimensionClass);
}
