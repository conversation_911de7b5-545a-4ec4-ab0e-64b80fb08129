<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="代理记编码" prop="agentCode">
        <el-input
          v-model="queryParams.agentCode"
          placeholder="请输入代理记编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="代理机名称" prop="agentName">
        <el-input
          v-model="queryParams.agentName"
          placeholder="请输入代理机名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务器类型" prop="serverType">
        <el-select v-model="queryParams.serverType" placeholder="请选择服务器类型" clearable size="small">
          <el-option
            v-for="dict in serverTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服务器地址" prop="serverIp">
        <el-input
          v-model="queryParams.serverIp"
          placeholder="请输入服务器地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务器端口" prop="serverPort">
        <el-input
          v-model="queryParams.serverPort"
          placeholder="请输入服务器端口"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务器用户" prop="serverUser">
        <el-input
          v-model="queryParams.serverUser"
          placeholder="请输入服务器用户"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务器密码" prop="serverPass">
        <el-input
          v-model="queryParams.serverPass"
          placeholder="请输入服务器密码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户端端口" prop="agentPort">
        <el-input
          v-model="queryParams.agentPort"
          placeholder="请输入客户端端口"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="并发线程数" prop="threadNum">
        <el-input
          v-model="queryParams.threadNum"
          placeholder="请输入并发线程数"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basic:agent:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basic:agent:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basic:agent:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basic:agent:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="agentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="代理机ID" align="center" prop="agentId" />
      <el-table-column label="代理记编码" align="center" prop="agentCode" />
      <el-table-column label="代理机名称" align="center" prop="agentName" />
      <el-table-column label="服务器类型" align="center" prop="serverType" :formatter="serverTypeFormat" />
      <el-table-column label="服务器地址" align="center" prop="serverIp" />
      <el-table-column label="服务器端口" align="center" prop="serverPort" />
      <el-table-column label="服务器用户" align="center" prop="serverUser" />
      <el-table-column label="服务器密码" align="center" prop="serverPass" />
      <el-table-column label="客户端路径" align="center" prop="agentAdd" />
      <el-table-column label="客户端端口" align="center" prop="agentPort" />
      <el-table-column label="并发线程数" align="center" prop="threadNum" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basic:agent:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basic:agent:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改代理机管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="代理记编码" prop="agentCode">
          <el-input v-model="form.agentCode" placeholder="请输入代理记编码" />
        </el-form-item>
        <el-form-item label="代理机名称" prop="agentName">
          <el-input v-model="form.agentName" placeholder="请输入代理机名称" />
        </el-form-item>
        <el-form-item label="服务器类型" prop="serverType">
          <el-select v-model="form.serverType" placeholder="请选择服务器类型">
            <el-option
              v-for="dict in serverTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="服务器地址" prop="serverIp">
          <el-input v-model="form.serverIp" placeholder="请输入服务器地址" />
        </el-form-item>
        <el-form-item label="服务器端口" prop="serverPort">
          <el-input v-model="form.serverPort" placeholder="请输入服务器端口" />
        </el-form-item>
        <el-form-item label="服务器用户" prop="serverUser">
          <el-input v-model="form.serverUser" placeholder="请输入服务器用户" />
        </el-form-item>
        <el-form-item label="服务器密码" prop="serverPass">
          <el-input v-model="form.serverPass" placeholder="请输入服务器密码" />
        </el-form-item>
        <el-form-item label="客户端路径" prop="agentAdd">
          <el-input v-model="form.agentAdd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="客户端端口" prop="agentPort">
          <el-input v-model="form.agentPort" placeholder="请输入客户端端口" />
        </el-form-item>
        <el-form-item label="并发线程数" prop="threadNum">
          <el-input v-model="form.threadNum" placeholder="请输入并发线程数" />
        </el-form-item>
        <el-form-item label="创建人ID" prop="createId">
          <el-input v-model="form.createId" placeholder="请输入创建人ID" />
        </el-form-item>
        <el-form-item label="修改人ID" prop="updateId">
          <el-input v-model="form.updateId" placeholder="请输入修改人ID" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAgent, getAgent, delAgent, addAgent, updateAgent, exportAgent } from "@/api/basic/agent";

export default {
  name: "Agent",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 代理机管理表格数据
      agentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 服务器类型字典
      serverTypeOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentCode: null,
        agentName: null,
        serverType: null,
        serverIp: null,
        serverPort: null,
        serverUser: null,
        serverPass: null,
        agentAdd: null,
        agentPort: null,
        threadNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentCode: [
          { required: true, message: "代理记编码不能为空", trigger: "blur" }
        ],
        agentName: [
          { required: true, message: "代理机名称不能为空", trigger: "blur" }
        ],
        serverType: [
          { required: true, message: "服务器类型不能为空", trigger: "change" }
        ],
        serverIp: [
          { required: true, message: "服务器地址不能为空", trigger: "blur" }
        ],
        serverPort: [
          { required: true, message: "服务器端口不能为空", trigger: "blur" }
        ],
        agentAdd: [
          { required: true, message: "客户端路径不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.serverTypeOptions = response.data;
    });
  },
  methods: {
    /** 查询代理机管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listAgent(this.queryParams).then(response => {
        this.agentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 服务器类型字典翻译
    serverTypeFormat(row, column) {
      return this.selectDictLabel(this.serverTypeOptions, row.serverType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        agentId: null,
        agentCode: null,
        agentName: null,
        serverType: null,
        serverIp: null,
        serverPort: null,
        serverUser: null,
        serverPass: null,
        agentAdd: null,
        agentPort: null,
        threadNum: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.agentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加代理机管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const agentId = row.agentId || this.ids
      getAgent(agentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改代理机管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.agentId != null) {
            updateAgent(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAgent(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const agentIds = row.agentId || this.ids;
      this.$confirm('是否确认删除代理机管理编号为"' + agentIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delAgent(agentIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有代理机管理数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportAgent(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
