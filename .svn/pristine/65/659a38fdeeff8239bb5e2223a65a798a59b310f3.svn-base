import request from '@/utils/request'

// 查询数据补录日志列表
export function listApiTemplateLog(query) {
  return request({
    url: '/api/apiTemplateLog/list',
    method: 'get',
    params: query
  })
}

// 查询数据补录日志详细
// export function getApiTemplateLog(importOperator) {
//   return request({
//     url: '/api/apiTemplateLog/' + importOperator,
//     method: 'get'
//   })
// }

// 新增数据补录日志
export function addApiTemplateLog(data) {
  return request({
    url: '/api/apiTemplateLog',
    method: 'post',
    data: data
  })
}

export function uploadFileTemp(data) {
  return request({
    url: '/api/apiTemplateMapping/uploadFileTemp',
    method: 'get',
    params: data
  })
}

// 修改数据补录日志
// export function updateApiTemplateLog(data) {
//   return request({
//     url: '/api/apiTemplateLog',
//     method: 'put',
//     data: data
//   })
// }

// 删除数据补录日志
export function delApiTemplateLog(importOperator) {
  return request({
    url: '/api/apiTemplateLog/' + importOperator,
    method: 'delete'
  })
}

// 导出数据补录日志
export function exportApiTemplateLog(query) {
  return request({
    url: '/api/apiTemplateLog/export',
    method: 'get',
    params: query
  })
}

export function templateIdToTable(query) {
  return request({
    url: `/api/apiTemplateMapping/templateIdToTable/${query}`,
    method: 'get'
  })
}

export function getTableMapping(query) {
  return request({
    url: `/api/apiTemplateMapping/getTableMapping/${query}`,
    method: 'get'
  })
}

export function taskManage_tempData(query) {
  return request({
    url: `/api/apiTemplateMapping/taskManage_tempData/${query}`,
    method: 'get'
  })
}

export function executeTable(query) {
  return request({
    url: `/api/apiTemplateMapping/executeTable/${query}`,
    method: 'get'
  })
}