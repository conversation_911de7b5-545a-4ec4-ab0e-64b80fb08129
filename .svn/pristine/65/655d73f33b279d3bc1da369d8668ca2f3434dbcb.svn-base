<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.task.mapper.EtlTaskRelationMapper">
    
    <resultMap type="EtlTaskRelation" id="EtlTaskRelationResult">
        <result property="taskRelationId"    column="task_relation_id"    />
        <result property="preTaskId"    column="pre_task_id"    />
        <result property="posTaskId"    column="pos_task_id"    />
        <result property="preTaskName"    column="pre_task_name"    />
        <result property="posTaskName"    column="pos_task_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="preTaskInstanceId"    column="pre_task_instance_id"    />
        <result property="posTaskInstanceId"    column="pos_task_instance_id"    />
        <result property="preTaskInstanceStatus"    column="pre_task_instance_status"    />
        <result property="posTaskInstanceStatus"    column="pos_task_instance_status"    />  
        <result property="batchId"    column="batch_id"    />        
    </resultMap>

    <sql id="selectEtlTaskRelationVo">
        select t.task_relation_id, t.pre_task_id, t.pos_task_id, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time 
        ,pre.task_name as pre_task_name,pos.task_name as pos_task_name
        from etl_task_relation t 
        LEFT JOIN etl_task pre ON t.pre_task_id=pre.task_id
        LEFT JOIN etl_task pos ON t.pos_task_id=pos.task_id
    </sql>

    <select id="selectEtlTaskRelationList" parameterType="EtlTaskRelation" resultMap="EtlTaskRelationResult">
        <include refid="selectEtlTaskRelationVo"/>
        <where>  
            <if test="preTaskId != null "> and t.pre_task_id = #{preTaskId}</if>
            <if test="posTaskId != null "> and t.pos_task_id = #{posTaskId}</if>
            <if test="preTaskName != null and preTaskName != '' "> and pre.task_name like concat('%', #{preTaskName}, '%')</if>
            <if test="posTaskName != null and posTaskName != '' "> and pos.task_name like concat('%', #{posTaskName}, '%')</if>
        </where>
        order by t.task_relation_id desc
    </select>
    <select id="exportEtlTaskRelationList" parameterType="EtlTask" resultMap="EtlTaskRelationResult">
        <include refid="selectEtlTaskRelationVo"/>
        <where>
            pre.task_name in(
            select task_name from etl_task task left join etl_task_class c on task.task_class_id=c.task_class_id
            where 1=1
            <if test="taskCode != null  and taskCode != ''"> and task.task_code like concat('%', #{taskCode}, '%')</if>
            <if test="taskName != null  and taskName != ''"> and task.task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskClassId != null and taskClassId != '0' "> and (c.ancestors like concat('%,', #{taskClassId}, '%') or task.task_class_id = #{taskClassId}) </if>
            <if test="taskGroupId != null "> and task.task_group_id = #{taskGroupId}</if>
            <if test="systemId != null "> and task.system_id = #{systemId}</if>
            <if test="type != null  and type != ''"> and task.type = #{type}</if>
            <if test="flag != null  and flag != ''"> and task.flag = #{flag}</if>
            <if test="level != null  and level != ''"> and task.level = #{level}</if>
            <if test="status != null  and status != ''"> and task.status = #{status}</if>
            <if test="agentId != null "> and task.agent_id = #{agentId}</if>
            )
            or pos.task_name in
            (
            select task_name from etl_task task1  left join etl_task_class c on task1.task_class_id=c.task_class_id
            where 1=1
            <if test="taskCode != null  and taskCode != ''"> and task1.task_code like concat('%', #{taskCode}, '%')</if>
            <if test="taskName != null  and taskName != ''"> and task1.task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskClassId != null and taskClassId != '0' "> and (c.ancestors like concat('%,', #{taskClassId}, '%') or task1.task_class_id = #{taskClassId}) </if>
            <if test="taskGroupId != null "> and task1.task_group_id = #{taskGroupId}</if>
            <if test="systemId != null "> and task1.system_id = #{systemId}</if>
            <if test="type != null  and type != ''"> and task1.type = #{type}</if>
            <if test="flag != null  and flag != ''"> and task1.flag = #{flag}</if>
            <if test="level != null  and level != ''"> and task1.level = #{level}</if>
            <if test="status != null  and status != ''"> and task1.status = #{status}</if>
            <if test="agentId != null "> and task1.agent_id = #{agentId}</if>
            )
        </where>
        order by t.task_relation_id desc
    </select>
    <select id="selectEtlTaskInstanceRelationList" parameterType="EtlTaskRelation" resultMap="EtlTaskRelationResult">
         select t.task_relation_id, t.pre_task_id, t.pos_task_id, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time 
        ,pre.task_name as pre_task_name,pos.task_name as pos_task_name,prei.task_instance_id as pre_task_instance_id,posi.task_instance_id as pos_task_instance_id
        ,prei.status as pre_task_instance_status,posi.status as pos_task_instance_status,prei.batch_id
        from etl_task_relation t 
        LEFT JOIN etl_task pre ON t.pre_task_id=pre.task_id
        LEFT JOIN etl_task_instance prei ON t.pre_task_id=prei.task_id
        LEFT JOIN etl_task pos ON t.pos_task_id=pos.task_id
        LEFT JOIN etl_task_instance posi ON t.pos_task_id=posi.task_id
        <where>  
            <if test="preTaskId != null "> and t.pre_task_id = #{preTaskId}</if>
            <if test="posTaskId != null "> and t.pos_task_id = #{posTaskId}</if>
            <if test="preTaskName != null and preTaskName != '' "> and pre.task_name like concat('%', #{preTaskName}, '%')</if>
            <if test="posTaskName != null and posTaskName != '' "> and pos.task_name like concat('%', #{posTaskName}, '%')</if>
            <if test="preTaskInstanceId != null "> and prei.task_instance_id = #{preTaskInstanceId}</if>
            <if test="posTaskInstanceId != null "> and posi.task_instance_id = #{posTaskInstanceId}</if>
            <if test="batchId != null "> and (prei.batch_id = #{batchId} or posi.batch_id = #{batchId})</if>
        </where>
        order by t.task_relation_id desc
    </select>
 
     <select id="selectEtlTaskInstanceRelationListByPre" parameterType="EtlTaskRelation" resultMap="EtlTaskRelationResult">
        select t.task_relation_id, t.pre_task_id, t.pos_task_id, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time 
        ,pos.task_name as pos_task_name,posi.task_instance_id as pos_task_instance_id
        ,posi.status as pos_task_instance_status,posi.batch_id
        from etl_task_relation t 
        LEFT JOIN etl_task pos ON t.pos_task_id=pos.task_id
        LEFT JOIN etl_task_instance posi ON t.pos_task_id=posi.task_id
        <where>  
            <if test="preTaskId != null "> and t.pre_task_id = #{preTaskId}</if>
            <if test="posTaskName != null and posTaskName != '' "> and pos.task_name like concat('%', #{posTaskName}, '%')</if>
            <if test="posTaskInstanceId != null "> and posi.task_instance_id = #{posTaskInstanceId}</if>
            <if test="batchId != null "> and posi.batch_id = #{batchId}</if>
        </where>
        order by t.task_relation_id desc
    </select>
    
        <select id="selectEtlTaskInstanceRelationListByPos" parameterType="EtlTaskRelation" resultMap="EtlTaskRelationResult">
         select t.task_relation_id, t.pre_task_id, t.pos_task_id, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time 
        ,pre.task_name as pre_task_name,prei.task_instance_id as pre_task_instance_id
        ,prei.status as pre_task_instance_status,prei.batch_id
        from etl_task_relation t 
        LEFT JOIN etl_task pre ON t.pre_task_id=pre.task_id
        LEFT JOIN etl_task_instance prei ON t.pre_task_id=prei.task_id
        <where>  
            <if test="posTaskId != null "> and t.pos_task_id = #{posTaskId}</if>
            <if test="preTaskName != null and preTaskName != '' "> and pre.task_name like concat('%', #{preTaskName}, '%')</if>
            <if test="preTaskInstanceId != null "> and prei.task_instance_id = #{preTaskInstanceId}</if>
            <if test="batchId != null "> and  prei.batch_id = #{batchId}</if>
        </where>
        order by t.task_relation_id desc
    </select>   
    <select id="selectEtlTaskRelationById" parameterType="Long" resultMap="EtlTaskRelationResult">
        <include refid="selectEtlTaskRelationVo"/>
        where t.task_relation_id = #{taskRelationId}
    </select>
        
    <insert id="insertEtlTaskRelation" parameterType="EtlTaskRelation" useGeneratedKeys="true" keyProperty="taskRelationId">
        insert into etl_task_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="preTaskId != null">pre_task_id,</if>
            <if test="posTaskId != null">pos_task_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="preTaskId != null">#{preTaskId},</if>
            <if test="posTaskId != null">#{posTaskId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEtlTaskRelation" parameterType="EtlTaskRelation">
        update etl_task_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="preTaskId != null">pre_task_id = #{preTaskId},</if>
            <if test="posTaskId != null">pos_task_id = #{posTaskId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_relation_id = #{taskRelationId}
    </update>

    <delete id="deleteEtlTaskRelationById" parameterType="Long">
        delete from etl_task_relation where task_relation_id = #{taskRelationId}
    </delete>

    <delete id="deleteEtlTaskRelationByIds" parameterType="String">
        delete from etl_task_relation where task_relation_id in 
        <foreach item="taskRelationId" collection="array" open="(" separator="," close=")">
            #{taskRelationId}
        </foreach>
    </delete>
    
    <delete id="delTaskRelationByNode" parameterType="EtlTaskRelation">
        delete from etl_task_relation where pre_task_id = #{preTaskId}  and pos_task_id=#{posTaskId}
    </delete>
    
    <delete id="delTaskRelationAll">
        delete from etl_task_relation
    </delete>
    
    <update id="resetTaskCheckAll">
        UPDATE etl_task SET loop_check='N'
    </update>    
</mapper>