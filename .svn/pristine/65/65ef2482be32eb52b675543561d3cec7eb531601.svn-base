<template>
  <div class="app-container">
  <el-row class="el-row-inline">
  <el-col :span="4" v-for="(item, index) in datasourseTypeList" :key="'datasourseType'+index" >
    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
      <img src="@/assets/images/dbase.png" class="image" v-if="item.datasourceTypeGroup === 'rdb'">
      <img src="@/assets/images/nosql.png" class="image" v-if="item.datasourceTypeGroup === 'ndb'">
      <img src="@/assets/images/file.png" class="image" v-if="item.datasourceTypeGroup === 'fdb'">
      <img src="@/assets/images/search.png" class="image" v-if="item.datasourceTypeGroup === 'edb'">
      <div style="padding: 14px;">
        <span  style="color: #228B22;">{{item.datasourceTypeName}}</span>
        <div class="bottom clearfix">
          <time class="time" >
          <i class="el-icon-eleme"></i>{{item.datasourceTypeCode}}
          </time>
          <el-button type="text" class="button" @click="handleDelete(item)"><i class="el-icon-delete"></i></el-button>
          <el-button type="text" class="button" @click="handleUpdate(item)"><i class="el-icon-edit"></i></el-button>
        </div>
      </div>
    </el-card>
  </el-col>
  <el-col :span="4" :key="'system'">
	  <el-card :body-style="{ padding: '0px' }" class="card" shadow="hover">
	      <img src="@/assets/images/add.png" class="image" @click="handleAdd">
	      <div style="padding: 14px;">
	        <span @click="handleAdd" style="color: #A9A9A9;">点击扩展</span>
	        <div class="bottom clearfix">
	          <time class="time"> &nbsp;</time>
	          <el-button type="text" class="button" @click="handleAdd"><i class="el-icon-circle-plus-outline"></i></el-button>
	        </div>
	      </div>
	    </el-card>
   </el-col>
</el-row>

    <!-- 添加或修改分组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="编码" prop="datasourceTypeCode">
          <el-input v-model="form.datasourceTypeCode" placeholder="请输入编码" />
        </el-form-item>
        <el-form-item label="名称" prop="datasourceTypeName">
          <el-input v-model="form.datasourceTypeName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="分组" prop="datasourceTypeGroup">
          <el-select v-model="form.datasourceTypeGroup" placeholder="请选择分组">
            <el-option
              v-for="dict in datasourceTypeGroupOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="驱动" prop="datasourceTypeDrive">
          <el-input v-model="form.datasourceTypeDrive" placeholder="请输入驱动" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }

  .card{
    margin:15px;
    min-width: 90%;
    height: 90%;
  }

  .el-row-inline {
    display: flex;
    flex-wrap: wrap;
  }
</style>
<script>
import { listDatasourseType, getDatasourseType, delDatasourseType, addDatasourseType, updateDatasourseType, exportDatasourseType } from "@/api/basic/datasourseType";

export default {
  name: "DatasourseType",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分组表格数据
      datasourseTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 分组字典
      datasourceTypeGroupOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
        datasourceTypeCode: null,
        datasourceTypeName: null,
        datasourceTypeGroup: null,
        datasourceTypeDrive: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("datasource_type_group").then(response => {
      this.datasourceTypeGroupOptions = response.data;
    });
  },
  methods: {
    /** 查询数据库分组列表 */
    getList() {
      this.loading = true;
      listDatasourseType(this.queryParams).then(response => {
        this.datasourseTypeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 数据库分组字典翻译
    datasourceTypeGroupFormat(row, column) {
      return this.selectDictLabel(this.datasourceTypeGroupOptions, row.datasourceTypeGroup);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        datasourceTypeId: null,
        datasourceTypeCode: null,
        datasourceTypeName: null,
        datasourceTypeGroup: null,
        datasourceTypeDrive: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.datasourceTypeId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据库分组";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const datasourceTypeId = row.datasourceTypeId || this.ids
      getDatasourseType(datasourceTypeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据库分组";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.datasourceTypeId != null) {
            updateDatasourseType(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDatasourseType(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const datasourceTypeIds = row.datasourceTypeId || this.ids;
      this.$confirm('是否确认删除数据库分组编号为"' + datasourceTypeIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDatasourseType(datasourceTypeIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有数据库分组数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDatasourseType(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
