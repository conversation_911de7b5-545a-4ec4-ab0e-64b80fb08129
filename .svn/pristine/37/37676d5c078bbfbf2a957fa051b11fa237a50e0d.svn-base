package com.dqms.mdm.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.mdm.domain.MdmDataEntityPropHis;
import com.dqms.mdm.mapper.MdmDataEntityPropHisMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.mdm.mapper.MdmDataEntityHisMapper;
import com.dqms.mdm.domain.MdmDataEntityHis;
import com.dqms.mdm.service.IMdmDataEntityHisService;

/**
 * 数据实体历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-31
 */
@Service
public class MdmDataEntityHisServiceImpl implements IMdmDataEntityHisService
{
    @Autowired
    private MdmDataEntityHisMapper mdmDataEntityHisMapper;

    @Autowired
    private MdmDataEntityPropHisMapper mdmDataEntityPropHisMapper;
    /**
     * 查询数据实体历史
     *
     * @param entityId 数据实体历史ID
     * @return 数据实体历史
     */
    @Override
    public MdmDataEntityHis selectMdmDataEntityHisById(Long entityId)
    {
        return mdmDataEntityHisMapper.selectMdmDataEntityHisById(entityId);
    }

    /**
     * 查询数据实体历史列表
     *
     * @param mdmDataEntityHis 数据实体历史
     * @return 数据实体历史
     */
    @Override
    @DataScope(systemAlias = "r")
    public List<MdmDataEntityHis> selectMdmDataEntityHisList(MdmDataEntityHis mdmDataEntityHis)
    {
        List<MdmDataEntityHis> mdmDataEntityHisList = mdmDataEntityHisMapper.selectMdmDataEntityHisList(mdmDataEntityHis);
//        for (MdmDataEntityHis dataEntityHis : mdmDataEntityHisList) {
//            Boolean flag = true;
//            List<MdmDataEntityPropHis>  mdmDataEntityPropHisList= dataEntityHis.getMdmDataEntityPropHis();
//            for (MdmDataEntityPropHis mdmDataEntityPropHis : mdmDataEntityPropHisList) {
//                if(StringUtils.isNotEmpty(mdmDataEntityPropHis.getModifyFields())){
//                    flag = false;
//                }
//            }
//            dataEntityHis.setPorpUpdate(flag);
//        }
        return mdmDataEntityHisList;
    }

    /**
     * 新增数据实体历史
     *
     * @param mdmDataEntityHis 数据实体历史
     * @return 结果
     */
    @Override
    public int insertMdmDataEntityHis(MdmDataEntityHis mdmDataEntityHis)
    {
        mdmDataEntityHis.setCreateTime(DateUtils.getNowDate());
        return mdmDataEntityHisMapper.insertMdmDataEntityHis(mdmDataEntityHis);
    }

    /**
     * 修改数据实体历史
     *
     * @param mdmDataEntityHis 数据实体历史
     * @return 结果
     */
    @Override
    public int updateMdmDataEntityHis(MdmDataEntityHis mdmDataEntityHis)
    {
        mdmDataEntityHis.setUpdateTime(DateUtils.getNowDate());
        return mdmDataEntityHisMapper.updateMdmDataEntityHis(mdmDataEntityHis);
    }

    /**
     * 批量删除数据实体历史
     *
     * @param entityIds 需要删除的数据实体历史ID
     * @return 结果
     */
    @Override
    public int deleteMdmDataEntityHisByIds(Long[] entityIds)
    {
        return mdmDataEntityHisMapper.deleteMdmDataEntityHisByIds(entityIds);
    }

    /**
     * 删除数据实体历史信息
     *
     * @param entityId 数据实体历史ID
     * @return 结果
     */
    @Override
    public int deleteMdmDataEntityHisById(Long entityId)
    {
        return mdmDataEntityHisMapper.deleteMdmDataEntityHisById(entityId);
    }

    @Override
    public Map getPreVesionData(MdmDataEntityHis mdmDataEntityHis) {
        if(mdmDataEntityHis.getVersionNo().equals("1")){
            return null;
        }else{
            String versonNo = String.valueOf(Integer.parseInt(mdmDataEntityHis.getVersionNo())-1);
            MdmDataEntityHis MdmDataEntityHisPre = mdmDataEntityHisMapper.selectAllByRegistryIdAndVersionNo(mdmDataEntityHis.getRegistryId(),versonNo);
            Map map = new HashMap();
            if(MdmDataEntityHisPre!=null){
                List<MdmDataEntityPropHis> mdmDataEntityPropHis = mdmDataEntityPropHisMapper.selectAllByEntityId(MdmDataEntityHisPre.getEntityId());
                map.put("mdmDataEntityPropHisPre",mdmDataEntityPropHis);
            }
            map.put("mdmDataEntityHisPre",MdmDataEntityHisPre);
            return map;
        }
    }
}
