<template>
  <div class="container-fluid" style="margin-top:0px;width:100%;position:relative;">
    <div
      id="scalingToolBar"
      :class="{right:right}"
      style="padding-top:10px;cursor:pointer;z-index: 99;width:791px;"
    >
      <div class="logo" v-show="!right">
        <img src="/favicon.ico">
        <span style="font-size:20px;font-weight:bold;margin-left:20px;">血缘引擎</span>
      </div>
      <el-select
        v-model="queryParams.srcEntityId"
        @change="srcChanged"
        placeholder="请选择源表"
        size="small"
        style="width:350px;"
        filterable
        clearable
        remote
        reserve-keyword
        :loading="loading"
        :remote-method="getMdmEntity"
      >
        <el-option
          v-for="item in srcEntityData"
          :key="item.entityId"
          :label="item.tableName"
          :value="item.entityId"
        >
        <span style="float: left">{{ item.tableSchema}}.{{ item.tableName }}</span>
      	<span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="queryParams.srcEntityPropId"
        placeholder="请选择源表字段"
        clearable
        size="small"
        style="width:350px;margin-left:10px;margin-right:10px;"
        filterable
      >
        <el-option
          v-for="item in srcEntityPropData"
          :key="item.propId"
          :label="item.propName"
          :value="item.propId"
        >
        <span style="float: left">{{ item.propName }}</span>
      	<span style="float: right; color: #8492a6; font-size: 13px">{{ item.propComment}}</span>
        </el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getList2()"
        ><i class="el-icon-search"></i>查询</el-button
      >
    </div>
    <div id="containerDiv">
      <div id="container" style="position: relative;"></div>
    </div>
  </div>
</template>

<script>
import {
  listMdmDataEntityShip,
  getMdmDataEntityShip,
  delMdmDataEntityShip,
  addMdmDataEntityShip,
  updateMdmDataEntityShip,
  exportMdmDataEntityShip,
  findAllEntity,
  findAllEntityProp,
  findShipList
} from "@/api/mdm/mdmDataEntityShip";
import { listSystem } from "@/api/basic/datasource";
import G6 from "@antv/g6";
export default {
  name: "mdmDataMap",
  components: {},
  data() {
    return {
      right:false,
      graphA: undefined,
      mdmDataEntityShipList: [],
      nodes: [],
      edges: [],
      g6graph: undefined,
      queryParams: {
        srcEntityIds: [],
        srcEntityPropIds: [],
        srcEntityPropId:'',
        entityId: ""
      },
      srcEntityData: [],
      tarEntityData: [],
      srcEntityPropData: [],
      tarEntityPropData: []
    };
  },
  created() {
    this.getMdmEntity();
  },
  methods: {
	    getMdmEntity(query) {
	        if (query !== "") {
	            this.loading = true;
	            setTimeout(() => {
	              this.loading = false;
	              let mdmDataEntity = { pageNum: 1,pageSize: 20,tableName: query };
	              findAllEntity(mdmDataEntity).then(response => {
	            	  this.srcEntityData = response.rows;
	      	        this.tarEntityData = response.rows;
	              });
	            }, 200);
	         } else {
	       	  this.srcEntityData = [];
	       	  this.tarEntityData = [];
	         }
	    },
    srcChanged(value) {
	    	this.queryParams.srcEntityPropId=null;
	    	if (value != null&&value != "") {
	    		let mdmDataEntityProp = { pageNum: 1,pageSize: 500,entityId:value};
		      findAllEntityProp(mdmDataEntityProp).then(response => {
		        this.srcEntityPropData = response.rows;
		      });
	    	}else{
	    		this.srcEntityPropData =[];
	    	}
    },
    tarChanged(value) {
      findAllEntityProp(value).then(response => {
        this.tarEntityPropData = response.data;
      });
    },
    getList2(){
      this.right = true
      this.getList()
    },
    getList() {
      if (this.g6graph) {
        this.g6graph.destroy();
      }
      if (
        !this.queryParams.srcEntityPropId  &&
        !this.queryParams.srcEntityId 
      ) {
        return;
      }
      this.nodes = [];
      this.edges = [];
      this.queryParams.srcEntityPropIds = [];
      if (!!this.queryParams.srcEntityPropId) {
        this.queryParams.srcEntityPropIds.push(
          this.queryParams.srcEntityPropId
        );
      }
      this.queryParams.srcEntityIds = [];
      if (this.queryParams.srcEntityId != null) {
        this.queryParams.srcEntityIds.push(this.queryParams.srcEntityId);
      }
      findShipList(this.queryParams).then(response => {
        for (var i = 0; i < response.data.length; i++) {
          this.nodes.push({
            id: response.data[i].entityId.toString(),
            label: response.data[i].tableName,
            attrs: response.data[i].mdmDataEntityProps
          });
          if (response.data[i].relations != null) {
            for (var j = 0; j < response.data[i].relations.length; j++) {
            	if(response.data[i].relations[j].srcEntityId!=null&&response.data[i].relations[j].tarEntityId!=null){
	              this.edges.push({
	                source: response.data[i].relations[j].srcEntityId.toString(),
	                target: response.data[i].relations[j].tarEntityId.toString(),
	                sourceKey: response.data[i].relations[j].srcPropName,
	                targetKey: response.data[i].relations[j].tarPropName,
	                label: response.data[i].relations[j].shipId
	              });
            	}
            }
          }
        }
        this.loading = false;
        this.init();
      });
    },
    init() {
      const { Util, registerBehavior, registerEdge, registerNode } = G6;

      const isInBBox = (point, bbox) => {
        const { x, y } = point;
        const { minX, minY, maxX, maxY } = bbox;

        return x < maxX && x > minX && y > minY && y < maxY;
      };

      const itemHeight = 30;
      registerBehavior("dice-er-scroll", {
        getDefaultCfg() {
          return {
            multiple: true
          };
        },
        getEvents() {
          return {
            itemHeight: 50,
            wheel: "scorll",
            click: "click",
            "node:mousemove": "move"
          };
        },
        scorll(e) {
          e.preventDefault();
          const { graph } = this;
          const nodes = graph.getNodes().filter(n => {
            const bbox = n.getBBox();

            return isInBBox(graph.getPointByClient(e.clientX, e.clientY), bbox);
          });

          const x = e.deltaX || e.movementX;
          let y = e.deltaY || e.movementY;
          if (!y && navigator.userAgent.indexOf("Firefox") > -1)
            y = (-e.wheelDelta * 125) / 3;

          if (nodes) {
            nodes.forEach(node => {
              const model = node.getModel();
              if (model.attrs.length < 9) {
                return;
              }
              const idx = model.startIndex || 0;
              let startX = model.startX || 0.5;
              let startIndex = idx + y * 0.02;
              startX -= x;
              if (startIndex < 0) {
                startIndex = 0;
              }
              if (startX > 0) {
                startX = 0;
              }
              if (startIndex > model.attrs.length - 1) {
                startIndex = model.attrs.length - 1;
              }
              graph.update(node, {
                startIndex,
                startX
              });
            });
          }
        },
        click(e) {
          const { graph } = this;
          const { y } = e;
          const item = e.item;
          const shape = e.shape;
          if (!item) {
            return;
          }
          const model = item.getModel();

          if (shape.get("name") === "collapse") {
            graph.updateItem(item, {
              collapsed: true,
              size: [300, 50]
            });
            setTimeout(() => graph.layout(), 100);
          } else if (shape.get("name") === "expand") {
            graph.updateItem(item, {
              collapsed: false,
              size: [300, 500]
            });
            setTimeout(() => graph.layout(), 100);
          }
        },
        move(e) {
          const name = e.shape.get("name");
          const item = e.item;

          if (name && name.startsWith("item")) {
            this.graph.updateItem(item, {
              selectedIndex: Number(name.split("-")[1])
            });
          } else {
            this.graph.updateItem(item, {
              selectedIndex: NaN
            });
          }
        }
      });

      registerEdge("dice-er-edge", {
        draw(cfg, group) {
          const edge = group.cfg.item;
          const sourceNode = edge.getSource().getModel();
          const targetNode = edge.getTarget().getModel();

          const sourceIndex = sourceNode.attrs.findIndex(
            e => e.key === cfg.sourceKey
          );

          const sourceStartIndex = sourceNode.startIndex || 0;

          let sourceY = 15;

          if (!sourceNode.collapsed && sourceIndex > sourceStartIndex - 1) {
            sourceY = 30 + (sourceIndex - sourceStartIndex + 0.5) * 30;
            sourceY = Math.min(sourceY, 300);
          }

          const targetIndex = targetNode.attrs.findIndex(
            e => e.key === cfg.targetKey
          );

          const targetStartIndex = targetNode.startIndex || 0;

          let targetY = 15;

          if (!targetNode.collapsed && targetIndex > targetStartIndex - 1) {
            targetY = (targetIndex - targetStartIndex + 0.5) * 30 + 30;
            targetY = Math.min(targetY, 300);
          }
          const startPoint = {
            ...cfg.startPoint
          };
          const endPoint = {
            ...cfg.endPoint
          };

          startPoint.y = startPoint.y + sourceY;
          endPoint.y = endPoint.y + targetY;

          let shape;
          if (sourceNode.id !== targetNode.id) {
            shape = group.addShape("path", {
              attrs: {
                stroke: "#5B8FF9",
                path: [
                  ["M", startPoint.x, startPoint.y],
                  [
                    "C",
                    endPoint.x / 3 + (2 / 3) * startPoint.x,
                    startPoint.y,
                    endPoint.x / 3 + (2 / 3) * startPoint.x,
                    endPoint.y,
                    endPoint.x,
                    endPoint.y
                  ]
                ],
                endArrow: true
              },
              name: "path-shape"
            });
          } else if (!sourceNode.collapsed) {
            let gap = Math.abs((startPoint.y - endPoint.y) / 3);
            if (startPoint["index"] === 1) {
              gap = -gap;
            }
            shape = group.addShape("path", {
              attrs: {
                stroke: "#5B8FF9",
                path: [
                  ["M", startPoint.x, startPoint.y],
                  [
                    "C",
                    startPoint.x - gap,
                    startPoint.y,
                    startPoint.x - gap,
                    endPoint.y,
                    startPoint.x,
                    endPoint.y
                  ]
                ],
                endArrow: true
              },
              name: "path-shape"
            });
          }

          return shape;
        },
        afterDraw(cfg, group) {
          const labelCfg = cfg.labelCfg || {};
          const edge = group.cfg.item;
          const sourceNode = edge.getSource().getModel();
          const targetNode = edge.getTarget().getModel();
          if (sourceNode.collapsed && targetNode.collapsed) {
            return;
          }
          const path = group.find(
            element => element.get("name") === "path-shape"
          );

          const labelStyle = Util.getLabelPosition(path, 0.5, 0, 0, true);
          const label = group.addShape("text", {
            attrs: {
              ...labelStyle,
              text: cfg.label || "",
              fill: "#000",
              textAlign: "center",
              stroke: "#fff",
              lineWidth: 1
            }
          });
          label.rotateAtStart(labelStyle.rotate);
        }
      });

      registerNode("dice-er-box", {
        draw(cfg, group) {
          const width = 250;
          const height = 316;
          const itemCount = 10;
          const boxStyle = {
            stroke: "#096DD9",
            radius: 4
          };

          const {
            attrs = [],
            startIndex = 0,
            selectedIndex,
            collapsed,
            icon
          } = cfg;
          const list = attrs;
          const afterList = list.slice(
            Math.floor(startIndex),
            Math.floor(startIndex + itemCount - 1)
          );
          const offsetY = (0.5 - (startIndex % 1)) * itemHeight + 30;

          group.addShape("rect", {
            attrs: {
              fill: boxStyle.stroke,
              height: 30,
              width,
              radius: [boxStyle.radius, boxStyle.radius, 0, 0]
            },
            draggable: true
          });

          let fontLeft = 12;

          if (icon && icon.show !== false) {
            group.addShape("image", {
              attrs: {
                x: 8,
                y: 8,
                height: 16,
                width: 16,
                ...icon
              }
            });
            fontLeft += 18;
          }

          group.addShape("text", {
            attrs: {
              y: 22,
              x: fontLeft,
              fill: "#fff",
              text: cfg.label,
              fontSize: 12,
              fontWeight: 500
            }
          });

          group.addShape("rect", {
            attrs: {
              x: 0,
              y: collapsed ? 30 : 300,
              height: 15,
              width,
              fill: "#eee",
              radius: [0, 0, boxStyle.radius, boxStyle.radius],
              cursor: "pointer"
            },
            name: collapsed ? "expand" : "collapse"
          });

          group.addShape("text", {
            attrs: {
              x: width / 2 - 6,
              y: (collapsed ? 30 : 300) + 12,
              text: collapsed ? "+" : "-",
              width,
              fill: "#000",
              radius: [0, 0, boxStyle.radius, boxStyle.radius],
              cursor: "pointer"
            },
            name: collapsed ? "expand" : "collapse"
          });

          const keyshape = group.addShape("rect", {
            attrs: {
              x: 0,
              y: 0,
              width,
              height: collapsed ? 45 : height,
              ...boxStyle
            },
            draggable: true
          });

          if (collapsed) {
            return keyshape;
          }

          const listContainer = group.addGroup({});
          listContainer.setClip({
            type: "rect",
            attrs: {
              x: -8,
              y: 30,
              width: width + 16,
              height: 300 - 30
            }
          });
          listContainer.addShape({
            type: "rect",
            attrs: {
              x: 1,
              y: 30,
              width: width - 2,
              height: 300 - 30,
              fill: "#fff"
            },
            draggable: true
          });

          if (list.length > itemCount) {
            const barStyle = {
              width: 4,
              padding: 0,
              boxStyle: {
                stroke: "#00000022"
              },
              innerStyle: {
                fill: "#00000022"
              }
            };

            listContainer.addShape("rect", {
              attrs: {
                y: 30,
                x: width - barStyle.padding - barStyle.width,
                width: barStyle.width,
                height: height - 30,
                ...barStyle.boxStyle
              }
            });

            const indexHeight =
              afterList.length > itemCount
                ? (afterList.length / list.length) * height
                : 10;

            listContainer.addShape("rect", {
              attrs: {
                y:
                  30 +
                  barStyle.padding +
                  (startIndex / list.length) * (height - 30),
                x: width - barStyle.padding - barStyle.width,
                width: barStyle.width,
                height: Math.min(height, indexHeight),
                ...barStyle.innerStyle
              }
            });
          }
          if (afterList) {
            afterList.forEach((e, i) => {
              const isSelected =
                Math.floor(startIndex) + i === Number(selectedIndex);
              let { key = "", type } = e;
              if (type) {
                key += " - " + type;
              }
              const label = key.length > 26 ? key.slice(0, 24) + "..." : key;

              listContainer.addShape("rect", {
                attrs: {
                  x: 1,
                  y: i * itemHeight - itemHeight / 2 + offsetY,
                  width: width - 4,
                  height: itemHeight,
                  radius: 2,
                  lineWidth: 1,
                  cursor: "pointer"
                },
                name: `item-${Math.floor(startIndex) + i}-content`,
                draggable: true
              });

              if (!cfg.hideDot) {
                listContainer.addShape("circle", {
                  attrs: {
                    x: 0,
                    y: i * itemHeight + offsetY,
                    r: 3,
                    stroke: boxStyle.stroke,
                    fill: "white",
                    radius: 2,
                    lineWidth: 1,
                    cursor: "pointer"
                  }
                });
                listContainer.addShape("circle", {
                  attrs: {
                    x: width,
                    y: i * itemHeight + offsetY,
                    r: 3,
                    stroke: boxStyle.stroke,
                    fill: "white",
                    radius: 2,
                    lineWidth: 1,
                    cursor: "pointer"
                  }
                });
              }

              listContainer.addShape("text", {
                attrs: {
                  x: 12,
                  y: i * itemHeight + offsetY + 6,
                  text: label,
                  fontSize: 12,
                  fill: "#000",
                  fontFamily:
                    "Avenir,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol",
                  full: e,
                  fontWeight: isSelected ? 500 : 100,
                  cursor: "pointer"
                },
                name: `item-${Math.floor(startIndex) + i}`
              });
            });
          }

          return keyshape;
        },
        getAnchorPoints() {
          return [
            [0, 0],
            [1, 0]
          ];
        }
      });

      const dataTransform = data => {
        const nodes = [];
        const edges = [];

        data.map(node => {
          nodes.push({
            ...node
          });
          if (node.attrs) {
            node.attrs.forEach(attr => {
              if (attr.relation) {
                attr.relation.forEach(relation => {
                  edges.push({
                    source: node.id,
                    target: relation.nodeId,
                    sourceKey: attr.key,
                    targetKey: relation.key,
                    label: relation.label
                  });
                });
              }
            });
          }
        });

        return {
          nodes,
          edges
        };
      };

      const container = document.getElementById("container");
      const minimap = new G6.Minimap({
        position: "absolute",
        "text-align": "right"
      });
      const width = container.scrollWidth;
      const height = document.body.clientHeight - 210;
      const graph = new G6.Graph({
        container: "container",
        width,
        height,
        plugins: [minimap],
        minZoom: 0.1,
        maxZoom: 1.5,
        defaultNode: {
          size: [300, 400],
          type: "dice-er-box",
          color: "#5B8FF9",
          style: {
            fill: "#9EC9FF",
            lineWidth: 3
          },
          labelCfg: {
            style: {
              fill: "black",
              fontSize: 20
            }
          }
        },
        defaultEdge: {
          type: "dice-er-edge",
          style: {
            stroke: "#e2e2e2",
            lineWidth: 4,
            endArrow: true
          }
        },
        modes: {
          default: ["dice-er-scroll", "drag-node", "drag-canvas"]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          align: "UL",
          controlPoints: true,
          nodesepFunc: () => 0.2,
          ranksepFunc: () => 0.5
        },
        animate: true
      });
      const data = {
        nodes: this.nodes,
        edges: this.edges
      };
      //graph.data(dataTransform(rawData));
      graph.data(data);
      graph.render();
      graph.get("container").style.backgroundSize = "auto 100%";
      graph.zoom(1, { x: width / 2, y: height / 2 });
      this.g6graph = graph;
    }
  },
  destroy() {
    //注意，VUE此处必须清理，否则切换界面会越来越卡
    this.graphA.clear();
    this.graphA.destroy();
  },
  mounted() {
    this.getList();
  }
};
</script>

<style>
.entity-container.fact {
  border: 1px solid #ced4de;
  height: 248px;
  width: 214px;
}
.entity-container {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border-radius: 2px;
  background-color: #fff;
}
.entity-container .content.fact {
  background-color: #ced4de;
}
.entity-container .content {
  margin: 1px;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.entity-container .content .head {
  width: calc(100% - 12px);
  height: 38px;
  margin-left: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.entity-container .content .head .type {
  padding-right: 8px;
}
.anticon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.anticon svg {
  display: inline-block;
}
svg:not(:root) {
  overflow: hidden;
}
.entity-container .content .head .more {
  cursor: pointer;
}
.entity-container .content .body {
  width: calc(100% - 12px);
  height: calc(100% - 42px);
  margin-left: 6px;
  margin-bottom: 6px;
  background-color: #fff;
  overflow: auto;
  cursor: pointer;
}
.entity-container .content .body .body-item {
  width: 100%;
  height: 28px;
  font-size: 12px;
  color: #595959;
  border-bottom: 1px solid rgba(206, 212, 222, 0.2);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.entity-container .content .body .body-item .name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 6px;
}
.entity-container .content .body .body-item .name .fk,
.entity-container .content .body .body-item .name .pk {
  width: 12px;
  font-family: "HelveticaNeue-CondensedBold";
  color: #ffd666;
  margin-right: 6px;
}
.entity-container .content .body .body-item .type {
  color: #bfbfbf;
  font-size: 8px;
  margin-right: 8px;
}
.container-fluid{
  height:calc(100vh - 84px);
}
#scalingToolBar{
  transition:all 2s ease;
  position:absolute;
  right:25%;
  top:10px;
}
#scalingToolBar.right{
  right:0;
  top:0;
}
.logo{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom:15px;
  width:706px;
}
</style>
