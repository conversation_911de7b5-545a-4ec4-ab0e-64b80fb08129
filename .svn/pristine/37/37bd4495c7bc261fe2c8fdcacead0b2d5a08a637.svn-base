package com.dqms.task.service.impl;

import java.util.List;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.framework.web.service.TokenService;

import com.dqms.task.domain.SysSystemSubscribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.task.mapper.EtlTaskSubscribeMapper;
import com.dqms.task.domain.EtlTaskSubscribe;
import com.dqms.task.enums.EtlConstants;
import com.dqms.task.service.IEtlTaskSubscribeService;

/**
 * 任务结果订阅Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-02
 */
@Service
public class EtlTaskSubscribeServiceImpl implements IEtlTaskSubscribeService
{
    @Autowired
    private EtlTaskSubscribeMapper etlTaskSubscribeMapper;

    @Autowired
    private TokenService tokenService;
    
    /**
     * 查询任务结果订阅
     *
     * @param taskSubscribeId 任务结果订阅ID
     * @return 任务结果订阅
     */
    @Override
    public EtlTaskSubscribe selectEtlTaskSubscribeById(Long taskSubscribeId)
    {
        return etlTaskSubscribeMapper.selectEtlTaskSubscribeById(taskSubscribeId);
    }

    /**
     * 查询任务结果订阅列表
     *
     * @param etlTaskSubscribe 任务结果订阅
     * @return 任务结果订阅
     */
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTaskSubscribe> selectEtlTaskSubscribeList(EtlTaskSubscribe etlTaskSubscribe)
    {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		Long userId=null;
		if(etlTaskSubscribe.getUserId()==null) {
			userId=loginUser.getUser().getUserId();
		}else {
			userId=etlTaskSubscribe.getUserId();
		}
    	etlTaskSubscribe.setUserId(userId);
        return etlTaskSubscribeMapper.selectEtlTaskSubscribeList(etlTaskSubscribe);
    }

    /**
     * 新增任务结果订阅
     *
     * @param etlTaskSubscribe 任务结果订阅
     * @return 结果
     */
    @Override
    @Transactional
	@DataScope(systemAlias = "t")
    public int insertEtlTaskSubscribe(EtlTaskSubscribe etlTaskSubscribe)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	Long userId=null;
    	if(etlTaskSubscribe.getUserId()==null) {
    		userId=loginUser.getUser().getUserId();
    	}else {
    		userId=etlTaskSubscribe.getUserId();
    	}
    	etlTaskSubscribe.setUserId(userId);
    	if(etlTaskSubscribe.getTaskIds()!=null&&etlTaskSubscribe.getTaskIds().length>0) {
    		for(Long id : etlTaskSubscribe.getTaskIds()) {
				EtlTaskSubscribe ets = new EtlTaskSubscribe();
				BeanUtils.copyBeanProp(ets, etlTaskSubscribe);
				EtlTaskSubscribe subscribe = etlTaskSubscribeMapper.selectEtlTaskSubscribeByUserId(id,userId);
    	    	if(subscribe==null) {
					ets.setTaskId(id);
					ets.setCreateTime(DateUtils.getNowDate());
					ets.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
					ets.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        			etlTaskSubscribeMapper.insertEtlTaskSubscribe(ets);
    	    	}else {
					ets.setTaskSubscribeId(subscribe.getTaskSubscribeId());
					ets.setUpdateTime(DateUtils.getNowDate());
					ets.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
					ets.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	    		etlTaskSubscribeMapper.updateEtlTaskSubscribe(ets);
    	    	}
    		}
    	}else {
    		EtlTaskSubscribe s = new EtlTaskSubscribe();
    		s.setTaskName(etlTaskSubscribe.getTaskName());
    		s.setTaskClassId(etlTaskSubscribe.getTaskClassId());
    		s.setType(etlTaskSubscribe.getType());
			s.setUserId(etlTaskSubscribe.getUserId());
			s.setSuccessFlag(etlTaskSubscribe.getOldSuccessFlag());
			s.setErrorFlag(etlTaskSubscribe.getOldErrorFlag());
			s.setEmailFlag(etlTaskSubscribe.getOldEmailFlag());
			s.setSmsFlag(etlTaskSubscribe.getOldSmsFlag());
			s.setNoticeFlag(etlTaskSubscribe.getOldNoticeFlag());
			s.setParams(etlTaskSubscribe.getParams());
			List<EtlTaskSubscribe> list = etlTaskSubscribeMapper.selectEtlTaskSubscribeList(s);
    		if(list!=null&&list.size()>0) {
    			for(EtlTaskSubscribe s1 : list) {
					EtlTaskSubscribe ets = new EtlTaskSubscribe();
					BeanUtils.copyBeanProp(ets, etlTaskSubscribe);
					if(s1.getTaskSubscribeId()==null) {
						ets.setTaskId(s1.getTaskId());
						ets.setCreateTime(DateUtils.getNowDate());
						ets.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
						ets.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            			etlTaskSubscribeMapper.insertEtlTaskSubscribe(ets);
    				}else {
						ets.setTaskSubscribeId(s1.getTaskSubscribeId());
						ets.setUpdateTime(DateUtils.getNowDate());
						ets.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
						ets.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        	    		etlTaskSubscribeMapper.updateEtlTaskSubscribe(ets);
    				}
    			}
    		}
    	}
        
        return 1;
    }

    /**
     * 修改任务结果订阅
     *
     * @param etlTaskSubscribe 任务结果订阅
     * @return 结果
     */
    @Override
    public int updateEtlTaskSubscribe(EtlTaskSubscribe etlTaskSubscribe)
    {
        etlTaskSubscribe.setUpdateTime(DateUtils.getNowDate());
        return etlTaskSubscribeMapper.updateEtlTaskSubscribe(etlTaskSubscribe);
    }

    /**
     * 批量删除任务结果订阅
     *
     * @param taskSubscribeIds 需要删除的任务结果订阅ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskSubscribeByIds(Long[] taskSubscribeIds)
    {
        return etlTaskSubscribeMapper.deleteEtlTaskSubscribeByIds(taskSubscribeIds);
    }

    /**
     * 删除任务结果订阅信息
     *
     * @param taskSubscribeId 任务结果订阅ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskSubscribeById(Long taskSubscribeId)
    {
        return etlTaskSubscribeMapper.deleteEtlTaskSubscribeById(taskSubscribeId);
    }
    
    /**
     * 修改订阅状态
     * 
     * @param etlTaskSubscribe 任务结果订阅
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEtlTaskSubscribeStatus(EtlTaskSubscribe etlTaskSubscribe)
    {
    	EtlTaskSubscribe subscribe = null;
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	Long userId=null;
    	if(etlTaskSubscribe.getUserId()==null) {
    		userId=loginUser.getUser().getUserId();
    	}else {
    		userId=etlTaskSubscribe.getUserId();
    	}
    	subscribe = etlTaskSubscribeMapper.selectEtlTaskSubscribeByUserId(etlTaskSubscribe.getTaskId(),userId);
    	etlTaskSubscribe.setUserId(userId);
    	etlTaskSubscribe.setUpdateTime(DateUtils.getNowDate());
    	etlTaskSubscribe.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	etlTaskSubscribe.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        if(subscribe==null) {
        	etlTaskSubscribe.setCreateTime(DateUtils.getNowDate());
        	etlTaskSubscribe.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        	etlTaskSubscribe.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            if(etlTaskSubscribe.getType().equals("successFlag")) {
            	etlTaskSubscribe.setSuccessFlag(EtlConstants.YES);
            }else if(etlTaskSubscribe.getType().equals("errorFlag")) {
            	etlTaskSubscribe.setErrorFlag(EtlConstants.YES);
            }else if(etlTaskSubscribe.getType().equals("emailFlag")) {
            	etlTaskSubscribe.setEmailFlag(EtlConstants.YES);
            }else if(etlTaskSubscribe.getType().equals("smsFlag")) {
            	etlTaskSubscribe.setSmsFlag(EtlConstants.YES);
            }else if(etlTaskSubscribe.getType().equals("noticeFlag")) {
            	etlTaskSubscribe.setNoticeFlag(EtlConstants.YES);
            }
        	return etlTaskSubscribeMapper.insertEtlTaskSubscribe(etlTaskSubscribe);
        }else {
            if(etlTaskSubscribe.getType().equals("successFlag")) {
            	if(subscribe.getSuccessFlag().equals(EtlConstants.YES)) {
            		etlTaskSubscribe.setSuccessFlag(EtlConstants.NO);
            	}else {
            		etlTaskSubscribe.setSuccessFlag(EtlConstants.YES);
            	}
            }else if(etlTaskSubscribe.getType().equals("errorFlag")) {
            	if(subscribe.getErrorFlag().equals(EtlConstants.YES)) {
            		etlTaskSubscribe.setErrorFlag(EtlConstants.NO);
            	}else {
            		etlTaskSubscribe.setErrorFlag(EtlConstants.YES);
            	}
            }else if(etlTaskSubscribe.getType().equals("emailFlag")) {
            	if(subscribe.getEmailFlag().equals(EtlConstants.YES)) {
            		etlTaskSubscribe.setEmailFlag(EtlConstants.NO);
            	}else {
            		etlTaskSubscribe.setEmailFlag(EtlConstants.YES);
            	}
            }else if(etlTaskSubscribe.getType().equals("smsFlag")) {
            	if(subscribe.getSmsFlag().equals(EtlConstants.YES)) {
            		etlTaskSubscribe.setSmsFlag(EtlConstants.NO);
            	}else {
            		etlTaskSubscribe.setSmsFlag(EtlConstants.YES);
            	}
            }else if(etlTaskSubscribe.getType().equals("noticeFlag")) {
            	if(subscribe.getNoticeFlag().equals(EtlConstants.YES)) {
            		etlTaskSubscribe.setNoticeFlag(EtlConstants.NO);
            	}else {
            		etlTaskSubscribe.setNoticeFlag(EtlConstants.YES);
            	}
            }

        	etlTaskSubscribe.setTaskSubscribeId(subscribe.getTaskSubscribeId());
        	return this.updateEtlTaskSubscribe(etlTaskSubscribe);
        }
    }
}
