package com.dqms.task.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.task.mapper.EtlTaskScheduleHisMapper;
import com.dqms.common.annotation.DataScope;
import com.dqms.task.domain.EtlTaskScheduleHis;
import com.dqms.task.service.IEtlTaskScheduleHisService;

/**
 * 调度执行历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@Service
public class EtlTaskScheduleHisServiceImpl implements IEtlTaskScheduleHisService
{
    @Autowired
    private EtlTaskScheduleHisMapper etlTaskScheduleHisMapper;

    /**
     * 查询调度执行历史
     *
     * @param taskScheduleHisId 调度执行历史ID
     * @return 调度执行历史
     */
    @Override
    public EtlTaskScheduleHis selectEtlTaskScheduleHisById(Long taskScheduleHisId)
    {
        return etlTaskScheduleHisMapper.selectEtlTaskScheduleHisById(taskScheduleHisId);
    }

    /**
     * 查询调度执行历史列表
     *
     * @param etlTaskScheduleHis 调度执行历史
     * @return 调度执行历史
     */
    @Override
    @DataScope(systemAlias = "ta")
    public List<EtlTaskScheduleHis> selectEtlTaskScheduleHisList(EtlTaskScheduleHis etlTaskScheduleHis)
    {
        return etlTaskScheduleHisMapper.selectEtlTaskScheduleHisList(etlTaskScheduleHis);
    }

    /**
     * 新增调度执行历史
     *
     * @param etlTaskScheduleHis 调度执行历史
     * @return 结果
     */
    @Override
    public int insertEtlTaskScheduleHis(EtlTaskScheduleHis etlTaskScheduleHis)
    {
        return etlTaskScheduleHisMapper.insertEtlTaskScheduleHis(etlTaskScheduleHis);
    }

    /**
     * 修改调度执行历史
     *
     * @param etlTaskScheduleHis 调度执行历史
     * @return 结果
     */
    @Override
    public int updateEtlTaskScheduleHis(EtlTaskScheduleHis etlTaskScheduleHis)
    {
        return etlTaskScheduleHisMapper.updateEtlTaskScheduleHis(etlTaskScheduleHis);
    }

    /**
     * 批量删除调度执行历史
     *
     * @param taskScheduleHisIds 需要删除的调度执行历史ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskScheduleHisByIds(Long[] taskScheduleHisIds)
    {
        return etlTaskScheduleHisMapper.deleteEtlTaskScheduleHisByIds(taskScheduleHisIds);
    }

    /**
     * 删除调度执行历史信息
     *
     * @param taskScheduleHisId 调度执行历史ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskScheduleHisById(Long taskScheduleHisId)
    {
        return etlTaskScheduleHisMapper.deleteEtlTaskScheduleHisById(taskScheduleHisId);
    }


}
