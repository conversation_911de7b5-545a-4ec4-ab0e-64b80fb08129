package com.dqms.dam.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import com.dqms.common.annotation.Excel;

/**
 * 资产评价对象 dam_assets_evaluate
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
public class DamAssetsEvaluate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    private Long assetsEvaluateId;

    /** 资产ID */
    @Excel(name = "资产ID")
    private Long assetsId;

    /** 评分 */
    @Excel(name = "评分")
    private BigDecimal grade;

    /** 创建人ID */
    private Long createId;

    public void setAssetsEvaluateId(Long assetsEvaluateId)
    {
        this.assetsEvaluateId = assetsEvaluateId;
    }

    public Long getAssetsEvaluateId()
    {
        return assetsEvaluateId;
    }
    public void setAssetsId(Long assetsId)
    {
        this.assetsId = assetsId;
    }

    public Long getAssetsId()
    {
        return assetsId;
    }
    public void setGrade(BigDecimal grade)
    {
        this.grade = grade;
    }

    public BigDecimal getGrade()
    {
        return grade;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("assetsEvaluateId", getAssetsEvaluateId())
            .append("assetsId", getAssetsId())
            .append("grade", getGrade())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
