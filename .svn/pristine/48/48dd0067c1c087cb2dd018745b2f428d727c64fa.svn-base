import request from '@/utils/request'

// 查询元数据采集历史列表
export function listMdmCollectHis(query) {
  return request({
    url: '/mdm/mdmCollectHis/list',
    method: 'get',
    params: query
  })
}

// 查询元数据采集历史详细
export function getMdmCollectHis(regName) {
  return request({
    url: '/mdm/mdmCollectHis/' + regName,
    method: 'get'
  })
}

// 新增元数据采集历史
export function addMdmCollectHis(data) {
  return request({
    url: '/mdm/mdmCollectHis',
    method: 'post',
    data: data
  })
}

// 修改元数据采集历史
export function updateMdmCollectHis(data) {
  return request({
    url: '/mdm/mdmCollectHis',
    method: 'put',
    data: data
  })
}

// 删除元数据采集历史
export function delMdmCollectHis(regName) {
  return request({
    url: '/mdm/mdmCollectHis/' + regName,
    method: 'delete'
  })
}

// 导出元数据采集历史
export function exportMdmCollectHis(query) {
  return request({
    url: '/mdm/mdmCollectHis/export',
    method: 'get',
    params: query
  })
}