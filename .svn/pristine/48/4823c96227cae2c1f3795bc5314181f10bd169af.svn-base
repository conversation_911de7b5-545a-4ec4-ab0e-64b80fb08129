<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmCheckMainMapper">
    
    <resultMap type="DsmCheckMain" id="DsmCheckMainResult">
        <result property="checkMainId"    column="check_main_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="dsmNum"    column="dsm_num"    />
        <result property="runNum"    column="run_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="discernTypes"    column="discernTypes"    />
        <result property="systems"    column="systems"    />
        <result property="discernTypeLabels"    column="discernTypeLabels"    />
        <result property="systemLabels"    column="systemLabels"    />
    </resultMap>

    <sql id="selectDsmCheckMainVo">
        select check_main_id, start_time, end_time, status, dsm_num, create_by, create_id, create_time ,run_num ,discernTypes,systems,discernTypeLabels,systemLabels from dsm_check_main
    </sql>

    <select id="selectDsmCheckMainList" parameterType="DsmCheckMain" resultMap="DsmCheckMainResult">
        <include refid="selectDsmCheckMainVo"/>
        <where>  
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="dsmNum != null "> and dsm_num = #{dsmNum}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
        </where>
    </select>
    
    <select id="selectDsmCheckMainById" parameterType="Long" resultMap="DsmCheckMainResult">
        <include refid="selectDsmCheckMainVo"/>
        where check_main_id = #{checkMainId}
    </select>
        
    <insert id="insertDsmCheckMain" parameterType="DsmCheckMain" useGeneratedKeys="true" keyProperty="checkMainId">
        insert into dsm_check_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="dsmNum != null">dsm_num,</if>
            <if test="runNum != null">run_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="discernTypes != null">discernTypes,</if>
            <if test="systems != null">systems,</if>
            <if test="discernTypeLabels != null">discernTypeLabels,</if>
            <if test="systemLabels != null">systemLabels,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="dsmNum != null">#{dsmNum},</if>
            <if test="runNum != null">#{runNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="discernTypes != null">#{discernTypes},</if>
            <if test="systems != null">#{systems},</if>
            <if test="discernTypeLabels != null">#{discernTypeLabels},</if>
            <if test="systemLabels != null">#{systemLabels},</if>
         </trim>
    </insert>

    <update id="updateDsmCheckMain" parameterType="DsmCheckMain">
        update dsm_check_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dsmNum != null">dsm_num = #{dsmNum},</if>
            <if test="runNum != null">run_num = #{runNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where check_main_id = #{checkMainId}
    </update>

    <delete id="deleteDsmCheckMainById" parameterType="Long">
        delete from dsm_check_main where check_main_id = #{checkMainId}
    </delete>

    <delete id="deleteDsmCheckMainByIds">
        delete from dsm_check_main  where dsm_check_main in 
        <foreach item="checkMainId" collection="array" open="(" separator="," close=")">
            #{checkMainId}
        </foreach>
    </delete>
    
   <delete id="deleteDsmCheckMainAll">
        delete from dsm_check_main
    </delete>
</mapper>