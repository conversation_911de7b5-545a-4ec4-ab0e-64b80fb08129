package com.dqms.dsc.domain;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 分级分类对象 dsc_entity_prop_class
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscEntityPropClass extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分类ID */
    private Long entityPropClassId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String className;

    /** 全路径 */
    @Excel(name = "全路径")
    private String classNameFull;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    
    /** 父菜单ID */
    private Long parentId;

    /** 显示顺序 */
    private Integer orderNum;

    /** 祖级列表 */
    private String ancestors;
    /** 分类说明 */
    private String classRemit;
    /** 指引级别 */
    private String guideLevel;
    /** 企业级别 */
    private String enterpriseLevel;
    /** 敏感级别 */
    private String sensitivityLevel;
    
    /** 子部门 */
    private List<DscEntityPropClass> children = new ArrayList<DscEntityPropClass>();

    public void setEntityPropClassId(Long entityPropClassId)
    {
        this.entityPropClassId = entityPropClassId;
    }

    public Long getEntityPropClassId()
    {
        return entityPropClassId;
    }
    public void setClassName(String className)
    {
        this.className = className;
    }

    public String getClassName()
    {
        return className;
    }
    public void setClassNameFull(String classNameFull)
    {
        this.classNameFull = classNameFull;
    }

    public String getClassNameFull()
    {
        return classNameFull;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public String getAncestors() {
		return ancestors;
	}

	public void setAncestors(String ancestors) {
		this.ancestors = ancestors;
	}

	public List<DscEntityPropClass> getChildren() {
		return children;
	}

	public void setChildren(List<DscEntityPropClass> children) {
		this.children = children;
	}

    public String getClassRemit() {
        return classRemit;
    }

    public void setClassRemit(String classRemit) {
        this.classRemit = classRemit;
    }

    public String getGuideLevel() {
        return guideLevel;
    }

    public void setGuideLevel(String guideLevel) {
        this.guideLevel = guideLevel;
    }

    public String getEnterpriseLevel() {
        return enterpriseLevel;
    }

    public void setEnterpriseLevel(String enterpriseLevel) {
        this.enterpriseLevel = enterpriseLevel;
    }

    public String getSensitivityLevel() {
        return sensitivityLevel;
    }

    public void setSensitivityLevel(String sensitivityLevel) {
        this.sensitivityLevel = sensitivityLevel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("entityPropClassId", getEntityPropClassId())
            .append("parentId", getParentId())
            .append("ancestors", getAncestors())
            .append("className", getClassName())
            .append("classNameFull", getClassNameFull())
            .append("orderNum", getOrderNum())
            .append("classRemit", getClassRemit())
            .append("guideLevel", getGuideLevel())
            .append("enterpriseLevel", getEnterpriseLevel())
            .append("sensitivityLevel", getSensitivityLevel())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
