package com.dqms.dsm.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.dsm.domain.DsmCheck;
import com.dqms.dsm.mapper.DsmCheckMapper;
import com.dqms.dsm.service.IDsmCheckService;
import com.dqms.framework.web.service.TokenService;
import com.dqms.task.job.DsmCheckThread;
import com.dqms.utils.ThreadPoolUtils;

/**
 * 标准对标Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-31
 */
@Service
public class DsmCheckServiceImpl implements IDsmCheckService
{
    @Autowired
    private DsmCheckMapper dsmCheckMapper;

    /**
     * 查询标准对标
     *
     * @param checkId 标准对标ID
     * @return 标准对标
     */
    @Override
    public DsmCheck selectDsmCheckById(Long checkId)
    {
        return dsmCheckMapper.selectDsmCheckById(checkId);
    }

    /**
     * 查询标准对标列表
     *
     * @param dsmCheck 标准对标
     * @return 标准对标
     */
    @Override
    public List<DsmCheck> selectDsmCheckList(DsmCheck dsmCheck)
    {
        return dsmCheckMapper.selectDsmCheckList(dsmCheck);
    }

    /**
     * 新增标准对标
     *
     * @param dsmCheck 标准对标
     * @return 结果
     */
    @Override
    public int insertDsmCheck(DsmCheck dsmCheck)
    {
    	LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
    	ThreadPoolUtils.addTask(-4L,0,new DsmCheckThread(dsmCheck,loginUser));
        return 1;
    }

    /**
     * 修改标准对标
     *
     * @param dsmCheck 标准对标
     * @return 结果
     */
    @Override
    public int updateDsmCheck(DsmCheck dsmCheck)
    {
        return dsmCheckMapper.updateDsmCheck(dsmCheck);
    }

    /**
     * 批量删除标准对标
     *
     * @param checkIds 需要删除的标准对标ID
     * @return 结果
     */
    @Override
    public int deleteDsmCheckByIds(Long[] checkIds)
    {
        return dsmCheckMapper.deleteDsmCheckByIds(checkIds);
    }

    /**
     * 删除标准对标信息
     *
     * @param checkId 标准对标ID
     * @return 结果
     */
    @Override
    public int deleteDsmCheckById(Long checkId)
    {
        return dsmCheckMapper.deleteDsmCheckById(checkId);
    }
    
}
