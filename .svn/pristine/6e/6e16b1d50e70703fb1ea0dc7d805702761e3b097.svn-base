<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscDesensitizationDetailMapper">
    
    <resultMap type="DscDesensitizationDetail" id="DscDesensitizationDetailResult">
        <result property="desensitizationDetailId"    column="desensitization_detail_id"    />
        <result property="desensitizationId"    column="desensitization_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="propId"    column="prop_id"    />
        <result property="runType"    column="run_type"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />        
    </resultMap>

    <sql id="selectDscDesensitizationDetailVo">
        SELECT t.desensitization_detail_id, t.desensitization_id, t.entity_id, t.prop_id, t.run_type ,
		m.prop_name,m.prop_comment
		FROM dsc_desensitization_detail t LEFT JOIN mdm_data_entity_prop m ON t.prop_id=m.prop_id
    </sql>

    <select id="selectDscDesensitizationDetailList" parameterType="DscDesensitizationDetail" resultMap="DscDesensitizationDetailResult">
        <include refid="selectDscDesensitizationDetailVo"/>
        <where>  
            <if test="desensitizationId != null "> and t.desensitization_id = #{desensitizationId}</if>
            <if test="entityId != null "> and t.entity_id = #{entityId}</if>
            <if test="propId != null "> and t.prop_id = #{propId}</if>
            <if test="runType != null  and runType != ''"> and t.run_type = #{runType}</if>
        </where>
    </select>
    
    <select id="selectDscDesensitizationDetailById" parameterType="Long" resultMap="DscDesensitizationDetailResult">
        <include refid="selectDscDesensitizationDetailVo"/>
        where t.desensitization_detail_id = #{desensitizationDetailId}
    </select>
        
    <insert id="insertDscDesensitizationDetail" parameterType="DscDesensitizationDetail" useGeneratedKeys="true" keyProperty="desensitizationDetailId">
        insert into dsc_desensitization_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="desensitizationId != null">desensitization_id,</if>
            <if test="entityId != null">entity_id,</if>
            <if test="propId != null">prop_id,</if>
            <if test="runType != null">run_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="desensitizationId != null">#{desensitizationId},</if>
            <if test="entityId != null">#{entityId},</if>
            <if test="propId != null">#{propId},</if>
            <if test="runType != null">#{runType},</if>
         </trim>
    </insert>

    <update id="updateDscDesensitizationDetail" parameterType="DscDesensitizationDetail">
        update dsc_desensitization_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="desensitizationId != null">desensitization_id = #{desensitizationId},</if>
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="propId != null">prop_id = #{propId},</if>
            <if test="runType != null">run_type = #{runType},</if>
        </trim>
        where desensitization_detail_id = #{desensitizationDetailId}
    </update>

    <delete id="deleteDscDesensitizationDetailById" parameterType="Long">
        delete from dsc_desensitization_detail where desensitization_detail_id = #{desensitizationDetailId}
    </delete>

    <delete id="deleteDscDesensitizationDetailByIds" parameterType="String">
        delete from dsc_desensitization_detail where desensitization_detail_id in 
        <foreach item="desensitizationDetailId" collection="array" open="(" separator="," close=")">
            #{desensitizationDetailId}
        </foreach>
    </delete>
    
    <delete id="deleteDscDesensitizationDetailByDesensitizationId" parameterType="Long">
        delete from dsc_desensitization_detail where desensitization_id = #{desensitizationDetailId}
    </delete>    
</mapper>