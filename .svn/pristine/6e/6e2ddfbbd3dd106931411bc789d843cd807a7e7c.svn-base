<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscEncryptionDetailMapper">
    
    <resultMap type="DscEncryptionDetail" id="DscEncryptionDetailResult">
        <result property="encryptionDetailId"    column="encryption_detail_id"    />
        <result property="encryptionId"    column="encryption_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="propId"    column="prop_id"    />
        <result property="runType"    column="run_type"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />        
    </resultMap>

    <sql id="selectDscEncryptionDetailVo">
        SELECT t.encryption_detail_id, t.encryption_id, t.entity_id, t.prop_id, t.run_type,m.prop_name,m.prop_comment
		FROM dsc_encryption_detail t LEFT JOIN mdm_data_entity_prop m ON t.prop_id=m.prop_id
    </sql>

    <select id="selectDscEncryptionDetailList" parameterType="DscEncryptionDetail" resultMap="DscEncryptionDetailResult">
        <include refid="selectDscEncryptionDetailVo"/>
        <where>  
            <if test="encryptionId != null "> and t.encryption_id = #{encryptionId}</if>
            <if test="entityId != null "> and t.entity_id = #{entityId}</if>
            <if test="propId != null "> and t.prop_id = #{propId}</if>
            <if test="runType != null  and runType != ''"> and t.run_type = #{runType}</if>
        </where>
    </select>
    
    <select id="selectDscEncryptionDetailById" parameterType="Long" resultMap="DscEncryptionDetailResult">
        <include refid="selectDscEncryptionDetailVo"/>
        where t.encryption_detail_id = #{encryptionDetailId}
    </select>
        
    <insert id="insertDscEncryptionDetail" parameterType="DscEncryptionDetail" useGeneratedKeys="true" keyProperty="encryptionDetailId">
        insert into dsc_encryption_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="encryptionId != null">encryption_id,</if>
            <if test="entityId != null">entity_id,</if>
            <if test="propId != null">prop_id,</if>
            <if test="runType != null">run_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="encryptionId != null">#{encryptionId},</if>
            <if test="entityId != null">#{entityId},</if>
            <if test="propId != null">#{propId},</if>
            <if test="runType != null">#{runType},</if>
         </trim>
    </insert>

    <update id="updateDscEncryptionDetail" parameterType="DscEncryptionDetail">
        update dsc_encryption_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="encryptionId != null">encryption_id = #{encryptionId},</if>
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="propId != null">prop_id = #{propId},</if>
            <if test="runType != null">run_type = #{runType},</if>
        </trim>
        where encryption_detail_id = #{encryptionDetailId}
    </update>

    <delete id="deleteDscEncryptionDetailById" parameterType="Long">
        delete from dsc_encryption_detail where encryption_detail_id = #{encryptionDetailId}
    </delete>

    <delete id="deleteDscEncryptionDetailByIds" parameterType="String">
        delete from dsc_encryption_detail where encryption_detail_id in 
        <foreach item="encryptionDetailId" collection="array" open="(" separator="," close=")">
            #{encryptionDetailId}
        </foreach>
    </delete>
    
    <delete id="deleteDscEncryptionDetailByEncryptionDetailId" parameterType="Long">
        delete from dsc_encryption_detail where encryption_id = #{encryptionDetailId}
    </delete>    
</mapper>