<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.mdm.mapper.MdmThemeMapper">

    <resultMap type="MdmTheme" id="MdmThemeResult">
        <result property="themeId"    column="theme_id"    />
        <result property="themeCode"    column="theme_code"    />
        <result property="themeName"    column="theme_name"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tableNameRule"    column="table_name_rule"    />
        <collection  property="users"    ofType="sysUser"  column="theme_id"     select="getUsers"/>
    </resultMap>

    <select id="getUsers" resultMap="SysUserResult" parameterType="java.lang.String">
        SELECT  u.user_name,u.user_id
        FROM sys_user  u left join mdm_user_theme mu on mu.user_id = u.user_id
        where  mu.theme_id =#{themeId}
    </select>

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
    </resultMap>

    <sql id="selectMdmThemeVo">
        select m.theme_id, m.theme_code, m.theme_name, m.status, m.create_by, m.update_by, m.create_id, m.update_id, m.create_time, m.update_time,m.table_name_rule
        from mdm_theme m
    </sql>

    <select id="selectMdmThemeList" parameterType="MdmTheme" resultMap="MdmThemeResult">
        <include refid="selectMdmThemeVo"/>
        <where>
            <if test="themeCode != null  and themeCode != ''"> and m.theme_code like concat('%', #{themeCode}, '%')</if>
            <if test="themeName != null  and themeName != ''"> and m.theme_name like concat('%', #{themeName}, '%')</if>
            <if test="status != null  and status != ''"> and m.status = #{status}</if>
        </where>
        order by m.create_time desc, m.update_time desc
    </select>

    <select id="selectMdmThemeById" parameterType="Long" resultMap="MdmThemeResult">
        <include refid="selectMdmThemeVo"/>
        where m.theme_id = #{themeId}
    </select>

    <insert id="insertMdmTheme" parameterType="MdmTheme" useGeneratedKeys="true" keyProperty="themeId">
        insert into mdm_theme
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="themeCode != null">theme_code,</if>
            <if test="themeName != null">theme_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tableNameRule != null">table_name_rule,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="themeCode != null">#{themeCode},</if>
            <if test="themeName != null">#{themeName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tableNameRule != null">#{tableNameRule},</if>
         </trim>
    </insert>

    <update id="updateMdmTheme" parameterType="MdmTheme" >
        <selectKey keyProperty='themeId' resultType='java.lang.Long' order='AFTER'>
            SELECT
            (select theme_id FROM mdm_theme WHERE
            theme_name = #{themeName}) theme_id
            from DUAL
        </selectKey>
        update mdm_theme
        <trim prefix="SET" suffixOverrides=",">
            <if test="themeCode != null">theme_code = #{themeCode},</if>
            <if test="themeName != null">theme_name = #{themeName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tableNameRule != null">table_name_rule = #{tableNameRule},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        <choose>
            <when test="themeId != null  and themeId != ''">
                where theme_id = #{themeId}
            </when>
            <otherwise>
                where theme_name = #{themeName}
            </otherwise>
        </choose>
    </update>

    <delete id="deleteMdmThemeById" parameterType="Long">
        delete from mdm_theme where theme_id = #{themeId}
    </delete>

    <delete id="deleteMdmThemeByIds" parameterType="String">
        delete from mdm_theme where theme_id in
        <foreach item="themeId" collection="array" open="(" separator="," close=")">
            #{themeId}
        </foreach>
    </delete>

    <insert id="batchUserTheme">
        insert into mdm_user_theme(user_id, theme_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.themeId})
        </foreach>
    </insert>

    <delete id="deleteUserThemeByThemeId" parameterType="Long">
        delete from mdm_user_theme where theme_id=#{themeId}
    </delete>

    <delete id="deleteUserTheme" parameterType="Long">
        delete from mdm_user_theme where theme_id in
        <foreach collection="array" item="themeId" open="(" separator="," close=")">
            #{themeId}
        </foreach>
    </delete>

    <select id="selectuserIdsByThemeId" parameterType="Long" resultType="java.lang.Integer">
        select u.user_id
        from sys_user u
                 left join mdm_user_theme mt on mt.user_id = u.user_id
        where mt.theme_id = #{themeId}
    </select>

    <select id="selectThemeByName" parameterType="String" resultMap="MdmThemeResult">
        select theme_id, theme_code, theme_name, status, create_by, update_by, create_id, update_id, create_time, update_time,table_name_rule
        from mdm_theme
        where theme_name = #{themeName}
    </select>

    <select id="selectUserIdByNames" parameterType="String" resultType="java.lang.Long">
        select user_id from sys_user  where user_name in
        <foreach collection="array" item="userName" open="(" separator="," close=")">
            #{userName}
        </foreach>
    </select>

    <select id="selectUserIdByName" parameterType="String" resultType="java.lang.Long">
        select user_id from sys_user  where user_name = #{userName}
    </select>

    <select id="checkThemeNameUnique" parameterType="MdmTheme" resultType="int">
        select count(1) from mdm_theme where theme_name = #{themeName}
        <if test="themeId != null  and themeId != ''"> and theme_id != #{themeId}</if>
        limit 1
    </select>

    <select id="checkThemeCodeUnique" parameterType="MdmTheme" resultType="int">
        select count(1) from mdm_theme where theme_code = #{themeCode}
        <if test="themeId != null  and themeId != ''"> and theme_id != #{themeId}</if>
        limit 1
    </select>

    <select id="checkThemeCodeUniqueByName" parameterType="MdmTheme" resultType="int">
        select count(1) from mdm_theme where theme_code = #{themeCode}
        <if test="themeName != null  and themeName != ''"> and theme_name != #{themeName}</if>
        limit 1
    </select>

    <select id="selectMdmThemeAll" resultMap="MdmThemeResult">
        select m.theme_id, m.theme_code, m.theme_name, m.status, m.create_by, m.update_by, m.create_id, m.update_id, m.create_time, m.update_time,m.table_name_rule
        from mdm_theme m where status = 'Y' order by m.create_time desc, m.update_time desc
    </select>
</mapper>
