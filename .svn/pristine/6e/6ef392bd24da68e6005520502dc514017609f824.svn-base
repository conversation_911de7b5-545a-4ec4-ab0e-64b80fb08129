<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dsc:dscDataSecurityClassificationStandard:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dsc:dscDataSecurityClassificationStandard:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dsc:dscDataSecurityClassificationStandard:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dsc:dscDataSecurityClassificationStandard:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="dscDataSecurityClassificationStandardList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="级别标识" align="center" width="90" prop="levelIdentification" />
      <el-table-column label="重要程度" align="center" prop="importanceIdentification" />
      <el-table-column label="影响程度" align="center" prop="degreeInfluence" />
      <el-table-column
          label="数据特征"
          align="center"
          prop="dataCharacteristics"
        >
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p>{{ scope.row.dataCharacteristics }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium" >{{
                scope.row.dataCharacteristics
              }}</el-tag>
            </div>
          </el-popover>
        </template>
        </el-table-column>


      <el-table-column label="开放范围" align="center" prop="openRange" />
      <el-table-column
          label="提取申请"
          align="center"
          prop="withdrawalApplication"
        >
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p>{{ scope.row.withdrawalApplication }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium" >{{
                scope.row.withdrawalApplication
              }}</el-tag>
            </div>
          </el-popover>
        </template>
        </el-table-column>
      <el-table-column
                label="修改删除申请"
                align="center"
                prop="updateordeleteApplication"
              >
              <template slot-scope="scope">
                <el-popover trigger="hover" placement="top">
                  <p>{{ scope.row.updateordeleteApplication }}</p>
                  <div slot="reference" class="name-wrapper">
                    <el-tag size="medium" >{{
                      scope.row.updateordeleteApplication
                    }}</el-tag>
                  </div>
                </el-popover>
              </template>
              </el-table-column>
      <el-table-column
          label="备注"
          align="center"
          prop="remit"
        >
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p>{{ scope.row.remit }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium" >{{
                scope.row.remit
              }}</el-tag>
            </div>
          </el-popover>
        </template>
        </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsc:dscDataSecurityClassificationStandard:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsc:dscDataSecurityClassificationStandard:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全等级划分标准对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="级别标识" prop="levelIdentification">
          <el-input v-model="form.levelIdentification" type="text" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="重要程度" prop="importanceIdentification">
          <el-input v-model="form.importanceIdentification" type="text" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="影响程度" prop="degreeInfluence">
          <el-input v-model="form.degreeInfluence" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据特征" prop="dataCharacteristics">
          <el-input v-model="form.dataCharacteristics" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="开放范围" prop="openRange">
          <el-input v-model="form.openRange" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="提取申请" prop="withdrawalApplication">
          <el-input v-model="form.withdrawalApplication" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="修改删除申请" prop="updateordeleteApplication">
          <el-input v-model="form.updateordeleteApplication" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remit">
          <el-input v-model="form.remit" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDscDataSecurityClassificationStandard, getDscDataSecurityClassificationStandard, delDscDataSecurityClassificationStandard, addDscDataSecurityClassificationStandard, updateDscDataSecurityClassificationStandard, exportDscDataSecurityClassificationStandard } from "@/api/dsc/dscDataSecurityClassificationStandard";

export default {
  name: "dscDataSecurityClassificationStandard",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全等级划分标准表格数据
      dscDataSecurityClassificationStandardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        importanceIdentification: null,
        levelIdentification:null,
        degreeInfluence: null,
        dataCharacteristics: null,
        openRange: null,
        withdrawalApplication: null,
        updateordeleteApplication: null,
        remit: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全等级划分标准列表 */
    getList() {
      this.loading = true;
      listDscDataSecurityClassificationStandard(this.queryParams).then(response => {
        this.dscDataSecurityClassificationStandardList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        levelIdentification:null,
        importanceIdentification: null,
        degreeInfluence: null,
        dataCharacteristics: null,
        openRange: null,
        withdrawalApplication: null,
        updateordeleteApplication: null,
        remit: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.standardId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全等级划分标准";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const standardId = row.standardId || this.ids
      getDscDataSecurityClassificationStandard(standardId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全等级划分标准";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.standardId != null) {
            updateDscDataSecurityClassificationStandard(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDscDataSecurityClassificationStandard(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const standardIds = row.standardId || this.ids;
      this.$confirm('是否确认删除安全等级划分标准编号为"' + standardIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDscDataSecurityClassificationStandard(standardIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有安全等级划分标准数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDscDataSecurityClassificationStandard(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
<style>
.el-popover{
  max-width:60%;
}
</style>
<style scoped>
>>> .cell .el-tag{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  border:0;
  background:#fff;
  color:#000;
  font-size:14px;
}
</style>
