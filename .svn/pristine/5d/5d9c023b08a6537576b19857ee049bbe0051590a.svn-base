package com.dqms.dsc.controller;

import java.util.List;

import com.dqms.dsc.domain.vo.DscEntityPropClassVo;
import com.dqms.dsm.domain.vo.DsmStandardVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsc.domain.DscEntityPropClass;
import com.dqms.dsc.service.IDscEntityPropClassService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 分级分类Controller
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@RestController
@RequestMapping("/dsc/dscEntityPropClass")
public class DscEntityPropClassController extends BaseController
{
    @Autowired
    private IDscEntityPropClassService dscEntityPropClassService;

    /**
     * 查询分级分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClass:list')")
    @GetMapping("/list")
    public AjaxResult list(DscEntityPropClass dscEntityPropClass)
    {
        List<DscEntityPropClass> list = dscEntityPropClassService.selectDscEntityPropClassList(dscEntityPropClass);
        return AjaxResult.success(list);
    }

    @GetMapping("/getGrandList")
    public AjaxResult getGrandList() {
        List<DscEntityPropClass> list = dscEntityPropClassService.getGrandList();
        return AjaxResult.success(list);
    }
    @GetMapping("/getParentList")
    public AjaxResult getParentList(DscEntityPropClass dscEntityPropClass){
        List<DscEntityPropClass> list = dscEntityPropClassService.getParentList(dscEntityPropClass);
        return AjaxResult.success(list);
    }
    @GetMapping("/getEntityPropClassList")
    public AjaxResult getEntityPropClassList(DscEntityPropClass dscEntityPropClass) {
        List<DscEntityPropClass> list = dscEntityPropClassService.getEntityPropClassList(dscEntityPropClass);
        return AjaxResult.success(list);
    }
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<DscEntityPropClassVo> util = new ExcelUtil<>(DscEntityPropClassVo.class);
        return util.importTemplateExcel("分级分类列表");
    }
    /**
     * 导出分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClassVo:export')")
    @Log(title = "数据分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DscEntityPropClassVo dscEntityPropClassVo)
    {
        List<DscEntityPropClassVo> list = dscEntityPropClassService.selectDscEntityPropClassListVo(dscEntityPropClassVo);
        ExcelUtil<DscEntityPropClassVo> util = new ExcelUtil<DscEntityPropClassVo>(DscEntityPropClassVo.class);
        return util.exportExcel(list, "dscEntityPropClassVo");
    }

    /**
     * 导入分类列表
     */
    @Log(title = "导入分类列表", businessType = BusinessType.IMPORT )
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClass:import')")
    @PostMapping("/import")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DscEntityPropClassVo> util = new ExcelUtil<>(DscEntityPropClassVo.class);
        List<DscEntityPropClassVo> dscEntityPropClassVoList = util.importExcel(file.getInputStream());
        String message = dscEntityPropClassService.importDscEntityPropClass(dscEntityPropClassVoList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 获取分级分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClass:query')")
    @GetMapping(value = "/{entityPropClassId}")
    public AjaxResult getInfo(@PathVariable("entityPropClassId") Long entityPropClassId)
    {
        return AjaxResult.success(dscEntityPropClassService.selectDscEntityPropClassById(entityPropClassId));
    }

    /**
     * 新增分级分类
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClass:add')")
    @Log(title = "分级分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DscEntityPropClass dscEntityPropClass)
    {
        return toAjax(dscEntityPropClassService.insertDscEntityPropClass(dscEntityPropClass));
    }

    /**
     * 修改分级分类
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClass:edit')")
    @Log(title = "分级分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DscEntityPropClass dscEntityPropClass)
    {
        return toAjax(dscEntityPropClassService.updateDscEntityPropClass(dscEntityPropClass));
    }

    /**
     * 删除分级分类
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropClass:remove')")
    @Log(title = "分级分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{entityPropClassIds}")
    public AjaxResult remove(@PathVariable Long[] entityPropClassIds)
    {
        return toAjax(dscEntityPropClassService.deleteDscEntityPropClassByIds(entityPropClassIds));
    }
    
    /**
     * 获取下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DscEntityPropClass dscEntityPropClass)
    {
        List<DscEntityPropClass> dscEntityPropClasss = dscEntityPropClassService.selectDscEntityPropClassList(dscEntityPropClass);
        return AjaxResult.success(dscEntityPropClassService.buildDscEntityPropClassTreeSelect(dscEntityPropClasss));
    }

    /**
     * 获取数据分类名称下拉框列表
     */
    @GetMapping("/selectClassName")
    public AjaxResult selectClassName(DscEntityPropClass dscEntityPropClass)
    {
        List<DscEntityPropClass> list = dscEntityPropClassService.selectClassName(dscEntityPropClass);
        return AjaxResult.success(list);
    }
    /**
     * 获取数据分类祖级路径下拉框列表
     */
    @GetMapping("/selectClassNameFull")
    public AjaxResult selectClassNameFull(DscEntityPropClass dscEntityPropClass)
    {
        List<DscEntityPropClass> list = dscEntityPropClassService.selectClassNameFull(dscEntityPropClass);
        return AjaxResult.success(list);
    }
}
