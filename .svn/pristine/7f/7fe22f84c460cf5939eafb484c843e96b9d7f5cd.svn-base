import request from '@/utils/request'

// 查询标准应用系统列表
export function listDsmStandardTarSystem(query) {
  return request({
    url: '/dsm/dsmStandardTarSystem/list',
    method: 'get',
    params: query
  })
}

// 查询标准应用系统详细
export function getDsmStandardTarSystem(standardId) {
  return request({
    url: '/dsm/dsmStandardTarSystem/' + standardId,
    method: 'get'
  })
}

// 新增标准应用系统
export function addDsmStandardTarSystem(data) {
  return request({
    url: '/dsm/dsmStandardTarSystem',
    method: 'post',
    data: data
  })
}

// 修改标准应用系统
export function updateDsmStandardTarSystem(data) {
  return request({
    url: '/dsm/dsmStandardTarSystem',
    method: 'put',
    data: data
  })
}

// 删除标准应用系统
export function delDsmStandardTarSystem(standardId) {
  return request({
    url: '/dsm/dsmStandardTarSystem/' + standardId,
    method: 'delete'
  })
}

// 导出标准应用系统
export function exportDsmStandardTarSystem(query) {
  return request({
    url: '/dsm/dsmStandardTarSystem/export',
    method: 'get',
    params: query
  })
}