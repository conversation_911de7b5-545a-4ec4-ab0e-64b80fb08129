<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="接口ID" prop="defineId">
        <el-input
          v-model="queryParams.defineId"
          placeholder="请输入接口ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="参数名称" prop="defineParamName">
        <el-input
          v-model="queryParams.defineParamName"
          placeholder="请输入参数名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="是否必填" prop="isMust">
        <el-select v-model="queryParams.isMust" placeholder="请选择是否必填" clearable size="small">
          <el-option
            v-for="dict in isMustOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="参数类型" prop="defineParamType">
        <el-select v-model="queryParams.defineParamType" placeholder="请选择参数类型" clearable size="small">
          <el-option
            v-for="dict in defineParamTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="默认值" prop="defualtValue">
        <el-input
          v-model="queryParams.defualtValue"
          placeholder="请输入默认值"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['api:apiDefineParam:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['api:apiDefineParam:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['api:apiDefineParam:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['api:apiDefineParam:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="apiDefineParamList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="参数ID" align="center" prop="defineParamId" />
      <el-table-column label="接口ID" align="center" prop="defineId" />
      <el-table-column label="参数名称" align="center" prop="defineParamName" />
      <el-table-column label="参数说明" align="center" prop="remark" />
      <el-table-column label="是否必填" align="center" prop="isMust" :formatter="isMustFormat" />
      <el-table-column label="参数类型" align="center" prop="defineParamType" :formatter="defineParamTypeFormat" />
      <el-table-column label="默认值" align="center" prop="defualtValue" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['api:apiDefineParam:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['api:apiDefineParam:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改接口参数对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="接口ID" prop="defineId">
          <el-input v-model="form.defineId" placeholder="请输入接口ID" clearable/>
        </el-form-item>
        <el-form-item label="参数名称" prop="defineParamName">
          <el-input v-model="form.defineParamName" placeholder="请输入参数名称" clearable/>
        </el-form-item>
        <el-form-item label="参数说明" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入参数说明" clearable/>
        </el-form-item>
        <el-form-item label="是否必填" prop="isMust">
          <el-select v-model="form.isMust" placeholder="请选择是否必填" clearable>
            <el-option
              v-for="dict in isMustOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参数类型" prop="defineParamType">
          <el-select v-model="form.defineParamType" placeholder="请选择参数类型" clearable>
            <el-option
              v-for="dict in defineParamTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="默认值" prop="defualtValue">
          <el-input v-model="form.defualtValue" placeholder="请输入默认值" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiDefineParam, getApiDefineParam, delApiDefineParam, addApiDefineParam, updateApiDefineParam, exportApiDefineParam } from "@/api/api/apiDefineParam";

export default {
  name: "ApiDefineParam",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 接口参数表格数据
      apiDefineParamList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否必填字典
      isMustOptions: [],
      // 参数类型字典
      defineParamTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        defineId: null,
        defineParamName: null,
        isMust: null,
        defineParamType: null,
        defualtValue: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        defineId: [
          { required: true, message: "接口ID不能为空", trigger: "blur" }
        ],
        defineParamName: [
          { required: true, message: "参数名称不能为空", trigger: "blur" }
        ],
        isMust: [
          { required: true, message: "是否必填不能为空", trigger: "change" }
        ],
        defineParamType: [
          { required: true, message: "参数类型不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response => {
      this.isMustOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.defineParamTypeOptions = response.data;
    });
  },
  methods: {
    /** 查询接口参数列表 */
    getList() {
      this.loading = true;
      listApiDefineParam(this.queryParams).then(response => {
        this.apiDefineParamList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 是否必填字典翻译
    isMustFormat(row, column) {
      return this.selectDictLabel(this.isMustOptions, row.isMust);
    },
    // 参数类型字典翻译
    defineParamTypeFormat(row, column) {
      return this.selectDictLabel(this.defineParamTypeOptions, row.defineParamType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        defineParamId: null,
        defineId: null,
        defineParamName: null,
        remark: null,
        isMust: null,
        defineParamType: null,
        defualtValue: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.defineParamId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加接口参数";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const defineParamId = row.defineParamId || this.ids
      getApiDefineParam(defineParamId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改接口参数";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.defineParamId != null) {
            updateApiDefineParam(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApiDefineParam(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const defineParamIds = row.defineParamId || this.ids;
      this.$confirm('是否确认删除接口参数编号为"' + defineParamIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delApiDefineParam(defineParamIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有接口参数数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportApiDefineParam(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
