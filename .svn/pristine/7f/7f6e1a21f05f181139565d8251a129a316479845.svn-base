package com.dqms.dsc.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 生命周期对象 dsc_entity
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 扩展ID */
    private Long dscEntityId;

    /** 实体ID */
    @Excel(name = "实体ID")
    private Long entityId;

    /** 区域类型 */
    @Excel(name = "区域类型")
    private String regionType;

    /** 生命周期 */
    @Excel(name = "生命周期")
    private Long lifeCycle;

    /** 存储格式 */
    @Excel(name = "存储格式")
    private String storageFormat;

    /** 分区字段 */
    @Excel(name = "分区字段")
    private String partitionField;

    /** 分区类型 */
    @Excel(name = "分区类型")
    private String partitionType;

    /** 分区生命 */
    @Excel(name = "分区生命")
    private Long partitionLifeCycle;

    /** 分区格式 */
    @Excel(name = "分区格式")
    private String partitionFormat;

    /** 分桶字段 */
    @Excel(name = "分桶字段")
    private String bucketField;

    /** 分桶数量 */
    @Excel(name = "分桶数量")
    private Integer bucketNum;

    /** HDFS路径 */
    @Excel(name = "HDFS路径")
    private String hdfsPath;

    /** 归档周期 */
    @Excel(name = "归档周期")
    private String fileCycle;

    /** 归档方式 */
    @Excel(name = "归档方式")
    private String fileType;

    /** 日期字段 */
    @Excel(name = "日期字段")
    private String dateField;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    private Long tableId;
    private String tableName;
    private Long datasourceId;
    private String datasourceName;
    private Long systemId;
    private String systemName;
    
    /** 关联任务 */
    private Long taskId;

    /**用户/库/脚本路径*/
    private String regDir;
    
    /** 关联任务 */
    private Long[] unIds;

    public String getRegDir() {
        return regDir;
    }

    public void setRegDir(String regDir) {
        this.regDir = regDir;
    }

    public void setDscEntityId(Long dscEntityId)
    {
        this.dscEntityId = dscEntityId;
    }

    public Long getDscEntityId()
    {
        return dscEntityId;
    }
    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setRegionType(String regionType)
    {
        this.regionType = regionType;
    }

    public String getRegionType()
    {
        return regionType;
    }
    public void setLifeCycle(Long lifeCycle)
    {
        this.lifeCycle = lifeCycle;
    }

    public Long getLifeCycle()
    {
        return lifeCycle;
    }
    public void setStorageFormat(String storageFormat)
    {
        this.storageFormat = storageFormat;
    }

    public String getStorageFormat()
    {
        return storageFormat;
    }
    public void setPartitionField(String partitionField)
    {
        this.partitionField = partitionField;
    }

    public String getPartitionField()
    {
        return partitionField;
    }
    public void setPartitionType(String partitionType)
    {
        this.partitionType = partitionType;
    }

    public String getPartitionType()
    {
        return partitionType;
    }
    public void setPartitionLifeCycle(Long partitionLifeCycle)
    {
        this.partitionLifeCycle = partitionLifeCycle;
    }

    public Long getPartitionLifeCycle()
    {
        return partitionLifeCycle;
    }
    public void setPartitionFormat(String partitionFormat)
    {
        this.partitionFormat = partitionFormat;
    }

    public String getPartitionFormat()
    {
        return partitionFormat;
    }
    public void setBucketField(String bucketField)
    {
        this.bucketField = bucketField;
    }

    public String getBucketField()
    {
        return bucketField;
    }
    public void setBucketNum(Integer bucketNum)
    {
        this.bucketNum = bucketNum;
    }

    public Integer getBucketNum()
    {
        return bucketNum;
    }
    public void setHdfsPath(String hdfsPath)
    {
        this.hdfsPath = hdfsPath;
    }

    public String getHdfsPath()
    {
        return hdfsPath;
    }
    public void setFileCycle(String fileCycle)
    {
        this.fileCycle = fileCycle;
    }

    public String getFileCycle()
    {
        return fileCycle;
    }
    public void setFileType(String fileType)
    {
        this.fileType = fileType;
    }

    public String getFileType()
    {
        return fileType;
    }
    public void setDateField(String dateField)
    {
        this.dateField = dateField;
    }

    public String getDateField()
    {
        return dateField;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public Long getDatasourceId() {
		return datasourceId;
	}

	public void setDatasourceId(Long datasourceId) {
		this.datasourceId = datasourceId;
	}

	public String getDatasourceName() {
		return datasourceName;
	}

	public void setDatasourceName(String datasourceName) {
		this.datasourceName = datasourceName;
	}

	public Long getSystemId() {
		return systemId;
	}

	public void setSystemId(Long systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public Long[] getUnIds() {
		return unIds;
	}

	public void setUnIds(Long[] unIds) {
		this.unIds = unIds;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dscEntityId", getDscEntityId())
            .append("entityId", getEntityId())
            .append("regionType", getRegionType())
            .append("lifeCycle", getLifeCycle())
            .append("storageFormat", getStorageFormat())
            .append("partitionField", getPartitionField())
            .append("partitionType", getPartitionType())
            .append("partitionLifeCycle", getPartitionLifeCycle())
            .append("partitionFormat", getPartitionFormat())
            .append("bucketField", getBucketField())
            .append("bucketNum", getBucketNum())
            .append("hdfsPath", getHdfsPath())
            .append("fileCycle", getFileCycle())
            .append("fileType", getFileType())
            .append("dateField", getDateField())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
