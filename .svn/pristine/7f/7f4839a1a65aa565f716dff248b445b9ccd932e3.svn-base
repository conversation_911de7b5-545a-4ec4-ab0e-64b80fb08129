<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmDimensionMapper">
    
    <resultMap type="DsmDimension" id="DsmDimensionResult">
        <result property="dimensionId"    column="dimension_id"    />
        <result property="dimensionName"    column="dimension_name"    />
        <result property="dimensionCode"    column="dimension_code"    />
        <result property="dimensionType"    column="dimension_type"    />
        <result property="systemId"    column="system_id"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="showType"    column="show_type"    />
        <result property="dataType"    column="data_type"    />
        <result property="status"    column="status"    />
        <result property="execSql"    column="exec_sql"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dimensionClassId"    column="dimension_class_id"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="taskMsg"    column="task_msg"    />
        <result property="taskId"    column="task_id"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="discernFlag"    column="discern_flag"    />
        <result property="checkFlag"    column="check_flag"    />       
    </resultMap>

    <resultMap type="DsmDimensionVo" id="DsmDimensionVoResult">
        <result property="dimensionId"    column="dimension_id"    />
        <result property="dimensionName"    column="dimension_name"    />
        <result property="dimensionCode"    column="dimension_code"    />
        <result property="dimensionType"    column="dimension_type"    />
        <result property="systemId"    column="system_id"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="showType"    column="show_type"    />
        <result property="dataType"    column="data_type"    />
        <result property="status"    column="status"    />
        <result property="execSql"    column="exec_sql"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dimensionClassId"    column="dimension_class_id"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="taskMsg"    column="task_msg"    />
        <result property="taskId"    column="task_id"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="discernFlag"    column="discern_flag"    />
        <result property="checkFlag"    column="check_flag"    />
    </resultMap>

    <sql id="selectDsmDimensionVo">
        select t.dimension_id, t.dimension_name, t.dimension_code, t.dimension_type, t.system_id, t.datasource_id, t.show_type, t.data_type, t.status, t.exec_sql, 
        t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time ,t.dimension_class_id,t.task_status,t.task_id ,t.task_msg
        ,c.class_name, c.class_name_full,t.discern_flag,t.check_flag
        from dsm_dimension t left join dsm_dimension_class c on t.dimension_class_id=c.dimension_class_id
    </sql>

    <select id="selectDsmDimensionList" parameterType="DsmDimension" resultMap="DsmDimensionResult">
        <include refid="selectDsmDimensionVo"/>
        <where>  
            <if test="dimensionName != null  and dimensionName != ''"> and t.dimension_name like concat('%', #{dimensionName}, '%')</if>
            <if test="dimensionCode != null  and dimensionCode != ''"> and t.dimension_code like concat('%',  #{dimensionCode}, '%')</if>
            <if test="dimensionType != null  and dimensionType != ''"> and t.dimension_type = #{dimensionType}</if>
            <if test="systemId != null "> and t.system_id = #{systemId}</if>
            <if test="datasourceId != null "> and t.datasource_id = #{datasourceId}</if>
            <if test="showType != null  and showType != ''"> and t.show_type = #{showType}</if>
            <if test="dataType != null  and dataType != ''"> and t.data_type = #{dataType}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="execSql != null  and execSql != ''"> and t.exec_sql = #{execSql}</if>
            <if test="dimensionClassId != null  and dimensionClassId != ''"> and (c.ancestors like concat('%,', #{dimensionClassId}, '%') or t.dimension_class_id = #{dimensionClassId})</if>
            <if test="taskStatus != null  and taskStatus != ''"> and t.task_status = #{taskStatus}</if>
            <if test="taskId != null  and taskId != ''"> and t.task_id = #{taskId}</if>
            <if test="discernFlag != null "> and t.discern_flag = #{discernFlag}</if>
            <if test="checkFlag != null "> and t.check_flag = #{checkFlag}</if>
        </where>
    </select>

    <select id="selectDsmDimensionVoList" parameterType="DsmDimensionVo" resultMap="DsmDimensionVoResult">
        <include refid="selectDsmDimensionVo"/>
        <where>
            <if test="dimensionName != null  and dimensionName != ''"> and t.dimension_name like concat('%', #{dimensionName}, '%')</if>
            <if test="dimensionCode != null  and dimensionCode != ''"> and t.dimension_code = #{dimensionCode}</if>
            <if test="dimensionType != null  and dimensionType != ''"> and t.dimension_type = #{dimensionType}</if>
            <if test="systemId != null "> and t.system_id = #{systemId}</if>
            <if test="datasourceId != null "> and t.datasource_id = #{datasourceId}</if>
            <if test="showType != null  and showType != ''"> and t.show_type = #{showType}</if>
            <if test="dataType != null  and dataType != ''"> and t.data_type = #{dataType}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="execSql != null  and execSql != ''"> and t.exec_sql = #{execSql}</if>
            <if test="dimensionClassId != null  and dimensionClassId != ''"> and (c.ancestors like concat('%,', #{dimensionClassId}, '%') or t.dimension_class_id = #{dimensionClassId})</if>
            <if test="taskStatus != null  and taskStatus != ''"> and t.task_status = #{taskStatus}</if>
            <if test="taskId != null  and taskId != ''"> and t.task_id = #{taskId}</if>
            <if test="discernFlag != null "> and t.discern_flag = #{discernFlag}</if>
            <if test="checkFlag != null "> and t.check_flag = #{checkFlag}</if>
        </where>
    </select>
    
    <select id="selectDsmDimensionById" parameterType="Long" resultMap="DsmDimensionResult">
        <include refid="selectDsmDimensionVo"/>
        where dimension_id = #{dimensionId}
    </select>

    <select id="selectDsmDimensionByIds" parameterType="Long" resultMap="DsmDimensionResult">
        <include refid="selectDsmDimensionVo"/>
        where dimension_id in
        <foreach collection="array" item="dimensionId" index="index" open="(" separator="," close=")">
            #{dimensionId}
        </foreach>
    </select>
        
    <insert id="insertDsmDimension" parameterType="DsmDimension" useGeneratedKeys="true" keyProperty="dimensionId">
        insert into dsm_dimension
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dimensionName != null and dimensionName != ''">dimension_name,</if>
            <if test="dimensionCode != null and dimensionCode != ''">dimension_code,</if>
            <if test="dimensionType != null and dimensionType != ''">dimension_type,</if>
            <if test="systemId != null">system_id,</if>
            <if test="datasourceId != null">datasource_id,</if>
            <if test="showType != null and showType != ''">show_type,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="status != null">status,</if>
            <if test="execSql != null">exec_sql,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="dimensionClassId != null">dimension_class_id,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="taskMsg != null">task_msg,</if>
            <if test="taskId != null">task_id,</if>
            <if test="discernFlag != null">discern_flag,</if>
            <if test="checkFlag != null">check_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dimensionName != null and dimensionName != ''">#{dimensionName},</if>
            <if test="dimensionCode != null and dimensionCode != ''">#{dimensionCode},</if>
            <if test="dimensionType != null and dimensionType != ''">#{dimensionType},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="showType != null and showType != ''">#{showType},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="status != null">#{status},</if>
            <if test="execSql != null">#{execSql},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="dimensionClassId != null">#{dimensionClassId},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="taskMsg != null">#{taskMsg},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="discernFlag != null">#{discernFlag},</if>
            <if test="checkFlag != null">#{checkFlag},</if>
         </trim>
    </insert>

    <update id="updateDsmDimension" parameterType="DsmDimension">
        update dsm_dimension
        <trim prefix="SET" suffixOverrides=",">
            <if test="dimensionName != null and dimensionName != ''">dimension_name = #{dimensionName},</if>
            <if test="dimensionCode != null and dimensionCode != ''">dimension_code = #{dimensionCode},</if>
            <if test="dimensionType != null and dimensionType != ''">dimension_type = #{dimensionType},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
            <if test="showType != null and showType != ''">show_type = #{showType},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="execSql != null">exec_sql = #{execSql},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="dimensionClassId != null">dimension_class_id = #{dimensionClassId},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="taskMsg != null">task_msg = #{taskMsg},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="discernFlag != null">discern_flag = #{discernFlag},</if>
            <if test="checkFlag != null">check_flag = #{checkFlag},</if>
        </trim>
        where dimension_id = #{dimensionId}
    </update>
    <update id="updateDsmDimensionStatus" parameterType="DsmDimension">
    update dsm_dimension
    <trim prefix="SET" suffixOverrides=",">
        <if test="status != null">status = #{status},</if>

    </trim>
        where dimension_id = #{dimensionId}
    </update>

    <delete id="deleteDsmDimensionById" parameterType="Long">
        delete from dsm_dimension where dimension_id = #{dimensionId}
    </delete>

    <delete id="deleteDsmDimensionByIds" parameterType="String">
        delete from dsm_dimension where dimension_id in 
        <foreach item="dimensionId" collection="array" open="(" separator="," close=")">
            #{dimensionId}
        </foreach>
    </delete>
    <select id="selectDsmDimensionByCodeOrName" parameterType="DsmDimension" resultMap="DsmDimensionResult">
        <include refid="selectDsmDimensionVo"/>
        where  t.system_id=#{systemId} and  t.dimension_code=#{dimensionCode}
    </select>





    <select id="forDetail" parameterType="DsmDimensionVo" resultMap="DsmDimensionVoResult">
        select t.dimension_id, t.dimension_name, concat(t.dimension_code,'-',c.class_name) as dimension_code, t.dimension_type, t.system_id, t.datasource_id, t.show_type, t.data_type, t.status, t.exec_sql,
        t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time ,t.dimension_class_id,t.task_status,t.task_id ,t.task_msg
        ,c.class_name, c.class_name_full,t.discern_flag,t.check_flag
        from dsm_dimension t left join dsm_dimension_class c on t.dimension_class_id=c.dimension_class_id
        <where>
            <if test="dimensionCode != null  and dimensionCode != ''"> and t.dimension_code like concat('%', #{dimensionCode}, '%')</if>
        </where>
    </select>

</mapper>