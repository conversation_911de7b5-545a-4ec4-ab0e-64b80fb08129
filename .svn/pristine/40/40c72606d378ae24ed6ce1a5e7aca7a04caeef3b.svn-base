package com.dqms.common.constant;

public class MdmConstants {

    /**是否判断-是*/
    public static final String BOOLEAN_YES = "Y";

    /**是否判断-否*/
    public static final String BOOLEAN_NO = "N";

    /**操作类型-新增*/
    public static final String OPER_ADD = "1";

    /**操作类型-修改*/
    public static final String OPER_UPDATE = "2";

    /**操作类型-删除*/
    public static final String OPER_DEL = "3";

    /**操作类型-无变化*/
    public static final String OPER_UNCHANGE = "10";

    /**审批状态-待审核 */
    public static final String APP_WAITING = "1";
    /**审批状态-审核通过 */
    public static final String APP_APPROVED = "2";
    /**审批状态-忽略 */
    public static final String APP_IGNORE = "3";
    /**审批状态-废弃 */
    public static final String APP_DISUSE = "4";
    /**注册表是否存在*/
    public static final String TABLE_EXIST = "1";
    public static final String TABLE_NOT_EXIST = "0";

    /** 采集状态 */
    public static final  String ACQ_NO="1";
    public static final  String ACQ_YES="2";
    public static final  String ACQ_PASS="3";

    /** 采集结果状态 */
    public static final  String ACQ_SUCCESS="1";
    public static final  String ACQ_FAIL="0";

    /** 元数据类型 */
    public static final  Integer TABLE=1;
    public static final  Integer VIEW=2;
    public static final  Integer SCRIPT=3;
    public static final  Integer PROCEDURE=4;
    public static final  Integer REPORT=5;

    public static final  String ACQ_MODE_SD="1";
    public static final  String ACQ_MODE_ZD="2";

    /** 解析状态 */
    public static final  String ALALYSE_SUCCESS="0";
    public static final  String ALALYSE_FAIL="1";

    /** 关系类型 */
    public static final  String SHIP_TYPE_ZH="1";
    public static final  String SHIP_TYPE_ZJ="2";

    /** 数据库类型 */
    public static final  String HIVE="HIVE2";
    public static final  String ORACLE="ORACLE";
    public static final  String SQLSERVER="SQLSERVER";
    public static final  String FTP="FTP";

    /** 数据源认证类型 */
    public static final  String PASSWORD_AUTH="1";
    public static final  String KERBEROS_AUTH="2";
}
