package com.dqms.api.mapper;

import java.util.List;
import com.dqms.api.domain.ApiTemplateLog;

/**
 * 数据补录日志Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-26
 */
public interface ApiTemplateLogMapper
{
    /**
     * 查询数据补录日志
     *
     * @param importOperator 数据补录日志ID
     * @return 数据补录日志
     */
    public ApiTemplateLog selectApiTemplateLogById(String logId);

    /**
     * 查询数据补录日志列表
     *
     * @param apiTemplateLog 数据补录日志
     * @return 数据补录日志集合
     */
    public List<ApiTemplateLog> selectApiTemplateLogList(ApiTemplateLog apiTemplateLog);

    /**
     * 新增数据补录日志
     *
     * @param apiTemplateLog 数据补录日志
     * @return 结果
     */
    public int insertApiTemplateLog(ApiTemplateLog apiTemplateLog);

    /**
     * 修改数据补录日志
     *
     * @param apiTemplateLog 数据补录日志
     * @return 结果
     */
    public int updateApiTemplateLog(ApiTemplateLog apiTemplateLog);

    /**
     * 删除数据补录日志
     *
     * @param importOperator 数据补录日志ID
     * @return 结果
     */
    public int deleteApiTemplateLogById(String logId);

    /**
     * 批量删除数据补录日志
     *
     * @param importOperators 需要删除的数据ID
     * @return 结果
     */
    public int deleteApiTemplateLogByIds(String[] logId);
}
