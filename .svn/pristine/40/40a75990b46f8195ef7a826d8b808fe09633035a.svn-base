<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscEntityPropDeptMapper">
    
    <resultMap type="DscEntityPropDept" id="DscEntityPropDeptResult">
        <result property="entityPropId"    column="entity_prop_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectDscEntityPropDeptVo">
        select entity_prop_id, dept_id from dsc_entity_prop_dept
    </sql>

    <select id="selectDscEntityPropDeptList" parameterType="DscEntityPropDept" resultMap="DscEntityPropDeptResult">
        <include refid="selectDscEntityPropDeptVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectDscEntityPropDeptById" parameterType="Long" resultMap="DscEntityPropDeptResult">
        <include refid="selectDscEntityPropDeptVo"/>
        where entity_prop_id = #{entityPropId}
    </select>
        
    <insert id="insertDscEntityPropDept" parameterType="DscEntityPropDept">
        insert into dsc_entity_prop_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityPropId != null">entity_prop_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityPropId != null">#{entityPropId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateDscEntityPropDept" parameterType="DscEntityPropDept">
        update dsc_entity_prop_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where entity_prop_id = #{entityPropId}
    </update>

    <delete id="deleteDscEntityPropDeptById" parameterType="Long">
        delete from dsc_entity_prop_dept where entity_prop_id = #{entityPropId}
    </delete>

    <delete id="deleteDscEntityPropDeptByIds" parameterType="String">
        delete from dsc_entity_prop_dept where entity_prop_id in 
        <foreach item="entityPropId" collection="array" open="(" separator="," close=")">
            #{entityPropId}
        </foreach>
    </delete>
</mapper>