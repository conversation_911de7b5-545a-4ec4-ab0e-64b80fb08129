package com.dqms.dqm.domain;

import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 模板参数对象 dqm_validation_mould_parameter
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
public class DqmValidationMouldParameter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模板参数ID */
    private Integer validationMouldParameterId;

    /** 模板id */
    @Excel(name = "模板id")
    private Integer validationMouldId;

    /** 参数名称 */
    @Excel(name = "参数名称")
    private String parameterName;

    /** 参数类型  */
    @Excel(name = "参数类型 ")
    private String parameterType;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createId;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long updateId;

    public void setValidationMouldParameterId(Integer validationMouldParameterId)
    {
        this.validationMouldParameterId = validationMouldParameterId;
    }

    public Integer getValidationMouldParameterId()
    {
        return validationMouldParameterId;
    }
    public void setValidationMouldId(Integer validationMouldId)
    {
        this.validationMouldId = validationMouldId;
    }

    public Integer getValidationMouldId()
    {
        return validationMouldId;
    }
    public void setParameterName(String parameterName)
    {
        this.parameterName = parameterName;
    }

    public String getParameterName()
    {
        return parameterName;
    }
    public void setParameterType(String parameterType)
    {
        this.parameterType = parameterType;
    }

    public String getParameterType()
    {
        return parameterType;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("validationMouldParameterId", getValidationMouldParameterId())
                .append("validationMouldId", getValidationMouldId())
                .append("parameterName", getParameterName())
                .append("parameterType", getParameterType())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("createId", getCreateId())
                .append("updateId", getUpdateId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
