<template>
  <div class="app-container" style="padding:20px;">
    <el-card shadow="always">
      <div slot="header" class="clearfix">
        <el-select v-model="form.sysDatasourceType" placeholder="请选择数据库类型" clearable size="small">
          <el-option
            v-for="item in datasourcetypeOptions"
            :key="item.datasourceTypeCode"
            :label="item.datasourceTypeName"
            :value="item.datasourceTypeCode"
          ></el-option>
        </el-select>
        <el-button
          class="runBtn"
          type="primary"
          icon="el-icon-caret-right"
          size="mini"
          @click="handleRestart(true)"
          >运行</el-button
        >
      </div>
      <el-col :span="12">
        <div id="sqlDamMdmAnalyseCol">
          <textarea
            ref="myCm"
            class="sqlTextarea"
            id="sqlDamMdmAnalyseText"
            name="sqlDamMdmAnalyseText"
          ></textarea>
        </div>
      </el-col>
      <el-col :span="12">
           <el-tabs type="border-card" ref="tabs" v-model="activeName">
		      <el-tab-pane label="视图" name="first">
		      	<div id="scalingToolBar" style="position:absolute;padding-top:10;right:0;height:100;cursor:pointer;z-index: 99;">
			    	<el-button type="primary" size="mini" @click="zoomTo('+')"><i class="el-icon-zoom-in"></i>放大</el-button>
			    	<el-button type="primary" size="mini" @click="zoomTo('-')"><i class="el-icon-zoom-out"></i>缩小</el-button>
				</div>
		        <div id="container" style="position: relative;"></div>
		      </el-tab-pane>
		      <el-tab-pane label="文本" name="second">
		        <div
		          style="overflow-y: scroll;height:100%;white-space: pre-wrap;background:#000; color:#FFF"
		          v-html="script"
		        ></div>
		      </el-tab-pane>
		    </el-tabs>
      </el-col>
    </el-card>

  </div>
</template>
<script>
import CodeMirror from "codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/merge/merge.js";
import "codemirror/addon/merge/merge.css";
import "codemirror/addon/hint/show-hint.css";
import "codemirror/addon/hint/sql-hint.js";
import { codemirror } from "vue-codemirror";
import "codemirror/theme/monokai.css";
import "codemirror/theme/idea.css";
import "codemirror/mode/sql/sql.js";
import "codemirror/addon/hint/show-hint.js";
import "codemirror/addon/display/autorefresh.js";
import DiffMatchPatch from "diff-match-patch";
import sqlFormatter from "sql-formatter";
window.diff_match_patch = DiffMatchPatch;
window.DIFF_DELETE = -1;
window.DIFF_INSERT = 1;
window.DIFF_EQUAL = 0;

import { getData} from "@/api/mdm/mdmDataEntityShip";
import { listApiDefineParam } from "@/api/api/apiDefineParam";
import { getToken } from "@/utils/auth";
import { listSystemAll } from "@/api/basic/system";
import { listDatasourceAll ,listDataSourceType} from "@/api/basic/datasource";
import request from "@/utils/request";
import * as echarts from "echarts";

import G6 from "@antv/g6";
export default {
  name: "apiDebug",
  components: {},
  data() {
    return {
      //SQL编辑器
      table: {}, // 用于自定义列表宽度
      info: [], // 用于自定义列表宽度s
      idbLexicon: {},
      isRealTimeTip: false, // 是否是实时的提示
      activeName: "first",
      sqlInfo: [],
      codeEditor: null,
      defaultProps: {
        icon: "iconOne",
        label: "label",
        isLeaf: "leaf"
      },
      tableData: [],
      infoData: [],
      datasourcetypeOptions: [],
      // 遮罩层
      loading: false,
      form: {
    	  defineSql:'',
    	  sysDatasourceType:"HIVE2"
      },
      script: "无",
      data: "无",
      graphA: undefined,
      g6graph: undefined,
      nodes: [],
      edges: []
    };
  },
  created() {
    if(this.$store.state.user.newEntity){
      this.form.defineSql = this.$store.state.user.newEntity
    }
    this.getDataSourceType();
  },
  mounted() {
    this.init();
    this.$store.commit('SET_NEWENTITY','')
  },
  methods: {
    getDataSourceType() {
      listDataSourceType().then(response => {
        this.datasourcetypeOptions = response.data;
      });
    },
    handleRestart() {
   	 if(this.g6graph){
   		this.nodes=[];
   		this.edges=[];
   		this.g6graph.clear();
   		this.g6graph.destroy();
   	 }
      let sqlScript =
        this.getSelection() != "" ? this.getSelection() : this.getValue();
      if (sqlScript == null || sqlScript == "") {
        this.$message.warning("请输入脚本后再点击运行");
        return;
      }
      this.form.sqlScripts = sqlScript;
      getData(this.form).then(response => {
        this.script = response.data.script;
        this.data = response.data.data;
        for (var i = 0; i < this.data.length; i++) {
            this.nodes.push({
              id: this.data[i].tableName,
              label: this.data[i].tableName,
              attrs: this.data[i].mdmDataEntityProps
            });
            if (this.data[i].relations != null) {
              for (var j = 0; j < this.data[i].relations.length; j++) {
            	  const shipType="转换";
            	  if(this.data[i].relations[j].shipType==1){
            		  shipType="外键";
            	  }
                this.edges.push({
                  source: this.data[i].relations[j].srcEntityId,
                  target: this.data[i].relations[j].tarEntityId,
                  sourceKey: this.data[i].relations[j].srcPropName,
                  targetKey: this.data[i].relations[j].tarPropName,
                  label: shipType
                });
              }
            }
          }
          this.loading = false;
          this.initGraph();
      });
    },
    init() {
      // 实例初始化
      const targetF = document.getElementById("sqlDamMdmAnalyseCol");
      targetF.innerHTML = '<textarea id="sqlDamMdmAnalyseText" name="sqlDamMdmAnalyseText" />';
      const target = document.getElementById("sqlDamMdmAnalyseText");
      this.codeEditor = CodeMirror.fromTextArea(target, {
        lineNumbers: true, //显示行号
        styleActiveLine: true,
        matchBrackets: true,
        mode: "text/x-sql",
        connect: "align",
        theme: "monokai",
        autoCloseBrackets: true,
        autoRefresh: true,
        readOnly: false,
        hintOptions: {
          completeSingle: false,
          tables: this.idbLexicon
        },
        extraKeys: {
          "Ctrl-Space": editor => {
            editor.showHint();
          }
        }
      });
      this.codeEditor.setValue(this.form.defineSql);
      this.codeEditor.on("keypress", editor => {
        const __Cursor = editor.getDoc().getCursor();
        const __Token = editor.getTokenAt(__Cursor);
        if (
          __Token.type &&
          __Token.type !== "string" &&
          __Token.type !== "punctuation" &&
          __Token.string.indexOf(".") === -1
        ) {
          // 把输入的关键字统一变成大写字母
          editor.replaceRange(
            __Token.string.toUpperCase(),
            {
              line: __Cursor.line,
              ch: __Token.start
            },
            {
              line: __Cursor.line,
              ch: __Token.end
            },
            __Token.string
          );
        }
        editor.showHint();
      });
    },
    // 设置value
    setValue(val) {
      this.codeEditor.setValue(val);
    },
    // 获取value
    getValue() {
      return this.codeEditor.getValue();
    },
    // 获取选中内容
    getSelection() {
      return this.codeEditor.getSelection();
    },
    initGraph() {
        const { Util, registerBehavior, registerEdge, registerNode } = G6;
        
        const isInBBox = (point, bbox) => {
          const { x, y } = point;
          const { minX, minY, maxX, maxY } = bbox;

          return x < maxX && x > minX && y > minY && y < maxY;
        };

        const itemHeight = 30;
        registerBehavior("dice-er-scroll", {
          getDefaultCfg() {
            return {
              multiple: true
            };
          },
          getEvents() {
            return {
              itemHeight: 50,
              wheel: "scorll",
              click: "click",
              "node:mousemove": "move"
            };
          },
          scorll(e) {
            e.preventDefault();
            const { graph } = this;
            const nodes = graph.getNodes().filter(n => {
              const bbox = n.getBBox();

              return isInBBox(graph.getPointByClient(e.clientX, e.clientY), bbox);
            });

            const x = e.deltaX || e.movementX;
            let y = e.deltaY || e.movementY;
            if (!y && navigator.userAgent.indexOf("Firefox") > -1)
              y = (-e.wheelDelta * 125) / 3;

            if (nodes) {
              nodes.forEach(node => {
                const model = node.getModel();
                if (model.attrs.length < 9) {
                  return;
                }
                const idx = model.startIndex || 0;
                let startX = model.startX || 0.5;
                let startIndex = idx + y * 0.02;
                startX -= x;
                if (startIndex < 0) {
                  startIndex = 0;
                }
                if (startX > 0) {
                  startX = 0;
                }
                if (startIndex > model.attrs.length - 1) {
                  startIndex = model.attrs.length - 1;
                }
                graph.update(node, {
                  startIndex,
                  startX
                });
              });
            }
          },
          click(e) {
            const { graph } = this;
            const { y } = e;
            const item = e.item;
            const shape = e.shape;
            if (!item) {
              return;
            }
            const model = item.getModel();

            if (shape.get("name") === "collapse") {
              graph.updateItem(item, {
                collapsed: true,
                size: [300, 50]
              });
              setTimeout(() => graph.layout(), 100);
            } else if (shape.get("name") === "expand") {
              graph.updateItem(item, {
                collapsed: false,
                size: [300, 500]
              });
              setTimeout(() => graph.layout(), 100);
            }
          },
          move(e) {
            const name = e.shape.get("name");
            const item = e.item;

            if (name && name.startsWith("item")) {
              this.graph.updateItem(item, {
                selectedIndex: Number(name.split("-")[1])
              });
            } else {
              this.graph.updateItem(item, {
                selectedIndex: NaN
              });
            }
          }
        });

        registerEdge("dice-er-edge", {
          draw(cfg, group) {
            const edge = group.cfg.item;
            const sourceNode = edge.getSource().getModel();
            const targetNode = edge.getTarget().getModel();

            const sourceIndex = sourceNode.attrs.findIndex(
              e => e.key === cfg.sourceKey
            );

            const sourceStartIndex = sourceNode.startIndex || 0;

            let sourceY = 15;

            if (!sourceNode.collapsed && sourceIndex > sourceStartIndex - 1) {
              sourceY = 30 + (sourceIndex - sourceStartIndex + 0.5) * 30;
              sourceY = Math.min(sourceY, 300);
            }

            const targetIndex = targetNode.attrs.findIndex(
              e => e.key === cfg.targetKey
            );

            const targetStartIndex = targetNode.startIndex || 0;

            let targetY = 15;

            if (!targetNode.collapsed && targetIndex > targetStartIndex - 1) {
              targetY = (targetIndex - targetStartIndex + 0.5) * 30 + 30;
              targetY = Math.min(targetY, 300);
            }
            const startPoint = {
              ...cfg.startPoint
            };
            const endPoint = {
              ...cfg.endPoint
            };

            startPoint.y = startPoint.y + sourceY;
            endPoint.y = endPoint.y + targetY;

            let shape;
            if (sourceNode.id !== targetNode.id) {
              shape = group.addShape("path", {
                attrs: {
                  stroke: "#5B8FF9",
                  path: [
                    ["M", startPoint.x, startPoint.y],
                    [
                      "C",
                      endPoint.x / 3 + (2 / 3) * startPoint.x,
                      startPoint.y,
                      endPoint.x / 3 + (2 / 3) * startPoint.x,
                      endPoint.y,
                      endPoint.x,
                      endPoint.y
                    ]
                  ],
                  endArrow: true
                },
                name: "path-shape"
              });
            } else if (!sourceNode.collapsed) {
              let gap = Math.abs((startPoint.y - endPoint.y) / 3);
              if (startPoint["index"] === 1) {
                gap = -gap;
              }
              shape = group.addShape("path", {
                attrs: {
                  stroke: "#5B8FF9",
                  path: [
                    ["M", startPoint.x, startPoint.y],
                    [
                      "C",
                      startPoint.x - gap,
                      startPoint.y,
                      startPoint.x - gap,
                      endPoint.y,
                      startPoint.x,
                      endPoint.y
                    ]
                  ],
                  endArrow: true
                },
                name: "path-shape"
              });
            }

            return shape;
          },
          afterDraw(cfg, group) {
            const labelCfg = cfg.labelCfg || {};
            const edge = group.cfg.item;
            const sourceNode = edge.getSource().getModel();
            const targetNode = edge.getTarget().getModel();
            if (sourceNode.collapsed && targetNode.collapsed) {
              return;
            }
            const path = group.find(
              element => element.get("name") === "path-shape"
            );

            const labelStyle = Util.getLabelPosition(path, 0.5, 0, 0, true);
            const label = group.addShape("text", {
              attrs: {
                ...labelStyle,
                text: cfg.label || "",
                fill: "#000",
                textAlign: "center",
                stroke: "#fff",
                lineWidth: 1
              }
            });
            label.rotateAtStart(labelStyle.rotate);
          }
        });

        registerNode("dice-er-box", {
          draw(cfg, group) {
            const width = 250;
            const height = 316;
            const itemCount = 10;
            const boxStyle = {
              stroke: "#096DD9",
              radius: 4
            };

            const {
              attrs = [],
              startIndex = 0,
              selectedIndex,
              collapsed,
              icon
            } = cfg;
            const list = attrs;
            const afterList = list.slice(
              Math.floor(startIndex),
              Math.floor(startIndex + itemCount - 1)
            );
            const offsetY = (0.5 - (startIndex % 1)) * itemHeight + 30;

            group.addShape("rect", {
              attrs: {
                fill: boxStyle.stroke,
                height: 30,
                width,
                radius: [boxStyle.radius, boxStyle.radius, 0, 0]
              },
              draggable: true
            });

            let fontLeft = 12;

            if (icon && icon.show !== false) {
              group.addShape("image", {
                attrs: {
                  x: 8,
                  y: 8,
                  height: 16,
                  width: 16,
                  ...icon
                }
              });
              fontLeft += 18;
            }

            group.addShape("text", {
              attrs: {
                y: 22,
                x: fontLeft,
                fill: "#fff",
                text: cfg.label,
                fontSize: 12,
                fontWeight: 500
              }
            });

            group.addShape("rect", {
              attrs: {
                x: 0,
                y: collapsed ? 30 : 300,
                height: 15,
                width,
                fill: "#eee",
                radius: [0, 0, boxStyle.radius, boxStyle.radius],
                cursor: "pointer"
              },
              name: collapsed ? "expand" : "collapse"
            });

            group.addShape("text", {
              attrs: {
                x: width / 2 - 6,
                y: (collapsed ? 30 : 300) + 12,
                text: collapsed ? "+" : "-",
                width,
                fill: "#000",
                radius: [0, 0, boxStyle.radius, boxStyle.radius],
                cursor: "pointer"
              },
              name: collapsed ? "expand" : "collapse"
            });

            const keyshape = group.addShape("rect", {
              attrs: {
                x: 0,
                y: 0,
                width,
                height: collapsed ? 45 : height,
                ...boxStyle
              },
              draggable: true
            });

            if (collapsed) {
              return keyshape;
            }

            const listContainer = group.addGroup({});
            listContainer.setClip({
              type: "rect",
              attrs: {
                x: -8,
                y: 30,
                width: width + 16,
                height: 300 - 30
              }
            });
            listContainer.addShape({
              type: "rect",
              attrs: {
                x: 1,
                y: 30,
                width: width - 2,
                height: 300 - 30,
                fill: "#fff"
              },
              draggable: true
            });

            if (list.length > itemCount) {
              const barStyle = {
                width: 4,
                padding: 0,
                boxStyle: {
                  stroke: "#00000022"
                },
                innerStyle: {
                  fill: "#00000022"
                }
              };

              listContainer.addShape("rect", {
                attrs: {
                  y: 30,
                  x: width - barStyle.padding - barStyle.width,
                  width: barStyle.width,
                  height: height - 30,
                  ...barStyle.boxStyle
                }
              });

              const indexHeight =
                afterList.length > itemCount
                  ? (afterList.length / list.length) * height
                  : 10;

              listContainer.addShape("rect", {
                attrs: {
                  y:
                    30 +
                    barStyle.padding +
                    (startIndex / list.length) * (height - 30),
                  x: width - barStyle.padding - barStyle.width,
                  width: barStyle.width,
                  height: Math.min(height, indexHeight),
                  ...barStyle.innerStyle
                }
              });
            }
            if (afterList) {
              afterList.forEach((e, i) => {
                const isSelected =
                  Math.floor(startIndex) + i === Number(selectedIndex);
                let { key = "", type } = e;
                if (type) {
                  key += " - " + type;
                }
                const label = key.length > 26 ? key.slice(0, 24) + "..." : key;

                listContainer.addShape("rect", {
                  attrs: {
                    x: 1,
                    y: i * itemHeight - itemHeight / 2 + offsetY,
                    width: width - 4,
                    height: itemHeight,
                    radius: 2,
                    lineWidth: 1,
                    cursor: "pointer"
                  },
                  name: `item-${Math.floor(startIndex) + i}-content`,
                  draggable: true
                });

                if (!cfg.hideDot) {
                  listContainer.addShape("circle", {
                    attrs: {
                      x: 0,
                      y: i * itemHeight + offsetY,
                      r: 3,
                      stroke: boxStyle.stroke,
                      fill: "white",
                      radius: 2,
                      lineWidth: 1,
                      cursor: "pointer"
                    }
                  });
                  listContainer.addShape("circle", {
                    attrs: {
                      x: width,
                      y: i * itemHeight + offsetY,
                      r: 3,
                      stroke: boxStyle.stroke,
                      fill: "white",
                      radius: 2,
                      lineWidth: 1,
                      cursor: "pointer"
                    }
                  });
                }

                listContainer.addShape("text", {
                  attrs: {
                    x: 12,
                    y: i * itemHeight + offsetY + 6,
                    text: label,
                    fontSize: 12,
                    fill: "#000",
                    fontFamily:
                      "Avenir,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol",
                    full: e,
                    fontWeight: isSelected ? 500 : 100,
                    cursor: "pointer"
                  },
                  name: `item-${Math.floor(startIndex) + i}`
                });
              });
            }

            return keyshape;
          },
          getAnchorPoints() {
            return [
              [0, 0],
              [1, 0]
            ];
          }
        });

        const dataTransform = data => {
          const nodes = [];
          const edges = [];

          data.map(node => {
            nodes.push({
              ...node
            });
            if (node.attrs) {
              node.attrs.forEach(attr => {
                if (attr.relation) {
                  attr.relation.forEach(relation => {
                    edges.push({
                      source: node.id,
                      target: relation.nodeId,
                      sourceKey: attr.key,
                      targetKey: relation.key,
                      label: relation.label
                    });
                  });
                }
              });
            }
          });

          return {
            nodes,
            edges
          };
        };

        const container = document.getElementById("container");
        const minimap = new G6.Minimap({
          position: "absolute",
          "text-align": "right"
        });
        const width = container.scrollWidth;
        const height = document.body.clientHeight - 430;
        const graph = new G6.Graph({
          container: "container",
          width,
          height,
          plugins: [minimap],
          minZoom: 0.1,
          maxZoom: 1.5,
          defaultNode: {
            size: [400, 500],
            type: "dice-er-box",
            color: "#5B8FF9",
            style: {
              fill: "#9EC9FF",
              lineWidth: 3
            },
            labelCfg: {
              style: {
                fill: "black",
                fontSize: 20
              }
            }
          },
          defaultEdge: {
            type: "dice-er-edge",
            style: {
              stroke: "#e2e2e2",
              lineWidth: 4,
              endArrow: true
            }
          },
          modes: {
            default: ["dice-er-scroll", "drag-node", "drag-canvas"]
          },
          layout: {
            type: "dagre",
            rankdir: "LR",
            align: "UL",
            controlPoints: true,
            nodesepFunc: () => 0.2,
            ranksepFunc: () => 0.5
          },
  		  animate: true,		  
  		  fitView:true,
  		  plugins: [ minimap ],
  		  minZoom:0.1,
  		  maxZoom:1.5
        });
        const data = {
          nodes: this.nodes,
          edges: this.edges
        };
        //graph.data(dataTransform(rawData));
        graph.data(data);
        graph.render();
        graph.get("container").style.backgroundSize = "auto 100%";
        graph.zoom(0.4,{ x: width/2, y: height/2 });
        this.g6graph = graph;
      },
  	zoomTo(value){
  		if(value=='+'){
  			this.g6graph.zoomTo(this.g6graph.getZoom()+0.1);
  		}else{
  			this.g6graph.zoomTo(this.g6graph.getZoom()-0.1);
  		}

  	}
  },
  destroy() {
    //注意，VUE此处必须清理，否则切换界面会越来越卡
    this.g6graph.clear();
    this.g6graph.destroy();
  }
};
</script>
<style scoped>
>>> .CodeMirror pre.CodeMirror-line,
>>> .CodeMirror pre.CodeMirror-line-like {
  line-height: 22px;
}
>>> .CodeMirror {
  height:100% !important;
}
>>> .CodeMirror-merge,
.CodeMirror-merge .CodeMirror {
  height: 480px !important;
}
>>> .CodeMirror-merge-r-chunk {
  background: #38380d !important;
}

>>> .infinite-list-wrapper {
  height: calc(100vh - 84px);
}
>>> .el-tag--medium {
  margin-left: 5px;
}
>>> .loadingStatus {
  text-align: center;
  color: #303133;
  font-size: 14px;
}
.sqlTextarea {
  resize: none;
  width: 100%;
  height: calc(100vh - 432px);
  min-height: 400px;
  background: #f5f7fa;
}
>>> .head-container:last-child {
  overflow-y: scroll;
  height: calc(100vh - 180px);
  -ms-overflow-style: none;
  scrollbar-width: none;
}
>>> .head-container2::-webkit-scrollbar {
  height: 0 !important;
  width: 0px !important;
}
>>> .sqlBtn {
  margin-bottom: 1px;
}
>>> .spanStyle {
  white-space: nowrap;
  width: 90%;
  word-break: keep-all;
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
}
>>> .el-table th > .cell {
  padding: 0;
  text-align: center;
}
>>> .getTextWidth {
  font-size: 14px;
}
>>> .el-card__body{
  height:100%;
}
>>>.el-col-12{
  height:90%;
}
#sqlDamMdmAnalyseCol{
  height:100%;
}
>>>.el-card.is-always-shadow{
  height:100%;
}
.app-container{
  height:calc(100vh - 84px)
}
>>>.el-tabs,>>>.el-tab-pane{
  height:100%;
}
>>>.el-tabs__content{
  height:calc(100% - 24px);
}
.entity-container.fact {
  border: 1px solid #ced4de;
  height: 248px;
  width: 214px;
}
.entity-container {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border-radius: 2px;
  background-color: #fff;
}
.entity-container .content.fact {
  background-color: #ced4de;
}
.entity-container .content {
  margin: 1px;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.entity-container .content .head {
  width: calc(100% - 12px);
  height: 38px;
  margin-left: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.entity-container .content .head .type {
  padding-right: 8px;
}
.anticon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.anticon svg {
  display: inline-block;
}
svg:not(:root) {
  overflow: hidden;
}
.entity-container .content .head .more {
  cursor: pointer;
}
.entity-container .content .body {
  width: calc(100% - 12px);
  height: calc(100% - 42px);
  margin-left: 6px;
  margin-bottom: 6px;
  background-color: #fff;
  overflow: auto;
  cursor: pointer;
}
.entity-container .content .body .body-item {
  width: 100%;
  height: 28px;
  font-size: 12px;
  color: #595959;
  border-bottom: 1px solid rgba(206, 212, 222, 0.2);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.entity-container .content .body .body-item .name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 6px;
}
.entity-container .content .body .body-item .name .fk,
.entity-container .content .body .body-item .name .pk {
  width: 12px;
  font-family: "HelveticaNeue-CondensedBold";
  color: #ffd666;
  margin-right: 6px;
}
.entity-container .content .body .body-item .type {
  color: #bfbfbf;
  font-size: 8px;
  margin-right: 8px;
}
#scalingToolBar2{
  transition:all 2s ease;
  right:25%;
  top:10px;
}
#scalingToolBar2.right{
  right:0;
  top:0;
}
</style>
