package com.dqms.mdm.domain;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.common.core.domain.entity.SysRole;
import com.dqms.common.core.domain.entity.SysUser;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

import java.util.List;

/**
 * 主题管理对象 mdm_theme
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
public class MdmTheme extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long themeId;

    /** 名称 */
    @Excel(name = "名称")
    private String themeName;

    /** 编号 */
    @Excel(name = "编号")
    private String themeCode;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "Y=启用,N=停用")
    private String status;

    /** 表名规则 */
    @Excel(name = "表名规则")
    private String tableNameRule;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    private Long[] sysPersonIds;

    private List<SysUser> users;

    @Excel(name = "责任人")
    private String userNames;

    public String getTableNameRule() {
        return tableNameRule;
    }

    public void setTableNameRule(String tableNameRule) {
        this.tableNameRule = tableNameRule;
    }

    public String getUserNames() {
        return userNames;
    }

    public void setUserNames(String userNames) {
        this.userNames = userNames;
    }

    public List<SysUser> getUsers() {
        return users;
    }

    public void setUsers(List<SysUser> users) {
        this.users = users;

    }

    public Long[] getSysPersonIds() {
        return sysPersonIds;
    }

    public void setSysPersonIds(Long[] sysPersonIds) {
        this.sysPersonIds = sysPersonIds;
    }

    public void setThemeId(Long themeId)
    {
        this.themeId = themeId;
    }

    public Long getThemeId()
    {
        return themeId;
    }
    public void setThemeCode(String themeCode)
    {
        this.themeCode = themeCode;
    }

    public String getThemeCode()
    {
        return themeCode;
    }
    public void setThemeName(String themeName)
    {
        this.themeName = themeName;
    }

    public String getThemeName()
    {
        return themeName;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("themeId", getThemeId())
            .append("themeCode", getThemeCode())
            .append("themeName", getThemeName())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
