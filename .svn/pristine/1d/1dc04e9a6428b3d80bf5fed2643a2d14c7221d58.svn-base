<template>
  <div class="app-container">
    <el-row>
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>API分布</span>
		  </div>
	      <div id="runFb" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="18">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>API趋势(近30日)</span>
		  </div>
	      <div id="runQs" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>执行总览(24h内)</span>
		  </div>
	      <div id="runZl" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>


	  <el-col :span="18">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>API耗时排名(24h内前10)</span>
		  </div>
		  <div id="runHs" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  		    
	  <el-col :span="24">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always" style=" height:350px">
	      <div slot="header" class="clearfix">
			    <span>API调用信息(24h内)</span>
		  </div>
	      	   <el-table v-loading="loading" :data="apiDefineHisList" height="230" border>
			      <el-table-column type="selection" width="55" align="center" />
			      <el-table-column label="接口" align="center" prop="defineName" />
			      <el-table-column label="系统" align="center" prop="systemName"/>
			      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
			        <template slot-scope="scope">
			          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
			        </template>
			      </el-table-column>
			      <el-table-column label="完成时间" align="center" prop="endTime" width="180">
			        <template slot-scope="scope">
			          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
			        </template>
			      </el-table-column>
			      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat"  width="80"/>
			      <el-table-column label="IP地址" align="center" prop="ipAddress"  width="180"/>
			    </el-table>
			        <pagination
				      v-show="total>0"
				      :total="total"
				      :page.sync="queryParams.pageNum"
				      :limit.sync="queryParams.pageSize"
				      @pagination="getList"
				    />
	    </el-card>
	  </el-col>
	  
    </el-row>
  </div>
</template>

<script>
import { getRunFb,getRunZl,getRunQs,getRunHs,getRunYc} from "@/api/api/apiView";
import G6 from '@antv/g6'
import backgroundImage from '@/assets/images/timg.jpg';

export default {
  name: "NeedsView",
  components: {
  },
  data() {
    return {
    	apiDefineHisList: [],
    	total: 0,
    	loading: true,
        systemIdOptions: [],
        statusOptions: [],
        queryParams: {
          pageNum: 1,
          pageSize: 10
        }
    };
  },
  mounted() {
	  this.showRunFb();
	  this.showRunZl();
	  this.showRunQs();
	  this.showRunHs();
	  this.getList();
  },
  created() {
	    this.getDicts("etl_task_run_status").then(response => {
	      this.statusOptions = response.data;
	    });
	  },
  methods: {
	    getList() {
	      this.loading = true;
	      getRunYc().then(response => {
	        this.apiDefineHisList = response.rows;
	        this.total = response.total;
	        this.loading = false;
	      });
	    },
	  showRunFb() {
    	  getRunFb();
      },
      showRunZl() {
    	  getRunZl();
      },
      showRunQs() {
    	  getRunQs();
      },
      showRunHs() {
    	  getRunHs();
      },
      statusFormat(row, column) {
         return this.selectDictLabel(this.statusOptions, row.status);
       }
  },
  destroyed () {
	  
  }
};
</script>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }
  
  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }
  
  .clearfix:after {
      clear: both
  }
  
  .card{
  	margin:10px;
  }
  
  </style>