# 项目相关配置
dqms:
  # 文件路径 示例（ Windows配置D:/gbicc/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /usr/local/tomcat/uploadPath
  #api 接口
  address: http://*************:20081
  #dqms-kafka 地址
  kafkaAddr: http://localhost:9099/dk/msg/massfetch?topic={topic}&maxcount={maxcount}
spring:
  #（部署修改es配置）
  elasticsearch:
    rest:
      uris: http://*************:9200  #es服务器地址（默认）
    # redis 配置
    #（部署修改redis配置）
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 20003
    # 数据库索引
    database: 1
    # 密码
    password: cfzq1234
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      #（部署修改mysql配置）
      master:
        url: *****************************************************************************************************************************************************
        #              url: *****************************************************************************************************************************************************
        username: root
        password: Nuofee7sho^o
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
