package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.annotation.Excel;

/**
 * 模型实例对象 dsm_model_entity_temp
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
public class DsmModelEntityTemp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模型实例 */
    private Long modelEntityId;

    /** 模型主题 */
    private DsmModelEntityClass modelEntityClass;
    private Long modelEntityClassId;

    /** 表名 */
    @Excel(name = "表名")
    private String tableName;

    /** 注释 */
    @Excel(name = "注释")
    private String tableComment;

    /** SCHEMA */
    @Excel(name = "SCHEMA")
    private String tableSchema;

    /** 版本 */
    private String versionNo;

    /** 数据源 */
    private Long datasourceId;

    /** 状态 */
    @Excel(name = "状态",dictType="mdm_apply_status",combo={"待审核","审核通过","已忽略"})
    private String status;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** SQL脚本 */
    
    private String sqlScript;

    /** 变更脚本 */
    private String sqlChange;

    /** 提交批次 */
    private String batchId;

    /** 变更ID */
    private Long modelEntityTempId;
    
    private SysDatasource sysDatasource;
    
    private List<DsmModelEntityPropTemp> dsmModelEntityProps;
    
    private List<DsmModelEntityShipTemp> dsmModelEntityShips;
    private String partitionType;
    private String partitionColType;
    private String bucketCol;
    private Long bucketNum;
    @Excel(name = "SQL脚本")
    private String createScript;    

    public void setModelEntityId(Long modelEntityId)
    {
        this.modelEntityId = modelEntityId;
    }

    public Long getModelEntityId()
    {
        return modelEntityId;
    }
    public void setModelEntityClassId(Long modelEntityClassId)
    {
        this.modelEntityClassId = modelEntityClassId;
    }

    public Long getModelEntityClassId()
    {
        return modelEntityClassId;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setTableComment(String tableComment)
    {
        this.tableComment = tableComment;
    }

    public String getTableComment()
    {
        return tableComment;
    }
    public void setTableSchema(String tableSchema)
    {
        this.tableSchema = tableSchema;
    }

    public String getTableSchema()
    {
        return tableSchema;
    }
    public void setVersionNo(String versionNo)
    {
        this.versionNo = versionNo;
    }

    public String getVersionNo()
    {
        return versionNo;
    }
    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setSqlScript(String sqlScript)
    {
        this.sqlScript = sqlScript;
    }

    public String getSqlScript()
    {
        return sqlScript;
    }
    public void setSqlChange(String sqlChange)
    {
        this.sqlChange = sqlChange;
    }

    public String getSqlChange()
    {
        return sqlChange;
    }
    public void setBatchId(String batchId)
    {
        this.batchId = batchId;
    }

    public String getBatchId()
    {
        return batchId;
    }
    public void setModelEntityTempId(Long modelEntityTempId)
    {
        this.modelEntityTempId = modelEntityTempId;
    }

    public Long getModelEntityTempId()
    {
        return modelEntityTempId;
    }

    public DsmModelEntityClass getModelEntityClass() {
		return modelEntityClass;
	}

	public void setModelEntityClass(DsmModelEntityClass modelEntityClass) {
		this.modelEntityClass = modelEntityClass;
	}

	public SysDatasource getSysDatasource() {
		return sysDatasource;
	}

	public void setSysDatasource(SysDatasource sysDatasource) {
		this.sysDatasource = sysDatasource;
	}

	public List<DsmModelEntityPropTemp> getDsmModelEntityProps() {
		return dsmModelEntityProps;
	}

	public void setDsmModelEntityProps(List<DsmModelEntityPropTemp> dsmModelEntityProps) {
		this.dsmModelEntityProps = dsmModelEntityProps;
	}

	public List<DsmModelEntityShipTemp> getDsmModelEntityShips() {
		return dsmModelEntityShips;
	}

	public void setDsmModelEntityShips(List<DsmModelEntityShipTemp> dsmModelEntityShips) {
		this.dsmModelEntityShips = dsmModelEntityShips;
	}

	public String getPartitionType() {
		return partitionType;
	}

	public void setPartitionType(String partitionType) {
		this.partitionType = partitionType;
	}

	public String getPartitionColType() {
		return partitionColType;
	}

	public void setPartitionColType(String partitionColType) {
		this.partitionColType = partitionColType;
	}

	public String getBucketCol() {
		return bucketCol;
	}

	public void setBucketCol(String bucketCol) {
		this.bucketCol = bucketCol;
	}

	public Long getBucketNum() {
		return bucketNum;
	}

	public void setBucketNum(Long bucketNum) {
		this.bucketNum = bucketNum;
	}

	public String getCreateScript() {
		return createScript;
	}

	public void setCreateScript(String createScript) {
		this.createScript = createScript;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("modelEntityId", getModelEntityId())
            .append("modelEntityClassId", getModelEntityClassId())
            .append("tableName", getTableName())
            .append("tableComment", getTableComment())
            .append("tableSchema", getTableSchema())
            .append("versionNo", getVersionNo())
            .append("datasourceId", getDatasourceId())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("sqlScript", getSqlScript())
            .append("sqlChange", getSqlChange())
            .append("batchId", getBatchId())
            .append("modelEntityTempId", getModelEntityTempId())
            .toString();
    }
}
