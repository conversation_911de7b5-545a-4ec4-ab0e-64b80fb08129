package com.dqms.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.api.mapper.ApiTemplateLogMapper;
import com.dqms.api.domain.ApiTemplateLog;
import com.dqms.api.service.IApiTemplateLogService;

/**
 * 数据补录日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-26
 */
@Service
public class ApiTemplateLogServiceImpl implements IApiTemplateLogService
{
    @Autowired
    private ApiTemplateLogMapper apiTemplateLogMapper;

    /**
     * 查询数据补录日志
     *
     * @param importOperator 数据补录日志ID
     * @return 数据补录日志
     */
    @Override
    public ApiTemplateLog selectApiTemplateLogById(String logId)
    {
        return apiTemplateLogMapper.selectApiTemplateLogById(logId);
    }

    /**
     * 查询数据补录日志列表
     *
     * @param apiTemplateLog 数据补录日志
     * @return 数据补录日志
     */
    @Override
    public List<ApiTemplateLog> selectApiTemplateLogList(ApiTemplateLog apiTemplateLog)
    {
        return apiTemplateLogMapper.selectApiTemplateLogList(apiTemplateLog);
    }

    /**
     * 新增数据补录日志
     *
     * @param apiTemplateLog 数据补录日志
     * @return 结果
     */
    @Override
    public int insertApiTemplateLog(ApiTemplateLog apiTemplateLog)
    {
        return apiTemplateLogMapper.insertApiTemplateLog(apiTemplateLog);
    }

    /**
     * 修改数据补录日志
     *
     * @param apiTemplateLog 数据补录日志
     * @return 结果
     */
    @Override
    public int updateApiTemplateLog(ApiTemplateLog apiTemplateLog)
    {
        return apiTemplateLogMapper.updateApiTemplateLog(apiTemplateLog);
    }

    /**
     * 批量删除数据补录日志
     *
     * @param importOperators 需要删除的数据补录日志ID
     * @return 结果
     */
    @Override
    public int deleteApiTemplateLogByIds(String[] logId)
    {
        return apiTemplateLogMapper.deleteApiTemplateLogByIds(logId);
    }

    /**
     * 删除数据补录日志信息
     *
     * @param importOperator 数据补录日志ID
     * @return 结果
     */
    @Override
    public int deleteApiTemplateLogById(String logId)
    {
        return apiTemplateLogMapper.deleteApiTemplateLogById(logId);
    }
}
