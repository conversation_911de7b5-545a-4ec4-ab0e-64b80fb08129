package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmMasterDataInstallRule;

/**
 * 配置规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface DsmMasterDataInstallRuleMapper 
{
    /**
     * 查询配置规则
     * 
     * @param masterDataInstallRuleId 配置规则ID
     * @return 配置规则
     */
    public DsmMasterDataInstallRule selectDsmMasterDataInstallRuleById(Long masterDataInstallRuleId);

    /**
     * 查询配置规则列表
     * 
     * @param dsmMasterDataInstallRule 配置规则
     * @return 配置规则集合
     */
    public List<DsmMasterDataInstallRule> selectDsmMasterDataInstallRuleList(DsmMasterDataInstallRule dsmMasterDataInstallRule);

    /**
     * 新增配置规则
     * 
     * @param dsmMasterDataInstallRule 配置规则
     * @return 结果
     */
    public int insertDsmMasterDataInstallRule(DsmMasterDataInstallRule dsmMasterDataInstallRule);

    /**
     * 修改配置规则
     * 
     * @param dsmMasterDataInstallRule 配置规则
     * @return 结果
     */
    public int updateDsmMasterDataInstallRule(DsmMasterDataInstallRule dsmMasterDataInstallRule);

    /**
     * 删除配置规则
     * 
     * @param masterDataInstallRuleId 配置规则ID
     * @return 结果
     */
    public int deleteDsmMasterDataInstallRuleById(Long masterDataInstallRuleId);
    public int deleteDsmMasterDataInstallRuleByMasterDataId(Long masterDataId);

    /**
     * 批量删除配置规则
     * 
     * @param masterDataInstallRuleIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmMasterDataInstallRuleByIds(Long[] masterDataInstallRuleIds);
}
