package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dsm.domain.DsmDimension;
import com.dqms.dsm.domain.DsmMdmRel;
import com.dqms.dsm.domain.DsmStandard;
import com.dqms.dsm.domain.vo.DsmMdmRelVo;
import com.dqms.dsm.mapper.DsmStandardMapper;
import com.dqms.framework.web.service.TokenService;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.mapper.MdmDataEntityPropMapper;
import com.dqms.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmStandardMdmRelMapper;
import com.dqms.dsm.domain.DsmStandardMdmRel;
import com.dqms.dsm.service.IDsmStandardMdmRelService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 标准落地Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Service
public class DsmStandardMdmRelServiceImpl implements IDsmStandardMdmRelService
{
    @Autowired
    private DsmStandardMdmRelMapper dsmStandardMdmRelMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;

    @Autowired
    private MdmDataEntityPropMapper mdmDataEntityPropMapper;
    @Autowired
    DsmStandardMapper dsmStandardMapper;

    /**
     * 查询标准落地
     *
     * @param standardId 标准落地ID
     * @return 标准落地
     */
    @Override
    public DsmStandardMdmRel selectDsmStandardMdmRelById(Long standardId)
    {
        return dsmStandardMdmRelMapper.selectDsmStandardMdmRelById(standardId);
    }

    /**
     * 查询标准落地列表
     *
     * @param dsmStandardMdmRel 标准落地
     * @return 标准落地
     */
    @Override
    public List<DsmStandardMdmRel> selectDsmStandardMdmRelList(DsmStandardMdmRel dsmStandardMdmRel)
    {
        return dsmStandardMdmRelMapper.selectDsmStandardMdmRelList(dsmStandardMdmRel);
    }
    
    /**
     * 查询标准未落地列表
     *
     * @param dsmStandardMdmRel 标准落地
     * @return 标准落地
     */
    @Override
    public List<DsmStandardMdmRel> selectUnDsmStandardMdmRelList(DsmStandardMdmRel dsmStandardMdmRel)
    {
        return dsmStandardMdmRelMapper.selectUnDsmStandardMdmRelList(dsmStandardMdmRel);
    }

    /**
     * 新增标准落地
     *
     * @param dsmStandardMdmRel 标准落地
     * @return 结果
     */
    @Override
    public int insertDsmStandardMdmRel(DsmStandardMdmRel dsmStandardMdmRel)
    {
        return dsmStandardMdmRelMapper.insertDsmStandardMdmRel(dsmStandardMdmRel);
    }

    /**
     * 修改标准落地
     *
     * @param dsmStandardMdmRel 标准落地
     * @return 结果
     */
    @Override
    public int updateDsmStandardMdmRel(DsmStandardMdmRel dsmStandardMdmRel)
    {
        return dsmStandardMdmRelMapper.updateDsmStandardMdmRel(dsmStandardMdmRel);
    }

    /**
     * 批量删除标准落地
     *
     * @param standardIds 需要删除的标准落地ID
     * @return 结果
     */
    @Override
    public int deleteDsmStandardMdmRelByIds(Long[] standardIds)
    {
        return dsmStandardMdmRelMapper.deleteDsmStandardMdmRelByIds(standardIds);
    }

    /**
     * 删除标准落地信息
     *
     * @param standardId 标准落地ID
     * @return 结果
     */
    @Override
    public int deleteDsmStandardMdmRelById(Long standardId)
    {
        return dsmStandardMdmRelMapper.deleteDsmStandardMdmRelById(standardId);
    }
    
    @Override
    public int deleteDsmStandardMdmRelByPk(DsmStandardMdmRel dsmStandardMdmRel)
    {
        return dsmStandardMdmRelMapper.deleteDsmStandardMdmRelByPk(dsmStandardMdmRel);
    }
    @Override
    @Transactional
    public String importDsmStandardMdmRel(List<DsmStandardMdmRel> dsmStandardMdmRelList,boolean isUpdateSupport){
        //获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if(StringUtils.isNull(dsmStandardMdmRelList) || dsmStandardMdmRelList.size()==0){
            throw new CustomException("导入任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        List<DsmStandardMdmRel> dsmStandardMdmRelListForinmport=new ArrayList<>();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int colNum = 2;//导入数据是从第二行开始的
        for (DsmStandardMdmRel vo : dsmStandardMdmRelList)
        {
            try{
                DsmStandardMdmRel t = new DsmStandardMdmRel();
                BeanUtils.copyBeanProp(t, vo);
                boolean checkFlag = true;

                //非空判断
                if(StringUtils.isEmpty(vo.getStandardCode())){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、数据标准与元数据映射 第"+colNum+"行 " + vo.getSystemName() + " 标准编码为空");
                    continue;
                }

                if(StringUtils.isEmpty(vo.getDatasourceName())){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、数据标准与元数据映射 第"+colNum+"行 " + vo.getDatasourceName() + " 元数据数据源为空");
                    continue;
                }else{
                    SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceByName(vo.getDatasourceName());
                    if(StringUtils.isNull(sysDatasource)){
                        failureNum++;
                        checkFlag = false;
                        failureMsg.append("<br/>" + failureNum + "、数据标准与元数据映射 第"+colNum+"行 " + vo.getDatasourceName() + " 数据源未定义");
                        continue;
                    }
                }
                if(vo.getTableName().indexOf(".")==-1) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、源表Schema." +vo.getTableName()+  " 未填写，例库名.表名");
                    continue;
                }
                if(StringUtils.isEmpty(vo.getPropName())){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、数据标准与元数据映射 第"+colNum+"行 " + vo.getPropName() + " 元数据表字段为空");
                    continue;
                }

                SysDatasource sysDatasource=new SysDatasource();
                sysDatasource.setName(vo.getDatasourceName());

                List<SysDatasource> list = sysDatasourceMapper.selectSysDatasourceList(sysDatasource);

                if(StringUtils.isEmpty(list)||list.size()==0){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、数据标准与元数据映射 第"+colNum+"行 " + vo.getDatasourceName() + " 元数据数据源不存在");
                    continue;
                }
                MdmDataEntityProp tarMdmDataEntityProp = new MdmDataEntityProp();
                tarMdmDataEntityProp.setSystemName(list.get(0).getSysSystem().getName());
                tarMdmDataEntityProp.setTableSchema( vo.getTableName().split("\\.")[0]);
                tarMdmDataEntityProp.setTableName(vo.getTableName().split("\\.")[1]);
                tarMdmDataEntityProp.setPropName(vo.getPropName());
                MdmDataEntityProp tarMdmDataEntityPropData = mdmDataEntityPropMapper.getDataByOther(tarMdmDataEntityProp);
                DsmStandard dd=new DsmStandard();
                dd.setStandardCode(vo.getStandardCode());
                List<DsmStandard> l= dsmStandardMapper.selectDsmStandardList(dd);
                if(l.size()!=0){
                    dd= l.get(0);
                }

                vo.setStandardId(dd.getStandardId());

                vo.setPropId(tarMdmDataEntityPropData.getPropId());
                if(isUpdateSupport){
                    dsmStandardMdmRelListForinmport.add(vo);
                }else if(!isUpdateSupport){//新增数据，需先判断是否已存在。存在则不需要添加
                    List<DsmStandardMdmRel> dsmStandardMdmRels = dsmStandardMdmRelMapper.selectDsmStandardMdmRelListSingle(vo);
                    if(dsmStandardMdmRels==null||dsmStandardMdmRels.size()==0){
                        dsmStandardMdmRelListForinmport.add(vo);
                    }
                }


                if(checkFlag){
                    if (!StringUtils.isNull(dsmStandardMdmRelListForinmport)&&dsmStandardMdmRelListForinmport.size()!=0)
                    {
                        successNum++;
                        String msg = StringUtils.format("<br/>{}、数据标准与元数据映射 {} 导入成功",successNum, vo.getDatasourceName());
                        successMsg.append(msg);
                    }

                }

            }catch (Exception e){
                failureNum++;
                String msg = StringUtils.format("<br/>{}、数据标准与元数据映射 {} 导入失败：",failureNum, vo.getSystemName());
                failureMsg.append(msg + e.getMessage());

            }finally {
                colNum++;
            }
        }

        if (failureNum > 0)
        {
            String msg = StringUtils.format("很抱歉，导入失败！共 {} 条数据不正确，错误如下：",failureNum);
            failureMsg.insert(0,msg);
            throw new CustomException(failureMsg.toString());
        }
        else
        {

            if(dsmStandardMdmRelListForinmport!=null&&dsmStandardMdmRelListForinmport.size()>0){
                if (isUpdateSupport) {
                    dsmStandardMdmRelMapper.deleteDsmStandardMdmRelALL(dsmStandardMdmRelListForinmport);
                }
                dsmStandardMdmRelMapper.importInsertDsmStandardMdmRel(dsmStandardMdmRelListForinmport);
            }
            String msg = StringUtils.format("恭喜您，数据已全部导入成功！共 {} 条，数据如下：",successNum);
            successMsg.insert(0,msg);
        }
        return successMsg.toString();
    }

}
