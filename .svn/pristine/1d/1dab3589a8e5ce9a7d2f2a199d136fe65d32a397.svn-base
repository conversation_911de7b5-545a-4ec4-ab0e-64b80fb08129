package com.dqms.api.service;

import java.util.List;
import com.dqms.api.domain.ApiDefineHis;
import com.dqms.api.domain.vo.ApiDefineHisVo;

/**
 * 接口日志Service接口
 * 
 * <AUTHOR>
 * @date 2021-08-03
 */
public interface IApiDefineHisService 
{
    /**
     * 查询接口日志
     * 
     * @param defineHisId 接口日志ID
     * @return 接口日志
     */
    public ApiDefineHis selectApiDefineHisById(Long defineHisId);

    /**
     * 查询接口日志列表
     * 
     * @param apiDefineHis 接口日志
     * @return 接口日志集合
     */
    public List<ApiDefineHis> selectApiDefineHisList(ApiDefineHis apiDefineHis);

    /**
     * 新增接口日志
     * 
     * @param apiDefineHis 接口日志
     * @return 结果
     */
    public int insertApiDefineHis(ApiDefineHis apiDefineHis);

    /**
     * 修改接口日志
     * 
     * @param apiDefineHis 接口日志
     * @return 结果
     */
    public int updateApiDefineHis(ApiDefineHis apiDefineHis);

    /**
     * 批量删除接口日志
     * 
     * @param defineHisIds 需要删除的接口日志ID
     * @return 结果
     */
    public int deleteApiDefineHisByIds(Long[] defineHisIds);

    /**
     * 删除接口日志信息
     * 
     * @param defineHisId 接口日志ID
     * @return 结果
     */
    public int deleteApiDefineHisById(Long defineHisId);
    
    public List<ApiDefineHisVo> getRunFb();
    public List<ApiDefineHisVo> getRunQs();
    public List<ApiDefineHisVo> getRunZl();
    public List<ApiDefineHisVo> getRunHs();
    public List<ApiDefineHis> getRunYc();
}
