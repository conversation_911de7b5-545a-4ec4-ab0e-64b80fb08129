<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscEntityPropMapper">

    <resultMap type="DscEntityProp" id="DscEntityPropResult">
        <result property="dscEntityPropId"    column="dsc_entity_prop_id"    />
        <result property="entityPropId"    column="entity_prop_id"    />
        <result property="gradeClass"    column="grade_class"    />
        <result property="grade"    column="grade"    />
        <result property="gradeValue"    column="gradeValue"    />
        <result property="sensitivitylevel"    column="sensitivity_level"    />
        <result property="sensitivitylevelValue"    column="sensitivity_level_Value"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="indexId"    column="index_id"    />
        <result property="indexName"    column="index_name"    />
        <result property="propId"    column="prop_id"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />
        <result property="tableId"    column="table_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <collection  property="tarSysDepts"   javaType="java.util.List"        resultMap="tdeptResult" />
        <collection  property="tarSystems"   javaType="java.util.List"        resultMap="tSysSystemResult" />
    </resultMap>

    <resultMap type="DscEntityPropVo" id="DscEntityPropVoResult">
        <result property="dscEntityPropId"    column="dsc_entity_prop_id"    />
        <result property="entityPropId"    column="entity_prop_id"    />
        <result property="gradeClass"    column="grade_class"    />
        <result property="grade"    column="grade"    />
        <result property="sensitivitylevel"    column="sensitivity_level"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="indexId"    column="index_id"    />
        <result property="indexName"    column="index_name"    />
        <result property="propId"    column="prop_id"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />
        <result property="tableId"    column="table_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="regDir"    column="reg_dir"    />
        <collection  property="tarSysDepts"   javaType="java.util.List"        resultMap="tdeptResult" />
        <collection  property="tarSystems"   javaType="java.util.List"        resultMap="tSysSystemResult" />
    </resultMap>

    <resultMap id="tdeptResult" type="SysDept">
		<id     property="deptId"   column="td_dept_id"     />
		<result property="deptName" column="td_dept_name"   />
	</resultMap>

    <resultMap type="SysSystem" id="tSysSystemResult">
        <result property="systemId"    column="ts_system_id"    />
        <result property="name"    column="ts_system_name"    />
    </resultMap>

    <sql id="selectDscEntityPropVo">
    SELECT m.prop_id,m.prop_name ,m.prop_comment,e.entity_id AS table_id ,e.table_name,ds.datasource_id ,ds.name AS datasource_name,ss.system_id ,ss.name AS system_name
	,t.dsc_entity_prop_id, t.entity_prop_id, t.grade_class, t.grade, t.sensitivity_level, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time,c.class_name,c.class_name_full
	FROM mdm_data_entity_prop m
	LEFT JOIN mdm_data_entity e ON m.entity_id=e.entity_id
	LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
	LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
	LEFT JOIN sys_system ss ON r.system_id=ss.system_id
	LEFT JOIN dsc_entity_prop t ON m.prop_id=t.entity_prop_id
	LEFT JOIN dsc_entity_prop_class c ON c.entity_prop_class_id=t.grade_class
    </sql>
    <sql id="selectDscEntityProp">
    SELECT
		m.prop_id,
		m.prop_name,
		m.prop_comment,
		e.entity_id AS table_id,
		e.table_name,
		ds.datasource_id,
		ds.NAME AS datasource_name,
		ss.system_id,
		ss.NAME AS system_name,
		t.dsc_entity_prop_id,
		t.entity_prop_id,
		t.grade_class,
		sd.dict_label AS gradeValue,
		t.grade,
		sds.dict_label AS sensitivity_level_Value,
		t.sensitivity_level,
		t.create_by,
		t.update_by,
		t.create_id,
		t.update_id,
		t.create_time,
		t.update_time,
		td.dept_name AS td_dept_name,
		ts.NAME AS ts_system_name,
		td.dept_id AS td_dept_id,
		ts.system_id AS ts_system_id,
		c.class_name,
		c.class_name_full,
		r.reg_dir
	FROM
	dsc_entity_prop t
	LEFT JOIN mdm_data_entity_prop m ON m.prop_id = t.entity_prop_id
	LEFT JOIN mdm_data_entity e ON m.entity_id = e.entity_id
	LEFT JOIN mdm_registry r ON e.registry_id = r.reg_id
	LEFT JOIN sys_datasource ds ON r.datasource_id = ds.datasource_id
	LEFT JOIN sys_system ss ON r.system_id = ss.system_id
	LEFT JOIN sys_dict_data sd ON t.grade = sd.dict_value and sd.dict_type='dsc_entity_prop_grade'
	LEFT JOIN sys_dict_data sds ON t.sensitivity_level = sds.dict_value and sds.dict_type='dsc_entity_prop_sensitivity_level'
	LEFT JOIN dsc_entity_prop_class c ON c.entity_prop_class_id = t.grade_class
	LEFT JOIN dsc_entity_prop_dept tdr ON tdr.entity_prop_id = t.entity_prop_id
	LEFT JOIN sys_dept td ON tdr.dept_id = td.dept_id
	LEFT JOIN dsc_entity_prop_system tsr ON tsr.entity_prop_id = t.entity_prop_id
	LEFT JOIN sys_system ts ON tsr.system_id = ts.system_id
    </sql>

    <select id="selectDscEntityPropList" parameterType="DscEntityProp" resultMap="DscEntityPropResult">
        <include refid="selectDscEntityPropVo"/>
        <where>
            <if test="systemId != null and systemId != ''"> and ss.system_id = #{systemId}</if>
            <if test="datasourceId != null and datasourceId != ''"> and ds.datasource_id = #{datasourceId}</if>
            <if test="tableId != null and tableId != ''">  and e.entity_id = #{tableId}</if>
        	<if test="propId != null and propId != ''">  and m.prop_id = #{propId}</if>
            <if test="grade != null  and grade != ''"> and t.grade = #{grade}</if>
            <if test="sensitivitylevel != null  and sensitivitylevel != ''"> and t.sensitivity_level = #{sensitivitylevel}</if>
            <if test="gradeClass != null and gradeClass != 0"> and (c.ancestors like concat('%,', #{gradeClass}, '%') or c.entity_prop_class_id = #{gradeClass})</if>
            <if test="gradeClass == 0 "> and t.grade_class is null</if>
            and r.del_flag != '2'
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.dsc_entity_prop_id desc,m.prop_id desc
    </select>
    <select id="getDscEntityPropListVo" parameterType="DscEntityPropVo" resultMap="DscEntityPropVoResult">
        <include refid="selectDscEntityProp"/>
        <where>
            
            <if test="systemId != null and systemId != ''"> and ss.system_id = #{systemId}</if>
            <if test="datasourceId != null and datasourceId != ''"> and ds.datasource_id = #{datasourceId}</if>
            <if test="tableId != null and tableId != ''">  and e.entity_id = #{tableId}</if>
            <if test="propId != null and propId != ''">  and m.prop_id = #{propId}</if>
            <if test="gradeClass != null and gradeClass != 0"> and (c.ancestors like concat('%,', #{gradeClass}, '%') or c.entity_prop_class_id = #{gradeClass})</if>
            <if test="gradeClass == 0 "> and t.grade_class is null</if>
            and r.del_flag != '2'
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.dsc_entity_prop_id desc,m.prop_id desc
    </select>

    <select id="getDscEntityPropById" parameterType="Long" resultMap="DscEntityPropResult">
        SELECT grade_class,dsc_entity_prop_id from dsc_entity_prop where entity_prop_id = #{propId}
    </select>

    <select id="selectDscEntityPropById" parameterType="Long" resultMap="DscEntityPropResult">
	    SELECT m.prop_id,m.prop_name ,m.prop_comment,e.entity_id AS table_id ,e.table_name,ds.datasource_id ,ds.name AS datasource_name,ss.system_id ,ss.name AS system_name
		,t.dsc_entity_prop_id, t.entity_prop_id, t.grade_class, t.grade, t.sensitivity_level, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time,
	        td.dept_name AS td_dept_name,ts.name AS ts_system_name,td.dept_id AS td_dept_id,ts.system_id AS ts_system_id,c.class_name,c.class_name_full
		FROM mdm_data_entity_prop m
		LEFT JOIN mdm_data_entity e ON m.entity_id=e.entity_id
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
		LEFT JOIN sys_system ss ON r.system_id=ss.system_id
		LEFT JOIN dsc_entity_prop t ON m.prop_id=t.entity_prop_id
	    LEFT JOIN dsc_entity_prop_class c ON c.entity_prop_class_id=t.grade_class
        LEFT JOIN dsc_entity_prop_dept tdr ON tdr.entity_prop_id=t.entity_prop_id LEFT JOIN sys_dept td ON tdr.dept_id=td.dept_id
        LEFT JOIN dsc_entity_prop_system tsr ON tsr.entity_prop_id=t.entity_prop_id LEFT JOIN sys_system ts ON tsr.system_id=ts.system_id
        where t.dsc_entity_prop_id = #{dscEntityPropId}
        	and r.del_flag != '2'
    </select>

    <insert id="insertDscEntityProp" parameterType="DscEntityProp" useGeneratedKeys="true" keyProperty="dscEntityPropId">
        insert into dsc_entity_prop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityPropId != null">entity_prop_id,</if>
            <if test="gradeClass != null">grade_class,</if>
            <if test="grade != null and grade != ''">grade,</if>
            <if test="sensitivitylevel != null and sensitivitylevel != ''">sensitivity_level,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityPropId != null">#{entityPropId},</if>
            <if test="gradeClass != null">#{gradeClass},</if>
            <if test="grade != null and grade != ''">#{grade},</if>
            <if test="sensitivitylevel != null and sensitivitylevel != ''">#{sensitivitylevel},</if>

            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDscEntityProp" parameterType="DscEntityProp">
        update dsc_entity_prop
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityPropId != null">entity_prop_id = #{entityPropId},</if>
            <if test="gradeClass != null">grade_class = #{gradeClass},</if>
            <if test="grade != null and grade != ''">grade = #{grade},</if>
            <if test="sensitivitylevel != null and sensitivitylevel != ''">sensitivity_level= #{sensitivitylevel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where dsc_entity_prop_id = #{dscEntityPropId}
    </update>

    <delete id="deleteDscEntityPropById" parameterType="Long">
        delete from dsc_entity_prop where dsc_entity_prop_id = #{dscEntityPropId}
    </delete>

    <delete id="deleteDscEntityPropByIds" parameterType="String">
        delete from dsc_entity_prop where dsc_entity_prop_id in
        <foreach item="dscEntityPropId" collection="array" open="(" separator="," close=")">
            #{dscEntityPropId}
        </foreach>
    </delete>

    <select id="selectDscEntityProp" resultMap="DscEntityPropResult">
        SELECT t.dsc_entity_prop_id,s.system_id FROM dsc_entity_prop t 
		LEFT JOIN mdm_data_entity_prop p ON t.entity_prop_id=p.prop_id
		LEFT JOIN mdm_data_entity e ON p.entity_id=e.entity_id
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN dsc_entity_prop_system s ON p.prop_id=s.entity_prop_id AND s.system_id=#{systemId}
		WHERE r.datasource_id=#{datasourceId} 
		AND r.reg_dir=#{regDir}
		AND r.reg_name=#{regName}
		AND p.prop_name=#{propName}
		AND t.grade IN (2,3,4)
		and r.del_flag != '2'
    </select>    
</mapper>