package com.dqms.task.mapper;

import java.util.List;
import com.dqms.task.domain.EtlTaskSchedule;
import com.dqms.task.domain.vo.EtlTaskScheduleVo;

/**
 * 任务调度计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-23
 */
public interface EtlTaskScheduleMapper 
{
    /**
     * 查询任务调度计划
     * 
     * @param taskScheduleId 任务调度计划ID
     * @return 任务调度计划
     */
    public EtlTaskSchedule selectEtlTaskScheduleById(Long taskScheduleId);

    /**
     * 查询任务调度计划列表
     * 
     * @param etlTaskSchedule 任务调度计划
     * @return 任务调度计划集合
     */
    public List<EtlTaskSchedule> selectEtlTaskScheduleList(EtlTaskSchedule etlTaskSchedule);
    public List<EtlTaskSchedule> selectEtlTaskScheduleListByPage(EtlTaskSchedule etlTaskSchedule);
    /**
     * 新增任务调度计划
     * 
     * @param etlTaskSchedule 任务调度计划
     * @return 结果
     */
    public int insertEtlTaskSchedule(EtlTaskSchedule etlTaskSchedule);

    /**
     * 修改任务调度计划
     * 
     * @param etlTaskSchedule 任务调度计划
     * @return 结果
     */
    public int updateEtlTaskSchedule(EtlTaskSchedule etlTaskSchedule);

    /**
     * 删除任务调度计划
     * 
     * @param taskScheduleId 任务调度计划ID
     * @return 结果
     */
    public int deleteEtlTaskScheduleById(Long taskScheduleId);

    /**
     * 批量删除任务调度计划
     * 
     * @param taskScheduleIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskScheduleByIds(Long[] taskScheduleIds);

    public List<EtlTaskScheduleVo> selectEtlTaskScheduleVoList(EtlTaskSchedule etlTaskSchedule);
}
