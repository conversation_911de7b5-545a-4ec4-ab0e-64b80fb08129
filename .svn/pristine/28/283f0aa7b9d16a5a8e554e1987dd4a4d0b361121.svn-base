package com.dqms.dsm.service.impl;

import java.util.List;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmMasterDataRuleMapper;
import com.dqms.dsm.domain.DsmMasterDataRule;
import com.dqms.dsm.service.IDsmMasterDataRuleService;

/**
 * 主数据规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@Service
public class DsmMasterDataRuleServiceImpl implements IDsmMasterDataRuleService
{
    @Autowired
    private DsmMasterDataRuleMapper dsmMasterDataRuleMapper;

    /**
     * 查询主数据规则
     *
     * @param masterDataRuleId 主数据规则ID
     * @return 主数据规则
     */
    @Override
    public DsmMasterDataRule selectDsmMasterDataRuleById(Long masterDataRuleId)
    {
        return dsmMasterDataRuleMapper.selectDsmMasterDataRuleById(masterDataRuleId);
    }

    /**
     * 查询主数据规则列表
     *
     * @param dsmMasterDataRule 主数据规则
     * @return 主数据规则
     */
    @Override
    public List<DsmMasterDataRule> selectDsmMasterDataRuleList(DsmMasterDataRule dsmMasterDataRule)
    {
        return dsmMasterDataRuleMapper.selectDsmMasterDataRuleList(dsmMasterDataRule);
    }

    /**
     * 新增主数据规则
     *
     * @param dsmMasterDataRule 主数据规则
     * @return 结果
     */
    @Override
    public int insertDsmMasterDataRule(DsmMasterDataRule dsmMasterDataRule)
    {
    	dsmMasterDataRule.setCreateTime(DateUtils.getNowDate());
    	dsmMasterDataRule.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmMasterDataRule.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmMasterDataRule.setUpdateTime(DateUtils.getNowDate());
    	dsmMasterDataRule.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmMasterDataRule.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        return dsmMasterDataRuleMapper.insertDsmMasterDataRule(dsmMasterDataRule);
    }

    /**
     * 修改主数据规则
     *
     * @param dsmMasterDataRule 主数据规则
     * @return 结果
     */
    @Override
    public int updateDsmMasterDataRule(DsmMasterDataRule dsmMasterDataRule)
    {
    	dsmMasterDataRule.setUpdateTime(DateUtils.getNowDate());
    	dsmMasterDataRule.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmMasterDataRule.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        return dsmMasterDataRuleMapper.updateDsmMasterDataRule(dsmMasterDataRule);
    }

    /**
     * 批量删除主数据规则
     *
     * @param masterDataRuleIds 需要删除的主数据规则ID
     * @return 结果
     */
    @Override
    public int deleteDsmMasterDataRuleByIds(Long[] masterDataRuleIds)
    {
        return dsmMasterDataRuleMapper.deleteDsmMasterDataRuleByIds(masterDataRuleIds);
    }

    /**
     * 删除主数据规则信息
     *
     * @param masterDataRuleId 主数据规则ID
     * @return 结果
     */
    @Override
    public int deleteDsmMasterDataRuleById(Long masterDataRuleId)
    {
        return dsmMasterDataRuleMapper.deleteDsmMasterDataRuleById(masterDataRuleId);
    }
}
