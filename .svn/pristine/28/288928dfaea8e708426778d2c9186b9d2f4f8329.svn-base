import request from '@/utils/request'

// 查询任务管理列表
export function listTask(query) {
  return request({
    url: '/task/task/list',
    method: 'get',
    params: query
  })
}

// 查询任务管理详细
export function getTask(taskId) {
  return request({
    url: '/task/task/' + taskId,
    method: 'get'
  })
}

// 新增任务管理
export function addTask(data) {
  return request({
    url: '/task/task',
    method: 'post',
    data: data
  })
}

// 修改任务管理
export function updateTask(data) {
  return request({
    url: '/task/task',
    method: 'put',
    data: data
  })
}

// 删除任务管理
export function delTask(taskId) {
  return request({
    url: '/task/task/' + taskId,
    method: 'delete'
  })
}

// 导出任务管理
export function exportTask(query) {
  return request({
    url: '/task/task/export',
    method: 'get',
    params: query
  })
}

export function listPreTask(query) {
	  return request({
	    url: '/task/task/listPre',
	    method: 'get',
	    params: query
	  })
}

//查询任务管理列表
export function listWTask(query) {
  return request({
    url: '/task/task/listW',
    method: 'get',
    params: query
  })
}

// 查询任务管理列表
export function listChainTask(query) {
  return request({
    url: '/task/task/listChain',
    method: 'get',
    params: query
  })
}

export function listGroupTask(query) {
  return request({
    url: '/task/task/listGroup',
    method: 'get',
    params: query
  })
}

// 下载任务导入模板
export function importTemplate() {
  return request({
    url: '/task/task/importTemplate',
    method: 'get'
  })
}

// 查询任务管理列表
export function listTaskPage(query) {
  return request({
    url: '/task/task/listPage',
    method: 'get',
    params: query
  })
}

export function listTaskRelByGroup(query) {
  return request({
    url: '/task/task/listTaskRelByGroup',
    method: 'get',
    params: query
  })
}