<template>
  <div class="app-container" style="height:100%;">
    <el-row type="flex" justify="start" align="top" style="height:calc(100vh - 130px);">
		<el-col :span="4" :xs="24" style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);" v-show="false">
			  <el-card shadow="hover">内容</el-card>
			  <el-checkbox-group v-model="checkList">
			    <el-checkbox v-for="item in dsmModelEntityPropList" @change="checked=>changeProp(checked,item)" :key="item.propName" :label="item.propName" :value="item.propName" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #909399;background-color:#909399;" border ></el-checkbox>
			  </el-checkbox-group>
		</el-col>
		<el-col :span="18" :xs="24" border style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);padding-top:20px;padding-right:20px;">
	       <div v-for="(item, key) in propMap">
	       		<el-form size="small" label-width="120px" ref="formO" :model="formO" :rules="rules">
	       		<el-col :span="item[1].width" :xs="item[1].width">
	       		<div @click="divClick(item[1])">
		       		<el-form-item :label="item[1].propComment" :prop="item[1].propName">
		       			<el-input v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='input'">
		       				<template slot="prepend" v-if="item[1].prefix!=null && item[1].prefix!=''">{{item[1].prefix}}{{formO.user_id}}</template>
		       				<template slot="append" v-if="item[1].postfix!=null && item[1].postfix!=''">{{item[1].postfix}}</template>
		       			</el-input>
		       		    <el-select v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" style="width:100%" :clearable="item[1].clearable=='Y'" v-if="item[1].type==='select'">
				         <el-option
				           v-for="dict in item[1].options"
				           :key="dict.detailCode"
				           :label="dict.detailName"
				           :value="dict.detailCode"
				         />
				        </el-select>
						<el-radio-group v-model="formO[item[1].propName]" v-if="item[1].type==='radio'">
						  <el-radio :label="dict.detailCode" v-for="dict in item[1].options" >{{dict.detailName}}</el-radio>
						</el-radio-group>
		       		</el-form-item>
		       	</div>	
		       	</el-col>	
	       		</el-form>
	       </div>
		</el-col>
		<el-col :span="6" :xs="24" border style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);">
			<el-tabs type="border-card">
			  <el-tab-pane label="组件属性" style="height:calc(100vh - 202px);">
				  <el-form size="small" label-width="90px">
					<el-form-item label="组件类型">
					  <el-select v-model="form.type" placeholder="请选择" style="width:100%" disabled>
					    <el-option-group
					      v-for="group in typeList"
					      :key="group.label"
					      :label="group.label">
					      <el-option
					        v-for="item in group.options"
					        :key="item.value"
					        :label="item.label"
					        :value="item.value">
					      </el-option>
					    </el-option-group>
					  </el-select>
			        </el-form-item>
			        
					<el-form-item label="名称">
				        <el-input
				          v-model="form.propName"
				          placeholder="请输入名称"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery" disabled/>
			        </el-form-item>		
			        
					<el-form-item label="注释">
				        <el-input
				          v-model="form.propComment"
				          placeholder="请输入注释"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery" disabled/>
			        </el-form-item>
			        
					<el-form-item label="占位提示">
				        <el-input
				          v-model="form.placeholder"
				          placeholder="请输入占位提示"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery" disabled/>
			        </el-form-item>	
			        
					<el-form-item label="组件宽度">
						<el-slider
					      v-model="form.width"
					      :step="1" :max="24"
					      show-stops disabled>
					    </el-slider>
			        </el-form-item>

					<el-form-item label="默认值">
				        <el-input
				          v-model="form.defaultValue"
				          placeholder="请输入默认值"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery" disabled/>
			        </el-form-item>
			        
					<el-form-item label="最大长度">
				        <el-input-number v-model="form.columnSize" :step="1" style="width:100%" @change="changeRules('1')" disabled></el-input-number>
			        </el-form-item>

					<el-form-item label="标签宽度">
				        <el-input-number v-model="form.labelWidth" :step="1" style="width:100%" disabled></el-input-number>
			        </el-form-item>		

					<el-form-item label="前缀">
				        <el-input
				          v-model="form.prefix"
				          placeholder="请输入前缀"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery" disabled/>
			        </el-form-item>
					<el-form-item label="后缀">
				        <el-input
				          v-model="form.postfix"
				          placeholder="请输入后缀"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery" disabled/>
			        </el-form-item>

					<el-form-item label="能否清空">
					  <el-switch
					    v-model="form.clearable"
					    active-value="Y"
					    inactive-value="N" disabled>
					  </el-switch>
			        </el-form-item>
					<el-form-item label="是否必填">
					  <el-switch
					    v-model="form.nullable"
					    active-value="Y"
					    inactive-value="N"
					    @change="changeRules('1')" disabled>
					  </el-switch>
			        </el-form-item>

			        <el-form-item label="维度字典">
			            <el-select
						    v-model="form.relId"
						    filterable
						    remote
						    reserve-keyword
						    placeholder="请输入维度字典"
						    :remote-method="remoteMethod"
						      style="width:100%" @change="optionChange($event)" disabled>
						    <el-option
						      v-for="item in relOptions"
						      :key="item.dimensionId"
						      :label="item.dimensionName"
						      :value="item.dimensionId">
						    </el-option>
						  </el-select>
			        </el-form-item>
			        <el-form-item style="margin-left:0px;">
		        		<el-button type="primary" round style="width:100%" @click="submitForm">保存</el-button>	
		        	</el-form-item>	        			        			        			        			        	        			        			        			        			        			        	        
				 </el-form>
			  </el-tab-pane>
			  <el-tab-pane label="关联规则" style="height:calc(100vh - 202px);">
				  <el-checkbox-group v-model="ruleList">
				    <el-checkbox v-for="item in dsmMasterDataRuleList" v-if="item.type==='Q'"  onclick="return false" :key="item.masterDataRuleId" :label="item.name" :value="item.masterDataRuleId" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #606266;background-color:#606266;" border ></el-checkbox>
				    <el-checkbox v-for="item in dsmMasterDataRuleList" v-if="item.type==='H'"  onclick="return false" :key="item.masterDataRuleId" :label="item.name" :value="item.masterDataRuleId" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #303133;background-color:#303133;" border ></el-checkbox>
				  </el-checkbox-group>
			  </el-tab-pane>
			</el-tabs>
		</el-col>
	</el-row>
  </div>
</template>

<script>
import { listDsmMasterData, getDsmMasterData, delDsmMasterData, addDsmMasterData, updateDsmMasterData, exportDsmMasterData ,validateRules,maxValidateRules,installDsmMasterData,saveDsmMasterData} from "@/api/dsm/dsmMasterData";
import { listDsmModelEntity,getDsmModelEntity} from "@/api/dsm/dsmModelEntity";
import { listDsmMasterDataRule} from "@/api/dsm/dsmMasterDataRule";
import { listDsmDimension } from "@/api/dsm/dsmDimension";
import { listDsmDimensionDetail } from "@/api/dsm/dsmDimensionDetail";
import { getToken } from "@/utils/auth";
import draggable from 'vuedraggable';
import { isArray } from 'util';

export default {
  name: "DsmMasterDataDeploy",
  components: {
	  draggable
  },
  data() {
    return {
    	masterDataId:null,
    	form:{},
    	formO:{},
    	dsmModelEntityPropList:[],
    	dsmMasterDataRuleList:[],
    	checkList:[],
    	ruleList:[],
    	propMap:new Map(),
    	prop:"",
    	relOptions:[],
    	typeList:[{
    		label: '输入型组件',
            options: [{
              value: 'input',
              label: '单行文本'
            }, {
              value: 'textarea',
              label: '多行文本'
            }, {
              value: 'number',
              label: '计数器'
            }, {
              value: 'password',
              label: '密码'
            }]
    	},{
    		label: '选择型组件',
            options: [{
              value: 'select',
              label: '下拉选择'
            }, {
              value: 'radio',
              label: '单选'
            }, {
              value: 'checkbox',
              label: '多选'
            }, {
              value: 'switch',
              label: '开关'
            }, {
              value: '滑块 ',
              label: 'slider'
            }, {
              value: 'timePicker',
              label: '时间选择'
            }, {
              value: 'timePickerArea',
              label: '时间范围'
            }, {
              value: 'datePicker',
              label: '日期选择'
            }, {
              value: 'datePickerArea',
              label: '日期范围'
            }, {
              value: 'upload',
              label: '上传'
            }]
    	}],
    	rules: {
    	}
    };
  },
  created() {
	  this.masterDataId = this.$route.params && this.$route.params.masterDataId;
	  this.getList();
	  const detailParams = {pageNum: 1,pageSize: 10000};
      listDsmMasterDataRule(detailParams).then(response => {
        this.dsmMasterDataRuleList = response.rows;
      });
  },
  methods: {
      
    getList() {
    	getDsmMasterData(this.masterDataId).then(response => {
            this.form = response.data;
            getDsmModelEntity(this.form.modelEntityId).then(response => { 
            	this.dsmModelEntityPropList = response.data.dsmModelEntityProps;
            })
            for(let i=0;i<this.form.dsmMasterDataInstalls.length;i++){
            	
            	this.$set(this.rules, this.form.dsmMasterDataInstalls[i].propName , this.form.dsmMasterDataInstalls[i].dsmMasterDataRules); 
            	let roles=[];
            	if(this.form.dsmMasterDataInstalls[i].dsmMasterDataRules!=null){roles=this.form.dsmMasterDataInstalls[i].dsmMasterDataRules;}
            	if(this.form.dsmMasterDataInstalls[i].nullable=='Y'){
            		if(this.form.dsmMasterDataInstalls[i].rules==null){this.form.dsmMasterDataInstalls[i].rules=[];}
            		roles.push({ masterDataRuleId:'nullable',required: true, message: this.form.dsmMasterDataInstalls[i].propComment+"不能为空", trigger: "blur" });
            	}
            	if(this.form.dsmMasterDataInstalls[i].columnSize!=null&&this.form.dsmMasterDataInstalls[i].columnSize>0){
            		if(this.form.dsmMasterDataInstalls[i].rules==null){this.form.dsmMasterDataInstalls[i].rules=[];}
            		roles.push({ masterDataRuleId:'columnSize',max: this.form.dsmMasterDataInstalls[i].columnSize, message: this.form.dsmMasterDataInstalls[i].propComment+"不能超过"+this.form.dsmMasterDataInstalls[i].columnSize, trigger: "blur" });
            	}
            	if(this.form.dsmMasterDataInstalls[i].rules==null){this.form.dsmMasterDataInstalls[i].rules=[];}
        		this.form.dsmMasterDataInstalls[i].rules.push(roles);
        		this.$set(this.rules, this.form.dsmMasterDataInstalls[i].propName , roles); 
            	this.propMap.set(this.form.dsmMasterDataInstalls[i].propName,this.form.dsmMasterDataInstalls[i]);
            }
          })
    },
    changeProp(checked,item) {
    	if(checked){
    		this.$set(this.formO, item.propName , ''); 
    		this.prop=item.propName;
    		this.propMap.set(item.propName,{propName:item.propName,propComment:item.propComment,type:"input",nullable:item.nullable,placeholder:"请输入"+(item.propComment!=undefined?item.propComment:""),width:24,columnSize:item.columnSize,labelWidth:120,options:[],rules:[]});
    		this.form=this.propMap.get(item.propName);
    		if(item.columnSize!=null&&item.columnSize>0){
    			this.changeRules('1');
    		}
    	}else{
    		this.propMap.delete(item.propName);
    	}
    	
    },
    optionChange(value) {
    	this.propMap.get(this.form.propName).relName=this.relOptions.find(val=>val.dimensionId==value).dimensionName;console.log(this.propMap);
        const detailParams = {
          dimensionId: value,
          pageNum: 1,
          pageSize: 1000
        };
        listDsmDimensionDetail(detailParams).then(response => {
        	this.form.options = response.rows;
        });  
    },
    divClick(item){
    	this.form=this.propMap.get(item.propName);
    	this.relOptions = [{dimensionId:this.form.relId,dimensionName:this.form.relName}];
    	this.ruleList=[];
    	if(this.rules[item.propName]!=null){
    		let rules=this.rules[item.propName];
        	for(let i=0;i<rules.length;i++){
        		if(rules[i].masterDataRuleId!='nullable'&&rules[i].masterDataRuleId!='columnSize'){
        			this.ruleList.push(rules[i].name);
        		}
        	}
    	}
    	
    },
    // 模糊搜索
    remoteMethod(query) {
        if (query !== '') {
          setTimeout(() => {
            let relParams = {dimensionName:query}
            listDsmDimension(relParams).then(response => {
	          this.relOptions = response.rows;
	        });
          }, 200);
        } else {
          this.relOptions = [];
        }
    },
    changeRules(type,checked,item){
    	let rules=[];
    	if(this.rules[this.form.propName]!=null){
    		rules=this.rules[this.form.propName];
    	}
    	
    	if(type=='1'){
    		for(let i=0;i<rules.length;i++){
    			if(rules[i].masterDataRuleId=='nullable'||rules[i].masterDataRuleId=='columnSize'){
    				rules.splice(i,1);
    			}
    		}
    	}
    	
    	if(type=='1'&&this.form.nullable=='Y'){
    		rules.push({ masterDataRuleId:'nullable',required: true, message: this.form.propComment+"不能为空", trigger: "blur" });
    	}
    	
    	if(type=='1'&&this.form.columnSize!=null&&this.form.columnSize!=''){
    		rules.push({ masterDataRuleId:'columnSize',max: this.form.columnSize, message: this.form.propComment+"不能超过"+this.form.columnSize, trigger: "blur" });
    	}
    	
    	if(type=='2'&&checked){
    		rules.push({ masterDataRuleId:item.masterDataRuleId,reg: item.rule, message: "数据不满足'"+item.name+"'", trigger: "blur" ,validator:validateRules,name:item.name});
    	}else if(type=='2'){
    		for(let i=0;i<rules.length;i++){
    			if(rules[i].masterDataRuleId==item.masterDataRuleId){
    				rules.splice(i,1);
    			}
    		}
    	}
    	this.$set(this.rules, this.form.propName , rules); 
    },
    submitForm(){
    	this.formO.masterDataId=this.masterDataId;
    	let list =[];
    	const that=this;
    	this.propMap.forEach(function(value,key){
    		console.log(that.formO[key]);
    		list.push(value);
    	});
    	let forms={
    		masterDataId:this.masterDataId,
    		dsmMasterDataInstalls:list
    	};
    	
    	 saveDsmMasterData(this.formO).then(response => {
           this.msgSuccess("新增成功");
        });	  
    	
    }
    
  },
  computed: {

  }
};
</script>
<style>
	html,body{
	  height: 100%;
	}
</style>