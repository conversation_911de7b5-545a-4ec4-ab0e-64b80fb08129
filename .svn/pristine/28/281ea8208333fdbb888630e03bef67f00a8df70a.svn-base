import request from "@/utils/request";

// 查询检查规则管理列表
export function listCate(query) {
  return request({
    url: "/dqm/cate/list",
    method: "get",
    params: query
  });
}

// 查询检查规则管理详细
export function getCate(validationRuleCateId) {
  return request({
    url: "/dqm/cate/" + validationRuleCateId,
    method: "get"
  });
}

// 获取表
export function findEntityAndSystem() {
  return request({
    url: "/mdm/mdmDataEntityShip/findEntityAndSystem",
    method: "get"
  });
}

// 获取条件
export function getConditionById(validationMouldId) {
  return request({
    url: "/dqm/cate/getConditionById/" + validationMouldId.validationMouldId,
    method: "get"
    // params: validationMouldId
  });
}

// 获取字段
export function findAllPropAndEntityAndSystem() {
  return request({
    url: "/mdm/mdmDataEntityShip/findAllPropAndEntityAndSystem",
    method: "get"
  });
}
// 获取模板
export function getMould() {
  return request({
    url: "/dqm/cate/getMould",
    method: "get"
  });
}

// 订阅
export function getSubscription(validationRuleCateIds) {
  return request({
    url: "/dqm/cate/addSubscription/" + validationRuleCateIds,
    method: "delete"
  });
}

// 分类
export function dqmtreeselect() {
  return request({
    url: "/dqm/dqmValidationClass/treeselect",
    method: "get"
  });
}

// 新增检查规则管理
export function addCate(data) {
  return request({
    url: "/dqm/cate",
    method: "post",
    data: data
  });
}

// 执行
export function executorDqmRuleCate(data) {
  return request({
    url: "/dqm/cate/executorDqmRuleCate",
    method: "post",
    data: data
  });
}

// 修改检查规则管理
export function updateCate(data) {
  return request({
    url: "/dqm/cate",
    method: "put",
    data: data
  });
}

// 删除检查规则管理
export function delCate(validationRuleCateId) {
  return request({
    url: "/dqm/cate/" + validationRuleCateId,
    method: "delete"
  });
}

// 导出检查规则管理
export function exportCate(query) {
  return request({
    url: "/dqm/cate/export",
    method: "get",
    params: query
  });
}

export function dqmlist(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/dqmlist',
    method: 'get',
    params: query
  })
}

export function unDqmlist(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/unDqmlist',
    method: 'get',
    params: query
  })
}
