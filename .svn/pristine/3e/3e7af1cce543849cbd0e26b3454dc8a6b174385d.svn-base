<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dqm.mapper.DqmValidationMouldParameterMapper">

    <resultMap type="DqmValidationMouldParameter" id="DqmValidationMouldParameterResult">
        <result property="validationMouldParameterId"    column="validation_mould_parameter_ID"    />
        <result property="validationMouldId"    column="validation_mould_id"    />
        <result property="parameterName"    column="parameter_name"    />
        <result property="parameterType"    column="parameter_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDqmValidationMouldParameterVo">
        select validation_mould_parameter_ID, validation_mould_id, parameter_name, parameter_type, create_by, update_by, create_id, update_id, create_time, update_time from dqm_validation_mould_parameter t
    </sql>

    <select id="selectDqmValidationMouldParameterList" parameterType="DqmValidationMouldParameter" resultMap="DqmValidationMouldParameterResult">
        <include refid="selectDqmValidationMouldParameterVo"/>
        <where>
            <if test="validationMouldId != null "> and validation_mould_id = #{validationMouldId}</if>
            <if test="parameterName != null  and parameterName != ''"> and parameter_name like concat('%', #{parameterName}, '%')</if>
            <if test="parameterType != null  and parameterType != ''"> and parameter_type = #{parameterType}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectGetDataById" parameterType="String" resultMap="DqmValidationMouldParameterResult">
        <include refid="selectDqmValidationMouldParameterVo"/>
        <where>
            <if test="validationMouldId != null "> and validation_mould_id = #{validationMouldId}</if>
        </where>
    </select>

    <select id="selectDqmValidationMouldParameterById" parameterType="Integer" resultMap="DqmValidationMouldParameterResult">
        <include refid="selectDqmValidationMouldParameterVo"/>
        where validation_mould_parameter_ID = #{validationMouldParameterId}
    </select>

    <insert id="insertDqmValidationMouldParameter" parameterType="DqmValidationMouldParameter" useGeneratedKeys="true" keyProperty="validationMouldParameterId">
        insert into dqm_validation_mould_parameter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="validationMouldId != null">validation_mould_id,</if>
            <if test="parameterName != null">parameter_name,</if>
            <if test="parameterType != null">parameter_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="validationMouldId != null">#{validationMouldId},</if>
            <if test="parameterName != null">#{parameterName},</if>
            <if test="parameterType != null">#{parameterType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDqmValidationMouldParameter" parameterType="DqmValidationMouldParameter">
        update dqm_validation_mould_parameter
        <trim prefix="SET" suffixOverrides=",">
            <if test="validationMouldId != null">validation_mould_id = #{validationMouldId},</if>
            <if test="parameterName != null">parameter_name = #{parameterName},</if>
            <if test="parameterType != null">parameter_type = #{parameterType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where validation_mould_parameter_ID = #{validationMouldParameterId}
    </update>

    <delete id="deleteDqmValidationMouldParameterById" parameterType="Integer">
        delete from dqm_validation_mould_parameter where validation_mould_parameter_ID = #{validationMouldParameterId}
    </delete>

    <delete id="deleteDqmValidationMouldParameterByIds" parameterType="String">
        delete from dqm_validation_mould_parameter where validation_mould_parameter_ID in
        <foreach item="validationMouldParameterId" collection="array" open="(" separator="," close=")">
            #{validationMouldParameterId}
        </foreach>
    </delete>

    <select id="selectConditionById" resultMap="DqmValidationMouldParameterResult">
        <include refid="selectDqmValidationMouldParameterVo"/>
        <where>
            <if test="validationMouldId != null "> and validation_mould_id = #{validationMouldId}</if>
            <if test="parameterType != null  and parameterType != ''"> and parameter_type = #{parameterType}</if>
        </where>
    </select>
</mapper>
