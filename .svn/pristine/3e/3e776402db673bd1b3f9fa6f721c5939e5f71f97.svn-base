package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmCheck;

/**
 * 标准对标Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-31
 */
public interface DsmCheckMapper 
{
    /**
     * 查询标准对标
     * 
     * @param checkId 标准对标ID
     * @return 标准对标
     */
    public DsmCheck selectDsmCheckById(Long checkId);

    /**
     * 查询标准对标列表
     * 
     * @param dsmCheck 标准对标
     * @return 标准对标集合
     */
    public List<DsmCheck> selectDsmCheckList(DsmCheck dsmCheck);

    /**
     * 新增标准对标
     * 
     * @param dsmCheck 标准对标
     * @return 结果
     */
    public int insertDsmCheck(DsmCheck dsmCheck);

    /**
     * 修改标准对标
     * 
     * @param dsmCheck 标准对标
     * @return 结果
     */
    public int updateDsmCheck(DsmCheck dsmCheck);

    /**
     * 删除标准对标
     * 
     * @param checkId 标准对标ID
     * @return 结果
     */
    public int deleteDsmCheckById(Long checkId);

    /**
     * 批量删除标准对标
     * 
     * @param checkIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmCheckByIds(Long[] checkId);
    
    /**
     * 批量删除标准对标
     * 
     * @param checkIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmCheckAll();
}
