
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="表名称" prop="regName">
        <el-input
          v-model="queryParams.regName"
          placeholder="请输入表代码/脚本名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批状态" prop="appStatus">
        <el-select v-model="queryParams.appStatus" placeholder="请选择审批状态" clearable size="small">
          <el-option
            v-for="dict in appStatusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="变更类型" prop="changeType">
        <el-select v-model="queryParams.changeType" placeholder="请选择变更类型" clearable size="small">
          <el-option
            v-for="dict in changeTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="源系统" prop="systemId">
        <el-select v-model="queryParams.systemId" placeholder="请选择所属系统" clearable size="small" filterable>
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>
       <el-form-item label="元类型" prop="metaType">
          <el-select
            v-model="queryParams.metaType"
            placeholder="请选择元类型"
            clearable
            size="small"
            filterable
          >
            <el-option
              v-for="dict in metaTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
      <el-form-item label="数据源" prop="datasourceId">
        <el-select
          v-model="queryParams.datasourceId"
          placeholder="请选择数据源"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采集时间" prop="acqTime">
        <el-date-picker clearable size="small"
          v-model="queryParams.acqTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择采集时间"
          style="width:93.5%"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="danger" icon="el-icon-smoking" size="mini" @click="batchChange" :disabled="multiple">批量处理</el-button>
      </el-form-item>
    </el-form>



    <el-table v-loading="loading" border :data="mdmDataMonitorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="元数据变更监控ID" align="center" prop="monitorId" />-->
      <el-table-column label="源系统" align="center" prop="systemName" width="155" />
      <el-table-column label="数据源" align="center" prop="datasourceName" width="155"/>
      <el-table-column label="数据源类型" align="center"  prop="dsType" :formatter="dsTypeFormat" width="100"/>
      <el-table-column label="相关表/文件" align="center" prop="regName" width="260" />
      <el-table-column label="审批状态" align="center" prop="appStatus" :formatter="appStatusFormat" />
      <el-table-column label="变更类型" align="center" prop="changeType" :formatter="changeTypeFormat" />
      <el-table-column label="采集时间" align="center" prop="acqTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.acqTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="变更时间" align="center" prop="appTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.appTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批人名称" align="center" prop="appUserName" width="100"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
        <template slot-scope="scope"  v-if="scope.row.appStatus==1">

          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleChanges(scope.row)"
            v-hasPermi="['mdm:mdmDataMonitor:change']"
            v-if="scope.row.metaType==1||scope.row.metaType==2"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleFtpChanges(scope.row)"
            v-if="scope.row.metaType==3||scope.row.metaType==4"
            v-hasPermi="['mdm:mdmDataMonitor:change']"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleIgnore(scope.row)"
            v-hasPermi="['mdm:mdmDataMonitor:change']"
          >忽略</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改元数据变更监控对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
<!--        <el-form-item label="元数据注册ID" prop="regId">-->
<!--          <el-input v-model="form.regId" placeholder="请输入元数据注册ID" clearable/>-->
<!--        </el-form-item>-->
        <el-form-item label="审批状态" prop="appStatus">
          <el-select v-model="form.appStatus" placeholder="请选择审批状态" clearable>
            <el-option
              v-for="dict in appStatusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="变更类型" prop="changeType">
          <el-select v-model="form.changeType" placeholder="请选择变更类型" clearable>
            <el-option
              v-for="dict in changeTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审批人名称" prop="appUserName">
          <el-input v-model="form.appUserName" placeholder="请输入审批人名称" clearable/>
        </el-form-item>
        <el-form-item label="变更时间" prop="appTime">
          <el-date-picker clearable size="small"
            v-model="form.appTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择变更时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审核人编号" prop="appUserId">
          <el-input v-model="form.appUserId" placeholder="请输入审核人编号" clearable/>
        </el-form-item>
        <el-form-item label="批次" prop="batchId">
          <el-input v-model="form.batchId" placeholder="请输入批次" clearable/>
        </el-form-item>
        <el-form-item label="修改人ID" prop="updateId">
          <el-input v-model="form.updateId" placeholder="请输入修改人ID" clearable/>
        </el-form-item>
        <el-form-item label="创建人ID" prop="createId">
          <el-input v-model="form.createId" placeholder="请输入创建人ID" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="openHandle" class="handleDialogClass" width="1200px"  append-to-body >
        <el-tabs v-model="activeName" @tab-click="handleClick" >
          <el-tab-pane label="实体信息" name="first">
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;font-weight: bold"  >变更后实体信息</el-tag>
                <el-table
                  :data="entityListNew"
                  border
                  :show-header="false"
                  style="width: 96%"
                  :cell-style="newEntityCellStyle">
<!--                  :header-cell-class-name="entityHeaderClassName">-->
<!--                  <el-table-column label="原实体信息" >-->
                    <el-table-column
                      label="属性">
                      <template slot-scope="scope">
                        <span style="font-weight:bold;">{{ scope.row.attrName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="value"
                      label="值">
                    </el-table-column>
                    <el-table-column
                      prop="changeType"
                      label="是否为改变字段" v-if="false">
                    </el-table-column>
<!--                  </el-table-column>-->
                </el-table>
              </el-col>

              <el-col :span="12">
                <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;margin-left: 20px;font-weight: bold"  >原实体信息（<span style="color:#1FB0FF">蓝色</span>为修改,<span style="color:#4FC414">绿色</span>为新增,<span style="color:#FF4050">红色</span>为删除）</el-tag>
                <el-table
                  :data="entityListOld"
                  border
                  :show-header="false"
                  style="width: 96%;margin-left: 20px"
                  :cell-style="oldEntityCellStyle">
<!--                  :header-cell-class-name="entityHeaderClassName">-->
<!--                  <el-table-column label="变更后实体信息（红色为修改）" >-->
                    <el-table-column
                      label="属性">
                      <template slot-scope="scope">
                        <span style="font-weight:bold;">{{ scope.row.attrName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="value"
                      label="值">
                    </el-table-column>
                    <el-table-column
                      prop="changeType"
                      label="是否为改变字段" v-if="false">
                    </el-table-column>
<!--                  </el-table-column>-->
                </el-table>
              </el-col>
            </el-row>

          </el-tab-pane>


          <el-tab-pane label="属性信息" name="second">

            <el-row type="flex" justify="start" align="top" >
              <el-col :span="12">
                <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;font-weight: bold"  >变更后字段信息</el-tag>
                <el-table
                  ref="leftTable"
                  max-height="500px"
                  :data="entityPropListNew"
                  border
                  style="width: 96%"
                  :cell-style="newEntityPropCellStyle">
  <!--                <el-table-column label="原表字段信息" >-->
                      <el-table-column
                        fixed="left"
                        prop="propName"
                        label="属性名称"
                        min-width="120px"
                        :show-overflow-tooltip="true"
                        v-if="columns[0].visible"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="orderId"
                        label="排序"
                        :show-overflow-tooltip="true"
                        v-if="columns[1].visible"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="propComment"
                        label="属性注释"
                        min-width="120px"
                        :show-overflow-tooltip="true"
                        v-if="columns[2].visible"
                      >
                      </el-table-column>
                      <el-table-column
                        v-if="columns[3].visible"
                        prop="isPriKey"
                        label="是否主键">
                      </el-table-column>
                      <el-table-column
                        v-if="columns[4].visible"
                        prop="nullable"
                        label="可否为空">
                      </el-table-column>
                      <el-table-column
                        v-if="columns[5].visible"
                        prop="columnSize"
                        label="列大小">
                      </el-table-column>
                      <el-table-column
                        v-if="columns[6].visible"
                        prop="decimalDigits"
                        label="小数位数">
                      </el-table-column>
                      <el-table-column
                        v-if="columns[7].visible"
                        prop="dataType"
                        label="数据类型"
                        min-width="100px"
                        :show-overflow-tooltip="true"
                      >
                      </el-table-column>
                      <el-table-column
                        v-if="columns[8].visible"
                        prop="defaultValue"
                        label="默认值"
                        :show-overflow-tooltip="true"
                      >
                      </el-table-column>
  <!--                </el-table-column>-->
                </el-table>
              </el-col>

              <el-col :span="12">
                <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;margin-left: 20px;font-weight: bold"  >原表字段信息（<span style="color:#1FB0FF">蓝色</span>为修改,<span style="color:#4FC414">绿色</span>为新增,<span style="color:#FF4050">红色</span>为删除</el-tag>
                <right-toolbar  :columns="columns" :refreshShow="false" :searchShow="false"></right-toolbar>
                <el-table
                  ref="rightTable"
                  max-height="500px"
                  :data="entityPropListOld"
                  border
                  style="width: 96%;margin-left: 20px"
                  :cell-style="oldEntityPropCellStyle">
  <!--                <el-table-column label="变更后字段信息（红色为修改,蓝色为新增,-为删除）" >-->
                    <el-table-column
                      fixed
                      prop="propName"
                      label="属性名称"
                      min-width="120px"
                      :show-overflow-tooltip="true"
                      v-if="columns[0].visible"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="orderId"
                      label="排序"
                      :show-overflow-tooltip="true"
                      v-if="columns[1].visible"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="propComment"
                      label="属性注释"
                      min-width="120px"
                      :show-overflow-tooltip="true"
                      v-if="columns[2].visible"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="columns[3].visible"
                      prop="isPriKey"
                      label="是否主键">
                    </el-table-column>
                    <el-table-column
                      v-if="columns[4].visible"
                      prop="nullable"
                      label="可否为空">
                    </el-table-column>
                    <el-table-column
                      v-if="columns[5].visible"
                      prop="columnSize"
                      label="列大小">
                    </el-table-column>
                    <el-table-column
                      v-if="columns[6].visible"
                      prop="decimalDigits"
                      label="小数位数">
                    </el-table-column>
                    <el-table-column
                      v-if="columns[7].visible"
                      prop="dataType"
                      label="数据类型"
                      min-width="100px"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="columns[8].visible"
                      prop="defaultValue"
                      label="默认值"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>
  <!--                </el-table-column>-->
                </el-table>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitChange">确 定</el-button>
        <el-button @click="cancelHandle">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="脚本对比信息" :visible.sync="ftpOpen" width="1200px"  append-to-body>
      <el-row>
        <el-col :span="12">
          <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;font-weight: bold"  >变更后脚本信息</el-tag>
        </el-col>
        <el-col :span="12">
          <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;margin-left: 6%;font-weight: bold"  >原脚本信息</el-tag>
        </el-col>
        <el-col :span="24">
          <div id="mergeView"></div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary"
          @click="ftpOpen=false,$store.commit('SET_NEWENTITY',newEntity),$router.push({ path:`/assets/damMdmAnalyse`})"
        >血缘分析</el-button>
        <el-button type="primary" @click="submitFtpChange">确 定</el-button>
        <el-button @click="ftpOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMdmDataMonitor, getMdmDataMonitor, delMdmDataMonitor, addMdmDataMonitor, updateMdmDataMonitor, exportMdmDataMonitor,change,batchChange,getEntityNewAndOld,ingore } from "@/api/mdm/mdmDataMonitor";
import { listDatasourceAll, listSystem } from '@/api/basic/datasource'
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/addon/merge/merge.js'
import 'codemirror/addon/merge/merge.css'
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/sql-hint.js';
import {codemirror} from 'vue-codemirror'
// 引入主题,配置后生效
import 'codemirror/theme/monokai.css'
import "codemirror/theme/idea.css";
//引入语言,配置后生效
import 'codemirror/mode/sql/sql.js'
import 'codemirror/addon/hint/show-hint.js';

// import 'codemirror/addon/display/fullscreen.css';
// import 'codemirror/mode/clike/clike.js';
// import 'codemirror/addon/display/autorefresh.js';
// import 'codemirror/addon/edit/matchbrackets.js';
//
// import 'codemirror/addon/selection/active-line.js';
// import 'codemirror/addon/display/fullscreen.js';

import DiffMatchPatch from 'diff-match-patch'
window.diff_match_patch = DiffMatchPatch
window.DIFF_DELETE = -1
window.DIFF_INSERT = 1
window.DIFF_EQUAL = 0
export default {
  name: "MdmDataMonitor",
  components: {
  },
  data() {
    return {
      scrollflag: false,
      flag: false,
      activeName: 'first',
      entityListNew:[],
      entityListOld:[],
      entityPropListNew:[],
      entityPropListOld:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 元类型字典
      metaTypeOptions: [],
      // 总条数
      total: 0,
      // 元数据变更监控表格数据
      mdmDataMonitorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      ftpOpen: false,
      ftpForm: {},
      open: false,
      // 是否显示处理弹出层
      openHandle: false,
      // 数据源类型字典
      dsTypeOptions: [],
      // 审批状态字典
      appStatusOptions: [],
      // 变更类型字典
      changeTypeOptions: [],
      //应用系统选项
      systemOptions: [],
      //数据源选项
      dataSourceOptions: [],
      row:{},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appStatus: null,
        changeType: null,
        appTime: null,
        systemId:null,
        metaType:null,
        regName:null
      },
      // 列信息
      columns: [
        { key: 0, label: `属性名称`, visible: true },
        { key: 1, label: `排序`, visible: true },
        { key: 2, label: `属性注释`, visible: true },
        { key: 3, label: `是否主键`, visible: true },
        { key: 4, label: `可否为空`, visible: true },
        { key: 5, label: `列大小`, visible: true },
        { key: 6, label: `小数位数`, visible: true },
        { key: 7, label: `数据类型`, visible: true },
        { key: 8, label: `默认值`, visible: true }
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },

  created() {
    this.getList();
     this.getDicts("mdm_meta_type").then(response => {
          this.metaTypeOptions = response.data;
        });
    this.getDicts("mdm_apply_status").then(response => {
      this.appStatusOptions = response.data;
    });
    this.getDicts("mdm_change_type").then(response => {
      this.changeTypeOptions = response.data;
    });
    this.getDicts("ds_type").then(response => {
      this.dsTypeOptions = response.data;
    });
    this.getSystem();
    this.getDataSource();
  },
  methods: {

    entityHeaderClassName(row){
      if(row.column.label!='原实体信息'&&row.column.label!='变更后实体信息（红色为修改）'){
        return 'entity-display';
      }else{
        return  'entity-title';
      }
    },
    entityPropHeaderClassName(row){
      if(row.column.label=='原表字段信息'||row.column.label=='变更后字段信息（红色为修改,蓝色为新增,-为删除）'){
        return 'entity-prop-title';
      }
    },
    oldEntityPropCellStyle(row){
      if(row.row.operType=='2'){
        let modifyFields =  row.row.modifyFields.split(',');
        if(modifyFields.indexOf(row.column.property)!=-1){
          return 'text-decoration:line-through;color: #FF4050;';
        }
      }
      if(row.row.operType=='3'){
        return 'text-decoration:line-through;color: #FF4050;';
      }
    },
    newEntityPropCellStyle(row){
      if(row.row.operType=='2'){
        let modifyFields =  row.row.modifyFields.split(',');
        if(modifyFields.indexOf(row.column.property)!=-1){
          return 'color:#1FB0FF';
        }
      }
      if(row.row.operType=='3'){
        return 'text-decoration:line-through;color: #FF4050;';
      }
      if(row.row.operType=='1'){
        return 'color:#4FC414';
      }
      return '';
    },
    newEntityCellStyle(row){
      if (row.column.label=='值'&&row.row.changeType == '1') {
        return 'color:#1FB0FF';
      }
      if (row.column.label=='值'&&row.row.changeType == '2') {
        return 'color:#4FC414';
      }
      return '';
    },
    oldEntityCellStyle(row){
      if (row.column.label=='值'&&row.row.changeType == '1') {
        return 'text-decoration:line-through;color: #FF4050;';
      }
      if (row.column.label=='值'&&row.row.changeType == '2') {
        return 'color:#4FC414';
      }
      return '';
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    /** 查询元数据变更监控列表 */
    getList() {
      this.loading = true;
      listMdmDataMonitor(this.queryParams).then(response => {
        this.mdmDataMonitorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 数据源类型字典翻译
    dsTypeFormat(row, column) {
      return this.selectDictLabel(this.dsTypeOptions, row.dsType);
    },
    // 元类型字典翻译
        metaTypeFormat(row, column) {
          return this.selectDictLabel(this.metaTypeOptions, row.metaType);
        },
    // 审批状态字典翻译
    appStatusFormat(row, column) {
      return this.selectDictLabel(this.appStatusOptions, row.appStatus);
    },
    // 变更类型字典翻译
    changeTypeFormat(row, column) {
      return this.selectDictLabel(this.changeTypeOptions, row.changeType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    cancelHandle() {
      this.activeName = 'first';
      this.entityListNew=[];
      this.entityListOld=[];
      this.entityPropListNew=[];
      this.entityPropListOld=[];
      this.openHandle = false;
    },
    // 表单重置
    reset() {
      this.form = {
        monitorId: null,
        regId: null,
        appStatus: null,
        changeType: null,
        appUserName: null,
        appTime: null,
        acqTime: null,
        appUserId: null,
        batchId: null,
        createBy: null,
        updateId: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        createId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.monitorId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加元数据变更监控";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const monitorId = row.monitorId || this.ids
      getMdmDataMonitor(monitorId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改元数据变更监控";
      });
    },
    /** 忽略元数据变更 */
    handleIgnore(row) {
      let sel = this;
      this.$confirm('是否确认忽略变更元数据表名为"' + row.regName + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        sel.loading = true;
        return ingore(row);
      }).then((response) => {
        sel.msgSuccess(response.msg);
        sel.loading = false;
        sel.getList();
      }).catch(function(error){
        sel.loading = false;
      });
    },
    /** 处理元数据操作 */
    handleChanges(row) {
      this.row = row;
      let name = "";
      name = row.metaType == "2" ? "视图" : "表";
      getEntityNewAndOld(row).then(response => {
        const entityDataNew =response.data.newEntity==null ? [] : response.data.newEntity;
        const entityDataOld =response.data.oldEntity==null ? [] :response.data.oldEntity;
        let newEntityProp = response.data.newEntityProp==null ? [] :response.data.newEntityProp;
        let oldEntityProp = response.data.oldEntityProp==null ? [] :response.data.oldEntityProp;
        if(row.changeType=='3'){
            this.entityListOld=[
              {
                attrName: name+'名',
                value: entityDataOld.tableName,
                changeType: '1'
              },
              {
                attrName: name+'注释',
                value: entityDataOld.tableComment,
                changeType: '1'
              },
              {
                attrName: 'SCHEMA',
                value: entityDataOld.tableSchema,
                changeType: '1'
              }
            ];
            this.entityListNew=[{
              attrName: name+'名',
              value: '-',
              changeType: ''
            },
              {
                attrName: name+'注释',
                value: '-',
                changeType: ''
              },
              {
                attrName: 'SCHEMA',
                value: '-',
                changeType: ''
              }
            ];

            for (let i = 0; i < oldEntityProp.length; i++) {
              oldEntityProp[i].operType = '3';
              newEntityProp[i]={propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"};
            }
          }

          if(row.changeType=='1'){
            this.entityListOld=[
              {
                attrName: name+'名',
                value: '-',
                changeType: ''
              },
              {
                attrName: name+'注释',
                value: '-',
                changeType: ''
              },
              {
                attrName: 'SCHEMA',
                value: '-',
                changeType: ''
              }
            ];
            this.entityListNew=[{
              attrName: name+'名',
              value: entityDataNew.tableName,
              changeType: '2'
            },
              {
                attrName: name+'注释',
                value: entityDataNew.tableComment,
                changeType: '2'
              },
              {
                attrName: 'SCHEMA',
                value: entityDataNew.tableSchema,
                changeType: '2'
              }
            ];

            for (let i = 0; i < newEntityProp.length; i++) {
              oldEntityProp[i]={propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"};
            }
          }

          if(row.changeType=='2'){
            let tableNameChangeType = '0';
            let tableCommentChangeType = '0';
            let tableSchemaChangeType = '0';
            if(entityDataNew.modifyFields!=null){
              const modifyFields = entityDataNew.modifyFields.split(",");
              if(modifyFields.indexOf("tableName")!=-1){
                tableNameChangeType = '1'
              }
              if(modifyFields.indexOf("tableComment")!=-1){
                tableCommentChangeType = '1'
              }
              if(modifyFields.indexOf("tableSchema")!=-1){
                tableSchemaChangeType = '1'
              }
            }
            this.entityListNew=[{
              attrName: name+'名',
              value: entityDataNew.tableName,
              changeType: tableNameChangeType
            },
              {
                attrName: name+'注释',
                value: entityDataNew.tableComment,
                changeType: tableCommentChangeType
              },
              {
                attrName: 'SCHEMA',
                value: entityDataNew.tableSchema,
                changeType: tableSchemaChangeType
              }
            ];

            this.entityListOld=[
              {
                attrName: name+'名',
                value: entityDataOld.tableName,
                changeType: tableNameChangeType
              },
              {
                attrName: name+'注释',
                value: entityDataOld.tableComment,
                changeType: tableCommentChangeType
              },
              {
                attrName: 'SCHEMA',
                value: entityDataOld.tableSchema,
                changeType: tableSchemaChangeType
              }
            ];
            let newPropNames = [];
            let oldPropNames = [];
            for (let i = 0; i < newEntityProp.length; i++) {
              newPropNames.push(newEntityProp[i].propName);
            }
            for (let i = 0; i < oldEntityProp.length; i++) {
              oldPropNames.push(oldEntityProp[i].propName);
            }
            let maxLength = 0;
            let isFlag = true;
            if(newEntityProp.length>=oldEntityProp.length){
              maxLength = newEntityProp.length;
            }else{
              maxLength = oldEntityProp.length;
            }

            for (let i = 0; i < oldEntityProp.length; i++) {
              for (let j = 0; j < newEntityProp.length; j++) {
                if(oldEntityProp[i].propName==newEntityProp[j].propName){
                  oldEntityProp[i].operType = newEntityProp[j].operType;
                  oldEntityProp[i].modifyFields = newEntityProp[j].modifyFields;
                }
              }
            }
            let falg =null;
            for (var i = 0; i < maxLength; i++) {
              try {
                falg =false;
                //字段在老数组里面没有，代表新增
                if(oldPropNames.indexOf(newEntityProp[i].propName)==-1){
                  oldEntityProp.splice(i,0,{propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"});
                  falg =true;
                }
                if(falg){
                  i++;
                  //字段在新数组里面没有，代表删除
                  if(newPropNames.indexOf(oldEntityProp[i].propName)==-1){
                    oldEntityProp[i].operType = '3'
                    newEntityProp.splice(i,0,{propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"});
                  }else{
                    i--;
                  }
                }else{
                  if(newPropNames.indexOf(oldEntityProp[i].propName)==-1){
                    oldEntityProp[i].operType = '3'
                    newEntityProp.splice(i,0,{propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"});
                  }
                }
                if(newEntityProp.length>=oldEntityProp.length){
                  maxLength = newEntityProp.length;
                }else{
                  maxLength = oldEntityProp.length;
                }
              }catch (e){
                if(newEntityProp.length>=oldEntityProp.length){
                  if(!falg){
                    oldEntityProp.splice(i,0,{propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"});
                  }else{
                    i--;
                  }
                }else{
                  oldEntityProp[i].operType = '3'
                  newEntityProp.splice(i,0,{propName:"-",propComment:"-",isPriKey:"-",nullable:"-",columnSize:"-",decimalDigits:"-",dataType:"-",defaultValue:"-"});
                }
              }
            }
          }
        this.entityPropListNew = newEntityProp;
        this.entityPropListOld = oldEntityProp;
        this.title = "元数据采集对比详细信息";
        this.openHandle=true;

        this.$nextTick(()=>{
          this.$refs.leftTable.bodyWrapper.addEventListener('scroll', (res) => {
            let height = res.target
            if (!this.scrollflag) {
              this.$refs.rightTable.$el.querySelector('.el-table__body-wrapper').scrollTop = height.scrollTop;
              this.$refs.rightTable.$el.querySelector('.el-table__body-wrapper').scrollLeft = height.scrollLeft;
            }
          },true);

          this.$refs.leftTable.bodyWrapper.addEventListener('mouseover', (res) => {
            this.scrollflag =false;
          },true);


          this.$refs.rightTable.bodyWrapper.addEventListener('scroll', (res) => {
            let height = res.target
            if (this.scrollflag) {
              this.$refs.leftTable.$el.querySelector('.el-table__body-wrapper').scrollTop = height.scrollTop;
              this.$refs.leftTable.$el.querySelector('.el-table__body-wrapper').scrollLeft = height.scrollLeft;
            }
          },true);

          this.$refs.rightTable.bodyWrapper.addEventListener('mouseover', (res) => {
            this.scrollflag =true;
          },true);
        })


      })
    },

    handleFtpChanges(row) {
      this.row = row;
      getEntityNewAndOld(row).then(response => {
        if(response.data.newEntity.sqlScripts){
          this.newEntity = response.data.newEntity.sqlScripts
          // this.$store.commit('SET_NEWENTITY',response.data.newEntity.sqlScripts)
        }

        this.ftpOpen = true;
        this.$nextTick(function() {
          const entityDataNew =response.data.newEntity==null ? [] : response.data.newEntity;
          const entityDataOld =response.data.oldEntity==null ? [] :response.data.oldEntity;
          // this.ftpForm = entityDataOld;
          const target = document.getElementById("mergeView");
          target.innerHTML = "";
          let editor =  CodeMirror.MergeView(target, {
            value: entityDataNew.length==0?" ":entityDataNew.sqlScripts,//本次内容
            origLeft: null,
            orig: entityDataOld.length==0?" ":entityDataOld.sqlScripts,//上次内容
            lineNumbers: true,//显示行号
            revertButtons:true,
            styleActiveLine: true,
            matchBrackets: true,
            collapseIdentical: false,
            mode: "text/x-sql",
            highlightDifferences: true,
            // autoRefresh: true,
            connect: 'align',
            theme: 'monokai',
            readOnly: false,//只读 不可修改
            hintOptions: {
              completeSingle: true // 当匹配只有一项的时候是否自动补全
            },
            extraKeys: {"Ctrl": "autocomplete"},
          })
          // this.Editor.edit.on("cursorActivity", (editor, change) => {
          //     editor.showHint()
          // });


        })
      })
    },
    submitChange(){
      let sel = this;
      this.$confirm('是否确认变更元数据表名为"' + this.row.regName + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        sel.loading = true;
        sel.openHandle=false;
        return change(sel.row);
      }).then((response) => {
        sel.msgSuccess(response.msg);
        sel.loading = false;
        sel.getList();
      }).catch(function(error){
        sel.loading = false;
      });
    },
    submitFtpChange(){
      let sel = this;
      this.$confirm('是否确认变更元数据表名为"' + this.row.regName + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        sel.loading = true;
        sel.ftpOpen=false;
        return change(sel.row);
      }).then((response) => {
        sel.msgSuccess(response.msg);
        sel.loading = false;
        sel.getList();
      }).catch(function(error){
        sel.loading = false;
      });
    },

    batchChange(){
      let sel = this;
      const monitorIds = this.ids;
      this.$confirm('是否确认要批量处理变更?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        sel.loading = true;
        return batchChange(monitorIds);
      }).then((response) => {
        sel.msgSuccess(response.msg);
        sel.loading = false;
        sel.getList();
      }).catch(function(error){
        sel.loading = false;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.monitorId != null) {
            updateMdmDataMonitor(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMdmDataMonitor(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const monitorIds = row.monitorId || this.ids;
      this.$confirm('是否确认删除元数据变更监控编号为"' + monitorIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delMdmDataMonitor(monitorIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有元数据变更监控数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportMdmDataMonitor(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    /** 获取应用系统 */
    getSystem() {
      listSystem().then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
  }
};
</script>
<style type="scss">
.CodeMirror pre.CodeMirror-line, .CodeMirror pre.CodeMirror-line-like {
  line-height: 22px;
}
.CodeMirror-merge-r-chunk {
  background: #38380d !important;
}
.CodeMirror-merge, .CodeMirror-merge .CodeMirror {
  height: 550px !important;
}

 .handleDialogClass .el-dialog__body{
    padding: 0px 20px;
 }

.entity-prop-title {
  font-size: 15px !important;
}
 .entity-title {
   font-size: 15px !important;
 }
.entity-display{

  display: none;
}
.CodeMirror-hints {
  z-index: 8888 !important;
}
</style>
