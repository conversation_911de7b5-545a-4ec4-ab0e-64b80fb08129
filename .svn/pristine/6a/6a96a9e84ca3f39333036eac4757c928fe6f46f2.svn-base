package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 指标关系对象 dsm_index_index
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public class DsmIndexIndex extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标关系ID */
    private Long indexIndexId;

    /** 原始指标 */
    @Excel(name = "原始指标")
    private Long souIndexId;

    /** 目标指标 */
    @Excel(name = "目标指标")
    private Long tarIndexId;

    public void setIndexIndexId(Long indexIndexId)
    {
        this.indexIndexId = indexIndexId;
    }

    public Long getIndexIndexId()
    {
        return indexIndexId;
    }
    public void setSouIndexId(Long souIndexId)
    {
        this.souIndexId = souIndexId;
    }

    public Long getSouIndexId()
    {
        return souIndexId;
    }
    public void setTarIndexId(Long tarIndexId)
    {
        this.tarIndexId = tarIndexId;
    }

    public Long getTarIndexId()
    {
        return tarIndexId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("indexIndexId", getIndexIndexId())
            .append("souIndexId", getSouIndexId())
            .append("tarIndexId", getTarIndexId())
            .toString();
    }
}
