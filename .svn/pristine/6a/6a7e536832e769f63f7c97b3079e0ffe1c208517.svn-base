<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dic.mapper.DicManualDataBatchMapper">
    
    <resultMap type="DicManualDataBatch" id="DicManualDataBatchResult">
        <result property="batchId"    column="batch_id"    />
        <result property="manualDataId"    column="manual_data_id"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="applyType"    column="apply_type"    />
        <result property="applyCount"    column="apply_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDicManualDataBatchVo">
        select batch_id, manual_data_id, apply_status, create_by, update_by, create_id, update_id, create_time, update_time,apply_type,apply_count from dic_manual_data_batch
    </sql>

    <select id="selectDicManualDataBatchList" parameterType="DicManualDataBatch" resultMap="DicManualDataBatchResult">
        <include refid="selectDicManualDataBatchVo"/>
        <where>  
            <if test="manualDataId != null "> and manual_data_id = #{manualDataId}</if>
            <if test="applyStatus != null  and applyStatus != ''"> and apply_status = #{applyStatus}</if>
        </where>
        order by apply_status asc ,create_time desc
    </select>
    
    <select id="selectDicManualDataBatchById" parameterType="String" resultMap="DicManualDataBatchResult">
        <include refid="selectDicManualDataBatchVo"/>
        where batch_id = #{batchId}
    </select>
        
    <insert id="insertDicManualDataBatch" parameterType="DicManualDataBatch">
        insert into dic_manual_data_batch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchId != null">batch_id,</if>
            <if test="manualDataId != null">manual_data_id,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="applyType != null">apply_type,</if>
            <if test="applyCount != null">apply_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchId != null">#{batchId},</if>
            <if test="manualDataId != null">#{manualDataId},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="applyType != null">#{applyType},</if>
            <if test="applyCount != null">#{applyCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDicManualDataBatch" parameterType="DicManualDataBatch">
        update dic_manual_data_batch
        <trim prefix="SET" suffixOverrides=",">
            <if test="manualDataId != null">manual_data_id = #{manualDataId},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="applyType != null">apply_type = #{applyType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where batch_id = #{batchId}
    </update>

    <delete id="deleteDicManualDataBatchById" parameterType="String">
        delete from dic_manual_data_batch where batch_id = #{batchId}
    </delete>

    <delete id="deleteDicManualDataBatchByIds" parameterType="String">
        delete from dic_manual_data_batch where batch_id in 
        <foreach item="batchId" collection="array" open="(" separator="," close=")">
            #{batchId}
        </foreach>
    </delete>
</mapper>