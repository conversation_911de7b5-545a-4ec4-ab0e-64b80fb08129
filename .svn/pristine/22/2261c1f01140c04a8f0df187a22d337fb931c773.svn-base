<template>
  <div class="app-container">
	<el-row class="el-row-inline">
	  <el-col :span="4" v-for="(item, index) in taskGroupList" :key="'taskGroup'+index" >
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <img src="@/assets/images/group.png" class="image">
	      <div style="padding: 14px;">
	        <span style="color: #228B22;">{{item.taskGroupName}}</span>
	        <div class="bottom clearfix">
	          <time class="time" >
	          <span ><i class="el-icon-user-solid"></i>{{item.taskGroupCode}}</span>
	          </time>
	          <el-button type="text" class="button" @click="handleDelete(item)"><i class="el-icon-delete"></i></el-button>
	          <el-button type="text" class="button" @click="handleUpdate(item)"><i class="el-icon-edit"></i></el-button>
	        </div>
	      </div>
	    </el-card>
	  </el-col>
	  <el-col :span="4" :key="'taskGroup'">
		  <el-card :body-style="{ padding: '0px' }" class="card" shadow="hover">
		      <img src="@/assets/images/add.png" class="image" @click="handleAdd">
		      <div style="padding: 14px;">
		        <span @click="handleAdd" style="color: #A9A9A9;">点击扩展</span>
		        <div class="bottom clearfix">
		          <time class="time"> 操作</time>
		          <el-tooltip class="item" effect="dark" content="添加新分组" placement="bottom">
		          <el-button type="text" class="button" @click="handleAdd"><i class="el-icon-circle-plus-outline"></i></el-button>
		          </el-tooltip>
		          <el-tooltip class="item" effect="dark" content="可视化分组关系设置" placement="bottom">
		          <el-button type="text" class="button" @click="handleMap"><i class="el-icon-share"></i></el-button>
		          </el-tooltip>
		        </div>
		      </div>
		    </el-card>
	   </el-col>
	</el-row>

    <!-- 添加或修改任务分组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="编码" prop="taskGroupCode">
          <el-input v-model="form.taskGroupCode" placeholder="请输入编码" clearable/>
        </el-form-item>
        <el-form-item label="名称" prop="taskGroupName">
          <el-input v-model="form.taskGroupName" placeholder="请输入名称" clearable/>
        </el-form-item>
        <el-form-item label="并发线程数" prop="threadNum">
          <el-input-number v-model="form.threadNum"  :min="0" :max="100" label="请输入并发线程数"></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-row>
    <el-drawer
		  title="操作栏" :with-header="false"
		  :visible.sync="drawer"
		  :direction="direction"
		  :before-close="handleClose" :modal=false :wrapperClosable=true :modal-append-to-body=false size='100%'>
		  <el-row type="flex" justify="start" align="top"  >
			<div class="container-fluid" style="margin-top:0px;width:100%;">
		    	<div id="scalingToolBar" style="position:absolute;padding-top:10;right:0;height:100;cursor:pointer;z-index: 99;">
		    	<el-col :span="4" >
		    	<el-input size="mini" v-model="queryParams.name" placeholder="请输入关键字" />
		    	</el-col>
		    	<el-col :span="20" >
		    	<el-tooltip class="item" effect="dark" content="根据任务名称进行居中定位" placement="bottom">
		    	<el-button type="primary" size="mini" @click="findTo()"><i class="el-icon-search"></i>查询</el-button>
		    	</el-tooltip>
		    	<el-button type="primary" size="mini" @click="zoomTo('+')"><i class="el-icon-zoom-in"></i>放大</el-button>
		    	<el-button type="primary" size="mini" @click="zoomTo('-')"><i class="el-icon-zoom-out"></i>缩小</el-button>
		    	<el-tooltip class="item" effect="dark" content="刷新界面并修改节点摆放位置" placement="bottom">
		    	<el-button type="primary" size="mini" @click="layoutTo('default')"><i class="el-icon-view"></i>设计</el-button>
		    	</el-tooltip>
		    	<el-tooltip class="item" effect="dark" content="连续点击两个节点确定关系" placement="bottom">
		    	<el-button type="primary" size="mini" @click="forTo('addEdge')"><i class="el-icon-share"></i>连线</el-button>
		    	</el-tooltip>
		    	<el-tooltip class="item" effect="dark" content="智能力导向布局" placement="bottom">
		    	<el-button type="primary" size="mini" @click="layoutTo('force')"><i class="el-icon-paperclip"></i>力导</el-button>
		    	</el-tooltip>
		    	<el-tooltip class="item" effect="dark" content="智能环形布局" placement="bottom">
		    	<el-button type="primary" size="mini" @click="layoutTo('circular')"><i class="el-icon-help"></i>环形</el-button>
		    	</el-tooltip>
		    	<el-tooltip class="item" effect="dark" content="智能层次布局" placement="bottom">
		    	<el-button type="primary" size="mini" @click="layoutTo('dagre')"><i class="el-icon-s-unfold"></i>层次</el-button>
		    	</el-tooltip>
		    	<el-tooltip class="item" effect="dark" content="智能格子布局" placement="bottom">
		    	<el-button type="primary" size="mini" @click="layoutTo('grid')"><i class="el-icon-menu"></i>格子</el-button>
		    	</el-tooltip>
		    	<el-tooltip class="item" effect="dark" content="关闭抽屉返回主界面" placement="bottom">
	    		<el-button type="danger" size="mini" @click="closeMap()"><i class="el-icon-close"></i>关闭</el-button>
	    		</el-tooltip>
		    	</el-col>
				</div>
				<div id="container" style="position: relative;width:100%;"></div>
		    </div>
		</el-row>
	</el-drawer>
    </el-row>

  </div>
</template>

<script>
import G6 from '@antv/g6'
import { listTaskGroup, getTaskGroup, delTaskGroup, addTaskGroup, updateTaskGroup, exportTaskGroup } from "@/api/task/taskGroup";
import { listTaskGroupRelation, getTaskGroupRelation, delTaskGroupRelation, addTaskGroupRelation, updateTaskGroupRelation, exportTaskGroupRelation ,delTaskGroupRelationByNode} from "@/api/task/taskGroupRelation";
import backgroundImage from '@/assets/images/timg.jpg'

export default {
  name: "TaskGroup",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务分组表格数据
      taskGroupList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      backgroundImage:"",
      g6graph:undefined,
      nodes:[],
      edges:[],
      drawer: false,
      layout:null,
      direction: 'btt',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
        taskGroupCode: null,
        taskGroupName: null,
        createId: null,
        updateId: null,
        threadNum: null,
      },
      // 表单参数
      form: {
          threadNum: 0
      },
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询任务分组列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      listTaskGroup(this.queryParams).then(response => {
        this.taskGroupList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        taskGroupId: null,
        taskGroupCode: null,
        taskGroupName: null,
        axesx: null,
        axesy: null,
        remark: null,
        threadNum: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.taskGroupId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务分组";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const taskGroupId = row.taskGroupId || this.ids
      getTaskGroup(taskGroupId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改任务分组";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.taskGroupId != null) {
            updateTaskGroup(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTaskGroup(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taskGroupIds = row.taskGroupId || this.ids;
      this.$confirm('是否确认删除任务分组编号为"' + taskGroupIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delTaskGroup(taskGroupIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有任务分组数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportTaskGroup(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    handleMap(){
    	this.layout=null;
    	this.drawer=true;
        this.nodes=[];
        this.edges=[];
        this.loading = true;
        this.queryParams.params = {};//获取数据节点及连线
        listTaskGroup(this.queryParams).then(response => {
          for(var i=0;i<response.rows.length;i++){
  			this.nodes.push({id:(response.rows[i].taskGroupId).toString(),label:response.rows[i].taskGroupName,code:response.rows[i].taskGroupCode,remark:response.rows[i].remark,x:Number(response.rows[i].axesx),y:Number(response.rows[i].axesy),labelCfg: { style: {fill: 'white',fontSize: 10} }});
  			if(response.rows[i].etlTaskGroupRelations!=null){
  				for(var j=0;j<response.rows[i].etlTaskGroupRelations.length;j++){
  					this.edges.push({"source":(response.rows[i].etlTaskGroupRelations[j].srcTaskGroupId).toString(),"target":(response.rows[i].etlTaskGroupRelations[j].tarTaskGroupId).toString(),"sourceAnchor": 0,"targetAnchor": 1,"taskGroupRelationId":response.rows[i].etlTaskGroupRelations[j].taskGroupRelationId});
  				}
  			}
  		}
          this.loading = false;
          this.init();
        });
    },
	layoutTo(type){
		const value = type;
		this.g6graph.destroy();
		  if(value=="force"){
			  this.layout={type: 'force',preventOverlap: true,nodeSize: 150,edgeStrength:1};  this.init();
		  }else if(value=="circular"){
			  this.layout={type: 'circular',center: [200, 200],radius: 500,startRadius: 10};this.init();
		  }else if(value=="radial"){
			  this.layout={type: 'radial',center: [200, 200],linkDistance: 150,maxIteration: 50,unitRadius: 200};this.init();
		  }else if(value=="grid"){
			  this.layout={type: 'grid',begin: [ 0, 0 ],preventOverlap: true,preventOverlapPdding: 10000,sortBy: 'degree' };this.init();
		  }else if(value=="concentric"){
			  this.layout={type: 'concentric',center: [200, 200],linkDistance: 50,sweep: 10,sortBy: 'degree'};this.init();
		  }else if(value=="dagre"){
			  this.layout={type: 'dagre',rankdir: 'LR',align: 'DL',controlPoints: true};this.init();
		  }else if(value=="mds"){
			  this.layout={type: 'mds',workerEnabled: true};this.init();
		  }else if(value=="comboForce"){
			  this.layout={type: 'comboForce',center: [ 200, 200 ],linkDistance: 50,edgeStrength: 0.1,onTick: () => { console.log('ticking');}, onLayoutEnd: () => { console.log('combo force layout done');}};
		  }else{
			  this.handleMap();
		  }
	},
	findTo(){
		var name = this.queryParams.name;
		this.g6graph.findAll('node', node => {
			if( node.get('model').label == name){
				console.log(node.get('model').x+"="+node.get('model').y);
				this.g6graph.updateItem(node, {
					  style: {stroke: 'red'},
				  });
				this.g6graph.focusItem(node);
				// 5秒后自动回复
				setTimeout( function(){
					this.g6graph.updateItem(node, {
						  style: {stroke: '#72CC4A',},
					  });
				}, 5 * 1000 );
			};
			});
	},
	forTo(value){
			this.g6graph.setMode(value);
	},
	zoomTo(value){
		if(value=='+'){
			this.g6graph.zoomTo(this.g6graph.getZoom()+0.1);
		}else{
			this.g6graph.zoomTo(this.g6graph.getZoom()-0.1);
		}

	},
	closeMap(){
			this.g6graph.clear();
			this.g6graph.destroy();
			this.drawer=false;
	},
	saveTaskShip(source,target){
		if(source==target){return;}
	    const formR ={srcTaskGroupId:source,tarTaskGroupId:target};
			addTaskGroupRelation(formR).then(response => {

            });
	},
	delTaskShip(source,target){
		let params = {};
    	params.srcTaskGroupId=source;
    	params.tarTaskGroupId=target;
		return delTaskGroupRelationByNode(params).then(response => {

        });
	},
	moveTaskAxis(id,x,y){
	 this.reset();
	 this.form.taskGroupId=id;
	 this.form.axesx=x;
	 this.form.axesy=y;
		updateTaskGroup(this.form).then(response => {
          this.reset();
        });
	},
	/******************节点点击事件***********/
	handleNodeClick(event) {
	  const item = event.item;
	  const matrix = item.get('group').getMatrix();
	  this.form.axesx=matrix[6];
	  this.form.axesy=matrix[7];
	  this.form.taskGroupId=item.get('model').id;
	  this.form.taskGroupCode=item.get('model').code;
	  this.form.taskGroupName=item.get('model').label;
	  this.form.remark=item.get('model').remark;
	  this.delDisabled=false;
	  this.drawer=true;
	},
	handleClose(done) {
        done();
  	},
	init(){
		let that=this;
		G6.registerBehavior('click-add-node', {
		  getEvents() {
		    return {
		      'canvas:click': 'onClick',
		    };
		  },
		  onClick(ev) {
		    const self = this;
		    const graph = self.graph;
		    const point3 = graph.getPointByCanvas(ev.canvasX, ev.canvasY);
		    that.form.axesx=point3.x;
		    that.form.axesy=point3.y;
		    that.drawer=true;
		    that.delDisabled=true;

		  },
		});

		G6.registerBehavior('click-add-edge', {
		  getEvents() {
		    return {
		      'node:click': 'onClick', // The event is canvas:click, the responsing function is onClick
		       mousemove: 'onMousemove', // The event is mousemove, the responsing function is onMousemove
		      'edge:click': 'onEdgeClick', // The event is edge:click, the responsing function is onEdgeClick
		    };
		  },
		  onClick(ev) {
		    const self = this;
		    const node = ev.item;
		    const graph = self.graph;
		    const point = { x: ev.x, y: ev.y };
		    const model = node.getModel();
		    if (self.addingEdge && self.edge) {
		      graph.updateItem(self.edge, {
		        target: model.id,
		      });
		      that.saveTaskShip(self.edge.get("model").source,self.edge.get("model").target);
		      self.edge = null;
		      self.addingEdge = false;
		    } else {
		      self.edge = graph.addItem('edge', {
		        source: model.id,
		        target: model.id,
		      });
		      self.addingEdge = true;
		    }
		  },
		  onMousemove(ev) {
		    const self = this;
		    const point = { x: ev.x, y: ev.y };
		    if (self.addingEdge && self.edge) {
		      self.graph.updateItem(self.edge, {
		        target: point,
		      });
		    }
		  },
		  onEdgeClick(ev) {
		    const self = this;
		    const currentEdge = ev.item;
		    if (self.addingEdge && self.edge === currentEdge) {
		      self.graph.removeItem(self.edge);
		      self.edge = null;
		      self.addingEdge = false;
		    }
		  },
		});

		console.log(this.edges);
		const graphContainer = document.getElementById('selector');
		const data = {
		  nodes: this.nodes,
		  edges: this.edges
		};
		// lineDash 的差值，可以在后面提供 util 方法自动计算
		const dashArray = [
		  [0, 1],
		  [0, 2],
		  [1, 2],
		  [0, 1, 1, 2],
		  [0, 2, 1, 2],
		  [1, 2, 1, 2],
		  [2, 2, 1, 2],
		  [3, 2, 1, 2],
		  [4, 2, 1, 2],
		];
		const lineDash = [4, 2, 1, 2];
		const interval = 9;

		G6.registerEdge('can-running',{
		    setState(name, value, item) {
		      const shape = item.get('keyShape');
		      if (name === 'running') {
		        if (value) {
		          const length = shape.getTotalLength(); // 后续 G 增加 totalLength 的接口
		          let totalArray = [];
		          for (let i = 0; i < length; i += interval) {
		            totalArray = totalArray.concat(lineDash);
		          }
		          let index = 0;
		          shape.animate(
		            () => {
		              const cfg = {
		                lineDash: dashArray[index].concat(totalArray),
		                stroke:"red"
		              };
		              index = (index + 1) % interval;
		              return cfg;
		            },
		            {
		              repeat: true,
		              duration: 3000,
		            }
		          );
		        } else {
		          shape.stopAnimate();
		          shape.attr('lineDash', null);
		          shape.animate(
		  	            () => {
		  	              const cfg = {
		  	                stroke:"#F6BD16"
		  	              };
		  	              return cfg;
		  	            }
		  	          );
		        }
		      }
		    },
		  },
		  'cubic-horizontal'
		);

		G6.registerNode('round-rect',
		  {
		    drawShape: function drawShape(cfg, group) {
		      const width = cfg.style.width;
		      const stroke = cfg.style.stroke;
		      const rect = group.addShape('rect', {
		        attrs: {
		          x: -width / 2,
		          y: -15,
		          width,
		          height: 40,
		          radius: 5,
		          stroke,
		          lineWidth: 1.2,
		          fillOpacity: 0.8,
		          fill: '#08284d'
		        },
		        name: 'rect-shape',
		      });
		      return rect;
		    },
		    getAnchorPoints: function getAnchorPoints() {
		      return [
		        [0, 0.5],
		        [1, 0.5],
		      ];
		    }
		  },
		  'single-node'
		);
		/******************小图***********/
		const minimap = new G6.Minimap({
			  position: "absolute",
			  "text-align": "right"
			});
		const width = document.getElementById('container').scrollWidth;
		let height = document.body.clientHeight;
		// 创建 G6 图实例
		const graph = new G6.Graph({
		  container: 'container', // 指定图画布的容器 id
		  linkCenter: true,
		  animate: true,
		  defaultNode: {
			    type: 'round-rect',
			    style: {
			      stroke: '#72CC4A',
			      width: 180,
			    },
			    hover: {
			        lineWidth: 5,
			        fillOpacity: 1,
			      }
			  },

		  defaultEdge: {
			  	type: 'can-running',
			    style: {
			      radius: 10,
			      lineWidth: 2,
			      offset: 30,
			      stroke: '#F6BD16',
			      endArrow: {
			    	  path: 'M 0,0 L 10,5 L 10,-5 Z',
			          fill: '#F6BD16'
			        }
			    },
			  },
		  modes: {
			    // 支持的 behavior
			    default: ['drag-canvas','drag-node','zoom-canvas', 'click-select'],
			    addNode: ['click-add-node', 'click-select'],
			    addEdge: ['click-add-edge', 'click-select'],
		  },
		  // 画布宽高
		  width: width,
		  height: height,
		  fitView:true,
		  plugins: [ minimap ],
		  minZoom:0.1,
		  maxZoom:1.5,
		  layout:this.layout
		});
		// 读取数据
		graph.data(data);
		// 渲染图
		graph.render();
		graph.get('container').style.background = '#08284d';
		graph.get('container').style.backgroundSize = 'auto 100%';
		/******************默认缩放***********/
		graph.zoom(1,{ x: width/2, y: height/2 });

		/******************节点连接高亮***********/
		graph.on('node:mouseenter', ev => {
		  const node = ev.item;
		  const edges = node.getEdges();
		  edges.forEach(edge => graph.setItemState(edge, 'running', true));
		  graph.updateItem(node, {
				style: {
			stroke: 'yellow',
				},
		  });
		})
		graph.on('node:mouseleave', ev => {
		  const node = ev.item;
		  const edges = node.getEdges();
		  edges.forEach(edge => graph.setItemState(edge, 'running', false));
		  graph.updateItem(node, {
			  style: {
			      stroke: '#72CC4A',
			    },
		  });
		});
		/******************节点最终位置***********/
		graph.on('node:dragend', ev => {
		  const node = ev.item;
		  if(graph.get('layout')==null){
		  	this.moveTaskAxis(node.get('model').id,node.get('model').x,node.get('model').y)
		  }
		})
		/******************删除连线***********/
		graph.on('edge:mouseenter', ev => {
		  const edge = ev.item;
		  graph.setItemState(edge, 'running', true)
		})
		graph.on('edge:mouseleave', ev => {
			const edge = ev.item;
			  graph.setItemState(edge, 'running', false)
		});
		graph.on('edge:dblclick', ev => {
			this.delTaskShip(ev.item.get('model').source,ev.item.get('model').target);
			graph.removeItem(ev.item);
		})
		/******************任务明细***********/
		graph.on('node:dblclick', ev => {
			this.handleNodeClick(ev);
		})

		// 监听节点上的click事件
		graph.on('node:click', ev => {
			if(graph.getCurrentMode()!='addEdge'){
				this.handleNodeClick(ev);
			}
		});

			this.g6graph=graph;
		}
  },
  destroyed () {
	  this.g6graph.clear(); //注意，VUE此处必须清理，否则切换界面会越来越卡
	  this.g6graph.destroy();
  }
};
</script>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }

  .card{
  	margin:15px;
    min-width: 90%;
    height: 90%;
  }

  .el-row-inline {
    display: flex;
    flex-wrap: wrap;
  }

  .g6-minimap{
	  position:absolute;width:200px;height:180px;text-align:right;margin-top:-115px
	}
  .g6-minimap-container {
    border: 2px solid #009999;position:absolute;width:200px;height:110px;text-align:right
  }
  .g6-minimap-viewport {
    border: 2px solid rgb(25, 128, 255);
  }
   #contextMenu {
    position: absolute;
    list-style-type: none;
    padding: 10px 8px;
    left: -150px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
  }
  #contextMenu li {
    cursor: pointer;
		list-style-type:none;
    list-style: none;
    margin-left: 0px;
  }
  #contextMenu li:hover {
    color: #aaa;
  }
    .g6-tooltip {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #000;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }


  .detail-table{}
  .detail-table table{
	width:100%;
	margin-top:10px;
	border-collapse:collapse;
	border-spacing:0;
  }
  .detail-table table td{padding:5px 8px; }
  .detail-table table .odd td{background-color:#f4f5f4;}
  #contextMenu {
    position: absolute;
    list-style-type: none;
    padding: 10px 8px;
    left: -150px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
  }
  #contextMenu li {
    cursor: pointer;
		list-style-type:none;
    list-style: none;
    margin-left: 0px;
  }
  #contextMenu li:hover {
    color: #aaa;
  }
</style>
