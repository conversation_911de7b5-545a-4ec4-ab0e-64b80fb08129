<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscEntityPropSystemMapper">
    
    <resultMap type="DscEntityPropSystem" id="DscEntityPropSystemResult">
        <result property="entityPropId"    column="entity_prop_id"    />
        <result property="systemId"    column="system_id"    />
    </resultMap>

    <sql id="selectDscEntityPropSystemVo">
        select entity_prop_id, system_id from dsc_entity_prop_system
    </sql>

    <select id="selectDscEntityPropSystemList" parameterType="DscEntityPropSystem" resultMap="DscEntityPropSystemResult">
        <include refid="selectDscEntityPropSystemVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectDscEntityPropSystemById" parameterType="Long" resultMap="DscEntityPropSystemResult">
        <include refid="selectDscEntityPropSystemVo"/>
        where entity_prop_id = #{entityPropId}
    </select>
        
    <insert id="insertDscEntityPropSystem" parameterType="DscEntityPropSystem">
        insert into dsc_entity_prop_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityPropId != null">entity_prop_id,</if>
            <if test="systemId != null">system_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityPropId != null">#{entityPropId},</if>
            <if test="systemId != null">#{systemId},</if>
         </trim>
    </insert>

    <update id="updateDscEntityPropSystem" parameterType="DscEntityPropSystem">
        update dsc_entity_prop_system
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemId != null">system_id = #{systemId},</if>
        </trim>
        where entity_prop_id = #{entityPropId}
    </update>

    <delete id="deleteDscEntityPropSystemById" parameterType="Long">
        delete from dsc_entity_prop_system where entity_prop_id = #{entityPropId}
    </delete>

    <delete id="deleteDscEntityPropSystemByIds" parameterType="String">
        delete from dsc_entity_prop_system where entity_prop_id in 
        <foreach item="entityPropId" collection="array" open="(" separator="," close=")">
            #{entityPropId}
        </foreach>
    </delete>
</mapper>