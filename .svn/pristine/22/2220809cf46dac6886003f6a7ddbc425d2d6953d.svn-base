package com.dqms.task.service;

import java.util.List;
import com.dqms.task.domain.EtlTaskGroup;

/**
 * 任务分组Service接口
 * 
 * <AUTHOR>
 * @date 2021-03-08
 */
public interface IEtlTaskGroupService 
{
    /**
     * 查询任务分组
     * 
     * @param taskGroupId 任务分组ID
     * @return 任务分组
     */
    public EtlTaskGroup selectEtlTaskGroupById(Long taskGroupId);

    /**
     * 查询任务分组列表
     * 
     * @param etlTaskGroup 任务分组
     * @return 任务分组集合
     */
    public List<EtlTaskGroup> selectEtlTaskGroupList(EtlTaskGroup etlTaskGroup);

    /**
     * 新增任务分组
     * 
     * @param etlTaskGroup 任务分组
     * @return 结果
     */
    public int insertEtlTaskGroup(EtlTaskGroup etlTaskGroup);

    /**
     * 修改任务分组
     * 
     * @param etlTaskGroup 任务分组
     * @return 结果
     */
    public int updateEtlTaskGroup(EtlTaskGroup etlTaskGroup);

    /**
     * 批量删除任务分组
     * 
     * @param taskGroupIds 需要删除的任务分组ID
     * @return 结果
     */
    public int deleteEtlTaskGroupByIds(Long[] taskGroupIds);

    /**
     * 删除任务分组信息
     * 
     * @param taskGroupId 任务分组ID
     * @return 结果
     */
    public int deleteEtlTaskGroupById(Long taskGroupId);
}
