import request from '@/utils/request'

// 查询接口管理列表
export function listApiDefine(query) {
  return request({
    url: '/api/apiDefine/list',
    method: 'get',
    params: query
  })
}

// 查询接口管理详细
export function getApiDefine(defineId) {
  return request({
    url: '/api/apiDefine/' + defineId,
    method: 'get'
  })
}

// 新增接口管理
export function addApiDefine(data) {
  return request({
    url: '/api/apiDefine',
    method: 'post',
    data: data
  })
}

// 修改接口管理
export function updateApiDefine(data) {
  return request({
    url: '/api/apiDefine',
    method: 'put',
    data: data
  })
}

// 删除接口管理
export function delApiDefine(defineId) {
  return request({
    url: '/api/apiDefine/' + defineId,
    method: 'delete'
  })
}

// 导出接口管理
export function exportApiDefine(query) {
  return request({
    url: '/api/apiDefine/export',
    method: 'get',
    params: query
  })
}

export function changeStatus(defineId,status) {
  const data = {
	  defineId,
	  status
  }
  return request({
    url: '/api/apiDefine/changeStatus',
    method: 'put',
    data: data
  })
}

export function updateApiDefineSystem(data) {
  return request({
    url: '/api/apiDefine/updateApiDefineSystem',
    method: 'put',
    data: data
  })
}

export function updateParam(data) {
  return request({
    url: '/api/apiDefine/updateApiDefineParam',
    method: 'put',
    data: data
  })
}

export function getData(data) {
  return request({
    url: '/api/apiDefine/getData',
    method: 'put',
    data: data
  })
}