package com.dqms.dqm.service;


import java.util.List;
import java.util.Map;

import com.dqms.dqm.domain.DqmValidationProblem;

/**
 * 检查规则任务问题管理Service接口
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
public interface IDqmValidationProblemService
{
    /**
     * 查询检查规则任务问题管理
     *
     * @param dqmValidationProblemId 检查规则任务问题管理ID
     * @return 检查规则任务问题管理
     */
    public DqmValidationProblem selectDqmValidationProblemById(Integer dqmValidationProblemId);

    /**
     * 查询检查规则任务问题管理列表
     *
     * @param dqmValidationProblem 检查规则任务问题管理
     * @return 检查规则任务问题管理集合
     */
    public List<DqmValidationProblem> selectDqmValidationProblemList(DqmValidationProblem dqmValidationProblem);

    /**
     * 新增检查规则任务问题管理
     *
     * @param dqmValidationProblem 检查规则任务问题管理
     * @return 结果
     */
    public int insertDqmValidationProblem(DqmValidationProblem dqmValidationProblem);

    /**
     * 修改检查规则任务问题管理
     *
     * @param dqmValidationProblem 检查规则任务问题管理
     * @return 结果
     */
    public int updateDqmValidationProblem(DqmValidationProblem dqmValidationProblem);

    /**
     * 批量删除检查规则任务问题管理
     *
     * @param dqmValidationProblemIds 需要删除的检查规则任务问题管理ID
     * @return 结果
     */
    public int deleteDqmValidationProblemByIds(Integer[] dqmValidationProblemIds);

    /**
     * 删除检查规则任务问题管理信息
     *
     * @param dqmValidationProblemId 检查规则任务问题管理ID
     * @return 结果
     */
    public int deleteDqmValidationProblemById(Integer dqmValidationProblemId);

    /**
     * 规则执行问题执行趋势
     * @return
     */
    public List<Map<Object,Object>> getProblemSumForDate(DqmValidationProblem dqmValidationProblem);

    /**
     * 问题数据每日处理统计
     * @return
     */
    public List<Map> getProblemHandleForDate(DqmValidationProblem dqmValidationProblem);

    /**
     * 待处理任务列表
     * @return
     */
    public List<DqmValidationProblem> getProblemHandleForNo(DqmValidationProblem dqmValidationProblem);

    /**
     * 获取系统评分
     * @param dqmValidationProblem
     * @return
     */
    public List<Map> getSystemScore(DqmValidationProblem dqmValidationProblem);
}

