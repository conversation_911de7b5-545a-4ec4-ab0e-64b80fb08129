package com.dqms.dsm.controller;

import java.util.List;

import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsm.domain.DsmDimensionDetailRelForDownOrUp;
import com.dqms.dsm.domain.vo.DsmDimensionDetailVo2;
import com.dqms.dsm.domain.vo.DsmIndexMdmRelVo;
import com.dqms.dsm.domain.vo.DsmMdmRelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmMdmRel;
import com.dqms.dsm.service.IDsmMdmRelService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 指标引用Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RestController
@RequestMapping("/dsm/dsmMdmRel")
public class DsmMdmRelController extends BaseController
{
    @Autowired
    private IDsmMdmRelService dsmMdmRelService;


    /**
     * 查询指标引用列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DsmMdmRel dsmMdmRel)
    {
        startPage();
        List<DsmMdmRel> list = dsmMdmRelService.selectDsmMdmRelList(dsmMdmRel);
        return getDataTable(list);
    }
    
    @GetMapping("/unlist")
    public TableDataInfo unlist(DsmMdmRel dsmMdmRel)
    {
        startPage();
        if(dsmMdmRel.getPropName()!=null&&dsmMdmRel.getPropName().indexOf(".")!=-1) {
        	String[] params=dsmMdmRel.getPropName().split("\\.");
        	dsmMdmRel.setTableName(params[0]);
        	dsmMdmRel.setPropName(params[1]);
        }
        List<DsmMdmRel> list = dsmMdmRelService.selectDsmMdmRelUnList(dsmMdmRel);
        return getDataTable(list);
    }

    /**
     * 修改指标引用
     */
    @Log(title = "指标引用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmMdmRel dsmMdmRel)
    {
        return toAjax(dsmMdmRelService.updateDsmMdmRel(dsmMdmRel));
    }

    /**
     * 修改指标引用
     */
    @Log(title = "指标引用", businessType = BusinessType.UPDATE)
    @PutMapping("/updateDsmMdmRelIndexUn")
    public AjaxResult updateDsmMdmRelIndexUn(@RequestBody DsmMdmRel dsmMdmRel)
    {
        return toAjax(dsmMdmRelService.updateDsmMdmRelIndexUn(dsmMdmRel));
    }
    
    /**
     * 修改指标引用
     */
    @Log(title = "指标引用", businessType = BusinessType.UPDATE)
    @PutMapping("/updateDsmMdmRelDimensionIdUn")
    public AjaxResult updateDsmMdmRelDimensionIdUn(@RequestBody DsmMdmRel dsmMdmRel)
    {
        return toAjax(dsmMdmRelService.updateDsmMdmRelDimensionIdUn(dsmMdmRel));
    }

    /**
     * 导入指标引用
     */
    @Log(title = "维度字典元数据映射关系导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importUpdateDsmMdmRelDimensionIdUn")
    public AjaxResult importUpdateDsmMdmRelDimensionIdUn(@PathVariable MultipartFile file,boolean updateSupport) throws Exception
    {
        ExcelUtil<DsmMdmRelVo> util = new ExcelUtil<>(DsmMdmRelVo.class);
        //获取excel中的数值植入实体集合
        List<DsmMdmRelVo> dsmMdmRelVolist = util.importExcel(file.getInputStream());

        String message = dsmMdmRelService.importUpdateDsmMdmRelDimensionIdUn(dsmMdmRelVolist ,updateSupport);
        return AjaxResult.success(message);
    }
    /**
     * 导出模板
     */
    @GetMapping("/exportimportDimensionMdmDataEntityShip")
    public AjaxResult exportimportDimensionMdmDataEntityShip()
    {
        ExcelUtil<DsmMdmRelVo> util = new ExcelUtil<>(DsmMdmRelVo.class);
        return util.importTemplateExcel("维度字典与元数据映射模板");
    }


    /**
     * 导入指标标准与元数据映射
     */
    @Log(title = "指标标准与元数据映射关系导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importDsmIndexMdmRelIndexIdUn")
    public AjaxResult importDsmIndexMdmRelIndexIdUn(@PathVariable MultipartFile file,boolean updateSupport) throws Exception
    {
        ExcelUtil<DsmIndexMdmRelVo> util = new ExcelUtil<>(DsmIndexMdmRelVo.class);
        //获取excel中的数值植入实体集合
        List<DsmIndexMdmRelVo> dsmIndexMdmRelVoList = util.importExcel(file.getInputStream());

        String message = dsmMdmRelService.importUpdateDsmindexMdmRelIndexIdUn(dsmIndexMdmRelVoList ,updateSupport);
        return AjaxResult.success(message);
    }
    /**
     * 导出模板
     */
    @GetMapping("/exportimportDsmIndexMdmRelDimensionIdUn")
    public AjaxResult exportimportDsmIndexMdmRelDimensionIdUn()
    {
        ExcelUtil<DsmIndexMdmRelVo> util = new ExcelUtil<>(DsmIndexMdmRelVo.class);
        return util.importTemplateExcel("指标标准与元数据映射模板");
    }


}
