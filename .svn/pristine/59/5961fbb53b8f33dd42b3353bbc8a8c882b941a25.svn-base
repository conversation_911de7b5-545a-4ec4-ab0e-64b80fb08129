package com.dqms.api.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.api.domain.ApiTableManager;
import com.dqms.api.mapper.ApiTableManagerMapper;
import com.dqms.api.service.IApiTableManagerService;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.framework.web.service.TokenService;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.mapper.MdmDataEntityMapper;
import com.dqms.mdm.mapper.MdmRegistryMapper;
import com.dqms.mdm.util.MetaDataContext;

/**
 * 数据管控Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-15
 */
@Service
public class ApiTableManagerServiceImpl implements IApiTableManagerService
{
    @Autowired
    private ApiTableManagerMapper apiTableManagerMapper;
    
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;

    @Autowired
    private MetaDataContext metaDataContext;
    
    @Autowired
    private SysSystemMapper sysSystemMapper;

    @Autowired
    private MdmDataEntityMapper mdmDataEntityMapper ;
    
    @Autowired
    private MdmRegistryMapper mdmRegistryMapper;
    /**
     * 查询数据管控
     *
     * @param entityId 数据管控ID
     * @return 数据管控
     */
    @Override
    public ApiTableManager selectApiTableManagerById(Long entityId)
    {
        return apiTableManagerMapper.selectApiTableManagerById(entityId);
    }

    /**
     * 查询数据管控列表
     *
     * @param apiTableManager 数据管控
     * @return 数据管控
     */
    @Override
    public List<ApiTableManager> selectApiTableManagerList(ApiTableManager apiTableManager)
    {
    	if(apiTableManager.getSrcSystemId()!=null) {
    		return apiTableManagerMapper.selectApiTableManagerBySystemList(apiTableManager);
    	}
        return apiTableManagerMapper.selectApiTableManagerList(apiTableManager);
    }

    @Override
    public List<ApiTableManager> listSystem(Long entityId)
    {
        return apiTableManagerMapper.listSystem(entityId);
    }
    
    /**
     * 新增数据管控
     *
     * @param apiTableManager 数据管控
     * @return 结果
     */
    @Override
    public int insertApiTableManager(ApiTableManager apiTableManager)
    {
    	MdmDataEntity entity =mdmDataEntityMapper.selectMdmDataEntityById(apiTableManager.getEntityId());
    	MdmRegistry registry =mdmRegistryMapper.selectMdmRegistryById(entity.getRegistryId());
    	SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(registry.getDatasourceId());
    	SysSystem system=sysSystemMapper.selectSysSystemById(apiTableManager.getSystemId());
    	metaDataContext.grantBytable(sysDatasource, system.getCode(),registry.getRegDir()+"."+registry.getRegName(),system.getPassword(),false);
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	apiTableManager.setCreateTime(DateUtils.getNowDate());
    	apiTableManager.setCreateBy(loginUser.getUser().getNickName());
    	apiTableManager.setUpdateTime(DateUtils.getNowDate());
    	apiTableManager.setUpdateBy(loginUser.getUser().getNickName());
        return apiTableManagerMapper.insertApiTableManager(apiTableManager);
    }

    @Override
    public int deleteApiTableManager(ApiTableManager apiTableManager)
    {
    	MdmDataEntity entity =mdmDataEntityMapper.selectMdmDataEntityById(apiTableManager.getEntityId());
    	MdmRegistry registry =mdmRegistryMapper.selectMdmRegistryById(entity.getRegistryId());
    	SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(registry.getDatasourceId());
    	SysSystem system=sysSystemMapper.selectSysSystemById(apiTableManager.getSystemId());
    	metaDataContext.revokeBytable(sysDatasource, system.getCode(),registry.getRegDir()+"."+registry.getRegName(),system.getPassword(),false);
        return apiTableManagerMapper.deleteApiTableManager(apiTableManager);
    }
    
    /**
     * 修改数据管控
     *
     * @param apiTableManager 数据管控
     * @return 结果
     */
    @Override
    public int updateApiTableManager(ApiTableManager apiTableManager)
    {
        return apiTableManagerMapper.updateApiTableManager(apiTableManager);
    }

    /**
     * 批量删除数据管控
     *
     * @param entityIds 需要删除的数据管控ID
     * @return 结果
     */
    @Override
    public int deleteApiTableManagerByIds(Long[] entityIds)
    {
        return apiTableManagerMapper.deleteApiTableManagerByIds(entityIds);
    }

    /**
     * 删除数据管控信息
     *
     * @param entityId 数据管控ID
     * @return 结果
     */
    @Override
    public int deleteApiTableManagerById(Long entityId)
    {
        return apiTableManagerMapper.deleteApiTableManagerById(entityId);
    }
}
