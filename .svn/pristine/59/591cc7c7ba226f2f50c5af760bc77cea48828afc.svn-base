<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="模板名称" prop="tName">
        <el-input
          v-model="queryParams.tName"
          placeholder="请输入模板名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="目标表" prop="tTable">
        <el-select v-model="queryParams.tTable" placeholder="请选择目标表" clearable size="small">
          <el-option
            v-for="dict in tTableOptions"
            :key="dict.val"
            :label="dict.val"
            :value="dict.val"
          />
        </el-select>
      </el-form-item>
      <el-form-item
          label="映射字段"
          prop="tRoleids"
          >
        <el-input
          v-model="queryParams.tRoleids"
          placeholder="请输入映射字段"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['api:apiTemplateMapping:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['api:apiTemplateMapping:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['api:apiTemplateMapping:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['api:apiTemplateMapping:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="apiTemplateMappingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板名称" align="center" prop="tName" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="目标表" align="center"
                        prop="tTable"
                         />
      <el-table-column label="模板路径" align="center" prop="tRoute">
        <template slot-scope="scope">
          <el-button type="primary" @click.prevent="getResource(scope.row.tRoute)">下载<i class="el-icon-download el-icon--right"></i></el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column label="映射字段" align="center" prop="tFields" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag size="medium"
         >{{ scope.row.tFields}}</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="前置过程" align="center" prop="procedureStart" />
      <el-table-column label="校验过程" align="center" prop="procedureCheck" />
      <el-table-column label="后置过程" align="center" prop="procedureEnd" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['api:apiTemplateMapping:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['api:apiTemplateMapping:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据补录模版对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板名称" prop="tName">
          <el-input v-model="form.tName" placeholder="请输入模板名称" clearable/>
        </el-form-item>
        <el-form-item label="表名" prop="tTempTable">
          <el-select v-model="form.tTempTable"
            placeholder="请选择目标表" clearable
            filterable
            style="width:100%;"
            @change="tTableNameChange">
            <el-option
              v-for="dict in tTableOptions"
              :key="dict.val"
              :label="dict.text"
              :value="dict.val"
            >
              <span style="float: left">{{ dict.val }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.text}}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标表" prop="tTempTable">
          <el-input v-model="form.tTempTable" placeholder="请选择目标表" readonly clearable/>
        </el-form-item>
        <el-form-item label="前置过程" prop="procedureStart">
          <el-input v-model="form.procedureStart" placeholder="请输入前置储存过程" clearable/>
        </el-form-item>
        <el-form-item label="校验过程" prop="procedureCheck">
          <el-input v-model="form.procedureCheck" placeholder="请输入校验储存过程" clearable/>
        </el-form-item>
        <el-form-item label="后置过程" prop="procedureEnd">
          <el-input v-model="form.procedureEnd" placeholder="请输入后置储存过程" clearable/>
        </el-form-item>
         <el-form-item label="excel字段名">
          <el-input v-model="form.excelName" placeholder="请输入excel字段名" clearable/>
        </el-form-item>
        <el-form-item label="附件路径" prop="tRoute">
          <el-input
            v-model="form.tRoute"
            maxLength="20"
            readonly
            placeholder="请上传附件"
          />
        </el-form-item>
        <el-form-item
          label-width="0px"
          :error="uploaderr"
        >
          <div class="upload">
            <el-upload
              ref="upload"
              class="upload-demo"
              :action="uploadUrl"
              accept=".jpg,.jpeg,.txt,.zip,.xls,.xlsx,.doc,.docx,.pdf,.PDF"
              :auto-upload="true"
              :file-list="fileList"
              :on-success="handleSuccess"
              :headers="myHeaders"
              :limit="1"
            >
              <el-button
                size="small"
                icon="el-icon-upload2"
                type="danger"
              >上传附件</el-button
              >
            </el-upload>
            <div class="uptxt">
              （支持pdf、word、excel、zip、jpg，文件限制500M以内）
            </div>
          </div>
        </el-form-item>
        <el-button
          size="small"
          type="danger"
          v-if="form.list.length"
          @click.prevent="add"
        >添加</el-button>
        <p v-if="form.list.length">注：默认值支持以#开头为查找单元格内内容。如：#C5。支持默认宏当前仅支持{$GUID}唯一标识,{$CURRENT_DATE}当前日期,{$CURRENT_TIME}当前时间。其它则不作处理直接录入</p>
        <div v-for="(item,index) in form.list" :key="index">
          <el-row  class="row-bg" >
            <el-col :span="8">
              <el-form-item
                  label="模板列名"
                  :rules="[
                    { required: true, message: '请选择目标表', trigger: 'blur' }
                  ]"
                >
                  <el-select v-model="item.commentstring"
                        placeholder="请选择目标表"
                        clearable
                        @change="(value)=>TemplateChange(value,index)"
                  >
                    <el-option
                      v-for="dict in list"
                      :key="dict.column_name"
                      :label="dict.commentstring"
                      :value="dict.commentstring"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="起始行"
                  :prop="'list.' + index + '.attachment'"
                  :rules="{
                    validator: validatePass, trigger: 'blur'
                  }"
                >
                  <el-input
                    v-model="item.attachment"
                    maxLength="20"
                    placeholder="请输入起始行"
                  />
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="表字段名">
                  <el-input
                    v-model="item.column_name"
                    maxLength="20"
                    placeholder="请输入表字段名"
                    disabled
                  />
                </el-form-item>
                <el-form-item label="结束行">
                  <el-input
                    v-model="item.attachment2"
                    maxLength="20"
                    placeholder="请输入结束行"
                  />
                </el-form-item>
            </el-col>
            <el-col :span="8" style="text-align:right">
              <el-form-item label="列号">
                  <el-input
                    v-model="item.code"
                    maxLength="20"
                    placeholder="请输入列号"
                  />
                </el-form-item>
                <el-form-item
                  label="默认值"
                  :prop="'list.' + index + '.attachment3'"
                  :rules="{
                    validator: validatePass2, trigger: 'blur'
                  }"
                >
                  <el-input
                    v-model="item.attachment3"
                    maxLength="20"
                    placeholder="请输入默认值"
                  />
                </el-form-item>
                <el-button
                  size="small"
                  type="danger"
                  @click.prevent="splice2(index)"
                >删除</el-button>
            </el-col>
          </el-row>
          <el-divider></el-divider>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiTemplateMapping,
    getApiTemplateMapping,
    delApiTemplateMapping,
    addApiTemplateMapping,
    updateApiTemplateMapping,
    exportApiTemplateMapping,
    getTableNames,
    getTableMapping } from "@/api/api/apiTemplateMapping";
import { getToken } from "@/utils/auth";
export default {
  name: "ApiTemplateMapping",
  components: {
  },
  provide() {
      return {
          rwDispatcherProvider: this
      };
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据补录模版表格数据
      apiTemplateMappingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 目标表字典
      tTableOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tName: null,
        tTable: null,
        tFields: null,
        tRoleids: null,
        procedureStart: null,
        procedureEnd: null,
        isExName: null,
        excelName: null,
        procedureCheck: null
      },
      // 表单参数
      form: {
        list:[]
      },
      list:[],
      // 表单校验
      rules: {
        tName: [
          { required: true, message: "模板名称不能为空", trigger: "blur" }
        ],
        tTempTable: [
          { required: true, message: "目标临时表不能为空", trigger: "blur" }
        ],
        tTable: [
          { required: true, message: "目标表不能为空", trigger: "change" }
        ],
        tRoute: [
          { required: true, message: "模板路径不能为空", trigger: "blur" }
        ],
        tFields: [
          { required: true, message: "映射字段不能为空", trigger: "blur" }
        ],
      },
      myHeaders: {
          Authorization: "Bearer " + getToken()
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploaderr: "",
      valueConsistsOf: "ALL_WITH_INDETERMINATE",
      fileList: []
    };
  },
  created() {
    this.getList();
    this.getTableNames();
  },
  methods: {
    getResource(resource){
      window.location.href = process.env.VUE_APP_BASE_API + "/common/download/resource?resource=" + encodeURI(resource);
    },
    TemplateChange(value,index){
      var i=this.list.findIndex((item)=>item.commentstring==value)
      this.form.list[index].column_name = this.list[i].column_name
    },
    validatePass2(rule, value, callback){
      this.validatePass(rule, value, callback)
    },
    validatePass(rule, value, callback){
      var index = rule.field.split('.')[1]
      if(!this.form.list[index].attachment&&!this.form.list[index].attachment3){
        callback(new Error('起始行跟默认值不能同时为空'));
      } else {
        callback();
      }
    },
    splice2(index){
      this.form.list.splice(index,1)
    },
    add(){
      this.form.list.push({commentstring: "", column_name: ""})
    },
    tTableNameChange(){
        this.form.tTable=this.form.tTempTable;
        getTableMapping(this.form.tTable).then(response => {
          response.data.map((el,index)=>{
            if(!el.code){
              if(index<26){
                el.code = String.fromCharCode(index+65)
              }else{
                var i = Math.floor((index+1)/26)
                var i2=(index+1)%26
                var letter
                for(var k=0;k<i;k++){
                  letter+='A'
                }
                el.code = String.fromCharCode(i2+65-1)
              }
            }
          })
          this.form.list=response.data
          this.list = JSON.parse(JSON.stringify(response.data))
        });
    },
    getTableNames() {
      getTableNames().then(response => {
          this.tTableOptions = response.data;
      });
    },
    handleSuccess(res, file, fileList) {
        // 文件上传成功处理
        this.form.tRoute = res.url;
        //成功后的业务逻辑处理
    },
    /** 查询数据补录模版列表 */
    getList() {
      this.loading = true;
      listApiTemplateMapping(this.queryParams).then(response => {
        this.apiTemplateMappingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 目标表字典翻译
    // tTableFormat(row, column) {
    //   return this.selectDictLabel(this.tTableOptions, row.tTable);
    // },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        list:[],
        id: null,
        tName: null,
        tTempTable: null,
        tTable: null,
        tRoute: null,
        tFields: null,
        tRoleids: null,
        procedureStart: null,
        procedureEnd: null,
        isExName: null,
        excelName: null,
        procedureCheck: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据补录模版";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0]
      getApiTemplateMapping(id).then(response => {
        var form = JSON.parse(JSON.stringify(response.data))
        form.list = []
        var tFields = response.data.tFields.split(';')
        tFields.map((el,index)=>{
          if(index!=tFields.length-1){
            var elSplit = el.split(':')
            form.list[index] = {
              commentstring:elSplit[0],
              column_name:elSplit[1],
              code:elSplit[2]=='-1'?'':elSplit[2],
              attachment:elSplit[3]=='-2'?'':elSplit[3],
              attachment2:elSplit[4]=='-3'?'':elSplit[4],
              attachment3:elSplit[5]=='-4'?'':elSplit[5]
            }
          }
        })
        console.log(form)
        this.form = form;
        this.open = true;
        this.title = "修改数据补录模版";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            console.log(this.form)
            var form = JSON.parse(JSON.stringify(this.form))
            delete form.list
            form.tFields = ''
            this.form.list.map(el=>{
              var code = el.code?el.code:-1
              var attachment = el.attachment?el.attachment:-2
              var attachment2 = el.attachment2?el.attachment2:-3
              var attachment3 = el.attachment3?el.attachment3:-4
              form.tFields += `${el.commentstring}:${el.column_name}:${code}:${attachment}:${attachment2}:${attachment3};`
            })
            updateApiTemplateMapping(form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            var form = JSON.parse(JSON.stringify(this.form))
            delete form.list
            form.tFields = ''
            this.form.list.map(el=>{
              var code = el.code?el.code:-1
              var attachment = el.attachment?el.attachment:-2
              var attachment2 = el.attachment2?el.attachment2:-3
              var attachment3 = el.attachment3?el.attachment3:-4
              form.tFields += `${el.commentstring}:${el.column_name}:${code}:${attachment}:${attachment2}:${attachment3};`
            })
            addApiTemplateMapping(form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除数据补录模版编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delApiTemplateMapping(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有数据补录模版数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportApiTemplateMapping(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
<style>
.el-tooltip__popper{font-size: 14px; max-width:50% }
</style>
