package com.dqms.dqm.service;

import java.util.List;
import java.util.Map;

import com.dqms.dqm.domain.DqmKnowledgeBase;

/**
 * 质量检查规则知识库Service接口
 *
 * <AUTHOR>
 * @date 2021-08-10
 */
public interface IDqmKnowledgeBaseService
{
    /**
     * 查询质量检查规则知识库
     *
     * @param knowledgeBaseId 质量检查规则知识库ID
     * @return 质量检查规则知识库
     */
    public DqmKnowledgeBase selectDqmKnowledgeBaseById(Long knowledgeBaseId);

    /**
     * 查询质量检查规则知识库列表
     *
     * @param dqmKnowledgeBase 质量检查规则知识库
     * @return 质量检查规则知识库集合
     */
    public List<DqmKnowledgeBase> selectDqmKnowledgeBaseList(DqmKnowledgeBase dqmKnowledgeBase);

    /**
     * ES查询质量检查规则知识库列表
     * @param dqmKnowledgeBase
     * @return
     */
    public List<DqmKnowledgeBase> selectDqmKnowledgeBaseListToES(DqmKnowledgeBase dqmKnowledgeBase);

    /**
     * 新增质量检查规则知识库
     *
     * @param dqmKnowledgeBase 质量检查规则知识库
     * @return 结果
     */
    public int insertDqmKnowledgeBase(DqmKnowledgeBase dqmKnowledgeBase);

    /**
     * 修改质量检查规则知识库
     *
     * @param dqmKnowledgeBase 质量检查规则知识库
     * @return 结果
     */
    public int updateDqmKnowledgeBase(DqmKnowledgeBase dqmKnowledgeBase);

    /**
     * 批量删除质量检查规则知识库
     *
     * @param knowledgeBaseIds 需要删除的质量检查规则知识库ID
     * @return 结果
     */
    public int deleteDqmKnowledgeBaseByIds(Long[] knowledgeBaseIds);

    /**
     * 删除质量检查规则知识库信息
     *
     * @param knowledgeBaseId 质量检查规则知识库ID
     * @return 结果
     */
    public int deleteDqmKnowledgeBaseById(Long knowledgeBaseId);

    /**
     * 知识库详情
     * @param knowledgeBaseId
     * @return
     */
    public Map getDqmKnowledgeBaseById(Long knowledgeBaseId);
}
