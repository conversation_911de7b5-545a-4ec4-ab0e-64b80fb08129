package com.dqms.dsm.service;

import java.util.List;

import com.dqms.dsm.domain.DsmStandardClass;
import com.dqms.dsm.domain.DsmStandardClassTreeSelect;

/**
 * 数据标准分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface IDsmStandardClassService 
{
    /**
     * 查询数据标准分类
     * 
     * @param standardClassId 数据标准分类ID
     * @return 数据标准分类
     */
    public DsmStandardClass selectDsmStandardClassById(Long standardClassId);

    /**
     * 查询数据标准分类列表
     * 
     * @param dsmStandardClass 数据标准分类
     * @return 数据标准分类集合
     */
    public List<DsmStandardClass> selectDsmStandardClassList(DsmStandardClass dsmStandardClass);

    /**
     * 新增数据标准分类
     * 
     * @param dsmStandardClass 数据标准分类
     * @return 结果
     */
    public int insertDsmStandardClass(DsmStandardClass dsmStandardClass);

    /**
     * 修改数据标准分类
     * 
     * @param dsmStandardClass 数据标准分类
     * @return 结果
     */
    public int updateDsmStandardClass(DsmStandardClass dsmStandardClass);

    /**
     * 批量删除数据标准分类
     * 
     * @param standardClassIds 需要删除的数据标准分类ID
     * @return 结果
     */
    public int deleteDsmStandardClassByIds(Long[] standardClassIds);

    /**
     * 删除数据标准分类信息
     * 
     * @param standardClassId 数据标准分类ID
     * @return 结果
     */
    public int deleteDsmStandardClassById(Long standardClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 树结构列表
     */
    public List<DsmStandardClass> buildDsmStandardClassTree(List<DsmStandardClass> dsmStandardClass);
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    public List<DsmStandardClassTreeSelect> buildDsmStandardClassTreeSelect(List<DsmStandardClass> dsmStandardClass);
}
