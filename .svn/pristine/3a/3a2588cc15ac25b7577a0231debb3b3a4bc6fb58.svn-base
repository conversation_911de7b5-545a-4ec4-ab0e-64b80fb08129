package com.dqms.task.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dqms.task.domain.vo.EtlTaskAssets;
import com.dqms.task.domain.vo.EtlTaskAssetsEdges;
import com.dqms.task.domain.vo.TaskRunFbVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.annotation.DataScope;
import com.dqms.task.domain.EtlTaskInstanceHis;
import com.dqms.task.mapper.EtlTaskInstanceHisMapper;
import com.dqms.task.service.IEtlTaskInstanceHisService;

/**
 * 任务监控历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
@Service
public class EtlTaskInstanceHisServiceImpl implements IEtlTaskInstanceHisService
{
    @Autowired
    private EtlTaskInstanceHisMapper etlTaskInstanceHisMapper;

    /**
     * 查询任务监控历史
     *
     * @param taskInstanceHisId 任务监控历史ID
     * @return 任务监控历史
     */
    @Override
    public EtlTaskInstanceHis selectEtlTaskInstanceHisById(Long taskInstanceHisId)
    {
        return etlTaskInstanceHisMapper.selectEtlTaskInstanceHisById(taskInstanceHisId);
    }

    /**
     * 查询任务监控历史列表
     *
     * @param etlTaskInstanceHis 任务监控历史
     * @return 任务监控历史
     */
    @Override
    public List<EtlTaskInstanceHis> selectEtlTaskInstanceHisList(EtlTaskInstanceHis etlTaskInstanceHis)
    {
        return etlTaskInstanceHisMapper.selectEtlTaskInstanceHisList(etlTaskInstanceHis);
    }

    /**
     * 新增任务监控历史
     *
     * @param etlTaskInstanceHis 任务监控历史
     * @return 结果
     */
    @Override
    public int insertEtlTaskInstanceHis(EtlTaskInstanceHis etlTaskInstanceHis)
    {
        return etlTaskInstanceHisMapper.insertEtlTaskInstanceHis(etlTaskInstanceHis);
    }

    /**
     * 修改任务监控历史
     *
     * @param etlTaskInstanceHis 任务监控历史
     * @return 结果
     */
    @Override
    public int updateEtlTaskInstanceHis(EtlTaskInstanceHis etlTaskInstanceHis)
    {
        return etlTaskInstanceHisMapper.updateEtlTaskInstanceHis(etlTaskInstanceHis);
    }

    /**
     * 批量删除任务监控历史
     *
     * @param taskInstanceHisIds 需要删除的任务监控历史ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskInstanceHisByIds(Long[] taskInstanceHisIds)
    {
        return etlTaskInstanceHisMapper.deleteEtlTaskInstanceHisByIds(taskInstanceHisIds);
    }

    /**
     * 删除任务监控历史信息
     *
     * @param taskInstanceHisId 任务监控历史ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskInstanceHisById(Long taskInstanceHisId)
    {
        return etlTaskInstanceHisMapper.deleteEtlTaskInstanceHisById(taskInstanceHisId);
    }

    @Override
    public Map<String , Object> getAssets() {
        int nodeId=1;
        List<EtlTaskAssets> listNode = new ArrayList<EtlTaskAssets>();
        List<EtlTaskAssetsEdges> listAdge = new ArrayList<EtlTaskAssetsEdges>();
        Map<String , Object> map = new HashMap<>();
        //源系统采集模块
        List<EtlTaskAssets> listSystemTask= etlTaskInstanceHisMapper.selectSystemTask();
        int i=0;
        double x = 54/1366f;
        for(EtlTaskAssets t : listSystemTask) {
            if(i==15) {
                i=0;
                x=x+94/1366f;
            }
            t.setAxesx(x+"");
            if(i==0) {
                t.setAxesy((360/657f)+"");
            } else if(i%2!=0) {
                t.setAxesy((360+(1.2*i+1)/2*30)/657f+"");
            }else {
                t.setAxesy((360-(1.2*i+1)/2*30)/657f+"");
            }
            t.setId("SRC"+nodeId);
            t.setFontSize(10L);
            listNode.add(t);
            EtlTaskAssetsEdges edge = new EtlTaskAssetsEdges();
            edge.setSource("SRC"+nodeId);
            edge.setTarget("DATAX");
            edge.setLabel((t.getSnum()==null?0:t.getSnum())+"/"+t.getZnum());
            edge.setFontSize(10L);
            listAdge.add(edge);
            i++;
            nodeId++;
        }
        //DATAX模块
        EtlTaskAssets datax =new EtlTaskAssets();
        datax.setId("DATAX");
        datax.setLabel("DATAX");
        datax.setAxesx(420/1366f+"");
        datax.setAxesy(360/657f+"");
        listNode.add(datax);

        //ODS模块
        EtlTaskAssetsEdges dataxods = new EtlTaskAssetsEdges();
        dataxods.setSource("DATAX");
        dataxods.setTarget("ODS");
        EtlTaskAssets ods = etlTaskInstanceHisMapper.selectSystemODSTask();
        listAdge.add(dataxods);
        ods.setId("ODS");
        ods.setLabel("ODS层");
        ods.setAxesx(500/1366f+"");
        ods.setAxesy(360/657f+"");
        ods.setType("TRUE");
//		ods.setFontSize(12L);
        listNode.add(ods);

        //数据质量
        EtlTaskAssetsEdges odsdqm = new EtlTaskAssetsEdges();
        odsdqm.setSource("ODS");
        odsdqm.setTarget("DQM");
        EtlTaskAssets dqm = etlTaskInstanceHisMapper.selectSystemDQMTask();
        odsdqm.setLabel(dqm.getZnum()+"");
        listAdge.add(odsdqm);
        dqm.setId("DQM");
        dqm.setLabel("数据质量");
        dqm.setAxesx(450/1366f+"");
        dqm.setAxesy(200/657f+"");
        //listNode.add(dqm);


        //EDW
        i=0;
        List<EtlTaskAssets> listEdwTask= etlTaskInstanceHisMapper.selectSystemEDWTask();
        for(EtlTaskAssets t : listEdwTask) {
            if(i==15) {
                i=0;
            }
            EtlTaskAssetsEdges edge1 = new EtlTaskAssetsEdges();
            t.setAxesx(650/1366f+"");
            t.setId("EDW"+nodeId);
            t.setType("TRUE");
            listNode.add(t);
            EtlTaskAssetsEdges edge = new EtlTaskAssetsEdges();
            edge.setSource("EDW"+nodeId);
            edge.setTarget("DDW");
            edge.setLabel((t.getSnum()==null?0:t.getSnum())+"/"+t.getZnum());
            edge1.setSource("ODS");
            edge1.setTarget("EDW"+nodeId);

            if(i==0) {
                edge1.setLabel((ods.getSnum()==null?0:ods.getSnum())+"/"+ods.getZnum());
                t.setAxesy(360/657f+"");
            } else if(i%2!=0) {
                t.setAxesy((360+(i+1)/2*70)/657f+"");
            }else {
                t.setAxesy((360-(i+1)/2*70)/657f+"");
            }
            listAdge.add(edge);
            listAdge.add(edge1);
            i++;
            nodeId++;
        }

        //DDW
        EtlTaskAssetsEdges ddwdm = new EtlTaskAssetsEdges();
        ddwdm.setSource("DDW");
        ddwdm.setTarget("DM");
        EtlTaskAssets ddw = etlTaskInstanceHisMapper.selectSystemDDWTask();
        ddwdm.setLabel((ddw.getSnum()==null?0:ddw.getSnum())+"/"+ddw.getZnum());
        listAdge.add(ddwdm);
        ddw.setId("DDW");
        ddw.setLabel("DDW层");
        ddw.setAxesx(820/1366f+"");
        ddw.setAxesy(360/657f+"");
        ddw.setType("TRUE");
//		ddw.setFontSize(12L);
        listNode.add(ddw);

        //DM
        EtlTaskAssetsEdges dmapi = new EtlTaskAssetsEdges();
        dmapi.setSource("DM");
        dmapi.setTarget("API");
        EtlTaskAssets dm = etlTaskInstanceHisMapper.selectSystemDMTask();
        dmapi.setLabel((dm.getSnum()==null?0:dm.getSnum())+"/"+dm.getZnum());
        listAdge.add(dmapi);
        dm.setId("DM");
        dm.setLabel("DM层");
        dm.setType("TRUE");
        dm.setAxesx(920/1366f+"");
        dm.setAxesy(360/657f+"");
//		dm.setFontSize(12L);
        listNode.add(dm);

        //接口
        EtlTaskAssetsEdges apiyy = new EtlTaskAssetsEdges();
        apiyy.setSource("API");
        apiyy.setTarget("YY");
        EtlTaskAssets api = etlTaskInstanceHisMapper.selectApiTask();
        api.setId("API");
        api.setLabel("API");
        api.setAxesx(1020/1366f+"");
        api.setAxesy(360/657f+"");
        apiyy.setLabel(api.getZnum()+"");
        listNode.add(api);

        EtlTaskAssetsEdges appyy = new EtlTaskAssetsEdges();
        appyy.setSource("APP");
        appyy.setTarget("YY");
        EtlTaskAssetsEdges appyy1 = new EtlTaskAssetsEdges();
        appyy1.setSource("DM");
        appyy1.setTarget("APP");
        listAdge.add(appyy);
        listAdge.add(appyy1);
        EtlTaskAssets app = etlTaskInstanceHisMapper.selectAppTaskZs();
        app.setId("APP");
        app.setLabel("APP");
        app.setAxesx(1020/1366f+"");
        app.setAxesy(290/657f+"");
        appyy.setLabel((app.getSnum()==null?0:app.getSnum())+"/"+app.getZnum());
        listNode.add(app);

        EtlTaskAssetsEdges QT = new EtlTaskAssetsEdges();
        QT.setSource("QT");
        QT.setTarget("YY");
        EtlTaskAssetsEdges QT1 = new EtlTaskAssetsEdges();
        QT1.setSource("DM");
        QT1.setTarget("QT");
        listAdge.add(QT);
        listAdge.add(QT1);
        EtlTaskAssets QQ = new EtlTaskAssets();
        QQ.setId("QT");
        QQ.setLabel("其他");
        QQ.setAxesx(1020/1366f+"");
        QQ.setAxesy(430/657f+"");
        QT.setLabel("0/0");
        listNode.add(QQ);

        EtlTaskAssets yy = new EtlTaskAssets();
        yy.setId("YY");
        yy.setLabel("应用系统");
        yy.setAxesx(1120/1366f+"");
        yy.setAxesy(360/657f+"");
        listNode.add(yy);
        listAdge.add(apiyy);

        EtlTaskAssets systemZs = etlTaskInstanceHisMapper.selectSystemZs();
        map.put("systemZs", systemZs.getZnum());
        EtlTaskAssets dsZs = etlTaskInstanceHisMapper.selectDsZs();
        map.put("dsZs", dsZs.getZnum());
        EtlTaskAssets dataSizeZs = etlTaskInstanceHisMapper.selectDataSizeZs();
        map.put("dataSizeZs", dataSizeZs.getZnum());
        EtlTaskAssets dataMdmZs = etlTaskInstanceHisMapper.selectMdmZs();
        map.put("dataMdmZs", dataMdmZs.getZnum());

        TaskRunFbVo vo = etlTaskInstanceHisMapper.selectTaskZs();
        map.put("taskZs", vo.getNum());
        map.put("success", vo.getSuccess());
        map.put("fail", vo.getFaild());
        map.put("dqmZs", dqm.getZnum());

        map.put("nodes", listNode);
        map.put("edges", listAdge);
        return map;
    }
}
