<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://http.net.sms.wisdom.com" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://http.net.sms.wisdom.com" xmlns:intf="http://http.net.sms.wisdom.com" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns1="http://messages.net.sms.wisdom.com" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!--WSDL created by Apache Axis version: 1.2.1
Built on Aug 08, 2005 (11:49:10 PDT)-->
 <wsdl:types>
  <schema targetNamespace="http://messages.net.sms.wisdom.com" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://xml.apache.org/xml-soap"/>
   <import namespace="http://http.net.sms.wisdom.com"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType abstract="true" name="AbstractMessage">
    <sequence>
     <element name="packetLength" type="xsd:int"/>
    </sequence>
   </complexType>
   <complexType name="Label">
    <complexContent>
     <extension base="tns1:AbstractMessage">
      <sequence>
       <element name="labelName" nillable="true" type="xsd:string"/>
       <element name="labelValue" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="SmsMessage">
    <complexContent>
     <extension base="tns1:AbstractMessage">
      <sequence>
       <element name="contents" nillable="true" type="xsd:string"/>
       <element name="labels" nillable="true" type="impl:ArrayOf_tns1_Label"/>
       <element name="orgCode" nillable="true" type="xsd:string"/>
       <element name="receiver" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
   <complexType name="SmsMessages">
    <complexContent>
     <extension base="tns1:AbstractMessage">
      <sequence>
       <element name="access" nillable="true" type="xsd:string"/>
       <element name="endDate" nillable="true" type="xsd:string"/>
       <element name="endTime" nillable="true" type="xsd:string"/>
       <element name="extension" nillable="true" type="xsd:string"/>
       <element name="extensionValue" nillable="true" type="xsd:string"/>
       <element name="hashMap" nillable="true" type="apachesoap:Map"/>
       <element name="hashMapExten" nillable="true" type="apachesoap:Map"/>
       <element name="messages" nillable="true" type="impl:ArrayOf_tns1_SmsMessage"/>
       <element name="needUseTemplateFlag" nillable="true" type="xsd:string"/>
       <element name="organizationId" nillable="true" type="xsd:string"/>
       <element name="serviceType" nillable="true" type="xsd:string"/>
       <element name="smTaskID" nillable="true" type="xsd:string"/>
       <element name="startDate" nillable="true" type="xsd:string"/>
       <element name="startTime" nillable="true" type="xsd:string"/>
       <element name="taskId" nillable="true" type="xsd:string"/>
       <element name="taskValue" nillable="true" type="xsd:string"/>
       <element name="templatId" nillable="true" type="xsd:string"/>
      </sequence>
     </extension>
    </complexContent>
   </complexType>
  </schema>
  <schema targetNamespace="http://xml.apache.org/xml-soap" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://messages.net.sms.wisdom.com"/>
   <import namespace="http://http.net.sms.wisdom.com"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="mapItem">
    <sequence>
     <element name="key" nillable="true" type="xsd:anyType"/>
     <element name="value" nillable="true" type="xsd:anyType"/>
    </sequence>
   </complexType>
   <complexType name="Map">
    <sequence>
     <element maxOccurs="unbounded" minOccurs="0" name="item" type="apachesoap:mapItem"/>
    </sequence>
   </complexType>
  </schema>
  <schema targetNamespace="http://http.net.sms.wisdom.com" xmlns="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://messages.net.sms.wisdom.com"/>
   <import namespace="http://xml.apache.org/xml-soap"/>
   <import namespace="http://schemas.xmlsoap.org/soap/encoding/"/>
   <complexType name="ArrayOf_tns1_Label">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns1:Label[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="ArrayOf_tns1_SmsMessage">
    <complexContent>
     <restriction base="soapenc:Array">
      <attribute ref="soapenc:arrayType" wsdl:arrayType="tns1:SmsMessage[]"/>
     </restriction>
    </complexContent>
   </complexType>
   <complexType name="Response">
    <sequence>
     <element name="message" nillable="true" type="xsd:string"/>
     <element name="status" nillable="true" type="xsd:string"/>
    </sequence>
   </complexType>
  </schema>
 </wsdl:types>

   <wsdl:message name="sendSMSResponse">

      <wsdl:part name="sendSMSReturn" type="impl:Response"/>

   </wsdl:message>

   <wsdl:message name="sendSMSRequest">

      <wsdl:part name="UserName" type="xsd:string"/>

      <wsdl:part name="Password" type="xsd:string"/>

      <wsdl:part name="msgs" type="tns1:SmsMessages"/>

   </wsdl:message>

   <wsdl:portType name="SmsService">

      <wsdl:operation name="sendSMS" parameterOrder="UserName Password msgs">

         <wsdl:input message="impl:sendSMSRequest" name="sendSMSRequest"/>

         <wsdl:output message="impl:sendSMSResponse" name="sendSMSResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="SmsServiceSoapBinding" type="impl:SmsService">

      <wsdlsoap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="sendSMS">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="sendSMSRequest">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://http.net.sms.wisdom.com" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="sendSMSResponse">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://http.net.sms.wisdom.com" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="SmsServiceService">

      <wsdl:port binding="impl:SmsServiceSoapBinding" name="SmsService">

         <wsdlsoap:address location="http://*************:8080/wssmsif/services/SmsService"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>