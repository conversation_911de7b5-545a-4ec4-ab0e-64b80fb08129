<template>
	<div class="container-fluid" style="margin-top:0px;width:100%;">
			    <div id="scalingToolBar" style="position:absolute;padding-top:10;right:0;height:100;cursor:pointer;z-index: 99;width:30%;">
		    	    <el-select 
					v-model="queryParams.tarEntityId"
					filterable
					remote
					reserve-keyword
					placeholder="请输入源表关键词"
					clearable
					size="small"
					:remote-method="remoteMethod"
					:loading="selectLoading" 
					@change="srcChanged"  style="width:200px;" 
					>
		              <el-option
		                v-for="item in srcEntityData"
		                :key="item.entityId"
		                :label="item.tableName"
		                :value="item.entityId"
		              ></el-option>
		            </el-select>
		            <el-select v-model="queryParams.tarEntityPropId" placeholder="请选择源表字段" clearable size="small" style="width:200px;" filterable>
		              <el-option
		                v-for="item in srcEntityPropData"
		                :key="item.propId"
		                :label="item.propName"
		                :value="item.propId"
		              ></el-option>
		            </el-select>
		    	<el-button type="primary" size="mini" @click="getList()"><i class="el-icon-search"></i>查询</el-button>
		    	</div>
		<div id="containerDiv"><div id="container" style="position: relative;"></div></div>
	</div>
</template>

<script>
import { listMdmDataEntityShip, getMdmDataEntityShip, delMdmDataEntityShip, addMdmDataEntityShip, updateMdmDataEntityShip, exportMdmDataEntityShip,findAllEntity,findAllEntityProp,findShipList } from "@/api/mdm/mdmDataEntityShip";
import { listSystem } from '@/api/basic/datasource'
import G6 from '@antv/g6'
export default {
  name: "mdmDataMap",
  components: {
  },
  data() {
    return {
		selectLoading:false,
    graphA:undefined,
    mdmDataEntityShipList:[],
    nodes:[],
    edges:[],
    layout:{type: 'force',preventOverlap: true,nodeSize: 150,edgeStrength:1},
    queryParams: {
        tarEntityIds: [],
        tarEntityPropIds: [],
        tarEntityId:222,
        entityId:""
      },
      srcEntityData: [],
      srcEntityPropData: [],
      tarEntityPropData: []
    };
  },
  created() {
  },
  methods: {
		remoteMethod(query){
			if (query !== '') {
				this.selectLoading = true;
				findAllEntity({tableName:query}).then(res => {
				this.srcEntityData = res.rows;
				this.selectLoading = false;
				});
			} else {
				this.options = [];
			}
		},
	    srcChanged(value){
	      findAllEntityProp(value).then(response => {
	        this.srcEntityPropData = response.data;
	      });
	    },
	    tarChanged(value){
	      findAllEntityProp(value).then(response => {
	        this.tarEntityPropData = response.data;
	      });
	    },
	    getList(){
	    	if(this.queryParams.tarEntityPropId==null&&this.queryParams.tarEntityId==null){
	    		return;
	    	}
	    	this.queryParams.tarEntityPropIds=[];
	    	this.queryParams.tarEntityPropIds.push(this.queryParams.tarEntityPropId);
	    	this.queryParams.tarEntityIds=[];
	    	this.queryParams.tarEntityIds.push(this.queryParams.tarEntityId);
		    findShipList(this.queryParams).then(response => {
		          for(var i=0;i<response.data.length;i++){
		    			this.nodes.push({id:(response.data[i].entityId).toString(),labelm:response.data[i].tableName,props:response.data[i].mdmDataEntityProps,labelCfg: { style: {fill: 'white',fontSize: 10} }});
		    			if(response.data[i].relations!=null){
		    				for(var j=0;j<response.data[i].relations.length;j++){
		    					this.edges.push({"source":(response.data[i].relations[j].srcEntityId).toString(),"target":(response.data[i].relations[j].tarEntityId).toString()});
		    				}
		    			}
		    		}
		            this.loading = false;
		            this.init();
		    });
	    },
	    init(){
	    	let that=this;
			
			console.log(this.edges);
			const graphContainer = document.getElementById('selector');
			const data = {
			  nodes: this.nodes,
			  edges: this.edges
			};
			
			G6.registerNode('round-rect',
			  {
			    drawShape: function drawShape(cfg, group) {
			      let html_="";
			      for(var i=0;i<cfg.props.length;i++){
			    	  html_+= '<div class="body-item"><div class="name">'+(cfg.props[i].isPriKey=='Y'?'<span class="pk">PK</span>':'')+cfg.props[i].propName+'</div><div class="type">'+cfg.props[i].dataType+'</div></div>'
			      }
			      const rect = group.addShape('dom', {
			    	  attrs: {
			    	    width: 220,
			    	    height: 250,
			    	    html: `
			    	    	  <div class="entity-container fact">
			    	        <div class="content fact">
			    	          <div class="head">
			    	            <div>
			    	              <span role="img" aria-label="bars" class="anticon anticon-bars type"
			    	                ><svg
			    	                  viewBox="0 0 1024 1024"
			    	                  focusable="false"
			    	                  data-icon="bars"
			    	                  width="1em"
			    	                  height="1em"
			    	                  fill="currentColor"
			    	                  aria-hidden="true"
			    	                >
			    	                  <path
			    	                    d="M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"
			    	                  ></path></svg></span
			    	              ><span>${cfg.labelm}</span>
			    	            </div>
			    	            <span
			    	              role="img"
			    	              aria-label="ellipsis"
			    	              class="anticon anticon-ellipsis more"
			    	              ><svg
			    	                viewBox="64 64 896 896"
			    	                focusable="false"
			    	                data-icon="ellipsis"
			    	                width="1em"
			    	                height="1em"
			    	                fill="currentColor"
			    	                aria-hidden="true"
			    	              >
			    	                <path
			    	                  d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
			    	                ></path></svg
			    	            ></span>
			    	          </div>
			    	          <div class="body"> `+
			    	            html_+`
			    	          </div>
			    	        </div>
			    	      </div>
			    	      `
			    	  },
			    	  name: 'rect-shape',
			    	  draggable: true,
			    	});
			      return rect;
			    },
			    getAnchorPoints: function getAnchorPoints() {
			      return [
			        [0, 0.5],
			        [1, 0.5],
			      ];
			    }
			  },
			  'single-node'
			);
			
			G6.registerEdge('can-running',{
			    setState(name, value, item) {
			      const shape = item.get('keyShape');
			      if (name === 'running') {
			        if (value) {
			          const length = shape.getTotalLength(); // 后续 G 增加 totalLength 的接口
			          let totalArray = [];
			          for (let i = 0; i < length; i += interval) {
			            totalArray = totalArray.concat(lineDash);
			          }
			          let index = 0;
			          shape.animate(
			            () => {
			              const cfg = {
			                lineDash: dashArray[index].concat(totalArray),
			                stroke:"red"
			              };
			              index = (index + 1) % interval;
			              return cfg;
			            },
			            {
			              repeat: true,
			              duration: 3000,
			            }
			          );
			        } else {
			          shape.stopAnimate();
			          shape.attr('lineDash', null);
			          shape.animate(
			  	            () => {
			  	              const cfg = {
			  	                stroke:"#F6BD16"
			  	              };
			  	              return cfg;
			  	            }
			  	          );
			        }
			      }
			    },
			  },
			  'cubic-horizontal'
			);
			/******************小图***********/
			const minimap = new G6.Minimap({
				  position: "absolute",
				  "text-align": "right"
				});
			const width = document.getElementById('container').scrollWidth;
			let height = document.body.clientHeight;
			// 创建 G6 图实例
			const graph = new G6.Graph({
			  container: 'container', // 指定图画布的容器 id
			  renderer:'svg',
			  defaultNode: {
				    type: 'round-rect'
			  },
			  defaultEdge: {
				  	type: 'can-running',
				    style: {
				      radius: 10,
				      lineWidth: 2,
				      offset: 30,
				      stroke: '#F6BD16',
				      endArrow: {
				    	  path: 'M 0,0 L 10,5 L 10,-5 Z',
				          fill: '#F6BD16'
				        }
				    },
			  },
			  modes: {
				    // 支持的 behavior
				    default: ['drag-canvas','drag-node','zoom-canvas', 'click-select'] 
			  },
			  // 画布宽高
			  width: width,
			  height: height,
			  fitView:true,
			  plugins: [ minimap ],
			  minZoom:0.1,
			  maxZoom:1.5,
			  layout:this.layout
			});
			// 读取数据
			graph.data(data);
			// 渲染图
			graph.render();
			graph.get('container').style.background = '#08284d';
			graph.get('container').style.backgroundSize = 'auto 100%';
			/******************默认缩放***********/
			graph.zoom(1,{ x: width/2, y: height/2 });
			this.g6graph=graph;	
	    }

  },
  destroyed () {
  	  //注意，VUE此处必须清理，否则切换界面会越来越卡
	  this.graphA.clear();
	  this.graphA.destroy();
  },
  mounted() {
	  this.getList();
		
  }
};
</script>

<style>
.entity-container.fact {
  border: 1px solid #ced4de;
  height: 248px;
  width: 214px;
}
.entity-container {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border-radius: 2px;
  background-color: #fff;
}
.entity-container .content.fact {
  background-color: #ced4de;
}
.entity-container .content {
  margin: 1px;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.entity-container .content .head {
  width: calc(100% - 12px);
  height: 38px;
  margin-left: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.entity-container .content .head .type {
  padding-right: 8px;
}
.anticon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.anticon svg {
  display: inline-block;
}
svg:not(:root) {
  overflow: hidden;
}
.entity-container .content .head .more {
  cursor: pointer;
}
.entity-container .content .body {
  width: calc(100% - 12px);
  height: calc(100% - 42px);
  margin-left: 6px;
  margin-bottom: 6px;
  background-color: #fff;
  overflow: auto;
  cursor: pointer;
}
.entity-container .content .body .body-item {
  width: 100%;
  height: 28px;
  font-size: 12px;
  color: #595959;
  border-bottom: 1px solid rgba(206, 212, 222, 0.2);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.entity-container .content .body .body-item .name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 6px;
}
.entity-container .content .body .body-item .name .fk,
.entity-container .content .body .body-item .name .pk {
  width: 12px;
  font-family: "HelveticaNeue-CondensedBold";
  color: #ffd666;
  margin-right: 6px;
}
.entity-container .content .body .body-item .type {
  color: #bfbfbf;
  font-size: 8px;
  margin-right: 8px;
}
</style>