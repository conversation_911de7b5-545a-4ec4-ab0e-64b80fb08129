<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="源系统" prop="srcSystemId">
        <el-select
          v-model="queryParams.srcSystemId"
          placeholder="请选择源系统"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="目标系统" prop="tarSystemId">
        <el-select
          v-model="queryParams.tarSystemId"
          placeholder="请选择目标系统"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="源表" prop="srcEntityId">
        <el-select
          v-model="queryParams.srcEntityId"
          placeholder="请选择源表"
          size="small"
          style="width:350px;"
          filterable
          clearable
          remote
          reserve-keyword
          :loading="loading"
          :remote-method="(query) => getMdmEntity(query, 1)"
        >
          <el-option
            v-for="item in srcEntityData"
            :key="item.entityId"
            :label="item.tableName"
            :value="item.entityId"
          >
            <span style="float: left">{{ item.tableSchema}}.{{ item.tableName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="目标表" prop="tarEntityId">
        <el-select
          v-model="queryParams.tarEntityId"
          placeholder="请选择源表"
          size="small"
          style="width:350px;"
          filterable
          clearable
          remote
          reserve-keyword
          :loading="loading"
          :remote-method="(query) => getMdmEntity(query, 2)"
        >
          <el-option
            v-for="item in tarEntityData"
            :key="item.entityId+'2'"
            :label="item.tableName"
            :value="item.entityId"
          >
            <span style="float: left">{{ item.tableSchema}}.{{ item.tableName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="padding-top:10px;">
			<el-switch
			  style="display: block"
			  v-model="queryParams.triggerType"
			  active-color="#13ce66"
			  inactive-color="#ff4949"
			  active-text="关联来源"
			  inactive-text="手工维护"
			  active-value="2"
    		inactive-value="1">
			</el-switch>
		</el-form-item>
		<el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mdm:mdmDataEntityShip:add']"
          >新增</el-button
        >
      </el-col>
<!--       <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mdm:mdmDataEntityShip:edit']"
          >修改</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mdm:mdmDataEntityShip:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['mdm:mdmRegistry:export']"
          >导入</el-button
        >
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--          v-hasPermi="['mdm:mdmDataEntityShip:export']"-->
      <!--        >导出</el-button>-->
      <!--      </el-col>-->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :refreshShow="true"
        :searchShow="true"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="mdmDataEntityShipList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="源系统" align="center" prop="srcSystemName" width="180"/>
      <el-table-column
        label="源表"
        align="center"
        prop="srcTableName"
        width="180"
      />
      <el-table-column label="源表字段" align="center" prop="srcPropName" width="180"/>
      <el-table-column label="目标系统" align="center" prop="tarSystemName" width="180"/>
      <el-table-column
        label="目标表"
        align="center"
        prop="tarTableName"
        width="180"
      />
      <el-table-column label="目标表字段" align="center" prop="tarPropName" width="180"/>
      <el-table-column
        label="关系类型"
        align="center"
        prop="shipType"
        :formatter="shipTypeFormat"
        width="180"
      />
      <el-table-column label="关系来源" align="center" prop="entityName" width="180"/>
      <!--      <el-table-column label="版本号" align="center" prop="versionNo" />-->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="变更时间"
        align="center"
        prop="updateTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="220"
        fixed="right" 
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mdm:mdmDataEntityShip:edit']"
            >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mdm:mdmDataEntityShip:remove']"
            >删除</el-button
          >
          <el-button
            v-if="scope.row.entityId"
            size="mini"
            type="text"
            icon="el-icon-grape"
            @click="handleRelieve(scope.row)"
            v-hasPermi="['mdm:mdmDataEntityShip:remove']"
            >解除来源</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据实体关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1045px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="源表" prop="srcEntityId">
              <el-select
                v-model="form.srcEntityId"
                @change="(value)=>srcChanged(value,1)"
                placeholder="请选择源表"
                size="small"
                style="width:350px;"
                filterable
                clearable
                remote
                reserve-keyword
                :loading="loading"
                :remote-method="(query)=>getMdmEntity(query,3)"
              >
                <el-option
                  v-for="item in srcEntityData2"
                  :key="item.entityId+'3'"
                  :label="item.tableName"
                  :value="item.entityId"
                >
                <span style="float: left">{{ item.tableSchema}}.{{ item.tableName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="源表字段" prop="srcEntityPropId">
              <el-select
                v-model="form.srcEntityPropId"
                placeholder="请选择源表字段"
                clearable
                size="small"
                style="width:350px;margin-right:10px;"
                filterable
              >
                <el-option
                  v-for="item in srcEntityPropData"
                  :key="item.propId"
                  :label="item.propName"
                  :value="item.propId"
                >
                <span style="float: left">{{ item.propName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.propComment}}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="目标表" prop="tarEntityId">
              <el-select
                v-model="form.tarEntityId"
                @change="(value)=>srcChanged(value,2)"
                placeholder="请选择源表"
                size="small"
                style="width:350px;"
                filterable
                clearable
                remote
                reserve-keyword
                :loading="loading"
                :remote-method="(query)=>getMdmEntity(query,4)"
              >
                <el-option
                  v-for="item in tarEntityData2"
                  :key="item.entityId+'2'"
                  :label="item.tableName"
                  :value="item.entityId"
                >
                <span style="float: left">{{ item.tableSchema}}.{{ item.tableName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标表字段" prop="tarEntityPropId">
              <el-select
                v-model="form.tarEntityPropId"
                placeholder="请选择源表字段"
                clearable
                size="small"
                style="width:350px;margin-right:10px;"
                filterable
              >
                <el-option
                  v-for="item in tarEntityPropData"
                  :key="item.propId+'2'"
                  :label="item.propName"
                  :value="item.propId"
                >
                <span style="float: left">{{ item.propName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.propComment}}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="关系来源" prop="entityId">
              <el-select
                v-model="form.entityId"
                placeholder="请选择关系来源"
                size="small"
                style="width:350px;"
                filterable
                clearable
                remote
                reserve-keyword
                :loading="loading"
                :remote-method="(query)=>getMdmEntityUnTable(query,4)"
              >
                <el-option
                  v-for="item in entityData"
                  :key="item.entityId+'2'"
                  :label="item.tableName"
                  :value="item.entityId"
                >
                <span style="float: left">{{ item.tableName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关系类型" prop="shipType">
              <el-select
                v-model="form.shipType"
                placeholder="请选择关系类型"
                clearable
              >
                <el-option
                  v-for="dict in shipTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
           <el-col :span="12">
	        <el-form-item label="转换规则表达式" prop="transferRule">
              <el-input
                v-model="form.shipName"
                placeholder="请输入关系名称"
                clearable
              />
	        </el-form-item>
          </el-col>
         <el-col :span="12">
	        <el-form-item label="转换规则表达式" prop="transferRule">
	          <el-input
	            v-model="form.transferRule"
	            type="textarea"
	            :rows="3"
	            placeholder="请输入内容"
	          />
	        </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
     <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">

          <el-link type="info" style="font-size:12px" @click="importTemplate"
            >下载模板</el-link
          >
        </div>
        <div class="el-upload__tip" style="color:#ff0000" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMdmDataEntityShip,
  getMdmDataEntityShip,
  getMdmDataEntityShipByRel,
  delMdmDataEntityShip,
  addMdmDataEntityShip,
  updateMdmDataEntityShip,
  exportMdmDataEntityShip,
  findAllEntity,
  findAllEntityProp,
  importTemplate,
  findAllEntityUnTable,
  removeSource
} from "@/api/mdm/mdmDataEntityShip";
import { listSystem } from "@/api/basic/datasource";
import { getToken } from "@/utils/auth";
export default {
  name: "MdmDataEntityShip",
  components: {},
  data() {
    return {
      // 主题导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/mdm/mdmDataEntityShip/import"
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据实体关系表格数据
      mdmDataEntityShipList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 关系类型字典
      shipTypeOptions: [],
      //源表数据
      srcEntityData: [],
      srcEntityData2: [],
      //源表数据
      tarEntityData: [],
      tarEntityData2: [],

      entityData:[],
      //源表数据
      srcEntityPropData: [],
      //源表数据
      tarEntityPropData: [],
      systemOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        srcEntityId: null,
        tarEntityId: null,
        srcSystemId: null,
        tarSystemId: null,
        shipName: null,
        triggerType:"1"
      },
      // 表单参数
      form: {
        srcEntityPropId:null,
        tarEntityId:null,
        srcEntityId:null,
        tarEntityPropId:null
      },
      // 表单校验
      rules: {
        srcEntityId:[
          { required: true, message: '请选择源表', trigger: 'change' }
        ],
        tarEntityId:[
          { required: true, message: '请选择目标表', trigger: 'change' }
        ],
        shipType:[
          { required: true, message: '请选择关系类型', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("mdm_ship_type").then(response => {
      this.shipTypeOptions = response.data;
    });
    this.getSystem();
    this.getMdmEntity();
    this.getMdmEntityUnTable();
  },
  methods: {
    handleRelieve(row) {
      removeSource({shipId:row.shipId,entityId:row.entityId}).then(res=>{
        this.msgSuccess('解除来源成功')
        row.entityId = ''
        row.entityName=''
      })
    },
     /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
     /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "元数据导入";
      this.upload.open = true;
    },
    getMdmEntity(query,index) {
        if (query !== "") {
            this.loading = true;
            setTimeout(() => {
              this.loading = false;
              let mdmDataEntity = { pageNum: 1,pageSize: 20,tableName: query };
              findAllEntity(mdmDataEntity).then(response => {
                if(index==1){
                  this.srcEntityData = response.rows;
                }else if(index==2){
                  this.tarEntityData = response.rows;
                }else if(index==3){
                  this.srcEntityData2 = response.rows;
                }else if(index==4){
                  this.tarEntityData2 = response.rows;
                }
                else{
                  this.srcEntityData = response.rows;
                  this.tarEntityData = response.rows;
                  this.srcEntityData2 = response.rows;
                  this.tarEntityData2 = response.rows;
                }
              });
            }, 200);
          } else {
            if(index==1){
              this.srcEntityData = [];
            }else if(index==2){
              this.tarEntityData = [];
            }else if(index==3){
              this.srcEntityData2 = [];
            }else if(index==4){
              this.tarEntityData2 = [];
            }
            else{
              this.srcEntityData = [];
              this.tarEntityData = [];
              this.srcEntityData2 = [];
              this.tarEntityData2 = [];
            }
          }
    },
    getMdmEntityUnTable(query,index) {
        if (query !== "") {
            this.loading = true;
            setTimeout(() => {
              this.loading = false;
              let mdmDataEntity = { pageNum: 1,pageSize: 20,tableName: query };
              findAllEntityUnTable(mdmDataEntity).then(response => {
                 this.entityData = response.rows;
              });
            }, 200);
          } else {
        	  this.entityData = [];
          }
    },
    srcChanged(value,index) {
      if(index==1){
         this.form.srcEntityPropId=null
      }else{
        this.form.tarEntityPropId=null
      }
      if (value != null&&value != "") {
        let mdmDataEntityProp = { pageNum: 1,pageSize: 500,entityId:value};
        findAllEntityProp(mdmDataEntityProp).then(response => {
          if(index==1){
            this.srcEntityPropData = response.rows
          }else{
            this.tarEntityPropData = response.rows
          }
        });
      }else{
        if(index==1){
          this.srcEntityPropData = []
        }else{
          this.tarEntityPropData = []
        }
      }
    },
    /** 查询数据实体关系列表 */
    getList() {
      this.loading = true;
      listMdmDataEntityShip(this.queryParams).then(response => {
        this.mdmDataEntityShipList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 关系类型字典翻译
    shipTypeFormat(row, column) {
      return this.selectDictLabel(this.shipTypeOptions, row.shipType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.srcEntityPropData = [];
      this.tarEntityPropData = [];
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        shipId: null,
        srcEntityId: null,
        srcEntityPropId: null,
        tarEntityId: null,
        tarEntityPropId: null,
        shipType: null,
        transferRule: null,
        shipName: null,
        versionNo: null,
        systemId: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.shipId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据实体关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const shipId = row.shipId || this.ids;
      if(row.entityId!='undefined'&&row.entityId!=null){
    	  const mdmDataEntityShip ={"shipId":row.shipId,"entityId":row.entityId};
    	  getMdmDataEntityShipByRel(mdmDataEntityShip).then(response => {
              this.srcChanged(response.data.srcEntityId,1)
              this.srcChanged(response.data.tarEntityId,2)
              this.form = response.data;
              this.title = "修改数据实体关系";
              this.$nextTick(()=>{
                this.open = true;
              })
            }).then(response =>{
              this.entityData=[{entityId:row.entityId,tableName:row.entityName}];
              const srcTableName = row.srcTableName || this.form.srcTableName;
              this.srcEntityData2=[{entityId:row.srcEntityId,tableName:srcTableName}];
              const tarTableName = row.tarTableName || this.form.tarTableName;
              this.tarEntityData2=[{entityId:row.tarEntityId,tableName:tarTableName}];
            });
      }else{
        getMdmDataEntityShip(shipId).then(response => {
            this.srcChanged(response.data.srcEntityId,1)
            this.srcChanged(response.data.tarEntityId,2)
            this.form = response.data;
            this.title = "修改数据实体关系";
            this.$nextTick(()=>{
              this.open = true;
            })
          }).then(response =>{
            const srcTableName = row.srcTableName || this.form.srcTableName;
            this.srcEntityData2=[{entityId:row.srcEntityId,tableName:srcTableName}];
            const tarTableName = row.tarTableName || this.form.tarTableName;
            this.tarEntityData2=[{entityId:row.tarEntityId,tableName:tarTableName}];
        });
      }

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.shipId != null) {
            updateMdmDataEntityShip(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMdmDataEntityShip(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const shipIds = row.shipId || this.ids;
      this.$confirm(
        '是否确认删除数据实体关系编号为"' + shipIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delMdmDataEntityShip(shipIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有数据实体关系数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportMdmDataEntityShip(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    },
    getSystem() {
      listSystem().then(response => {
        this.systemOptions = response.data;
      });
    }
  }
};
</script>
