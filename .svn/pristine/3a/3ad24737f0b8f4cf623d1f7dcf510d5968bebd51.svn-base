package com.dqms.dqm.service.impl;



import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.dqm.domain.DqmValidationMould;
import com.dqms.dqm.domain.DqmValidationMouldParameter;
import com.dqms.dqm.domain.DqmValidationMouldPojo;
import com.dqms.dqm.mapper.DqmValidationMouldMapper;
import com.dqms.dqm.mapper.DqmValidationMouldParameterMapper;
import com.dqms.dqm.service.IDqmValidationMouldService;

/**
 * 质量模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@Service
public class DqmValidationMouldServiceImpl implements IDqmValidationMouldService
{
    @Autowired
    private DqmValidationMouldMapper dqmValidationMouldMapper;

    @Autowired
    private DqmValidationMouldParameterMapper dqmValidationMouldParameterMapper;

    /**
     * 查询质量模板
     *
     * @param validationMouldId 质量模板ID
     * @return 质量模板
     */
    @Override
    public DqmValidationMould selectDqmValidationMouldById(Integer validationMouldId)
    {
        return dqmValidationMouldMapper.selectDqmValidationMouldById(validationMouldId);
    }

    /**
     * 查询质量模板列表
     *
     * @param dqmValidationMould 质量模板
     * @return 质量模板
     */
    @Override
    public List<DqmValidationMould> selectDqmValidationMouldList(DqmValidationMould dqmValidationMould)
    {
        return dqmValidationMouldMapper.selectDqmValidationMouldList(dqmValidationMould);
    }

    /**
     * 新增质量模板
     *
     * @param dqmValidationMouldPojo 质量模板
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDqmValidationMould(DqmValidationMouldPojo dqmValidationMouldPojo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        DqmValidationMould dqmValidationMould = dqmValidationMouldPojo.getDqmValidationMould();
        dqmValidationMould.setCreateTime(DateUtils.getNowDate());
        dqmValidationMould.setCreateId(loginUser.getUser().getUserId());
        dqmValidationMould.setCreateBy(loginUser.getUser().getNickName());
        dqmValidationMould.setUpdateTime(DateUtils.getNowDate());
        dqmValidationMould.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        dqmValidationMould.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        List<DqmValidationMould> t=dqmValidationMouldMapper.selectDqmValidationMouldByName(dqmValidationMould);
        if(t!=null&&t.size()!=0){
            throw new RuntimeException("模板名称已经存在！");
        }
        int returnVal = dqmValidationMouldMapper.insertDqmValidationMould(dqmValidationMould);
        List<DqmValidationMouldParameter> dqmValidationMouldParameter = dqmValidationMouldPojo.getDqmValidationMouldParameter();
        if (!CollectionUtils.isEmpty(dqmValidationMouldParameter)) {
            // 参数赋值主表ID
            dqmValidationMouldParameter.forEach(p->{
                p.setValidationMouldId(dqmValidationMould.getValidationMouldId());
                p.setCreateTime(DateUtils.getNowDate());
                p.setCreateId(loginUser.getUser().getUserId());
                p.setCreateBy(loginUser.getUser().getNickName());
            });
            // method1 循环插入
            for (DqmValidationMouldParameter validationMouldParameter : dqmValidationMouldParameter) {
                dqmValidationMouldParameterMapper.insertDqmValidationMouldParameter(validationMouldParameter);
            }
        }
        return returnVal;
    }

    /**
     * 修改质量模板
     *
     * @param dqmValidationMould 质量模板
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDqmValidationMould(DqmValidationMould dqmValidationMould)
    {
        dqmValidationMould.setUpdateTime(DateUtils.getNowDate());
        dqmValidationMould.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        dqmValidationMould.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        List<DqmValidationMould> t=dqmValidationMouldMapper.selectDqmValidationMouldByName(dqmValidationMould);
        if(t.size()>1){
            throw new RuntimeException("模板名称已经存在！");
        }else if(t.size()==1){
             if(!t.get(0).getValidationMouldId().equals(dqmValidationMould.getValidationMouldId())) {
                 throw new RuntimeException("模板名称已经存在！");
             }
        }
        return dqmValidationMouldMapper.updateDqmValidationMould(dqmValidationMould);
    }

    /**
     * 批量删除质量模板
     *
     * @param validationMouldIds 需要删除的质量模板ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationMouldByIds(Integer[] validationMouldIds)
    {
        return dqmValidationMouldMapper.deleteDqmValidationMouldByIds(validationMouldIds);
    }

    /**
     * 删除质量模板信息
     *
     * @param validationMouldId 质量模板ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationMouldById(Integer validationMouldId)
    {
        return dqmValidationMouldMapper.deleteDqmValidationMouldById(validationMouldId);
    }

    /**
     * 根据模板id批量删除模板参数
     *
     * @param validationMouldIds 需要删除的模板参数ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationMouldParameterByMouldIds(Integer[] validationMouldIds)
    {
        return dqmValidationMouldMapper.deleteDqmValidationMouldParameterByMouldIds(validationMouldIds);
    }

    @Override
    public List<DqmValidationMould> selectMould(DqmValidationMould dqmValidationMould) {
        return dqmValidationMouldMapper.selectMould(dqmValidationMould);
    }

}

