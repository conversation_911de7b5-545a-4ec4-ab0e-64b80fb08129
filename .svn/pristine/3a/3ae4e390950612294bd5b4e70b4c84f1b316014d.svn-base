import request from '@/utils/request'

// 查询制度管理列表
export function listDsmSystemManagement(query) {
  return request({
    url: '/dsm/dsmSystemManagement/list',
    method: 'get',
    params: query
  })
}

// 查询制度管理详细
export function getDsmSystemManagement(systemId) {
  return request({
    url: '/dsm/dsmSystemManagement/' + systemId,
    method: 'get'
  })
}

// 新增制度管理
export function addDsmSystemManagement(data) {
  return request({
    url: '/dsm/dsmSystemManagement',
    method: 'post',
    data: data
  })
}

// 修改制度管理
export function updateDsmSystemManagement(data) {
  return request({
    url: '/dsm/dsmSystemManagement',
    method: 'put',
    data: data
  })
}

// 删除制度管理
export function delDsmSystemManagement(systemId) {
  return request({
    url: '/dsm/dsmSystemManagement/' + systemId,
    method: 'delete'
  })
}

// 导出制度管理
export function exportDsmSystemManagement(query) {
  return request({
    url: '/dsm/dsmSystemManagement/export',
    method: 'get',
    params: query
  })
}

// 下载制度管理
export function downloadSystem(data) {
  return request({
    url: '/dsm/dsmSystemManagement/downloadSystem',
    method: 'post',
    params: data,
  })
}
