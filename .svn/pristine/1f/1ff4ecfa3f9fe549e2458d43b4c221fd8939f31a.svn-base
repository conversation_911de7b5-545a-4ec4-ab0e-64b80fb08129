package com.dqms.mdm.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.mdm.domain.MdmDataEntityShip;
import com.dqms.mdm.domain.vo.MdmDataEntityVo;

/**
 * 数据实体关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-10
 */
public interface MdmDataEntityShipMapper 
{
    /**
     * 查询数据实体关系
     * 
     * @param shipId 数据实体关系ID
     * @return 数据实体关系
     */
    public MdmDataEntityShip selectMdmDataEntityShipById(Long shipId);
    public MdmDataEntityShip selectMdmDataEntityShipByRel(MdmDataEntityShip mdmDataEntityShip);

    /**
     * 查询数据实体关系列表
     * 
     * @param mdmDataEntityShip 数据实体关系
     * @return 数据实体关系集合
     */
    public List<MdmDataEntityShip> selectMdmDataEntityShipList(MdmDataEntityShip mdmDataEntityShip);
    public List<MdmDataEntityShip> selectMdmDataEntityShipListByRel(MdmDataEntityShip mdmDataEntityShip);
    public List<MdmDataEntityShip> selectMdmDataEntityShipForTableRelList(MdmDataEntityShip mdmDataEntityShip);

    /**
     * 新增数据实体关系
     * 
     * @param mdmDataEntityShip 数据实体关系
     * @return 结果
     */
    public int insertMdmDataEntityShip(MdmDataEntityShip mdmDataEntityShip);

    /**
     * 修改数据实体关系
     * 
     * @param mdmDataEntityShip 数据实体关系
     * @return 结果
     */
    public int updateMdmDataEntityShip(MdmDataEntityShip mdmDataEntityShip);

    /**
     * 删除数据实体关系
     * 
     * @param shipId 数据实体关系ID
     * @return 结果
     */
    public int deleteMdmDataEntityShipById(Long shipId);
    public int deleteMdmDataEntityShipByEntityId(@Param("srcEntityId")Long srcEntityId,@Param("tarEntityId")Long tarEntityId);

    /**
     * 批量删除数据实体关系
     * 
     * @param shipIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMdmDataEntityShipByIds(Long[] shipIds);
    
    /**
     * 根据任务关系查询血缘或影响
     *
     * @param tarEntityIds 数据实体关系
     * @return 数据实体关系集合
     */
    public List<MdmDataEntityVo> findShipMdmDataEntityVoByTarEntityIds(@Param("tarEntityIds")Long[] tarEntityIds , @Param("tarEntityPropIds")Long[] tarEntityPropIds);
    public List<MdmDataEntityVo> findShipMdmDataEntityVoBySrcEntityIds(@Param("srcEntityIds")Long[] srcEntityIds , @Param("srcEntityPropIds")Long[] srcEntityPropIds);
    public List<MdmDataEntityVo> findShipMdmDataEntityVoByTarEntityPropIds(@Param("tarEntityIds")Long[] tarEntityIds , @Param("tarEntityPropIds")Long[] tarEntityPropIds);
    public List<MdmDataEntityVo> findShipMdmDataEntityVoBySrcEntityPropIds(@Param("srcEntityIds")Long[] srcEntityIds , @Param("srcEntityPropIds")Long[] srcEntityPropIds);
    public List<MdmDataEntityVo> findShipListByTheme(@Param("themeId")Long themeId );
    /**
     * 通过源系统名称、表名、字段名获取对应ID
     *
     * @param mdmDataEntityShip 数据实体关系
     * @return 数据实体关系集合
     */
    public List<MdmDataEntityShip> selectSrcMdmDataEntityShipList(MdmDataEntityShip mdmDataEntityShip);
    /**
     * 通过目标系统名称、表名、字段名获取对应ID
     *
     * @param mdmDataEntityShip 数据实体关系
     * @return 数据实体关系集合
     */

    public List<MdmDataEntityShip> selectTarMdmDataEntityShipList(MdmDataEntityShip mdmDataEntityShip);

    public int removeSource(MdmDataEntityShip mdmDataEntityShip);
    public int insertMdmDataEntityShipList(List<MdmDataEntityShip> mdmDataEntityShipList) ;
}
