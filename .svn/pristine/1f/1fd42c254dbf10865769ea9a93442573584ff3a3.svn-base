import request from '@/utils/request'


export function getDqmKnowledgeBaseById(val) {
  return request({
    url: "/dqm/dqmKnowledgeBase/getDqmKnowledgeBaseById/" + val,
    method: "get"
  });
}
// 查询质量检查规则知识库列表
export function listDqmKnowledgeBase(query) {
  return request({
    url: '/dqm/dqmKnowledgeBase/list',
    method: 'get',
    params: query
  })
}

// 查询质量检查规则知识库详细
export function getDqmKnowledgeBase(knowledgeBaseId) {
  return request({
    url: '/dqm/dqmKnowledgeBase/' + knowledgeBaseId,
    method: 'get'
  })
}

// 新增质量检查规则知识库
export function addDqmKnowledgeBase(data) {
  return request({
    url: '/dqm/dqmKnowledgeBase',
    method: 'post',
    data: data
  })
}

// 修改质量检查规则知识库
export function updateDqmKnowledgeBase(data) {
  return request({
    url: '/dqm/dqmKnowledgeBase',
    method: 'put',
    data: data
  })
}

// 删除质量检查规则知识库
export function delDqmKnowledgeBase(knowledgeBaseId) {
  return request({
    url: '/dqm/dqmKnowledgeBase/' + knowledgeBaseId,
    method: 'delete'
  })
}

// 导出质量检查规则知识库
export function exportDqmKnowledgeBase(query) {
  return request({
    url: '/dqm/dqmKnowledgeBase/export',
    method: 'get',
    params: query
  })
}
