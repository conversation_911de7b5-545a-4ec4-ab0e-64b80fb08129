<template>
  <div>
    <el-table
      v-el-table-infinite-scroll="handleCurrentChange"
      height="400px"
      border
      :data="dsmStandardMdmUnRelList">
      <!--<el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="系统" align="center" prop="systemName" />
      <el-table-column label="数据源" align="center" prop="datasourceName" />
      <el-table-column label="用户" align="center" prop="tableSchema" />
      <el-table-column label="表" align="center" prop="tableName" />
      <el-table-column label="字段" align="center" prop="propName" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot="header" slot-scope="scope">
          <el-input
            v-model="relSearch"
            size="mini"
            placeholder="输入表名或字段名"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="getUnRelList"
              size="mini"
            ></el-button>
          </el-input>
        </template>
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-share"
            @click="addRelMdm(scope.row, scope.$index)"
            v-hasPermi="['dsm:dsmStandard:remove']"
            >引用</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination
      @current-change="handleCurrentChange"
      :current-page="currentPage4"
      :page-size="50"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination> -->
  </div>
</template>

<script>
import {
  listDsmStandardMdmRel,
  addDsmStandardMdmRel
} from "@/api/dsm/dsmStandard";
import elTableInfiniteScroll from 'el-table-infinite-scroll';
export default {
  props: ["loading", "standardId", "listUnDsmStandardMdmRel", "handleType"],
  data() {
    return {
      relSearch: "",
      dsmStandardMdmUnRelList: [],
      total: 20,
      pageNum:1
    };
  },
  directives: {
    'el-table-infinite-scroll': elTableInfiniteScroll
  },
  created() {
    // this.addRelMdmForm();
  },
  methods: {
    addRelMdm(row, index) {
      if (this.handleType == "UPDATA") {
        const dsmMdmForm = { standardId: this.standardId, propId: row.propId };
        addDsmStandardMdmRel(dsmMdmForm).then(response => {
          this.msgSuccess("修改成功");
          const relQueryParams = {
            standardId: this.standardId,
            pageNum: 1,
            pageSize: 20
          };
//          listDsmStandardMdmRel(relQueryParams).then(response => {

//          });
          this.$emit("dsmStandardMdmRelListChange", row);
          this.dsmStandardMdmUnRelList.splice(index, 1);
        });
      } else {
        this.$emit("dsmStandardMdmRelListChange", row);
        this.dsmStandardMdmUnRelList.splice(index, 1);
      }
    },
    handleCurrentChange() {
      this.pageNum++
      const relQueryParams = {
        standardId: this.standardId,
        propName: this.relSearch,
        pageNum: this.pageNum,
        pageSize: 20
      };
      if(this.pageNum*20<=this.total){
        this.list(relQueryParams);
      }
    },
    addRelMdmForm() {
      const relQueryParams = {
        standardId: this.standardId,
        pageNum: this.pageNum,
        pageSize: 20
      };
      this.list(relQueryParams);
    },
    list(relQueryParams) {
      this.listUnDsmStandardMdmRel(relQueryParams).then(response => {
        this.dsmStandardMdmUnRelList.push(...response.rows);
        this.total = response.total;
      });
    },
    getUnRelList() {
      this.pageNum = 1
      const relQueryParams = {
        standardId: this.standardId,
        propName: this.relSearch,
        pageNum: this.pageNum,
        pageSize: 20
      };
      this.dsmStandardMdmUnRelList = []
      this.list(relQueryParams);
    }
  }
};
</script>

<style></style>
