package com.dqms.dic.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 字段规则对象 dic_manual_data_install_rule
 *
 * <AUTHOR>
 * @date 2022-06-28
 */
public class DicManualDataInstallRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 字段ID */
    @Excel(name = "字段ID")
    private Long manualDataInstallId;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long manualDataRuleId;

    public void setManualDataInstallId(Long manualDataInstallId)
    {
        this.manualDataInstallId = manualDataInstallId;
    }

    public Long getManualDataInstallId()
    {
        return manualDataInstallId;
    }
    public void setManualDataRuleId(Long manualDataRuleId)
    {
        this.manualDataRuleId = manualDataRuleId;
    }

    public Long getManualDataRuleId()
    {
        return manualDataRuleId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("manualDataInstallId", getManualDataInstallId())
            .append("manualDataRuleId", getManualDataRuleId())
            .toString();
    }
}
