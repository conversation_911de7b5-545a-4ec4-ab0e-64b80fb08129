<template>
  <div class="app-container" style="height: calc(100vh - 84px);">
    <el-container style="height: 100%; border: 1px solid #eee">
      <el-aside
        style="background-color: rgb(48, 65, 86);padding-top:25px;margin-bottom: 0px;border-radius: 10px;"
      >
        <div id="runWcl" style="width:100%; height:200px"></div>
        <el-divider>{{ dsmCheckMain.startTime }}</el-divider>
        <el-form :model="form" ref="form" :inline="true" label-width="70px">
          <div>
            <el-checkbox-group v-model="form.discernTypeLabels" size="small">
              <el-checkbox-button
                v-for="dict in discernTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-checkbox-button>
            </el-checkbox-group>
          </div>
          <div style="height: calc(100vh - 550px);overflow-y:auto;">
            <el-checkbox-group v-model="form.systemLabels" size="small">
              <el-checkbox
                v-for="item in systemOptions"
                :key="item.systemId"
                :label="item.name"
                :value="item.systemId"
                style="display:block;margin:5px 0px ;color:#FFF;"
                border
              ></el-checkbox>
            </el-checkbox-group>
          </div>
          <br />
          <div style="text-align:center;">
            <el-button
              icon="el-icon-caret-right"
              :disabled="
                form.systemLabels.length == 0 ||
                form.discernTypeLabels.length == 0
                  ? true
                  : false
              "
              type="primary"
              @click="submitForm"
              plain
              round
              >立即执行</el-button
            >
          </div>
        </el-form>
      </el-aside>
      <el-main>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="70px"
        >
          <el-form-item label="标准" prop="dsmId">
            <el-input
              v-model="queryParams.dsmId"
              placeholder="请输入标准"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="字段" prop="prodId">
            <el-input
              v-model="queryParams.prodId"
              placeholder="请输入字段"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['dsm:dsmCheck:remove']"
              >删除</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
            :refreshShow="true"
            :searchShow="true"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="dsmCheckList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="类型"
            align="center"
            prop="checkType"
            :formatter="discernTypeFormat"
            width="120"
          />
          <el-table-column
            label="标准"
            align="center"
            prop="dsmName"
            width="200"
          />
          <el-table-column
            label="字段"
            align="center"
            prop="prodName"
            width="350"
          />
          <el-table-column label="对标结果" align="center" prop="checkMsg" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['dsm:dsmCheck:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import {
  listDsmCheck,
  getDsmCheck,
  delDsmCheck,
  addDsmCheck,
  updateDsmCheck,
  exportDsmCheck,
  getRunWcl
} from "@/api/dsm/dsmCheck";
import { listDsmCheckMain } from "@/api/dsm/dsmCheckMain";
import { listSystemAll } from "@/api/basic/system";

export default {
  name: "DsmCheck",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 标准对标表格数据
      dsmCheckList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 字段ID字典
      prodIdOptions: [],
      // 标准落标表格数据
      dsmCheckList: [],
      dsmCheckMain: {},
      // 推荐类型字典
      discernTypeOptions: [],
      // 系统字典
      systemOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dsmId: null,
        prodId: null,
        checkMsg: null
      },
      // 表单参数
      form: {
        discernTypes: [],
        systems: [],
        discernTypeLabels: [],
        systemLabels: []
      },
      // 表单校验
      rules: {}
    };
  },
  created() {
    listDsmCheckMain(this.queryParams).then(response => {
      if (response.rows != null && response.rows.length > 0) {
        this.dsmCheckMain = response.rows[0];
        this.form.discernTypeLabels = this.dsmCheckMain.discernTypeLabels.split(
          ","
        );
        this.form.systemLabels = this.dsmCheckMain.systemLabels.split(",");
        this.showRunWcl(
          ((this.dsmCheckMain.runNum / this.dsmCheckMain.dsmNum) * 100).toFixed(
            2
          )
        );
      } else {
        this.showRunWcl(0);
      }
    });
    this.getList();
    this.getDicts("dsm_type").then(response => {
      this.discernTypeOptions = response.data;
    });
    listSystemAll().then(response => {
      this.systemOptions = response.data;
    });
  },
  methods: {
    /** 查询标准对标列表 */
    getList() {
      this.loading = true;
      listDsmCheck(this.queryParams).then(response => {
        this.dsmCheckList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 推荐类型字典翻译
    discernTypeFormat(row, column) {
      return this.selectDictLabel(this.discernTypeOptions, row.checkType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        discernTypes: [],
        systems: [],
        discernTypeLabels: [],
        systemLabels: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.checkId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 提交按钮 */
    submitForm() {
      let systems = [];
      for (let i = 0; i < this.systemOptions.length; i++) {
        if (this.form.systemLabels.indexOf(this.systemOptions[i].name) != -1) {
          systems.push(this.systemOptions[i].systemId);
        }
      }
      this.form.systems = systems;

      let types = [];
      for (let i = 0; i < this.discernTypeOptions.length; i++) {
        if (
          this.form.discernTypeLabels.indexOf(
            this.discernTypeOptions[i].dictLabel
          ) != -1
        ) {
          types.push(this.discernTypeOptions[i].dictValue);
        }
      }
      this.form.discernTypes = types;
      console.log(types);
      this.$refs["form"].validate(valid => {
        if (valid) {
          addDsmCheck(this.form).then(response => {
            this.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const checkIds = row.checkId || this.ids;
      this.$confirm(
        '是否确认删除标准对标编号为"' + checkIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delDsmCheck(checkIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    showRunWcl(value_) {
      getRunWcl(value_);
    }
  }
};
</script>
<style scoped>
>>> .el-divider__text {
  background-color: rgb(48, 65, 86);
  color: #fff;
  width: 180px;
}
>>> .el-checkbox {
  margin-left: 0px;
}
</style>
