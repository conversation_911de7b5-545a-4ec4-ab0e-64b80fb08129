package com.dqms.dqm.service;

import java.util.List;
import com.dqms.dqm.domain.DqmValidationMould;
import com.dqms.dqm.domain.DqmValidationMouldPojo;
import com.dqms.mdm.domain.MdmDataEntityProp;

/**
 * 质量模板Service接口
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
public interface IDqmValidationMouldService
{
    /**
     * 查询质量模板
     *
     * @param validationMouldId 质量模板ID
     * @return 质量模板
     */
    public DqmValidationMould selectDqmValidationMouldById(Integer validationMouldId);

    /**
     * 查询质量模板列表
     *
     * @param dqmValidationMould 质量模板
     * @return 质量模板集合
     */
    public List<DqmValidationMould> selectDqmValidationMouldList(DqmValidationMould dqmValidationMould);

    /**
     * 新增质量模板
     *
     * @param dqmValidationMouldPojo 质量模板
     * @return 结果
     */
    public int insertDqmValidationMould(DqmValidationMouldPojo dqmValidationMouldPojo);
    /**
     * 修改质量模板
     *
     * @param dqmValidationMould 质量模板
     * @return 结果
     */
    public int updateDqmValidationMould(DqmValidationMould dqmValidationMould);

    /**
     * 批量删除质量模板
     *
     * @param validationMouldIds 需要删除的质量模板ID
     * @return 结果
     */
    public int deleteDqmValidationMouldByIds(Integer[] validationMouldIds);

    /**
     * 删除质量模板信息
     *
     * @param validationMouldId 质量模板ID
     * @return 结果
     */
    public int deleteDqmValidationMouldById(Integer validationMouldId);
    /**
     * 根据模板id批量删除模板参数
     *
     * @param validationMouldIds 需要删除的模板ID
     * @return 结果
     */
    public int deleteDqmValidationMouldParameterByMouldIds(Integer[] validationMouldIds);


    List<DqmValidationMould> selectMould(DqmValidationMould dqmValidationMould);


}
