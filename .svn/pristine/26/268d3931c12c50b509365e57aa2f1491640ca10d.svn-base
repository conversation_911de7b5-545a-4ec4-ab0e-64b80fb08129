package com.dqms.dsm.mapper;

import java.util.List;

import com.dqms.dsm.domain.DsmIndex;
import com.dqms.dsm.domain.DsmIndexDimension;
import com.dqms.dsm.domain.DsmIndexIndex;
import com.dqms.dsm.domain.vo.DsmIndexVo;
import org.apache.ibatis.annotations.Param;

/**
 * 指标管理Mapper接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmIndexMapper
{
    /**
     * 查询指标管理
     *
     * @param indexId 指标管理ID
     * @return 指标管理
     */
    public DsmIndex selectDsmIndexById(Long indexId);
    public DsmIndex selectDsmIndexByCodeOrName(@Param("indexCode") String indexCode,@Param("indexName") String indexName);
    public DsmIndexVo selectDsmIndexVoById(Long indexId);

    /**
     * 查询指标管理列表
     *
     * @param dsmIndex 指标管理
     * @return 指标管理集合
     */
    public List<DsmIndex> selectDsmIndexList(DsmIndex dsmIndex);

    public List<DsmIndex> selectDsmIndexListByPage(DsmIndex dsmIndex);
    public List<DsmIndex> selectDsmIndexListForShow(DsmIndex dsmIndex);

    public List<DsmIndexVo> selectDsmIndexVoList(DsmIndex dsmIndex);

    public List<DsmIndex> selectDsmIndexListByNo(DsmIndex dsmIndex);

    public List<DsmIndex> selectDsmIndexListMap(Long[] indexIds);

    public List<DsmIndex> selectDsmIndexRelBySRCList(Long[] indexIds);

    /**
     * 新增指标管理
     *
     * @param dsmIndex 指标管理
     * @return 结果
     */
    public int insertDsmIndex(DsmIndex dsmIndex);
    public int insertDsmIndexIndex(List<DsmIndexIndex> list);
    public int insertDsmIndexDimension(List<DsmIndexDimension> list);

    /**
     * 修改指标管理
     *
     * @param dsmIndex 指标管理
     * @return 结果
     */
    public int updateDsmIndex(DsmIndex dsmIndex);
    public int updateDsmIndexIndexBySou(@Param("indexId")Long indexId,@Param("oldIndexId")Long oldIndexId);
    public int updateDsmIndexIndexByTar(@Param("indexId")Long indexId,@Param("oldIndexId")Long oldIndexId);
    public int updateDsmIndexDimension(@Param("indexId")Long indexId,@Param("oldIndexId")Long oldIndexId);
    public int updateDsmIndexStatus(DsmIndex dsmIndex);



    /**
     * 删除指标管理
     *
     * @param indexId 指标管理ID
     * @return 结果
     */
    public int deleteDsmIndexById(Long indexId);

    /**
     * 批量删除指标管理
     *
     * @param indexIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmIndexByIds(Long[] indexIds);
    public List<DsmIndex> selectDsmIndexByName(DsmIndex dsmIndex);
    public List<DsmIndex> selectDsmIndexByCode(DsmIndex dsmIndex);

    public int deleteDsmIndexByNos(String indexNo);

}
