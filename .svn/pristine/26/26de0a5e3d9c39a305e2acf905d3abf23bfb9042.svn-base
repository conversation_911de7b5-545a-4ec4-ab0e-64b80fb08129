package com.dqms.dsm.service;

import java.util.List;
import com.dqms.dsm.domain.DsmModelEntityPropTemp;

/**
 * 模型字段Service接口
 * 
 * <AUTHOR>
 * @date 2021-12-29
 */
public interface IDsmModelEntityPropTempService 
{
    /**
     * 查询模型字段
     * 
     * @param modelEntityPropTempId 模型字段ID
     * @return 模型字段
     */
    public DsmModelEntityPropTemp selectDsmModelEntityPropTempById(Long modelEntityPropTempId);

    /**
     * 查询模型字段列表
     * 
     * @param dsmModelEntityPropTemp 模型字段
     * @return 模型字段集合
     */
    public List<DsmModelEntityPropTemp> selectDsmModelEntityPropTempList(DsmModelEntityPropTemp dsmModelEntityPropTemp);

    /**
     * 新增模型字段
     * 
     * @param dsmModelEntityPropTemp 模型字段
     * @return 结果
     */
    public int insertDsmModelEntityPropTemp(DsmModelEntityPropTemp dsmModelEntityPropTemp);

    /**
     * 修改模型字段
     * 
     * @param dsmModelEntityPropTemp 模型字段
     * @return 结果
     */
    public int updateDsmModelEntityPropTemp(DsmModelEntityPropTemp dsmModelEntityPropTemp);

    /**
     * 批量删除模型字段
     * 
     * @param modelEntityPropTempIds 需要删除的模型字段ID
     * @return 结果
     */
    public int deleteDsmModelEntityPropTempByIds(Long[] modelEntityPropTempIds);

    /**
     * 删除模型字段信息
     * 
     * @param modelEntityPropTempId 模型字段ID
     * @return 结果
     */
    public int deleteDsmModelEntityPropTempById(Long modelEntityPropTempId);
}
