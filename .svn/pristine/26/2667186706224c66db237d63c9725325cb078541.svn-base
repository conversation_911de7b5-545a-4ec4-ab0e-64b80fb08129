import request from '@/utils/request'

// 查询主数据配置列表
export function listDsmMasterDataInstall(query) {
  return request({
    url: '/dsm/dsmMasterDataInstall/list',
    method: 'get',
    params: query
  })
}

// 查询主数据配置详细
export function getDsmMasterDataInstall(masterDataInstallId) {
  return request({
    url: '/dsm/dsmMasterDataInstall/' + masterDataInstallId,
    method: 'get'
  })
}

// 新增主数据配置
export function addDsmMasterDataInstall(data) {
  return request({
    url: '/dsm/dsmMasterDataInstall',
    method: 'post',
    data: data
  })
}

// 修改主数据配置
export function updateDsmMasterDataInstall(data) {
  return request({
    url: '/dsm/dsmMasterDataInstall',
    method: 'put',
    data: data
  })
}

// 删除主数据配置
export function delDsmMasterDataInstall(masterDataInstallId) {
  return request({
    url: '/dsm/dsmMasterDataInstall/' + masterDataInstallId,
    method: 'delete'
  })
}

// 导出主数据配置
export function exportDsmMasterDataInstall(query) {
  return request({
    url: '/dsm/dsmMasterDataInstall/export',
    method: 'get',
    params: query
  })
}