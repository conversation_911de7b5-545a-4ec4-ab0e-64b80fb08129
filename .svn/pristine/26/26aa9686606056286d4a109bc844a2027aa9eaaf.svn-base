package com.dqms.system.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.constant.UserConstants;
import com.dqms.system.domain.SysNotice;
import com.dqms.system.mapper.SysNoticeMapper;
import com.dqms.system.service.ISysNoticeService;

/**
 * 公告 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl implements ISysNoticeService
{
    @Autowired
    private SysNoticeMapper noticeMapper;

    /**
     * 查询公告信息
     * 
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId)
    {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectNoticeList(SysNotice notice)
    {
        return noticeMapper.selectNoticeList(notice);
    }
    /**
     * 查询我的消息列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectMyNoticeList(SysNotice notice)
    {
        return noticeMapper.selectMyNoticeList(notice);
    }


    /**
     * 查询我的公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectMyNoticeGgList(SysNotice notice)
    {
        return noticeMapper.selectMyNoticeGgList(notice);
    }

    @Override
    public SysNotice getMultiIndexDescription() {
        return noticeMapper.getMultiIndexDescription();
    }

    @Override
    public SysNotice getSingleIndexDescription() {
        return noticeMapper.getSingleIndexDescription();
    }

    /**
     * 新增公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int insertNotice(SysNotice notice)
    {
        return noticeMapper.insertNotice(notice);
    }

    /**
     * 修改公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int updateNotice(SysNotice notice)
    {
        return noticeMapper.updateNotice(notice);
    }

    /**
     * 修改全部的阅读状态
     *
     * @return 结果
     */
    @Override
    public int updateNoticesByIds(String receiverId) {
        return noticeMapper.updateNoticesByIds(receiverId);
    }

    /**
     * 根据id修改公告的阅读状态
     *
     * @param noticeId 公告id
     * @param status   阅读状态
     * @return 结果
     */
    @Override
    public int updateNoticeStatusById(Long noticeId, String status) {
        return noticeMapper.updateNoticeStatusById(noticeId,status);
    }
    /**
     * 根据id修改公告的阅读状态-非消息类
     *
     * @param noticeId 公告id
     * @param status   阅读状态
     * @return 结果
     */
    @Override
    public int updateNoticeStatusByFxx(Long noticeId, String status, String userNo) {
    	if(status.equals(UserConstants.YES)) {
    		return noticeMapper.insertNoticeReceiver(noticeId,userNo);
    	}else {
    		return noticeMapper.deleteNoticeReceiver(noticeId,userNo);
    	}
        
    }

    /**
     * 删除公告对象
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId)
    {
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds)
    {
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }

    /**
     * 查看未读的消息
     *
     * @param receiver_id
     * @return
     */
    @Override
    public int selectNoticeByReceiverId(String receiver_id) {
        return noticeMapper.selectNoticeByReceiverId(receiver_id);
    }
}
