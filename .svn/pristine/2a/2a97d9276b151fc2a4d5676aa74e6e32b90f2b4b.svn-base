<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmDimensionDetailRelForDownOrUpMapper">
    
    <resultMap type="DsmDimensionDetailRelForDownOrUp" id="DsmDimensionDetailRelForDownOrUp">
        <result property="souDimensionDetailId"    column="sou_dimension_detail_id"    />
        <result property="tarDimensionDetailId"    column="tar_dimension_detail_id"    />
        <result property="souSystemName"    column="sou_dimension_system_name"    />
        <result property="souSysDictTypeCode"    column="sou_system_dict_type_code"    />
        <result property="souSysDictCode"    column="sou_system_dict_code"    />
        <result property="tarSystemName"    column="tar_dimension_system_name"    />
        <result property="tarSysDictTypeCode"    column="tar_system_dict_type_code"    />
        <result property="tarSysDictCode"    column="tar_system_dict_code"    />

    </resultMap>

    <sql id="selectDsmDimensionDetailRelVo">
       SELECT
	t.sou_dimension_detail_id,
	t.tar_dimension_detail_id,
	ss. NAME AS sou_dimension_system_name,
	sd. dimension_code as sou_system_dict_type_code,
	sdd. detail_code as sou_system_dict_code,
	ts. NAME AS tar_dimension_system_name
	td. dimension_code as tar_system_dict_type_code,
	tdd. detail_code as tar_system_dict_code,

FROM
	dsm_dimension_detail_rel t
LEFT JOIN dsm_dimension_detail sdd ON t.sou_dimension_detail_id = sdd.dimension_detail_id
LEFT JOIN dsm_dimension sd ON sdd.dimension_id = sd.dimension_id
LEFT JOIN sys_system ss ON sd.system_id = ss.system_id
LEFT JOIN dsm_dimension_detail tdd ON t.tar_dimension_detail_id = tdd.dimension_detail_id
LEFT JOIN dsm_dimension td ON tdd.dimension_id = td.dimension_id
LEFT JOIN sys_system ts ON td.system_id = ts.system_id
    </sql>


    
    <select id="selectDsmDimensionDetailRelForDownOrUpList" resultMap="DsmDimensionDetailRelForDownOrUp">
      select

	ss. NAME AS sou_dimension_system_name,
	sd. dimension_code as sou_system_dict_type_code,
	sdd. detail_code as sou_system_dict_code,
	ts. NAME AS tar_dimension_system_name,
	td. dimension_code as tar_system_dict_type_code,
	tdd. detail_code as tar_system_dict_code

FROM
	dsm_dimension_detail_rel t
LEFT JOIN dsm_dimension_detail sdd ON t.sou_dimension_detail_id = sdd.dimension_detail_id
LEFT JOIN dsm_dimension sd ON sdd.dimension_id = sd.dimension_id
LEFT JOIN sys_system ss ON sd.system_id = ss.system_id
LEFT JOIN dsm_dimension_detail tdd ON t.tar_dimension_detail_id = tdd.dimension_detail_id
LEFT JOIN dsm_dimension td ON tdd.dimension_id = td.dimension_id
LEFT JOIN sys_system ts ON td.system_id = ts.system_id
		<where>
			<if test="dimensionName != null  and dimensionName != ''"> and sd.dimension_name like concat('%', #{dimensionName}, '%')</if>
			<if test="dimensionCode != null  and dimensionCode != ''"> and sd.dimension_code = #{dimensionCode}</if>
			<if test="dimensionType != null  and dimensionType != ''"> and sd.dimension_type = #{dimensionType}</if>
			<if test="systemId != null "> and sd.system_id = #{systemId}</if>
			<if test="datasourceId != null "> and sd.datasource_id = #{datasourceId}</if>
			<if test="showType != null  and showType != ''"> and sd.show_type = #{showType}</if>
			<if test="dataType != null  and dataType != ''"> and sd.data_type = #{dataType}</if>
			<if test="status != null  and status != ''"> and sd.status = #{status}</if>
		</where>
    </select>
    


    <select id="selectDsmDimensionDetailRelForDownOrUpList_sou" parameterType="Long" resultMap="DsmDimensionDetailRelForDownOrUp">
    select
    t.sou_dimension_detail_id,
	t.tar_dimension_detail_id,
	ss. NAME AS sou_dimension_system_name,
	sd. dimension_code as sou_system_dict_type_code,
	sdd. detail_code as sou_system_dict_code,
	ts. NAME AS tar_dimension_system_name,
	td. dimension_code as tar_system_dict_type_code,
	tdd. detail_code as tar_system_dict_code

FROM
	dsm_dimension_detail_rel t
LEFT JOIN dsm_dimension_detail sdd ON t.sou_dimension_detail_id = sdd.dimension_detail_id
LEFT JOIN dsm_dimension sd ON sdd.dimension_id = sd.dimension_id
LEFT JOIN sys_system ss ON sd.system_id = ss.system_id
LEFT JOIN dsm_dimension_detail tdd ON t.tar_dimension_detail_id = tdd.dimension_detail_id
LEFT JOIN dsm_dimension td ON tdd.dimension_id = td.dimension_id
LEFT JOIN sys_system ts ON td.system_id = ts.system_id
where t.sou_dimension_detail_id= #{souDimensionId}
    </select>

	<select id="selectDsmDimensionDetailRelForDownOrUpListForId" parameterType="Long" resultMap="DsmDimensionDetailRelForDownOrUp">
select * from dsm_dimension_detail_rel



      </select>


	<select id="getIfHaveRelId" parameterType="DsmDimensionDetailRelForDownOrUp" resultType="Long">
	  select
    count(*)


FROM
	dsm_dimension_detail_rel t
LEFT JOIN dsm_dimension_detail sdd ON t.sou_dimension_detail_id = sdd.dimension_detail_id
LEFT JOIN dsm_dimension sd ON sdd.dimension_id = sd.dimension_id
LEFT JOIN sys_system ss ON sd.system_id = ss.system_id
LEFT JOIN dsm_dimension_detail tdd ON t.tar_dimension_detail_id = tdd.dimension_detail_id
LEFT JOIN dsm_dimension td ON tdd.dimension_id = td.dimension_id
LEFT JOIN sys_system ts ON td.system_id = ts.system_id
	  where  binary tdd.detail_code =  #{tarSysDictCode}
and binary td.dimension_code =  #{tarSysDictTypeCode}
and binary ts.name = #{tarSystemName}
and binary sdd.detail_code =  #{souSysDictCode}
and binary sd.dimension_code =  #{souSysDictTypeCode}
and binary ss.name = #{souSystemName}
	</select>
	<select id="getSouId" parameterType="DsmDimensionDetailRelForDownOrUp" resultType="Long">
	 select
     t.dimension_detail_id  as sou_dimension_detail_id
FROM
	dsm_dimension_detail t
LEFT JOIN dsm_dimension sd ON t.dimension_id = sd.dimension_id
LEFT JOIN sys_system ss ON sd.system_id = ss.system_id
where binary sd.dimension_code=#{souSysDictTypeCode}
and binary t.detail_code=#{souSysDictCode}
and binary ss.name=#{souSystemName}
	</select>

	<select id="getTarId" parameterType="DsmDimensionDetailRelForDownOrUp" resultType="Long">
	 select
     t.dimension_detail_id  as tar_dimension_detail_id


FROM
	dsm_dimension_detail t
LEFT JOIN dsm_dimension sd ON t.dimension_id = sd.dimension_id
LEFT JOIN sys_system ss ON sd.system_id = ss.system_id
where binary sd.dimension_code=#{tarSysDictTypeCode}
and binary t.detail_code=#{tarSysDictCode}
and binary ss.name=#{tarSystemName}
	</select>
</mapper>