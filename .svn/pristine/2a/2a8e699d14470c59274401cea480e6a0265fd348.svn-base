<template>
  <div class="app-container" style="height:100%;padding:0px">
    <el-row type="flex" justify="start" align="top" style="height:calc(100vh - 90px);">
		<el-col :span="4" :xs="24" style="height:100%;overflow:auto;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);">
			  <el-checkbox-group v-model="checkList">
			    <el-checkbox v-for="item in dicManualDataInstallList" @change="checked=>changeProp(checked,item)" :key="item.propName" :value="item.propName" :label="item.propName+'('+item.propComment+')'" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #909399;background-color:#909399;" border ></el-checkbox>
			  </el-checkbox-group>
		</el-col>
		<el-col :span="14" :xs="24" border style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);padding-top:20px;padding-right:20px;">
	       <div v-for="(item, key) in propMap">
	       		<el-form size="small" label-width="120px" ref="formO" :model="formO" :rules="rules">
	       		<el-col :span="item[1].width" :xs="item[1].width">
	       		<div @click="divClick(item[1])">
		       		<el-form-item :label="item[1].propComment" :prop="item[1].propName">
		       			<el-input v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='input'">
		       				<template slot="prepend" v-if="item[1].prefix!=null && item[1].prefix!=''">{{item[1].prefix}}</template>
		       				<template slot="append" v-if="item[1].postfix!=null && item[1].postfix!=''">{{item[1].postfix}}</template>
		       			</el-input>
		       		    <el-select v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" style="width:100%" :clearable="item[1].clearable=='Y'" v-if="item[1].type==='select'">
				         <el-option
				           v-for="dict in item[1].options"
				           :key="dict.detailCode"
				           :label="dict.detailName"
				           :value="dict.detailCode"
				         />
				        </el-select>
						<el-radio-group v-model="formO[item[1].propName]" v-if="item[1].type==='radio'">
						  <el-radio :label="dict.detailCode" v-for="dict in item[1].options" >{{dict.detailName}}</el-radio>
						</el-radio-group>
						<el-input v-model="formO[item[1].propName]" placeholder="禁用" :disabled="true" size="small" v-if="item[1].type==='auto'||item[1].type==='uuid'"/>
						<el-input type="textarea" v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" size="small" v-if="item[1].type==='textarea'"/>
						<el-input-number v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" :precision="2" :step="0.1" size="small" v-if="item[1].type==='number2'" style="width:100%"/>
						<el-input-number v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='number'" style="width:100%"/>
						<el-date-picker type="date" v-model="formO[item[1].propName]" :placeholder="item[1].placeholder" :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='timePicker'" style="width:100%"/>
						<el-date-picker type="datetime" v-model="formO[item[1].propName]"  :clearable="item[1].clearable=='Y'" size="small" v-if="item[1].type==='datePicker'" style="width:100%"/>
		       		</el-form-item>
		       	</div>	
		       	</el-col>	
	       		</el-form>
	       </div>
		</el-col>
		<el-col :span="6" :xs="24" border style="height:100%;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);">
			<el-tabs type="border-card">
			  <el-tab-pane label="组件属性">
				  <el-form size="small" label-width="90px">
				  <el-row type="flex" justify="start" align="top">
						<el-form-item label="组件类型">
						  <el-select v-model="form.type" placeholder="请选择" style="width:100%">
						    <el-option-group
						      v-for="group in typeList"
						      :key="group.label"
						      :label="group.label">
						      <el-option
						        v-for="item in group.options"
						        :key="item.value"
						        :label="item.label"
						        :value="item.value">
						      </el-option>
						    </el-option-group>
						  </el-select>
				        </el-form-item>
						<el-form-item label="是否主键">
						  <el-switch
						    v-model="form.isKey"
						    active-value="Y"
						    inactive-value="N">
						  </el-switch>
				        </el-form-item>				        
			        </el-row>
					<el-form-item label="名称">
				        <el-input
				          v-model="form.propName"
				          placeholder="请输入名称"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery"/>
			        </el-form-item>		
			        
					<el-form-item label="注释">
				        <el-input
				          v-model="form.propComment"
				          placeholder="请输入注释"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery"/>
			        </el-form-item>
			        
					<el-form-item label="占位提示">
				        <el-input
				          v-model="form.placeholder"
				          placeholder="请输入占位提示"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery"/>
			        </el-form-item>	
			        
					<el-form-item label="组件宽度">
						<el-slider
					      v-model="form.width"
					      :step="1" :max="24"
					      show-stops>
					    </el-slider>
			        </el-form-item>

					<el-form-item label="默认值">
				        <el-input
				          v-model="form.defaultValue"
				          placeholder="请输入默认值"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery"/>
			        </el-form-item>
			        
					<el-form-item label="最大长度">
				        <el-input-number v-model="form.columnSize" :step="1" style="width:100%" @change="changeRules('1')"></el-input-number>
			        </el-form-item>

					<el-form-item label="标签宽度">
				        <el-input-number v-model="form.labelWidth" :step="1" style="width:100%"></el-input-number>
			        </el-form-item>		

					<el-form-item label="前缀">
				        <el-input
				          v-model="form.prefix"
				          placeholder="请输入前缀"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery"/>
			        </el-form-item>
					<el-form-item label="后缀">
				        <el-input
				          v-model="form.postfix"
				          placeholder="请输入后缀"
				          clearable
				          size="small"
				          @keyup.enter.native="handleQuery"/>
			        </el-form-item>
					<el-row type="flex" justify="start" align="top">
					<el-form-item label="能否清空">
					  <el-switch
					    v-model="form.clearable"
					    active-value="Y"
					    inactive-value="N">
					  </el-switch>
			        </el-form-item>
					<el-form-item label="是否必填">
					  <el-switch
					    v-model="form.nullable"
					    active-value="N"
					    inactive-value="Y"
					    @change="changeRules('1')">
					  </el-switch>
			        </el-form-item>
			        </el-row>
			        <el-row type="flex" justify="start" align="top">
					<el-form-item label="条件显示">
					  <el-switch
					    v-model="form.isSearch"
					    active-value="Y"
					    inactive-value="N">
					  </el-switch>
			        </el-form-item>
					<el-form-item label="列表显示">
					  <el-switch
					    v-model="form.isTable"
					    active-value="Y"
					    inactive-value="N">
					  </el-switch>
			        </el-form-item>
					</el-row>
			        <el-form-item label="维度字典">
			            <el-select
						    v-model="form.relId"
						    filterable
						    remote
						    reserve-keyword
						    placeholder="请输入维度字典"
						    :remote-method="remoteMethod"
						      style="width:100%" @change="optionChange($event)">
						    <el-option
						      v-for="item in relOptions"
						      :key="item.dimensionId"
						      :label="item.dimensionName"
						      :value="item.dimensionId">
						    </el-option>
						  </el-select>
			        </el-form-item>
			        
			        			        
					<el-form-item label="EXCEL坐标">
				        <el-input
				          v-model="form.place"
				          placeholder="请输入坐标"
				          clearable
				          size="small"/>
			        </el-form-item>
			        
			        <el-form-item style="margin-left:0px;">
		        		<el-button type="primary" round style="width:100%" @click="submitForm">保存</el-button>	
		        	</el-form-item>	        			        			        			        			        	        			        			        			        			        			        	        
				 </el-form>
			  </el-tab-pane>
			  <el-tab-pane label="关联规则">
				  <el-checkbox-group v-model="ruleList">
				    <el-checkbox v-for="item in dsmMasterDataRuleList" v-if="item.type==='Q'" @change="checked=>changeRules('2',checked,item)" :key="item.masterDataRuleId" :label="item.name" :value="item.masterDataRuleId" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #C0C0C0;background-color:#C0C0C0;" border ></el-checkbox>
				    <el-checkbox v-for="item in dsmMasterDataRuleList" v-if="item.type==='H'" @change="checked=>changeRules('2',checked,item)" :key="item.masterDataRuleId" :label="item.name" :value="item.masterDataRuleId" style="width: 90%;display:block;margin:5px 5px ;color:#FFF;border-color: #909399;background-color:#909399;" border ></el-checkbox>
				  </el-checkbox-group>
			  </el-tab-pane>
			</el-tabs>
		</el-col>
	</el-row>
  </div>
</template>

<script>
import { listDicManualDataDefine, getDicManualDataDefine, delDicManualDataDefine, addDicManualDataDefine, updateDicManualDataDefine, exportDicManualDataDefine,getDicManualDataDefineVo,validateRules,installDicManualDataDefine } from "@/api/dic/dicManualDataDefine";
import { listDicManualDataInstall, getDicManualDataInstall, delDicManualDataInstall, addDicManualDataInstall, updateDicManualDataInstall, exportDicManualDataInstall } from "@/api/dic/dicManualDataInstall";
import { listDsmMasterDataRule} from "@/api/dsm/dsmMasterDataRule";
import { listDsmDimension } from "@/api/dsm/dsmDimension";
import { listDsmDimensionDetail } from "@/api/dsm/dsmDimensionDetail";
import { getToken } from "@/utils/auth";
import draggable from 'vuedraggable';
import { isArray } from 'util';

export default {
  name: "DicManualDataManager",
  components: {
	  draggable
  },
  data() {
    return {
    	manualDataId:null,
    	form:{},
    	formO:{},
    	dicManualDataInstallList:[],
    	dsmMasterDataRuleList:[],
    	checkList:[],
    	ruleList:[],
    	propMap:new Map(),
    	prop:"",
    	relOptions:[],
    	typeList:[{
    		label: '输入型组件',
            options: [{
              value: 'input',
              label: '单行文本'
            }, {
              value: 'textarea',
              label: '多行文本'
            }, {
              value: 'number',
              label: '计数器'
            } , {
                value: 'number2',
                label: '计数器+小数'
            },{
              value: 'uuid',
              label: '随机码值'
            }, {
              value: 'auto',
              label: '自增码值'
            }]
    	},{
    		label: '选择型组件',
            options: [{
              value: 'select',
              label: '下拉选择'
            }, {
              value: 'radio',
              label: '单选'
            }/* , {
              value: 'checkbox',
              label: '多选'
            }, {
              value: 'switch',
              label: '开关'
            }, {
              value: '滑块 ',
              label: 'slider'
            } */, {
              value: 'timePicker',
              label: '时间选择'
            }, {
              value: 'datePicker',
              label: '日期选择'
            }/*, {
            value: 'timePickerArea',
            label: '时间范围'
          } , {
              value: 'datePickerArea',
              label: '日期范围'
            }, {
              value: 'upload',
              label: '上传'
            } */]
    	}],
    	rules: {
    	}
    };
  },
  created() {
	  this.manualDataId = this.$route.params && this.$route.params.manualDataId;
	  this.getList();
	  const detailParams = {pageNum: 1,pageSize: 10000};
      listDsmMasterDataRule(detailParams).then(response => {
        this.dsmMasterDataRuleList = response.rows;
      });
  },
  methods: {
      
    getList() {
    	getDicManualDataDefineVo(this.manualDataId).then(response => {
            this.form = response.data;
            this.dicManualDataInstallList = response.data.dicManualDataInstallList;
            for(let i=0;i<this.dicManualDataInstallList.length;i++){
            	if(this.dicManualDataInstallList[i].manualDataInstallId!=null){
            		console.log(this.dicManualDataInstallList[i].dsmMasterDataRules);
            		this.$set(this.rules, this.dicManualDataInstallList[i].propName , this.dicManualDataInstallList[i].dsmMasterDataRules); 
                	let roles=[];
                	if(this.dicManualDataInstallList[i].dsmMasterDataRules!=null){roles=this.dicManualDataInstallList[i].dsmMasterDataRules;}
                	if(this.dicManualDataInstallList[i].nullable=='N'){
                		if(this.dicManualDataInstallList[i].rules==null){this.dicManualDataInstallList[i].rules=[];}
                		roles.push({ masterDataRuleId:'nullable',required: true, message: this.dicManualDataInstallList[i].propComment+"不能为空", trigger: "blur" });
                	}
                	if(this.dicManualDataInstallList[i].columnSize!=null&&this.dicManualDataInstallList[i].columnSize>0&&this.dicManualDataInstallList[i].type!='number2'&&this.dicManualDataInstallList[i].type!='number'){
                		if(this.dicManualDataInstallList[i].rules==null){this.dicManualDataInstallList[i].rules=[];}
                		roles.push({ masterDataRuleId:'columnSize',max: this.dicManualDataInstallList[i].columnSize, message: this.dicManualDataInstallList[i].propComment+"不能超过"+this.dicManualDataInstallList[i].columnSize, trigger: "blur" });
                	}
                	if(this.dicManualDataInstallList[i].rules==null){this.dicManualDataInstallList[i].rules=[];}
            		this.dicManualDataInstallList[i].rules.push(roles);
            		this.$set(this.rules, this.dicManualDataInstallList[i].propName , roles); 
                	this.propMap.set(this.dicManualDataInstallList[i].propName,this.dicManualDataInstallList[i]);
                	this.checkList.push(this.dicManualDataInstallList[i].propName+"("+this.dicManualDataInstallList[i].propComment+")");
            	}
            }
          })
    },
    changeProp(checked,item) {
    	if(checked){
    		this.$set(this.formO, item.propName , ''); 
    		this.prop=item.propName;
    		this.relOptions=item.options;
    		this.propMap.set(item.propName,{modelEntityPropId:item.modelEntityPropId,propName:item.propName,propComment:item.propComment,type:"input",nullable:item.nullable,placeholder:"请输入"+(item.propComment!=undefined?item.propComment:""),width:24,columnSize:item.columnSize,labelWidth:120,options:[],rules:[],isSearch:"N",isTable:"N",isKey:item.isKey,clearable:item.clearable,place:item.propComment,propType:item.propType});
    		this.form=this.propMap.get(item.propName);
    		if(item.columnSize!=null&&item.columnSize>0){
    			this.changeRules('1');
    		}
    	}else{
    		this.propMap.delete(item.propName);
    	}
    	
    },
    optionChange(value) {
    	this.propMap.get(this.form.propName).relName=this.relOptions.find(val=>val.dimensionId==value).dimensionName;
        const detailParams = {
          dimensionId: value,
          pageNum: 1,
          pageSize: 1000
        };
        listDsmDimensionDetail(detailParams).then(response => {
        	this.form.options = response.rows;
        });  
    },
    divClick(item){
    	this.form=this.propMap.get(item.propName);
    	this.relOptions = [{dimensionId:this.form.relId,dimensionName:this.form.relName}];
    	this.ruleList=[];
    	if(this.rules[item.propName]!=null){
    		let rules=this.rules[item.propName];
        	for(let i=0;i<rules.length;i++){
        		if(rules[i].masterDataRuleId!='nullable'&&rules[i].masterDataRuleId!='columnSize'){
        			this.ruleList.push(rules[i].name);
        		}
        	}
    	}
    	
    },
    // 模糊搜索
    remoteMethod(query) {
        if (query !== '') {
          setTimeout(() => {
            let relParams = {dimensionName:query}
            listDsmDimension(relParams).then(response => {
	          this.relOptions = response.rows;
	        });
          }, 200);
        } else {
          this.relOptions = [];
        }
    },
    changeRules(type,checked,item){
    	let rules=[];
    	if(this.rules[this.form.propName]!=null){
    		rules=this.rules[this.form.propName];
    	}
    	
    	if(type=='1'){
    		for(let i=0;i<rules.length;i++){
    			if(rules[i].masterDataRuleId=='nullable'||rules[i].masterDataRuleId=='columnSize'){
    				rules.splice(i,1);
    			}
    		}
    	}
    	
    	if(type=='1'&&this.form.nullable=='N'){
    		rules.push({ masterDataRuleId:'nullable',required: true, message: this.form.propComment+"不能为空", trigger: "blur" });
    	}
    	if(type=='1'&&this.form.columnSize!=null&&this.form.columnSize!=''&&this.form.type!='number2'&&this.form.type!='number'){
    		rules.push({ masterDataRuleId:'columnSize',max: this.form.columnSize, message: this.form.propComment+"不能超过"+this.form.columnSize, trigger: "blur" });
    	}
    	
    	if(type=='2'&&checked){
    		rules.push({ masterDataRuleId:item.masterDataRuleId,reg: item.rule, message: "数据不满足'"+item.name+"'", trigger: "blur" ,validator:validateRules,name:item.name});
    	}else if(type=='2'){
    		for(let i=0;i<rules.length;i++){
    			if(rules[i].masterDataRuleId==item.masterDataRuleId){
    				rules.splice(i,1);
    			}
    		}
    	}
    	this.$set(this.rules, this.form.propName , rules); 
    },
    submitForm(){
    	let list =[];
    	const that=this;
    	this.propMap.forEach(function(value,key){
    		value.rules=[];
    		if(that.rules[key]){
    			let rules=that.rules[key];
    			for(let i=0;i<rules.length;i++){
        			if(rules[i].masterDataRuleId!='nullable'&&rules[i].masterDataRuleId!='columnSize'){
        				value.rules.push(rules[i].masterDataRuleId);
        			}
        		}
    		}
    		value.dsmMasterDataRules=null;
    		list.push(value);
    	});
    	let forms={
    			manualDataId:this.manualDataId,
    			dicManualDataInstalls:list
    	};
    	
    	installDicManualDataDefine(forms).then(response => {
           this.msgSuccess("新增成功");
        });	 
    	
    }
    
  },
  computed: {

  }
};
</script>
<style>
	html,body{
	  height: 100%;
	}
</style>