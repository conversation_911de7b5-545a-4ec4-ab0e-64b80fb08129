package com.dqms.basic.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.basic.service.ISysSystemService;
import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.entity.SysSystemPerson;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;

/**
 * 应用系统Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
@Service
public class SysSystemServiceImpl implements ISysSystemService
{
    @Autowired
    private SysSystemMapper sysSystemMapper;

    /**
     * 查询应用系统
     *
     * @param systemId 应用系统ID
     * @return 应用系统
     */
    @Override
    public SysSystem selectSysSystemById(Long systemId)
    {
        return sysSystemMapper.selectSysSystemById(systemId);
    }

    /**
     * 查询应用系统列表
     *
     * @param sysSystem 应用系统
     * @return 应用系统
     */
    @Override
    @DataScope(systemAlias = "a")
    public List<SysSystem> selectSysSystemList(SysSystem sysSystem)
    {
        return sysSystemMapper.selectSysSystemList(sysSystem);
    }

    @Override
    @DataScope(systemAlias = "a")
    public List<SysSystem> selectSysSystemAll(SysSystem sysSystem) {
        return sysSystemMapper.selectSysSystemAll(sysSystem);
    }

    /**
     * 新增应用系统
     *
     * @param sysSystem 应用系统
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSysSystem(SysSystem sysSystem)
    {
    	sysSystem.setCreateTime(DateUtils.getNowDate());
    	sysSystem.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId()+"");
    	sysSystem.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	sysSystem.setUpdateTime(DateUtils.getNowDate());
    	sysSystem.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId()+"");
    	sysSystem.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	List<SysSystem> tName = sysSystemMapper.ListSysSystemByName(sysSystem);
    	if(tName!=null&&tName.size()!=0) {
    		throw new RuntimeException("系统名称已经存在！");
    	}
        List<SysSystem> tCode = sysSystemMapper.selectSysSystemByCode(sysSystem);
        if(tCode!=null&&tCode.size()!=0) {
            throw new RuntimeException("系统编码已经存在！");
        }
        int rows = sysSystemMapper.insertSysSystem(sysSystem);
        insertSysSystemPerson(sysSystem);
        return rows;
    }

    /**
     * 修改应用系统
     *
     * @param sysSystem 应用系统
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSysSystem(SysSystem sysSystem)
    {
    	sysSystem.setUpdateTime(DateUtils.getNowDate());
    	sysSystem.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId()+"");
    	sysSystem.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	List<SysSystem> tName = sysSystemMapper.ListSysSystemByName(sysSystem);
    	if(tName.size()>1) {
    		throw new RuntimeException("系统名称已经存在！");
    	}else if(tName.size()==1){
            if(!tName.get(0).getSystemId().equals(sysSystem.getSystemId())){
                throw new RuntimeException("系统名称已经存在！");
            }
        }
        List<SysSystem> tCode = sysSystemMapper.selectSysSystemByCode(sysSystem);
        if(tCode.size()>1) {
            throw new RuntimeException("系统编码已经存在！");
        }else if(tCode.size()==1){
            if(!tCode.get(0).getSystemId().equals(sysSystem.getSystemId())){
                throw new RuntimeException("系统编码已经存在！");
            }
        }
        sysSystemMapper.deleteSysSystemPersonBySystemId(sysSystem.getSystemId());
        insertSysSystemPerson(sysSystem);
        return sysSystemMapper.updateSysSystem(sysSystem);
    }

    /**
     * 批量删除应用系统
     *
     * @param systemIds 需要删除的应用系统ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSysSystemByIds(Long[] systemIds)
    {
        sysSystemMapper.deleteSysSystemPersonBySystemIds(systemIds);
        return sysSystemMapper.deleteSysSystemByIds(systemIds);
    }

    /**
     * 删除应用系统信息
     *
     * @param systemId 应用系统ID
     * @return 结果
     */
    @Override
    public int deleteSysSystemById(Long systemId)
    {
        sysSystemMapper.deleteSysSystemPersonBySystemId(systemId);
        return sysSystemMapper.deleteSysSystemById(systemId);
    }

    /**
     * 新增系统负责人信息
     *
     * @param sysSystem 应用系统对象
     */
    public void insertSysSystemPerson(SysSystem sysSystem)
    {
    	Long[] sysPersonIds = sysSystem.getSysPersonIds();
    	if(sysPersonIds==null||sysPersonIds.length==0) {
    		return;
    	}
        Long systemId = sysSystem.getSystemId();
            List<SysSystemPerson> list = new ArrayList<SysSystemPerson>();
            for (Long sysPersonId : sysPersonIds)
            {
            	SysSystemPerson person = new SysSystemPerson();
            	person.setSystemId(systemId);
            	person.setUserId(sysPersonId);
                list.add(person);
            }
            if (list.size() > 0)
            {
                sysSystemMapper.batchSysSystemPerson(list);
            }
    }

    /**
     * 根据用户ID获取应用系统选择框列表
     *
     * @param userId 用户ID
     * @return 选中应用系统ID列表
     */
    @Override
    public List<Integer> selectSystemListByUserId(Long userId)
    {
        return sysSystemMapper.selectSystemListByUserId(userId);
    }

    /**
     * 根据系统ID获取负责人系统选择框列表
     *
     * @param systemId 用户ID
     * @return 选中负责人ID列表
     */
    @Override
    public List<Integer> selectPersonIdsBySystemId(Long userId)
    {
        return sysSystemMapper.selectPersonIdsBySystemId(userId);
    }
}
