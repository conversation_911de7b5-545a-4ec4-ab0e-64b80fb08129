package com.dqms.dsc.service;

import java.util.List;
import com.dqms.dsc.domain.DscDesensitization;
import com.dqms.dsc.domain.vo.DscDesensitizationVo;

/**
 * 数据脱敏Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscDesensitizationService 
{
    /**
     * 查询数据脱敏
     * 
     * @param desensitizationId 数据脱敏ID
     * @return 数据脱敏
     */
    public DscDesensitization selectDscDesensitizationById(Long desensitizationId);

    /**
     * 查询数据脱敏列表
     * 
     * @param dscDesensitization 数据脱敏
     * @return 数据脱敏集合
     */
    public List<DscDesensitization> selectDscDesensitizationList(DscDesensitization dscDesensitization);


    public List<DscDesensitizationVo> getDscDesensitizationVoList(DscDesensitizationVo dscDesensitizationVo);

    /**
     * 新增数据脱敏
     * 
     * @param dscDesensitization 数据脱敏
     * @return 结果
     */
    public int insertDscDesensitization(DscDesensitization dscDesensitization);

    /**
     * 修改数据脱敏
     * 
     * @param dscDesensitization 数据脱敏
     * @return 结果
     */
    public int updateDscDesensitization(DscDesensitization dscDesensitization);

    /**
     * 批量删除数据脱敏
     * 
     * @param desensitizationIds 需要删除的数据脱敏ID
     * @return 结果
     */
    public int deleteDscDesensitizationByIds(Long[] desensitizationIds);

    /**
     * 删除数据脱敏信息
     * 
     * @param desensitizationId 数据脱敏ID
     * @return 结果
     */
    public int deleteDscDesensitizationById(Long desensitizationId);
}
