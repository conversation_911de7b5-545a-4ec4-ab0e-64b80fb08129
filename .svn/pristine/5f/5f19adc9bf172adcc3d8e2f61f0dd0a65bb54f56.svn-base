package com.dqms.mdm.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.common.annotation.DataScope;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.enums.DsType;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.Encoding;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.SftpUtil;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.common.utils.http.HttpUtils;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.enums.DamConstants;
import com.dqms.dam.service.IDamAssetsService;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityHis;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmDataEntityPropHis;
import com.dqms.mdm.domain.MdmDataEntityPropTemp;
import com.dqms.mdm.domain.MdmDataEntityTemp;
import com.dqms.mdm.domain.MdmDataMonitor;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.mapper.MdmDataEntityHisMapper;
import com.dqms.mdm.mapper.MdmDataEntityMapper;
import com.dqms.mdm.mapper.MdmDataEntityPropHisMapper;
import com.dqms.mdm.mapper.MdmDataEntityPropMapper;
import com.dqms.mdm.mapper.MdmDataEntityPropTempMapper;
import com.dqms.mdm.mapper.MdmDataEntityTempMapper;
import com.dqms.mdm.mapper.MdmDataMonitorMapper;
import com.dqms.mdm.mapper.MdmRegistryMapper;
import com.dqms.mdm.service.IMdmDataEntityShipAnalysisService;
import com.dqms.mdm.service.IMdmDataEntityShipService;
import com.dqms.mdm.service.IMdmDataMonitorService;
import com.dqms.mdm.util.MetaDataContext;
import com.dqms.system.service.ISysConfigService;
import com.dqms.utils.JdbcTemplateUtils;

import lombok.extern.slf4j.Slf4j;

import javax.management.RuntimeErrorException;

/**
 * 元数据变更监控Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-13
 */
@Service
@Slf4j
public class MdmDataMonitorServiceImpl implements IMdmDataMonitorService
{
    @Autowired
    private MdmDataMonitorMapper mdmDataMonitorMapper;

    @Autowired
    private MdmDataEntityMapper mdmDataEntityMapper ;

    @Autowired
    private MdmDataEntityPropMapper mdmDataEntityPropMapper ;

    @Autowired
    private MdmDataEntityTempMapper mdmDataEntityTempMapper ;

    @Autowired
    private MdmDataEntityPropTempMapper mdmDataEntityPropTempMapper;

    @Autowired
    private MdmDataEntityHisMapper mdmDataEntityHisMapper ;

    @Autowired
    private MdmDataEntityPropHisMapper mdmDataEntityPropHisMapper ;

    @Autowired
    private MdmRegistryMapper mdmRegistryMapper;

    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;

    @Autowired
    private MetaDataContext metaDataContext;

    @Autowired
    private MdmRegistryServiceImpl mdmRegistryService;

    @Autowired
    private IDamAssetsService damAssetsService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IMdmDataEntityShipAnalysisService mdmDataEntityShipAnalysisService;
    /**
     * 查询元数据变更监控
     *
     * @param monitorId 元数据变更监控ID
     * @return 元数据变更监控
     */
    @Override
    public MdmDataMonitor selectMdmDataMonitorById(Long monitorId)
    {
        return mdmDataMonitorMapper.selectMdmDataMonitorById(monitorId);
    }

    /**
     * 查询元数据变更监控列表
     *
     * @param mdmDataMonitor 元数据变更监控
     * @return 元数据变更监控
     */
    @Override
    @DataScope(systemAlias = "r")
    public List<MdmDataMonitor> selectMdmDataMonitorList(MdmDataMonitor mdmDataMonitor)
    {
        List<MdmDataMonitor> list = mdmDataMonitorMapper.selectMdmDataMonitorList(mdmDataMonitor);
        return list;
    }

    /**
     * 新增元数据变更监控
     *
     * @param mdmDataMonitor 元数据变更监控
     * @return 结果
     */
    @Override
    public int insertMdmDataMonitor(MdmDataMonitor mdmDataMonitor)
    {
        mdmDataMonitor.setCreateTime(DateUtils.getNowDate());
        return mdmDataMonitorMapper.insertMdmDataMonitor(mdmDataMonitor);
    }

    /**
     * 修改元数据变更监控
     *
     * @param mdmDataMonitor 元数据变更监控
     * @return 结果
     */
    @Override
    public int updateMdmDataMonitor(MdmDataMonitor mdmDataMonitor)
    {
        mdmDataMonitor.setUpdateTime(DateUtils.getNowDate());
        return mdmDataMonitorMapper.updateMdmDataMonitor(mdmDataMonitor);
    }

    /**
     * 批量删除元数据变更监控
     *
     * @param monitorIds 需要删除的元数据变更监控ID
     * @return 结果
     */
    @Override
    public int deleteMdmDataMonitorByIds(Long[] monitorIds)
    {
        return mdmDataMonitorMapper.deleteMdmDataMonitorByIds(monitorIds);
    }

    /**
     * 删除元数据变更监控信息
     *
     * @param monitorId 元数据变更监控ID
     * @return 结果
     */
    @Override
    public int deleteMdmDataMonitorById(Long monitorId)
    {
        return mdmDataMonitorMapper.deleteMdmDataMonitorById(monitorId);
    }

    @Override
    public void batchChange(Long[] monitorIds) {
        List<MdmDataMonitor> mdmDataMonitorList = mdmDataMonitorMapper.findAllByMonitorIdIn(monitorIds);
        for (MdmDataMonitor mdmDataMonitor : mdmDataMonitorList) {
            this.change(mdmDataMonitor);
        }
    }

    @Override
    @Transactional
    public void change(MdmDataMonitor mdmDataMonitor) {

        Date nowDate = DateUtils.getNowDate();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUsername();
        /**
         * 1、判断操作类型
         * 2、删除操作：清理实体表和实体属性表，插入到历史信息表，标记为删除，不需要再标记版本号
         * 3、新增操作：转移临时表的数据到真实表，真实表加上版本号1.0，转移后清除临时表数据，再记录一份数据到历史表
         * 4、修改操作：转移临时表的数据到真实表，查询之前的最大版本号，加上1.0，转移后清除临时表数据，再记录一份最新的数据到历史表
         */
        if(mdmDataMonitor.getChangeType().equals(MdmConstants.OPER_ADD)){
            operAdd(mdmDataMonitor,userId,userName,nowDate);
        }
        if(mdmDataMonitor.getChangeType().equals(MdmConstants.OPER_UPDATE)){
            operUpdate(mdmDataMonitor,userId,userName,nowDate);
        }

        if(mdmDataMonitor.getChangeType().equals(MdmConstants.OPER_DEL)){
            operDel(mdmDataMonitor,userId,userName,nowDate);
        }
        /** 清理临时表 */
        mdmDataEntityPropTempMapper.delByRegId(mdmDataMonitor.getRegId());
        mdmDataEntityTempMapper.deleteByRegistryId(mdmDataMonitor.getRegId());

        mdmDataMonitor.setAppStatus(MdmConstants.APP_APPROVED);
        mdmDataMonitor.setAppTime(nowDate);
        mdmDataMonitor.setAppUserId(userId);
        mdmDataMonitor.setAppUserName(userName);

        mdmDataMonitorMapper.updateMdmDataMonitor(mdmDataMonitor);
    }

    private void operAdd(MdmDataMonitor mdmDataMonitor,long userId,String userName,Date nowDate){
    	String remark = "";
    	DamAssets damAssets = new DamAssets();
        String version = mdmDataEntityHisMapper.selectVersionNoByRegistryId(mdmDataMonitor.getRegId());
        if(StringUtils.isEmpty(version)){
            version = "1";
        }else{
            version = Integer.parseInt(version)+1+"";
        }
        MdmDataEntityTemp mdmDataEntityTemp = mdmDataEntityTempMapper.selectMdmDataEntityTempByRegId(mdmDataMonitor.getRegId());
        MdmDataEntity mdmDataEntity = new MdmDataEntity();
        MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
        if(mdmDataEntityTemp!=null){
            /** 插入到实体表 */
            BeanUtils.copyBeanProp(mdmDataEntity,mdmDataEntityTemp);
            mdmDataEntity.setVersionNo(version);
            mdmDataEntity.setCreateId(userId);
            mdmDataEntity.setCreateTime(nowDate);
            mdmDataEntity.setCreateBy(userName);
            mdmDataEntity.setEntityId(null);
            mdmDataEntityMapper.insertMdmDataEntity(mdmDataEntity);

            /** 插入到实体历史表 */
            BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntityTemp);
            dataEntityHis.setVersionNo(version);
            dataEntityHis.setCreateId(userId);
            dataEntityHis.setCreateTime(nowDate);
            dataEntityHis.setCreateBy(userName);
            dataEntityHis.setIsMtc("0");
            dataEntityHis.setEntityId(null);
            mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);
            if(StringUtils.isNotEmpty(mdmDataEntity.getTableComment())) {
            	remark=remark+mdmDataEntity.getTableComment()+"\t";
            }
            if(StringUtils.isNotEmpty(mdmDataEntity.getSqlScripts())) {
            	remark=remark+mdmDataEntity.getSqlScripts()+"\t";
            }
        }

        /** 插入到属性表和属性历史表 */
        List<MdmDataEntityPropTemp> propTempList = mdmDataEntityPropTempMapper.selectMdmDataEntityPropTempByRegId(mdmDataMonitor.getRegId());
        if(!propTempList.isEmpty()){
            List<MdmDataEntityProp> insertPropList = new ArrayList<>();
            List<MdmDataEntityPropHis> insertPropHisList = new ArrayList<>();
            for (MdmDataEntityPropTemp mdmDataEntityPropTemp : propTempList) {
            	remark=remark+" "+mdmDataEntityPropTemp.getPropName()+"("+(mdmDataEntityPropTemp.getPropComment()==null?"无":mdmDataEntityPropTemp.getPropComment())+")";

                MdmDataEntityProp mdmDataEntityProp = new MdmDataEntityProp();
                BeanUtils.copyBeanProp(mdmDataEntityProp,mdmDataEntityPropTemp);
                mdmDataEntityProp.setCreateId(userId);
                mdmDataEntityProp.setCreateTime(nowDate);
                mdmDataEntityProp.setCreateBy(userName);
                mdmDataEntityProp.setEntityId(mdmDataEntity.getEntityId());
                insertPropList.add(mdmDataEntityProp);

                MdmDataEntityPropHis mdmDataEntityPropHis = new MdmDataEntityPropHis();
                BeanUtils.copyBeanProp(mdmDataEntityPropHis,mdmDataEntityPropTemp);
                mdmDataEntityPropHis.setCreateId(userId);
                mdmDataEntityPropHis.setCreateTime(nowDate);
                mdmDataEntityPropHis.setCreateBy(userName);
                mdmDataEntityPropHis.setEntityId(dataEntityHis.getEntityId());
                insertPropHisList.add(mdmDataEntityPropHis);
            }
            if(StringUtils.isNotEmpty(insertPropList)){
                mdmDataEntityPropMapper.insertMdmDataEntityPropBatch(insertPropList);
            }
            if(StringUtils.isNotEmpty(insertPropHisList)){
                mdmDataEntityPropHisMapper.insertMdmDataEntityPropHisBatch(insertPropHisList);
            }
        }

        MdmRegistry mdmRegistry1 = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
        if(mdmRegistry1.getIsAutoAnalysis().equals(MdmConstants.BOOLEAN_YES)) {
            SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry1.getDatasourceId());
            String dsType=MdmConstants.HIVE;
            if(sysDatasource.getSysDatasourceType()==null||sysDatasource.getSysDatasourceType().getDatasourceTypeCode()==null||sysDatasource.getSysDatasourceType().getDatasourceTypeCode().equals(MdmConstants.FTP)) {
            	dsType= sysConfigService.selectConfigByKey("mdm.analyes.ds.type");
            }
            if(Integer.parseInt(mdmRegistry1.getMetaType())==MdmConstants.VIEW) {
            	dsType= sysConfigService.selectConfigByKey("mdm.analyes.ds.type");//视图是使用默认数据库类型进行检查
            	mdmDataEntityShipAnalysisService.analyseForTableRel(mdmDataEntity.getSqlScripts(),mdmDataEntity.getEntityId(),dsType);
            }else {
            	mdmDataEntityShipAnalysisService.analyse(mdmDataEntity.getSqlScripts(),mdmDataEntity.getEntityId(),dsType);
            }
            
        }

        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
            damAssets.setRemark(remark);
            damAssets.setAssetsType(DamConstants.DSM_TYPE_MDM);
            damAssets.setAssetsCode(DamConstants.DSM_TYPE_MDM+mdmDataEntity.getEntityId());
            damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
            damAssets.setRelId(mdmDataEntity.getRegistryId()+"");
            MdmRegistry mdmRegistry  = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
            try{
                damAssets.setAssetsName(mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName());
            }catch (Exception e){
                log.info("mdmDataMonitor:{}",mdmDataMonitor.toString());
                log.info("mdmDataEntityTemp:{}",mdmDataEntityTemp.toString());
                throw new RuntimeException("调试抛出异常");
            }

            damAssets.setSystemId(mdmRegistry.getSystemId());
            damAssets.setDatasourceId(mdmRegistry.getDatasourceId());
            damAssetsService.insertDamAssets(damAssets);
        }

    }

    private void operUpdate(MdmDataMonitor mdmDataMonitor,long userId,String userName,Date nowDate){
    	String remark = "";
    	DamAssets damAssets = new DamAssets();
        MdmDataEntityTemp mdmDataEntityTemp = mdmDataEntityTempMapper.selectMdmDataEntityTempByRegId(mdmDataMonitor.getRegId());
        MdmDataEntity mdmDataEntity = null;
        MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
        List<MdmDataEntityProp> idProps = mdmDataEntityPropMapper.selectMdmDataEntityPropByRegId(mdmDataMonitor.getRegId());
        if(mdmDataEntityTemp!=null){
            /** 插入到实体表 */
            mdmDataEntity = mdmDataEntityMapper.selectMdmDataEntityByRegId(mdmDataMonitor.getRegId());
            String versionNo = Integer.parseInt(mdmDataEntity.getVersionNo())+1+"";

            /**
             * 先根据entityID获取实体和属性数据，用于ID还原
             */
            Long entityId = mdmDataEntity.getEntityId();
            /**
             * 先清理属性表，再清理实体表，防止关联ID丢失
             */
//            mdmDataEntityPropMapper.deleteMdmDataEntityPropByEntityId(mdmDataEntity.getEntityId());
//            mdmDataEntityMapper.deleteMdmDataEntityById(mdmDataEntity.getEntityId());

            BeanUtils.copyBeanProp(mdmDataEntity,mdmDataEntityTemp);
            mdmDataEntity.setVersionNo(versionNo);
            mdmDataEntity.setCreateId(userId);
            mdmDataEntity.setCreateTime(nowDate);
            mdmDataEntity.setCreateBy(userName);
            mdmDataEntity.setEntityId(entityId);
            mdmDataEntityMapper.updateMdmDataEntity(mdmDataEntity);

            /** 插入到实体历史表 */
            BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntityTemp);
            dataEntityHis.setVersionNo(versionNo);
            dataEntityHis.setCreateId(userId);
            dataEntityHis.setCreateTime(nowDate);
            dataEntityHis.setCreateBy(userName);
            dataEntityHis.setIsMtc("0");
            mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);
            if(StringUtils.isNotEmpty(mdmDataEntity.getTableComment())) {
            	remark=remark+mdmDataEntity.getTableComment()+"\t";
            }
            if(StringUtils.isNotEmpty(mdmDataEntity.getSqlScripts())) {
            	remark=mdmDataEntity.getSqlScripts()+"\t";
            }
            
        }
        /** 插入到属性表和属性历史表 */
        List<MdmDataEntityPropTemp> propTempList = mdmDataEntityPropTempMapper.selectMdmDataEntityPropTempAllByRegId(mdmDataMonitor.getRegId());
        if(!propTempList.isEmpty()){
//            List<MdmDataEntityProp> insertPropList = new ArrayList<>();
            List<MdmDataEntityPropHis> insertPropHisList = new ArrayList<>();
            for (MdmDataEntityPropTemp mdmDataEntityPropTemp : propTempList) {
                if(!mdmDataEntityPropTemp.getOperType().equals(MdmConstants.OPER_DEL)){
                    MdmDataEntityProp mdmDataEntityProp = new MdmDataEntityProp();
                    BeanUtils.copyBeanProp(mdmDataEntityProp,mdmDataEntityPropTemp);
                    mdmDataEntityProp.setEntityId(mdmDataEntity.getEntityId());
                    mdmDataEntityProp.setCreateId(userId);
                    mdmDataEntityProp.setCreateTime(nowDate);
                    mdmDataEntityProp.setCreateBy(userName);
                    for (MdmDataEntityProp idProp : idProps) {
                        if(idProp.getPropName().equals(mdmDataEntityProp.getPropName())){
                            mdmDataEntityProp.setPropId(idProp.getPropId());
                        }
                    }
                    int updateNum = mdmDataEntityPropMapper.updateMdmDataEntityProp(mdmDataEntityProp);
                    if(updateNum==0){
                        mdmDataEntityProp.setPropId(null);
                        mdmDataEntityPropMapper.insertMdmDataEntityProp(mdmDataEntityProp);
                    }
//                    insertPropList.add(mdmDataEntityProp);
                }else if(mdmDataEntityPropTemp.getOperType().equals(MdmConstants.OPER_DEL)){
                    for (MdmDataEntityProp idProp : idProps) {
                        if(idProp.getPropName().equals(mdmDataEntityPropTemp.getPropName())){
                            mdmDataEntityPropMapper.deleteMdmDataEntityPropById(idProp.getPropId());
                        }
                    }
                }
                MdmDataEntityPropHis mdmDataEntityPropHis = new MdmDataEntityPropHis();
                BeanUtils.copyBeanProp(mdmDataEntityPropHis,mdmDataEntityPropTemp);
                mdmDataEntityPropHis.setEntityId(dataEntityHis.getEntityId());
                mdmDataEntityPropHis.setCreateId(userId);
                mdmDataEntityPropHis.setCreateTime(nowDate);
                mdmDataEntityPropHis.setCreateBy(userName);
                insertPropHisList.add(mdmDataEntityPropHis);
                remark=remark+" "+mdmDataEntityPropTemp.getPropName()+"("+mdmDataEntityPropTemp.getPropComment()+")";
            }
//            if(!insertPropList.isEmpty()){
//                mdmDataEntityPropMapper.insertMdmDataEntityPropBatch(insertPropList);
//            }
            if(!insertPropHisList.isEmpty()){
                mdmDataEntityPropHisMapper.insertMdmDataEntityPropHisBatch(insertPropHisList);
            }
        }

        MdmRegistry mdmRegistry  = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
        if(mdmRegistry.getIsAutoAnalysis().equals(MdmConstants.BOOLEAN_YES)) {
        	SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        	String dsType=MdmConstants.HIVE;
            if(sysDatasource.getSysDatasourceType()==null||sysDatasource.getSysDatasourceType().getDatasourceTypeCode()==null||sysDatasource.getSysDatasourceType().getDatasourceTypeCode().equals(MdmConstants.FTP)) {
            	dsType= sysConfigService.selectConfigByKey("mdm.analyes.ds.type");
            }
            if(Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.VIEW) {
            	dsType= sysConfigService.selectConfigByKey("mdm.analyes.ds.type");//视图是使用默认数据库类型进行检查
            	mdmDataEntityShipAnalysisService.analyseForTableRel(mdmDataEntity.getSqlScripts(),mdmDataEntity.getEntityId(),dsType);
            }else {
            	mdmDataEntityShipAnalysisService.analyse(mdmDataEntity.getSqlScripts(),mdmDataEntity.getEntityId(),dsType);
            }
        	
        }

        //BHQ 将元数据自动添加到资产管理
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
	        damAssets.setRemark(remark);
	        damAssets.setAssetsType(DamConstants.DSM_TYPE_MDM);
	        damAssets.setRelId(mdmDataEntity.getRegistryId()+"");
	        DamAssets d = damAssetsService.selectDamAssetsByRel(damAssets.getRelId(), DamConstants.DSM_TYPE_MDM);
	        damAssets.setSystemId(mdmRegistry.getSystemId());
	        damAssets.setDatasourceId(mdmRegistry.getDatasourceId());
            damAssets.setAssetsName(mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName());
	        //根据资产编号及类型查看资产是否存在，不存在则新增，存在则修改
	        if(d==null) {
	        	damAssets.setAssetsCode(DamConstants.DSM_TYPE_MDM+mdmDataEntity.getEntityId());
	            damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
	        	damAssetsService.insertDamAssets(damAssets);
	        }else {
	        	damAssets.setDamAssetsId(d.getDamAssetsId());
	        	damAssetsService.updateDamAssets(damAssets);
	        }
        }

    }

    private void operDel(MdmDataMonitor mdmDataMonitor,long userId,String userName,Date nowDate){
        MdmDataEntity mdmDataEntity = mdmDataEntityMapper.selectMdmDataEntityByRegId(mdmDataMonitor.getRegId());
        if(mdmDataEntity!=null){
            String versionNo = Integer.parseInt(mdmDataEntity.getVersionNo())+1+"";
            MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
            BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntity);
            dataEntityHis.setCreateId(userId);
            dataEntityHis.setCreateTime(nowDate);
            dataEntityHis.setCreateBy(userName);
            dataEntityHis.setOperType(MdmConstants.OPER_DEL);
            dataEntityHis.setEntityId(null);
            dataEntityHis.setVersionNo(versionNo);
            dataEntityHis.setIsMtc("0");
            mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);
            List<MdmDataEntityPropHis> insertPropHisList = new ArrayList<>();
            List<MdmDataEntityProp> entityPropList = mdmDataEntityPropMapper.selectMdmDataEntityPropByRegId(mdmDataMonitor.getRegId());
            for (MdmDataEntityProp mdmDataEntityProp : entityPropList) {
                MdmDataEntityPropHis mdmDataEntityPropHis = new MdmDataEntityPropHis();
                BeanUtils.copyBeanProp(mdmDataEntityPropHis,mdmDataEntityProp);
                mdmDataEntityPropHis.setEntityId(dataEntityHis.getEntityId());
                mdmDataEntityPropHis.setCreateId(userId);
                mdmDataEntityPropHis.setCreateTime(nowDate);
                mdmDataEntityPropHis.setCreateBy(userName);
                mdmDataEntityPropHis.setOperType(MdmConstants.OPER_DEL);
                insertPropHisList.add(mdmDataEntityPropHis);
            }
            if(!insertPropHisList.isEmpty()){
                mdmDataEntityPropHisMapper.insertMdmDataEntityPropHisBatch(insertPropHisList);
            }
            /**
             * 先清理属性表，再清理实体表，防止关联ID丢失
             */
            mdmDataEntityPropMapper.deleteMdmDataEntityPropByEntityId(mdmDataEntity.getEntityId());
            mdmDataEntityMapper.deleteMdmDataEntityById(mdmDataEntity.getEntityId());
            String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
            if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
	            DamAssets d = damAssetsService.selectDamAssetsByRel(mdmDataEntity.getRegistryId()+"", DamConstants.DSM_TYPE_MDM);
	            if(d!=null) {
	            	damAssetsService.deleteDamAssetsById(d.getDamAssetsId());
	            }
            }
        }

        /** 修改采集表的状态为已删除  */
        MdmRegistry mdmRegistry = mdmRegistryMapper.selectMdmRegistryById(mdmDataMonitor.getRegId());
        mdmRegistry.setAcqStatus(MdmConstants.ACQ_PASS);
        mdmRegistryMapper.updateMdmRegistry(mdmRegistry);
    }

    @Transactional
    public void mtcUpdate(MdmDataEntity mdmDataEntity){

        /** 防止变更冲突，标记相关变更信息为忽略 */
        //查询是否有变更信息
        MdmDataMonitor mdmDataMonitor = mdmDataMonitorMapper.findAllByRegId(mdmDataEntity.getRegistryId());
        if(mdmDataMonitor!=null){
            Date nowDate = DateUtils.getNowDate();
            Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
            String userName = SecurityUtils.getLoginUser().getUsername();

            /** 清理临时表 */
            mdmDataEntityPropTempMapper.delByRegId(mdmDataMonitor.getRegId());
            mdmDataEntityTempMapper.deleteByRegistryId(mdmDataMonitor.getRegId());

            /** 设置监控信息审批状态为忽略 */
            mdmDataMonitor.setAppStatus(MdmConstants.APP_IGNORE);
            mdmDataMonitor.setAppTime(nowDate);
            mdmDataMonitor.setAppUserId(userId);
            mdmDataMonitor.setAppUserName(userName);
            mdmDataMonitorMapper.updateMdmDataMonitor(mdmDataMonitor);
        }

        SysDatasource sysDatasource= sysDatasourceMapper.sysDatasourceByRegId(mdmDataEntity.getRegistryId());
        if(sysDatasource.getDsType().equals(DsType.DATABASE.getCode())){
            MdmRegistry mdmRegistry = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
            if(Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.TABLE){
                mtcDbTableUpdate(mdmDataEntity);
            }
            if(Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.VIEW){
                mtcDbViewUpdate(mdmDataEntity,sysDatasource);
            }
            if(Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.PROCEDURE){
                mtcProUpdate(mdmDataEntity,sysDatasource);
            }
        }else if (sysDatasource.getDsType().equals(DsType.FTP.getCode())){
            mtcFtpUpdate(mdmDataEntity,sysDatasource);
        }

        /** 修改采集表状态为已采集 */
        MdmRegistry mdmRegistry = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
        mdmRegistry.setAcqStatus(MdmConstants.ACQ_YES);
        mdmRegistryMapper.updateMdmRegistry(mdmRegistry);
    }

    private void mtcProUpdate(MdmDataEntity mdmDataEntity,SysDatasource sysDatasource){
        Date nowDate = DateUtils.getNowDate();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUsername();

        String operType = MdmConstants.OPER_UPDATE;
        String version = "";
        /**如果版本号为空，表示新增 */
        if(StringUtils.isEmpty(mdmDataEntity.getVersionNo())){
            /**查询历史表里面有没有数据，如果有，代表之前删除了，获取最大的版本号加1，如果没有，就从1开始 */
            version = mdmDataEntityHisMapper.selectVersionNoByRegistryId(mdmDataEntity.getRegistryId());
            if(StringUtils.isEmpty(version)){
                version = "1";
            }else{
                version = Integer.parseInt(version)+1+"";
            }
            operType = MdmConstants.OPER_ADD;
        }else{
            version = Integer.parseInt(mdmDataEntity.getVersionNo())+1+"";
        }

        /** 先清理属性表，再清理实体表，防止关联ID丢失  */
//        mdmDataEntityPropMapper.deleteMdmDataEntityPropByEntityId(mdmDataEntity.getEntityId());
//        mdmDataEntityMapper.deleteMdmDataEntityById(mdmDataEntity.getEntityId());

        /** 插入到实体表 */
        mdmDataEntity.setVersionNo(version);
        mdmDataEntity.setCreateId(userId);
        mdmDataEntity.setCreateTime(nowDate);
        mdmDataEntity.setCreateBy(userName);
        if(StringUtils.isNotNull(mdmDataEntity.getEntityId())){
            mdmDataEntityMapper.updateMdmDataEntity(mdmDataEntity);
        }else{
            mdmDataEntityMapper.insertMdmDataEntity(mdmDataEntity);
        }


        /** 插入到实体历史表 */
        MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
        BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntity);
        dataEntityHis.setVersionNo(version);
        dataEntityHis.setCreateId(userId);
        dataEntityHis.setCreateTime(nowDate);
        dataEntityHis.setCreateBy(userName);
        dataEntityHis.setOperType(operType);
        dataEntityHis.setIsMtc("1");
        mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);

        //BHQ 将元数据自动添加到资产管理
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	DamAssets damAssets = new DamAssets();
        	String remark="";
        	if(StringUtils.isNotEmpty(mdmDataEntity.getTableComment())) {
            	remark=remark+mdmDataEntity.getTableComment()+"\t";
            }
        	if(StringUtils.isNotEmpty(mdmDataEntity.getSqlScripts())) {
            	remark=remark+mdmDataEntity.getSqlScripts()+"\t";
            }
	        damAssets.setRemark(remark);
	        damAssets.setAssetsType(DamConstants.DSM_TYPE_MDM);
	        damAssets.setRelId(mdmDataEntity.getRegistryId()+"");
	        DamAssets d = damAssetsService.selectDamAssetsByRel(damAssets.getRelId(), DamConstants.DSM_TYPE_MDM);
	        MdmRegistry mdmRegistry  = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
	        damAssets.setSystemId(mdmRegistry.getSystemId());
	        damAssets.setDatasourceId(mdmRegistry.getDatasourceId());
            damAssets.setAssetsName(mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName());
	        //根据资产编号及类型查看资产是否存在，不存在则新增，存在则修改
	        if(d==null) {
	        	damAssets.setAssetsCode(DamConstants.DSM_TYPE_MDM+mdmDataEntity.getEntityId());
	            damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
	        	damAssetsService.insertDamAssets(damAssets);
	        }else {
	        	damAssets.setDamAssetsId(d.getDamAssetsId());
	        	damAssetsService.updateDamAssets(damAssets);
	        }
        }

        /** 是否要推送到数据库 */
        if(mdmDataEntity.getPushServer()){
            JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
            try {
                jd.execute(mdmDataEntity.getSqlScripts());
            }catch (Exception e){
                e.printStackTrace();
                if(e.getMessage().contains("bad SQL grammar")){
                    throw new RuntimeErrorException(null, "运行失败，SQL语法错误");
                }else{
                    throw new RuntimeErrorException(null, e.getMessage());
                }
            }
        }

    }

    private void mtcFtpUpdate(MdmDataEntity mdmDataEntity,SysDatasource sysDatasource){
        Date nowDate = DateUtils.getNowDate();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUsername();

        String operType = MdmConstants.OPER_UPDATE;
        String version = "";
        /**如果版本号为空，表示新增 */
        if(StringUtils.isEmpty(mdmDataEntity.getVersionNo())){
            /**查询历史表里面有没有数据，如果有，代表之前删除了，获取最大的版本号加1，如果没有，就从1开始 */
            version = mdmDataEntityHisMapper.selectVersionNoByRegistryId(mdmDataEntity.getRegistryId());
            if(StringUtils.isEmpty(version)){
                version = "1";
            }else{
                version = Integer.parseInt(version)+1+"";
            }
            operType = MdmConstants.OPER_ADD;
        }else{
            version = Integer.parseInt(mdmDataEntity.getVersionNo())+1+"";
        }

        /** 先清理属性表，再清理实体表，防止关联ID丢失  */
//        mdmDataEntityPropMapper.deleteMdmDataEntityPropByEntityId(mdmDataEntity.getEntityId());
//        mdmDataEntityMapper.deleteMdmDataEntityById(mdmDataEntity.getEntityId());

        /** 插入到实体表 */
        mdmDataEntity.setVersionNo(version);
        mdmDataEntity.setCreateId(userId);
        mdmDataEntity.setCreateTime(nowDate);
        mdmDataEntity.setCreateBy(userName);
        if(StringUtils.isNotNull(mdmDataEntity.getEntityId())){
            mdmDataEntityMapper.updateMdmDataEntity(mdmDataEntity);
        }else{
            mdmDataEntityMapper.insertMdmDataEntity(mdmDataEntity);
        }


        /** 插入到实体历史表 */
        MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
        BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntity);
        dataEntityHis.setVersionNo(version);
        dataEntityHis.setCreateId(userId);
        dataEntityHis.setCreateTime(nowDate);
        dataEntityHis.setCreateBy(userName);
        dataEntityHis.setOperType(operType);
        dataEntityHis.setIsMtc("1");
        mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);

        MdmRegistry mdmRegistry  = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());

        //BHQ 将元数据自动添加到资产管理
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	DamAssets damAssets = new DamAssets();
        	String remark="";
        	if(StringUtils.isNotEmpty(mdmDataEntity.getTableComment())) {
            	remark=remark+mdmDataEntity.getTableComment()+"\t";
            }
        	if(StringUtils.isNotEmpty(mdmDataEntity.getSqlScripts())) {
            	remark=remark+mdmDataEntity.getSqlScripts()+"\t";
            }
	        damAssets.setRemark(remark);
	        damAssets.setAssetsType(DamConstants.DSM_TYPE_MDM);
	        damAssets.setRelId(mdmDataEntity.getRegistryId()+"");
	        DamAssets d = damAssetsService.selectDamAssetsByRel(damAssets.getRelId(), DamConstants.DSM_TYPE_MDM);
	        damAssets.setSystemId(mdmRegistry.getSystemId());
	        damAssets.setDatasourceId(mdmRegistry.getDatasourceId());
            damAssets.setAssetsName(mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName());
	        //根据资产编号及类型查看资产是否存在，不存在则新增，存在则修改
	        if(d==null) {
	        	damAssets.setAssetsCode(DamConstants.DSM_TYPE_MDM+mdmDataEntity.getEntityId());
	            damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
	        	damAssetsService.insertDamAssets(damAssets);
	        }else {
	        	damAssets.setDamAssetsId(d.getDamAssetsId());
	        	damAssetsService.updateDamAssets(damAssets);
	        }
        }

        /** 是否要推送到服务器 */
        if(mdmDataEntity.getPushServer()){
            Map map = HttpUtils.splitUrl(sysDatasource.getUrl());
            SftpUtil sftpUtil= SftpUtil.builder()
                    .username(sysDatasource.getUsername())
                    .password(sysDatasource.getPassword())
                    .host(map.get("host").toString())
                    .port((int) map.get("port"))
                    .build();
            String sqlScripts = mdmDataEntity.getSqlScripts();
            String charsetName = Encoding.getEncoding(sqlScripts);
            sftpUtil.upload(mdmRegistry.getRegDir(),mdmRegistry.getRegName(),sqlScripts,charsetName);
        }


    }

    private void mtcDbViewUpdate(MdmDataEntity mdmDataEntity,SysDatasource sysDatasource){
        /**
         * 1.获取版本号
         * 2.清理正式表
         * 3.插入到实体表
         * 4.插入到实体历史表
         */
        Date nowDate = DateUtils.getNowDate();
        Long userId =  SecurityUtils.getLoginUser().getUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUsername();;

        String operType = MdmConstants.OPER_UPDATE;
        String version = "";
        List<MdmDataEntityPropHis> mdmDataEntityPropHisPre = new ArrayList<>();
        MdmDataEntityHis mdmDataEntityHisPre = new MdmDataEntityHis();
        /**如果版本号为空，表示新增 */
        if(StringUtils.isEmpty(mdmDataEntity.getVersionNo())){
            /**查询历史表里面有没有数据，如果有，代表之前删除了，获取最大的版本号加1，如果没有，就从1开始 */
            version = mdmDataEntityHisMapper.selectVersionNoByRegistryId(mdmDataEntity.getRegistryId());
            if(StringUtils.isEmpty(version)){
                version = "1";
            }else{
                version = Integer.parseInt(version)+1+"";
            }
            operType = MdmConstants.OPER_ADD;
        }else{
            /** 获取上个版本实体和属性，用于对比实体属性，区分新增，修改，删除字段 */
            mdmDataEntityHisPre = mdmDataEntityHisMapper.selectAllByRegistryIdAndVersionNo(mdmDataEntity.getRegistryId(),mdmDataEntity.getVersionNo());
            mdmDataEntityPropHisPre = mdmDataEntityPropHisMapper.selectAllByEntityId(mdmDataEntityHisPre.getEntityId());
            version = Integer.parseInt(mdmDataEntity.getVersionNo())+1+"";
        }
        List<MdmDataEntityPropHis> mdmDataEntityPropHisPreFinal = mdmDataEntityPropHisPre;

        /**根据注册ID获取正式表属性ID，方便还原ID*/
        List<MdmDataEntityProp> idProps = mdmDataEntityPropMapper.selectMdmDataEntityPropByRegId(mdmDataEntity.getRegistryId());
//        mdmDataEntityPropMapper.deleteMdmDataEntityPropByEntityId(mdmDataEntity.getEntityId());
//        mdmDataEntityMapper.deleteMdmDataEntityById(mdmDataEntity.getEntityId());

        /** 插入到实体表 */
        mdmDataEntity.setVersionNo(version);
        mdmDataEntity.setCreateId(userId);
        mdmDataEntity.setCreateTime(nowDate);
        mdmDataEntity.setCreateBy(userName);
        if(StringUtils.isNotNull(mdmDataEntity.getEntityId())){
            mdmDataEntityMapper.updateMdmDataEntity(mdmDataEntity);
        }else{
            mdmDataEntityMapper.insertMdmDataEntity(mdmDataEntity);
        }


        /** 插入到实体历史表 */
        MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
        BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntity);
        dataEntityHis.setVersionNo(version);
        dataEntityHis.setCreateId(userId);
        dataEntityHis.setCreateTime(nowDate);
        dataEntityHis.setCreateBy(userName);
        dataEntityHis.setOperType(operType);
        dataEntityHis.setIsMtc("1");
        /** 对比实体 */
        MdmDataEntity compartEntity = new MdmDataEntity();
        BeanUtils.copyBeanProp(compartEntity,mdmDataEntityHisPre);
        Map compareEntityMap  = BeanUtils.compareFields(compartEntity, mdmDataEntity,
                new String[]{"registryId", "entityId", "tableName","versionNo", "tableSchema","operType","modifyFields","batchId","createId", "updateId", "createTime", "updateTime", "createBy", "updateBy", "searchValue", "remark", "params"});
        if(!compareEntityMap.isEmpty()){
            //记录变动属性字段
            dataEntityHis.setModifyFields(String.join(",", compareEntityMap.keySet()));
        }
        mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);

        /**执行视图语句 */
        JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        jd.execute(mdmDataEntity.getSqlScripts());
        /**执行之后采集视图，获取字段 */
        MdmRegistry mdmRegistry = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
        List<Map<String, Object>> columnsList = metaDataContext.getColumns(mdmRegistry);
        /** 转换columnsList为 List<MdmDataEntityProp>集合*/
        List<MdmDataEntityProp> propList = mdmRegistryService.converMapToDataEntity(columnsList);
//        /** 对比实体属性 */
//        List<MdmDataEntityProp> propList = mdmDataEntity.getProps();

        if(!propList.isEmpty()) {
            List<MdmDataEntityPropHis> insertPropHisList = new ArrayList<>();
            for (MdmDataEntityProp mdmDataEntityProp : propList) {
                mdmDataEntityProp.setEntityId(mdmDataEntity.getEntityId());
                mdmDataEntityProp.setCreateId(userId);
                mdmDataEntityProp.setCreateTime(nowDate);
                mdmDataEntityProp.setCreateBy(userName);

                MdmDataEntityPropHis mdmDataEntityPropHis = new MdmDataEntityPropHis();
                BeanUtils.copyBeanProp(mdmDataEntityPropHis, mdmDataEntityProp);
                mdmDataEntityPropHis.setEntityId(dataEntityHis.getEntityId());
                mdmDataEntityPropHis.setCreateId(userId);
                mdmDataEntityPropHis.setCreateTime(nowDate);
                mdmDataEntityPropHis.setCreateBy(userName);
                insertPropHisList.add(mdmDataEntityPropHis);
            }

            /**新增的字段筛选*/
            List<MdmDataEntityPropHis> addEntityPropList = insertPropHisList.stream()
                    .filter(item -> !mdmDataEntityPropHisPreFinal.stream()
                            .map(MdmDataEntityPropHis::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("新增的字段集合:{}", addEntityPropList.toString());

            /**删除的字段筛选*/
            List<MdmDataEntityPropHis> delEntityPropList = mdmDataEntityPropHisPre.stream()
                    .filter(item -> !insertPropHisList.stream()
                            .map(MdmDataEntityPropHis::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("删除的字段集合:{}", delEntityPropList.toString());

            /**修改或者无变化字段筛选*/
            List<MdmDataEntityPropHis> updateEntityPropList = insertPropHisList.stream()
                    .filter(item -> mdmDataEntityPropHisPreFinal.stream()
                            .map(MdmDataEntityPropHis::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName())
                    )
                    .collect(Collectors.toList());
            log.info("修改或者无变化字段集合:{}", updateEntityPropList.toString());

            List<MdmDataEntityPropHis> insertPropHisListByType = new ArrayList<>();

            /**添加新增字段到插入集合*/
            addEntityPropList.stream().forEach(entityPropHis -> {
                entityPropHis.setOperType(MdmConstants.OPER_ADD);
                insertPropHisListByType.add(entityPropHis);
            });

            /**添加删除字段到插入集合*/
            delEntityPropList.stream().forEach(entityPropHis -> {
                entityPropHis.setEntityId(dataEntityHis.getEntityId());
                entityPropHis.setOperType(MdmConstants.OPER_DEL);
                entityPropHis.setCreateId(userId);
                entityPropHis.setCreateBy(userName);
                entityPropHis.setCreateTime(nowDate);
                insertPropHisListByType.add(entityPropHis);
            });


            /**添加修改或者无变化字段到插入集合*/
            updateEntityPropList.stream().forEach(entityProp -> {
                mdmDataEntityPropHisPreFinal.stream().forEach(oldEntityProp -> {
                    if (entityProp.getPropName().equals(oldEntityProp.getPropName())) {
                        Map comparePropMap = BeanUtils.compareFields(oldEntityProp, entityProp,
                                new String[]{"propId", "entityId", "propName", "versionNo", "operType", "modifyFields", "batchId", "createId", "updateId", "createTime", "updateTime", "createBy", "updateBy", "searchValue", "remark", "params"});
                        if (comparePropMap.isEmpty()) {
                            //标记为无变化字段
                            entityProp.setOperType(MdmConstants.OPER_UNCHANGE);
                        } else {
                            //标记为修改字段,并记录变动属性字段
                            entityProp.setModifyFields(String.join(",", comparePropMap.keySet()));
                            entityProp.setOperType(MdmConstants.OPER_UPDATE);
                        }
                        insertPropHisListByType.add(entityProp);
                    }
                });
            });

            /*******为了保持ID不变，做的实体字段对比 start****/
            /**新增的字段筛选*/
            List<MdmDataEntityProp> addPropList = propList.stream()
                    .filter(item -> !idProps.stream()
                            .map(MdmDataEntityProp::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("新增的字段集合:{}",addEntityPropList.toString());
            if(StringUtils.isNotEmpty(addPropList)){
                mdmDataEntityPropMapper.insertMdmDataEntityPropBatch(addPropList);
            }

            /**删除的字段筛选*/
            List<MdmDataEntityProp> delPropList = idProps.stream()
                    .filter(item -> !propList.stream()
                            .map(MdmDataEntityProp::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("删除的字段集合:{}",delEntityPropList.toString());
            Long [] propIds = new Long[delPropList.size()];
            for (int i = 0; i < delPropList.size(); i++) {
                propIds[i] = delPropList.get(i).getPropId();
            }
            if(StringUtils.isNotEmpty(propIds)){
                mdmDataEntityPropMapper.deleteMdmDataEntityPropByIds(propIds);
            }

            /**修改或者无变化字段筛选*/
            List<MdmDataEntityProp> updatePropList = propList.stream()
                    .filter(item -> idProps.stream()
                            .map(MdmDataEntityProp::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName())
                    )
                    .collect(Collectors.toList());
            log.info("修改或者无变化字段集合:{}",updateEntityPropList.toString());
            for (MdmDataEntityProp mdmDataEntityProp : updatePropList) {
                mdmDataEntityPropMapper.updateMdmDataEntityProp(mdmDataEntityProp);
            }
            /*******为了保持ID不变，做的实体字段对比 end****/

            if (!insertPropHisListByType.isEmpty()) {
                mdmDataEntityPropHisMapper.insertMdmDataEntityPropHisBatch(insertPropHisListByType);
            }
        }
        //BHQ 将元数据自动添加到资产管理
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	DamAssets damAssets = new DamAssets();
        	String remark="";
        	if(StringUtils.isNotEmpty(mdmDataEntity.getTableComment())) {
            	remark=remark+mdmDataEntity.getTableComment()+"\t";
            }
        	if(StringUtils.isNotEmpty(mdmDataEntity.getSqlScripts())) {
            	remark=remark+mdmDataEntity.getSqlScripts()+"\t";
            }
	        damAssets.setRemark(remark);
	        damAssets.setAssetsType(DamConstants.DSM_TYPE_MDM);
	        damAssets.setRelId(mdmDataEntity.getRegistryId()+"");
	        DamAssets d = damAssetsService.selectDamAssetsByRel(damAssets.getRelId(), DamConstants.DSM_TYPE_MDM);
	        damAssets.setSystemId(mdmRegistry.getSystemId());
	        damAssets.setDatasourceId(mdmRegistry.getDatasourceId());
            damAssets.setAssetsName(mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName());
	        //根据资产编号及类型查看资产是否存在，不存在则新增，存在则修改
	        if(d==null) {
	        	damAssets.setAssetsCode(DamConstants.DSM_TYPE_MDM+mdmDataEntity.getEntityId());
	            damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
	        	damAssetsService.insertDamAssets(damAssets);
	        }else {
	        	damAssets.setDamAssetsId(d.getDamAssetsId());
	        	damAssetsService.updateDamAssets(damAssets);
	        }
        }
    }

    private void mtcDbTableUpdate(MdmDataEntity mdmDataEntity){
    	String remark="";
    	if(StringUtils.isNotEmpty(mdmDataEntity.getTableComment())) {
        	remark=remark+mdmDataEntity.getTableComment()+"\t";
        }
        Date nowDate = DateUtils.getNowDate();
        Long userId =  SecurityUtils.getLoginUser().getUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUsername();;

        String operType = MdmConstants.OPER_UPDATE;
        String version = "";
        List<MdmDataEntityPropHis> mdmDataEntityPropHisPre = new ArrayList<>();
        MdmDataEntityHis mdmDataEntityHisPre = new MdmDataEntityHis();
        /**如果版本号为空，表示新增 */
        if(StringUtils.isEmpty(mdmDataEntity.getVersionNo())){
            /**查询历史表里面有没有数据，如果有，代表之前删除了，获取最大的版本号加1，如果没有，就从1开始 */
            version = mdmDataEntityHisMapper.selectVersionNoByRegistryId(mdmDataEntity.getRegistryId());
            if(StringUtils.isEmpty(version)){
                version = "1";
            }else{
                version = Integer.parseInt(version)+1+"";
            }
            operType = MdmConstants.OPER_ADD;
        }else{
            /** 获取上个版本实体和属性，用于对比实体属性，区分新增，修改，删除字段 */
            mdmDataEntityHisPre = mdmDataEntityHisMapper.selectAllByRegistryIdAndVersionNo(mdmDataEntity.getRegistryId(),mdmDataEntity.getVersionNo());
            mdmDataEntityPropHisPre = mdmDataEntityPropHisMapper.selectAllByEntityId(mdmDataEntityHisPre.getEntityId());
            version = Integer.parseInt(mdmDataEntity.getVersionNo())+1+"";
        }
        List<MdmDataEntityPropHis> mdmDataEntityPropHisPreFinal = mdmDataEntityPropHisPre;


        /**根据注册ID获取正式表属性ID，方便还原ID*/
        List<MdmDataEntityProp> idProps = mdmDataEntityPropMapper.selectMdmDataEntityPropByRegId(mdmDataEntity.getRegistryId());
//        mdmDataEntityPropMapper.deleteMdmDataEntityPropByEntityId(mdmDataEntity.getEntityId());
//        mdmDataEntityMapper.deleteMdmDataEntityById(mdmDataEntity.getEntityId());

        mdmDataEntity.setVersionNo(version);
        mdmDataEntity.setCreateId(userId);
        mdmDataEntity.setCreateTime(nowDate);
        mdmDataEntity.setCreateBy(userName);
        if(StringUtils.isNotNull(mdmDataEntity.getEntityId())){
            mdmDataEntityMapper.updateMdmDataEntity(mdmDataEntity);
        }else {
            mdmDataEntityMapper.insertMdmDataEntity(mdmDataEntity);
        }



        MdmDataEntityHis dataEntityHis = new MdmDataEntityHis();
        BeanUtils.copyBeanProp(dataEntityHis,mdmDataEntity);
        dataEntityHis.setVersionNo(version);
        dataEntityHis.setCreateId(userId);
        dataEntityHis.setCreateTime(nowDate);
        dataEntityHis.setCreateBy(userName);
        dataEntityHis.setOperType(operType);
        dataEntityHis.setIsMtc("1");
        /** 对比实体 */
        MdmDataEntity compartEntity = new MdmDataEntity();
        BeanUtils.copyBeanProp(compartEntity,mdmDataEntityHisPre);
        Map compareEntityMap  = BeanUtils.compareFields(compartEntity, mdmDataEntity,
                new String[]{"registryId", "entityId", "tableName","versionNo", "tableSchema","operType","modifyFields","batchId","createId", "updateId", "createTime", "updateTime", "createBy", "updateBy", "searchValue", "remark", "params"});
        if(!compareEntityMap.isEmpty()){
            //记录变动属性字段
            dataEntityHis.setModifyFields(String.join(",", compareEntityMap.keySet()));
        }
        mdmDataEntityHisMapper.insertMdmDataEntityHis(dataEntityHis);

        /** 对比实体属性 */
        List<MdmDataEntityProp> propList = mdmDataEntity.getProps();
        if(!propList.isEmpty()) {
            List<MdmDataEntityPropHis> insertPropHisList = new ArrayList<>();
            for (MdmDataEntityProp mdmDataEntityProp : propList) {
            	remark=remark+" "+mdmDataEntityProp.getPropName()+"("+(mdmDataEntityProp.getPropComment()==null?"无":mdmDataEntityProp.getPropComment())+")";
                mdmDataEntityProp.setEntityId(mdmDataEntity.getEntityId());
                mdmDataEntityProp.setCreateId(userId);
                mdmDataEntityProp.setCreateTime(nowDate);
                mdmDataEntityProp.setCreateBy(userName);

                MdmDataEntityPropHis mdmDataEntityPropHis = new MdmDataEntityPropHis();
                BeanUtils.copyBeanProp(mdmDataEntityPropHis, mdmDataEntityProp);
                mdmDataEntityPropHis.setEntityId(dataEntityHis.getEntityId());
                mdmDataEntityPropHis.setCreateId(userId);
                mdmDataEntityPropHis.setCreateTime(nowDate);
                mdmDataEntityPropHis.setCreateBy(userName);
                insertPropHisList.add(mdmDataEntityPropHis);
            }

            /**新增的字段筛选*/
            List<MdmDataEntityPropHis> addEntityPropList = insertPropHisList.stream()
                    .filter(item -> !mdmDataEntityPropHisPreFinal.stream()
                            .map(MdmDataEntityPropHis::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("新增的字段集合:{}",addEntityPropList.toString());

            /**删除的字段筛选*/
            List<MdmDataEntityPropHis> delEntityPropList = mdmDataEntityPropHisPre.stream()
                    .filter(item -> !insertPropHisList.stream()
                            .map(MdmDataEntityPropHis::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("删除的字段集合:{}",delEntityPropList.toString());

            /**修改或者无变化字段筛选*/
            List<MdmDataEntityPropHis> updateEntityPropList = insertPropHisList.stream()
                    .filter(item -> mdmDataEntityPropHisPreFinal.stream()
                            .map(MdmDataEntityPropHis::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName())
                    )
                    .collect(Collectors.toList());
            log.info("修改或者无变化字段集合:{}",updateEntityPropList.toString());

            List<MdmDataEntityPropHis> insertPropHisListByType = new ArrayList<>();

            /**添加新增字段到插入集合*/
            addEntityPropList.stream().forEach(entityPropHis ->{
                entityPropHis.setOperType(MdmConstants.OPER_ADD);
                insertPropHisListByType.add(entityPropHis);
            });

            /**添加删除字段到插入集合*/
            delEntityPropList.stream().forEach(entityPropHis ->{
                entityPropHis.setEntityId(dataEntityHis.getEntityId());
                entityPropHis.setOperType(MdmConstants.OPER_DEL);
                entityPropHis.setCreateId(userId);
                entityPropHis.setCreateBy(userName);
                entityPropHis.setCreateTime(nowDate);
                insertPropHisListByType.add(entityPropHis);
            });


            /**添加修改或者无变化字段到插入集合*/
            updateEntityPropList.stream().forEach(entityProp ->{
                mdmDataEntityPropHisPreFinal.stream().forEach(oldEntityProp ->{
                    if(entityProp.getPropName().equals(oldEntityProp.getPropName())){
                        Map comparePropMap = BeanUtils.compareFields(oldEntityProp, entityProp,
                                new String[]{"propId", "entityId", "propName", "versionNo","operType","modifyFields","batchId","createId", "updateId", "createTime", "updateTime", "createBy", "updateBy", "searchValue", "remark", "params"});
                        if(comparePropMap.isEmpty()){
                            //标记为无变化字段
                            entityProp.setOperType(MdmConstants.OPER_UNCHANGE);
                        }else{
                            //标记为修改字段,并记录变动属性字段
                            entityProp.setModifyFields(String.join(",", comparePropMap.keySet()));
                            entityProp.setOperType(MdmConstants.OPER_UPDATE);
                        }
                        insertPropHisListByType.add(entityProp);
                    }
                });
            });
            /*******为了保持ID不变，做的实体字段对比 start****/
            /**新增的字段筛选*/
            List<MdmDataEntityProp> addPropList = propList.stream()
                    .filter(item -> !idProps.stream()
                            .map(MdmDataEntityProp::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("新增的字段集合:{}",addEntityPropList.toString());
            if(StringUtils.isNotEmpty(addPropList)){
                mdmDataEntityPropMapper.insertMdmDataEntityPropBatch(addPropList);
            }

            /**删除的字段筛选*/
            List<MdmDataEntityProp> delPropList = idProps.stream()
                    .filter(item -> !propList.stream()
                            .map(MdmDataEntityProp::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName()))
                    .collect(Collectors.toList());
            log.info("删除的字段集合:{}",delEntityPropList.toString());
            Long [] propIds = new Long[delPropList.size()];
            for (int i = 0; i < delPropList.size(); i++) {
                propIds[i] = delPropList.get(i).getPropId();
            }
            if(StringUtils.isNotEmpty(propIds)){
                mdmDataEntityPropMapper.deleteMdmDataEntityPropByIds(propIds);
            }

            /**修改或者无变化字段筛选*/
            List<MdmDataEntityProp> updatePropList = propList.stream()
                    .filter(item -> idProps.stream()
                            .map(MdmDataEntityProp::getPropName)
                            .collect(Collectors.toList())
                            .contains(item.getPropName())
                    )
                    .collect(Collectors.toList());
            log.info("修改或者无变化字段集合:{}",updateEntityPropList.toString());
            for (MdmDataEntityProp mdmDataEntityProp : updatePropList) {
                mdmDataEntityPropMapper.updateMdmDataEntityProp(mdmDataEntityProp);
            }
            /*******为了保持ID不变，做的实体字段对比 end****/

            if (!insertPropHisListByType.isEmpty()) {
                mdmDataEntityPropHisMapper.insertMdmDataEntityPropHisBatch(insertPropHisListByType);
            }
        }
        //BHQ 将元数据自动添加到资产管理
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	DamAssets damAssets = new DamAssets();
	        damAssets.setRemark(remark);
	        damAssets.setAssetsType(DamConstants.DSM_TYPE_MDM);
	        damAssets.setRelId(mdmDataEntity.getRegistryId()+"");
	        DamAssets d = damAssetsService.selectDamAssetsByRel(damAssets.getRelId(), DamConstants.DSM_TYPE_MDM);
	        MdmRegistry mdmRegistry  = mdmRegistryMapper.selectMdmRegistryById(mdmDataEntity.getRegistryId());
	        damAssets.setSystemId(mdmRegistry.getSystemId());
	        damAssets.setDatasourceId(mdmRegistry.getDatasourceId());
            damAssets.setAssetsName(mdmRegistry.getRegDir()+"."+mdmRegistry.getRegName());
	        //根据资产编号及类型查看资产是否存在，不存在则新增，存在则修改
	        if(d==null) {
	        	damAssets.setAssetsCode(DamConstants.DSM_TYPE_MDM+mdmDataEntity.getEntityId());
	            damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
	        	damAssetsService.insertDamAssets(damAssets);
	        }else {
	        	damAssets.setDamAssetsId(d.getDamAssetsId());
	        	damAssetsService.updateDamAssets(damAssets);
	        }
        }


    }




    @Override
    @Transactional
    public void ingore(MdmDataMonitor mdmDataMonitor) {
        Date nowDate = DateUtils.getNowDate();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        String userName = SecurityUtils.getLoginUser().getUsername();

        /** 清理临时表 */
        mdmDataEntityPropTempMapper.delByRegId(mdmDataMonitor.getRegId());
        mdmDataEntityTempMapper.deleteByRegistryId(mdmDataMonitor.getRegId());

        /** 如果是新增的审批被忽略，回退注册信息为未采集 */
        if(mdmDataMonitor.getChangeType().equals(MdmConstants.OPER_ADD)){
            MdmRegistry mdmRegistry = mdmRegistryMapper.selectMdmRegistryById(mdmDataMonitor.getRegId());
            mdmRegistry.setAcqStatus(MdmConstants.ACQ_NO);
            mdmRegistryMapper.updateMdmRegistry(mdmRegistry);
        }

        mdmDataMonitor.setAppStatus(MdmConstants.APP_IGNORE);
        mdmDataMonitor.setAppTime(nowDate);
        mdmDataMonitor.setAppUserId(userId);
        mdmDataMonitor.setAppUserName(userName);

        mdmDataMonitorMapper.updateMdmDataMonitor(mdmDataMonitor);
    }

    @Override
    public Map getEntityNewAndOld(MdmDataMonitor mdmDataMonitor) {
        Long regId = mdmDataMonitor.getRegId();
        MdmDataEntity mdmDataEntity = mdmDataEntityMapper.selectMdmDataEntityByRegId(regId);
        MdmDataEntityTemp mdmDataEntityTemp = mdmDataEntityTempMapper.selectMdmDataEntityTempByRegId(regId);
        List<MdmDataEntityProp> mdmDataEntityPropList = mdmDataEntityPropMapper.selectMdmDataEntityPropByRegId(regId);
        List<MdmDataEntityPropTemp> mdmDataEntityPropTempList = mdmDataEntityPropTempMapper.selectMdmDataEntityPropTempByRegId(regId);
        Map map = new HashMap();
        map.put("newEntity",mdmDataEntityTemp);
        map.put("oldEntity",mdmDataEntity);
        map.put("newEntityProp",mdmDataEntityPropTempList);
        map.put("oldEntityProp",mdmDataEntityPropList);
        return map;
    }

}
