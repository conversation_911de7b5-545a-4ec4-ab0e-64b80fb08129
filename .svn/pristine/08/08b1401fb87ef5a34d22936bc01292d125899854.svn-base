<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="className"
            placeholder="请输入分类名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="assetsClassOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleTreeNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24" style="border-left: 1px solid #304156;">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="70px"
        >
          <el-form-item label="资产名称" prop="assetsName">
            <el-input
              v-model="queryParams.assetsName"
              placeholder="请输入资产名称"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="资产编码" prop="assetsCode">
            <el-input
              v-model="queryParams.assetsCode"
              placeholder="请输入资产编码"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="资产类型" prop="assetsType">
            <el-select
              v-model="queryParams.assetsType"
              placeholder="请选择资产类型"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in assetsTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="管理系统" prop="systemId">
            <el-select
              v-model="queryParams.systemId"
              placeholder="请选择管理系统"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in systemIdOptions"
                :key="dict.systemId"
                :label="dict.name"
                :value="dict.systemId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据来源" prop="datasourceId">
            <el-select
              v-model="queryParams.datasourceId"
              placeholder="请选择数据来源"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in datasourceIdOptions"

                :key="dict.datasourceId"
                :label="dict.name"
                :value="dict.datasourceId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="负责人" prop="person">
            <el-select
              v-model="queryParams.person"
              filterable
              clearable
              size="small"
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="remoteMethod"
              @change="$forceUpdate()"
              :loading="loading"
            >
              <el-option
                v-for="item in personOptions"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="生效日期" prop="startTime">
            <el-date-picker
              clearable
              size="small"
              v-model="queryParams.startTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择生效日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="失效日期" prop="endTime">
            <el-date-picker
              clearable
              size="small"
              v-model="queryParams.endTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择失效日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="资产等级" prop="level">
            <el-select
              v-model="queryParams.level"
              placeholder="请选择资产等级"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in levelOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="资产状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择资产状态"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in statusOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['dam:damAssets:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['dam:damAssets:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['dam:damAssets:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['dam:damAssets:export']"
              >导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['dsm:dsmStandard:export']"
              >导入</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
            :refreshShow="true"
            :searchShow="true"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          border
          :data="damAssetsList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />

          <el-table-column label="资产名称" align="center" prop="assetsName"   width="255"/>
          <el-table-column label="资产编码" align="center" prop="assetsCode"  width="120"/>
          <el-table-column label="资产标签" align="center" prop="classNames"  width="155"/>

          <el-table-column label="管理系统" align="center" prop="systemId" :formatter="systemFormat" width="155"/>
          <el-table-column label="数据源" align="center" prop="datasourceId" :formatter="dataSourceFormat"  width="155"/>
          <el-table-column
            label="资产类型"
            align="center"
            prop="assetsType"
            :formatter="assetsTypeFormat" width="105"
          />
          <el-table-column
            label="生效日期"
            align="center"
            prop="startTime"
            width="100"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.startTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="失效日期"
            align="center"
            prop="endTime"
            width="100"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.endTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="资产等级"
            align="center"
            prop="level"
            :formatter="levelFormat" width="80"
          />
          <el-table-column
            label="资产状态"
            align="center"
            prop="status"
            :formatter="statusFormat" width="80"
          />
          <el-table-column
            label="操作"
            align="center"

            class-name="small-padding fixed-width" width="180" fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['dam:damAssets:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['dam:damAssets:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 添加或修改数据资产对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="资产名称" prop="assetsName">
              <el-input
                v-model="form.assetsName"
                placeholder="请输入资产名称"
                clearable
                :disabled="
                  form.assetsType == '' ||
                  form.assetsType == undefined ||
                  form.assetsType == 'DEFAULT'
                    ? false
                    : true
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="资产标签" prop="assetsClassIds">
              <treeselect
                v-model="form.assetsClassIds"
                placeholder="请输入标准标签"
                :multiple="true"
                :options="assetsClassOptions"
                :disable-branch-nodes="true"
                :show-count="true"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="资产类型" prop="assetsType">
              <el-select
                v-model="form.assetsType"
                placeholder="请选择资产类型 "
                style="width:100%"
                @change="resetRel"
                filterable
                clearable
              >
                <el-option
                  v-for="dict in assetsTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产编码" prop="assetsCode">
              <el-input
                v-model="form.assetsCode"
                placeholder="请输入资产编码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="资产说明" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="管理系统" prop="systemId">
              <el-select
                v-model="form.systemId"
                placeholder="请选择管理系统 "
                style="width:100%"
                filterable
                clearable
              >
                <el-option
                  v-for="item in systemIdOptions"
                  :key="item.systemId"
                  :label="item.name"
                  :value="item.systemId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据来源" prop="datasourceId">
              <el-select
                v-model="form.datasourceId"
                placeholder="请选择数据来源 "
                style="width:100%"
                filterable
                clearable
              >
                <el-option
                  v-for="item in datasourceIdOptions"
                  :key="item.datasourceId"
                  :label="item.name"
                  :value="item.datasourceId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="资产等级" prop="level">
              <el-select
                v-model="form.level"
                placeholder="请选择资产等级 "
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="dict in levelOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="person">
              <el-select
                v-model="form.person"
                filterable
                clearable
                style="width:100%"
                remote
                placeholder="请输入关键词"
                :remote-method="remoteMethod"
                @change="$forceUpdate()"
                :loading="loading"
              >
                <el-option
                  v-for="item in personOptions"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="生效日期" prop="startTime">
              <el-date-picker
                clearable
                size="small"
                style="width:100%"
                v-model="form.startTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择生效日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期" prop="endTime">
              <el-date-picker
                clearable
                size="small"
                style="width:100%"
                v-model="form.endTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择失效日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="资产状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="false">
            <el-form-item label="关联属性" prop="relId">
              <el-input
                v-model="form.relId"
                placeholder="请输入关联属性"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label-width="0px" :error="uploaderr">
              <div class="upload">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :action="uploadUrl"
                  accept=".jpg,.jpeg,.txt,.zip,.xls,.xlsx,.doc,.docx,.pdf,.PDF"
                  :auto-upload="true"
                  :file-list="fileList"
                  :on-success="handleSuccess"
                  :on-remove="handleRemove"
                  :headers="myHeaders"
                  :limit="1"
                >
                  <el-button size="small" icon="el-icon-upload2" type="danger"
                    >上传附件</el-button
                  >
                </el-upload>
                <div class="uptxt">
                  （支持pdf、word、excel、zip、jpg，文件限制500M以内）
                </div>
              </div>
            </el-form-item>
            <el-form-item label="文档名称" prop="attachment" v-show="false">
              <el-input
                v-model="form.attachment"
                maxLength="20"
                placeholder="请输入文档名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="success"
          @click="handleAddRel('STANDARD')"
          icon="el-icon-share"
          plain
          :disabled="
            form.assetsType == '' ||
            form.assetsType == undefined ||
            form.assetsType == 'STANDARD'
              ? false
              : true
          "
          >基础标准</el-button
        >
        <el-button
          type="success"
          @click="handleAddRel('INDEX')"
          icon="el-icon-share"
          plain
          :disabled="
            form.assetsType == '' ||
            form.assetsType == undefined ||
            form.assetsType == 'INDEX'
              ? false
              : true
          "
          >指标标准</el-button
        >
        <el-button
          type="success"
          @click="handleAddRel('MDM')"
          icon="el-icon-share"
          plain
          :disabled="
            form.assetsType == '' ||
            form.assetsType == undefined ||
            form.assetsType == 'MDM'
              ? false
              : true
          "
          >元数据</el-button
        >
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="reltitle"
      :visible.sync="relopen"
      width="900px"
      append-to-body
    >
      <el-table
        v-loading="loading"
        border
        :data="dsmIndexList"
        v-show="form.assetsType == 'INDEX' ? true : false"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="指标编码" align="center" prop="indexCode" />
        <el-table-column label="指标名称" align="center" prop="indexName" />
        <el-table-column
          label="指标分类"
          align="center"
          prop="indexClassNameFull"
          width="255"
        />
        <el-table-column label="版本" align="center" prop="version" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-share"
              @click="handleRel(scope.row, 'INDEX')"
              v-hasPermi="['dsm:dsmIndex:remove']"
              >绑定</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-loading="loading"
        border
        :data="dsmStandardList"
        v-show="form.assetsType == 'STANDARD' ? true : false"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="标准编码" align="center" prop="standardCode" />
        <el-table-column label="标准名称" align="center" prop="standardName" />
        <el-table-column label="标准别名" align="center" prop="standardAlias" />
        <el-table-column
          label="标准分类"
          align="center"
          prop="standardClassNameFull"
          width="255"
        />
        <el-table-column label="版本" align="center" prop="version" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-share"
              @click="handleRel(scope.row, 'STANDARD')"
              v-hasPermi="['dsm:dsmIndex:remove']"
              >绑定</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-loading="loading"
        border
        :data="mdmRegistryList"
        v-show="form.assetsType == 'MDM' ? true : false"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="名称" align="center" prop="regName" />
        <el-table-column
          label="元类型"
          align="center"
          prop="metaType"
          :formatter="metaTypeFormat"
        />
        <el-table-column label="中文注释" align="center" prop="regAnt" />
        <el-table-column label="数据源" align="center" prop="datasourceName" />
        <el-table-column label="所属系统" align="center" prop="systemName" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-share"
              @click="handleRel(scope.row, 'MDM')"
              v-hasPermi="['dsm:dsmIndex:remove']"
              >绑定</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="relcancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 主题导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox
            v-model="upload.updateSupport"
          />是否更新已经存在的基础标准,以<span style="font-weight: bold"
            >名称</span
          >为主键更新
          <el-link type="info" style="font-size:12px" @click="importTemplate"
            >下载模板</el-link
          >
        </div>
        <div class="el-upload__tip" style="color:#ff0000" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDamAssets,
  getDamAssets,
  importTemplate,
  delDamAssets,
  addDamAssets,
  updateDamAssets,
  exportDamAssets
} from "@/api/dam/damAssets";
import { treeselect } from "@/api/dam/damAssetsClass";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getToken } from "@/utils/auth";
import { listSystemAll } from "@/api/basic/system";
import { listDatasourceAll } from "@/api/basic/datasource";
import { listMdmRegistry } from "@/api/mdm/mdmRegistry";
import { listDsmIndex, getDsmIndexVo } from "@/api/dsm/dsmIndex";
import { listDsmStandard, getDsmStandardVo } from "@/api/dsm/dsmStandard";
import {listUser, getUser } from "@/api/system/user";

export default {
  name: "DamAssets",
  components: {
    Treeselect
  },
  data() {
    return {
      // 主题导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/dam/damAssets/import"
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据资产表格数据
      damAssetsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      reltitle: "",
      // 是否显示弹出层
      relopen: false,
      // 资产类型字典
      assetsTypeOptions: [],
      // 管理系统字典
      systemIdOptions: [],
      // 数据来源字典
      datasourceIdOptions: [],
      // 资产等级字典
      levelOptions: [],
      // 资产状态字典
      statusOptions: [],
      // 负责人字典
      personOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      assetsClassOptions: [],
      datasourceOptions: [],
      systemOptions: [],
      className: "",
      defaultProps: {
        children: "children",
        label: "label"
      },
      uploading: false,
      uploaderr: "",
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/attachment/upload",
      myHeaders: {
        Authorization: "Bearer " + getToken()
      },
      isfile: true,
      fileList: [],
      dsmIndexList: [],
      dsmStandardList: [],
      mdmRegistryList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assetsClassId: null,
        assetsName: null,
        assetsCode: null,
        assetsType: null,
        systemId: null,
        datasourceId: null,
        person: null,
        startTime: null,
        endTime: null,
        level: null,
        relId: null,
        status: null,
        attachment: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        assetsName: [
          { required: true, message: "资产名称不能为空", trigger: "blur" }
        ],
        assetsType: [
          { required: true, message: "资产类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getSystem();
    this.getDatasource();
    this.getDicts("assets_type").then(response => {
      this.assetsTypeOptions = response.data;
    });
    this.getDicts("sys_important_level").then(response => {
      this.levelOptions = response.data;
    });
    this.getDicts("dsm_standard_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("mdm_meta_type").then(response => {
      this.metaTypeOptions = response.data;
    });
  },
  watch: {
    className(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    handleRemove(res, file, fileList){
      //文件移除处理
      this.form.attachment = "";
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据资产导入";
      this.upload.open = true;
    },
    /** 查询数据资产列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && "" != this.daterangeCreateTime) {
        this.queryParams.params[
          "beginCreateTime"
        ] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && "" != this.daterangeUpdateTime) {
        this.queryParams.params[
          "beginUpdateTime"
        ] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listDamAssets(this.queryParams).then(response => {
        this.damAssetsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.getDicts("assets_type").then(response => {
        this.assetsTypeOptions = response.data;
      });
      this.getDicts("sys_important_level").then(response => {
        this.levelOptions = response.data;
      });
    },
    getTreeselect() {
      treeselect().then(response => {
        this.assetsClassOptions = response.data;
        const newChild = { id: 0, label: "未分类信息", children: [] };
        this.assetsClassOptions.push(newChild);
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleTreeNodeClick(data) {
      this.queryParams.assetsClassId = data.id;
      this.form.assetsClassId = data.id;
      this.getList();
    },
    handleSuccess(res, file, fileList) {
      // 文件上传成功处理
      this.form.attachment = res.msg;
      //成功后的业务逻辑处理
    },
    /** 获取应用系统 */
    getSystem() {
   	  listSystemAll().then(response => {
        this.systemIdOptions = response.data;
      });
    },
    getDatasource() {
      listDatasourceAll().then(response => {
        this.datasourceIdOptions = response.data;
      });
    },
    // 资产类型字典翻译
    assetsTypeFormat(row, column) {
      return this.selectDictLabel(this.assetsTypeOptions, row.assetsType);
    },
    // 管理系统字典翻译
    systemIdFormat(row, column) {
      return this.selectDictLabel(this.systemIdOptions, row.systemId);
    },
    // 数据来源字典翻译
    datasourceIdFormat(row, column) {
      return this.selectDictLabel(this.datasourceIdOptions, row.datasourceId);
    },
    // 资产等级字典翻译
    levelFormat(row, column) {
      return this.selectDictLabel(this.levelOptions, row.level);
    },
    // 资产状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 元类型字典翻译
    metaTypeFormat(row, column) {
      return this.selectDictLabel(this.metaTypeOptions, row.metaType);
    },
    // 输出类型字典翻译
    dataSourceFormat(row, column) {
    	for(let i=0;i<=this.datasourceIdOptions.length;i++){
    		if(this.datasourceIdOptions[i]&&this.datasourceIdOptions[i].datasourceId==row.datasourceId){
    			return this.datasourceIdOptions[i].name;
    		}
    	}
      return "";
    },
    systemFormat(row, column) {
    	for(let i=0;i<=this.systemIdOptions.length;i++){
    		if(this.systemIdOptions[i]&&this.systemIdOptions[i].systemId==row.systemId){
    			return this.systemIdOptions[i].name;
    		}
    	}
      return "";
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    relcancel() {
      this.relopen = false;
    },
    // 表单重置
    reset() {
      this.form = {
        damAssetsId: null,
        assetsClassIds: null,
        assetsName: null,
        assetsCode: null,
        assetsType: null,
        remark: null,
        systemId: null,
        datasourceId: null,
        person: null,
        startTime: null,
        endTime: null,
        level: null,
        relId: null,
        status: "0",
        attachment: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.queryParams.assetsClassId=null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.damAssetsId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据资产";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const damAssetsId = row.damAssetsId || this.ids;
      getDamAssets(damAssetsId).then(response => {
        this.form = response.data;
        if(this.form.person != null && this.form.person!="" && this.form.person!=undefined){
          getUser(this.form.person).then(response => {
            // this.personOptions = [{"userId":response.data.userId,"nickName":response.data.nickName}];
            this.personOptions = [response.data];
            this.form.person = Number(this.form.person);
          });
        }else{
          this.personOptions = [];
        }
   	 	if(response.data.attachment!=null && response.data.attachment!=""){
            this.fileList = [
              {
                name: response.data.attachment,
                url: response.data.attachment
              }
            ];
          }
        this.open = true;
        this.title = "修改数据资产";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.damAssetsId != null) {
            updateDamAssets(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDamAssets(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const damAssetsIds = row.damAssetsId || this.ids;
      this.$confirm(
        '是否确认删除数据资产编号为"' + damAssetsIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delDamAssets(damAssetsIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有数据资产数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportDamAssets(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    },
    handleAddRel(type) {
      this.resetRel();
      this.relopen = true;
      this.reltitle = "绑定";
      let param = {};
      if (type == "INDEX") {
        this.form.assetsType = "INDEX";
        listDsmIndex(param).then(response => {
          this.dsmIndexList = response.rows;
        });
      } else if (type == "STANDARD") {
        this.form.assetsType = "STANDARD";
        listDsmStandard(param).then(response => {
          this.dsmStandardList = response.rows;
        });
      } else if (type == "MDM") {
        this.form.assetsType = "MDM";
        listMdmRegistry(param).then(response => {
          this.mdmRegistryList = response.rows;
        });
      }
    },
    handleRel(row, type) {
      if (type == "INDEX") {
        getDsmIndexVo(row.indexId).then(response => {
          this.form.remark = response.data.memo;
        });
        this.form.assetsName = row.indexName;
        this.form.relId = row.indexId;
        this.form.assetsCode = row.indexCode;
      } else if (type == "STANDARD") {
        getDsmStandardVo(row.standardId).then(response => {
          this.form.remark = response.data.memo;
        });
        this.form.assetsName = row.standardName;
        this.form.relId = row.standardId;
        this.form.assetsCode = row.indexCode;
      } else if (type == "MDM") {
        this.form.assetsName = row.regName;
        this.form.relId = row.regId;
        this.form.remark = row.reg_ant;
      }
      this.relopen = false;
    },
    resetRel() {
      this.form.assetsName = null;
      this.form.relId = null;
      this.form.assetsCode = null;
      this.form.remark = null;
    },
    // 模糊搜索
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          let userParams = { nickName: query };
          listUser(userParams).then(response => {
            this.personOptions = response.rows;
          });
        }, 200);
      } else {
        this.personOptions = [];
      }
    },
  }
};
</script>
