package com.dqms.dqm.controller;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dqm.domain.DqmValidationTask;
import com.dqms.dqm.service.IDqmValidationTaskService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 检查规则任务管理Controller
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@RestController
@RequestMapping("/dqm/task")
public class DqmValidationTaskController extends BaseController
{
    @Autowired
    private IDqmValidationTaskService dqmValidationTaskService;

    /**
     * 查询检查规则任务管理列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:list')")
    @GetMapping("/list")
    public TableDataInfo list(DqmValidationTask dqmValidationTask)
    {
        startPage();
        List<DqmValidationTask> list = dqmValidationTaskService.selectDqmValidationTaskList(dqmValidationTask);
        return getDataTable(list);
    }

    /**
     * 导出检查规则任务管理列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:export')")
    @Log(title = "检查规则任务管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmValidationTask dqmValidationTask)
    {
        List<DqmValidationTask> list = dqmValidationTaskService.selectDqmValidationTaskList(dqmValidationTask);
        ExcelUtil<DqmValidationTask> util = new ExcelUtil<DqmValidationTask>(DqmValidationTask.class);
        return util.exportExcel(list, "task");
    }

    /**
     * 获取检查规则任务管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:query')")
    @GetMapping(value = "/{dqmValidationTaskId}")
    public AjaxResult getInfo(@PathVariable("dqmValidationTaskId") Integer dqmValidationTaskId)
    {
        return AjaxResult.success(dqmValidationTaskService.selectDqmValidationTaskById(dqmValidationTaskId));
    }

    /**
     * 新增检查规则任务管理
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:add')")
    @Log(title = "检查规则任务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DqmValidationTask dqmValidationTask)
    {
        return toAjax(dqmValidationTaskService.insertDqmValidationTask(dqmValidationTask));
    }

    /**
     * 修改检查规则任务管理
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:edit')")
    @Log(title = "检查规则任务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmValidationTask dqmValidationTask)
    {
        return toAjax(dqmValidationTaskService.updateDqmValidationTask(dqmValidationTask));
    }

    /**
     * 删除检查规则任务管理
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:remove')")
    @Log(title = "检查规则任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dqmValidationTaskIds}")
    public AjaxResult remove(@PathVariable Integer[] dqmValidationTaskIds)
    {
        return toAjax(dqmValidationTaskService.deleteDqmValidationTaskByIds(dqmValidationTaskIds));
    }
    /**
     * 查询最近30天规则任务趋势
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationTask:getExeCuteState30')")
    @GetMapping(value = "/getExeCuteState30/{validationRuleCateId}")
    public AjaxResult getExeCuteState30(@PathVariable("validationRuleCateId") String validationRuleCateId)
    {
        DqmValidationTask dqmValidationTask=new DqmValidationTask();
        dqmValidationTask.getParams().put("validationRuleCateId",validationRuleCateId);
        return AjaxResult.success(dqmValidationTaskService.getExeCuteState30(dqmValidationTask));
    }
    /**
     * 查询最近30天规则任务错误结果趋势
     */
    @GetMapping(value = "/getErrorRestule30/{validationRuleCateId}")
    public AjaxResult getErrorRestule30(@PathVariable("validationRuleCateId") String validationRuleCateId)
    {
        DqmValidationTask dqmValidationTask=new DqmValidationTask();
        dqmValidationTask.getParams().put("validationRuleCateId",validationRuleCateId);
        return AjaxResult.success(dqmValidationTaskService.getErrorRestule30(dqmValidationTask));
    }

    /**
     * 规则任务每日运行统计
     */
    @GetMapping("/getTaskRun")
    public AjaxResult getTaskRun()
    {
        DqmValidationTask dqmValidationTask=new DqmValidationTask();
        return AjaxResult.success(dqmValidationTaskService.getTaskRun(dqmValidationTask));
    }

    /**
     * 任务平均耗时排行
     */
    @GetMapping("/getTaskRunTime")
    public AjaxResult getTaskRunTime()
    {
        DqmValidationTask dqmValidationTask=new DqmValidationTask();
        return AjaxResult.success(dqmValidationTaskService.getTaskRunTime(dqmValidationTask));
    }
}
