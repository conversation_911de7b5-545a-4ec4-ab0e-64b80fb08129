package com.dqms.dsc.controller;

import java.util.List;

import com.dqms.dsc.domain.vo.DscEntityPropVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsc.domain.DscEntityProp;
import com.dqms.dsc.service.IDscEntityPropService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 分级分类Controller
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@RestController
@RequestMapping("/dsc/dscEntityProp")
public class DscEntityPropController extends BaseController
{
    @Autowired
    private IDscEntityPropService dscEntityPropService;

    /**
     * 查询分级分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:list')")
    @GetMapping("/list")
    public TableDataInfo list(DscEntityProp dscEntityProp)
    {
        startPage();
        List<DscEntityProp> list = dscEntityPropService.selectDscEntityPropList(dscEntityProp);
        return getDataTable(list);
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<DscEntityPropVo> util = new ExcelUtil<>(DscEntityPropVo.class);
        return util.importTemplateExcel("分级列表");
    }

    /**
     * 导入指标分级列表
     */
    @Log(title = "导入分级列表", businessType = BusinessType.IMPORT )
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:import')")
    @PostMapping("/import")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DscEntityPropVo> util = new ExcelUtil<>(DscEntityPropVo.class);
        List<DscEntityPropVo> dscEntityPropVoList = util.importExcel(file.getInputStream());
        String message = dscEntityPropService.importDscEntityProp(dscEntityPropVoList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导出分级列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:export')")
    @Log(title = "导出分级列表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DscEntityPropVo dscEntityPropVo)
    {
        List<DscEntityPropVo> list = dscEntityPropService.getDscEntityPropListVo(dscEntityPropVo);
        ExcelUtil<DscEntityPropVo> util = new ExcelUtil<DscEntityPropVo>(DscEntityPropVo.class);
        return util.exportExcel(list, "dscEntityPropVo");
    }

    /**
     * 获取分级分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:query')")
    @GetMapping(value = "/{dscEntityPropId}")
    public AjaxResult getInfo(@PathVariable("dscEntityPropId") Long dscEntityPropId)
    {
        return AjaxResult.success(dscEntityPropService.selectDscEntityPropById(dscEntityPropId));
    }

    /**
     * 新增分级分类
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:add')")
    @Log(title = "分级分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DscEntityProp dscEntityProp)
    {
        return toAjax(dscEntityPropService.insertDscEntityProp(dscEntityProp));
    }

    /**
     * 修改分级分类
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:edit')")
    @Log(title = "分级分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DscEntityProp dscEntityProp)
    {
        return toAjax(dscEntityPropService.updateDscEntityProp(dscEntityProp));
    }

    /**
     * 删除分级分类
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityProp:remove')")
    @Log(title = "分级分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dscEntityPropIds}")
    public AjaxResult remove(@PathVariable Long[] dscEntityPropIds)
    {
        return toAjax(dscEntityPropService.deleteDscEntityPropByIds(dscEntityPropIds));
    }
}
