package com.dqms.mdm.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.dqms.common.constant.UserConstants;
import com.dqms.common.core.domain.entity.SysUser;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.mdm.domain.MdmLayer;
import com.dqms.mdm.domain.MdmLayer;
import com.dqms.mdm.domain.MdmUserLayer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.mdm.mapper.MdmLayerMapper;
import com.dqms.mdm.domain.MdmLayer;
import com.dqms.mdm.service.IMdmLayerService;

/**
 * 分层管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-18
 */
@Service
@Slf4j
public class MdmLayerServiceImpl implements IMdmLayerService
{
    @Autowired
    private MdmLayerMapper mdmLayerMapper;

    /**
     * 查询分层管理
     *
     * @param layerId 分层管理ID
     * @return 分层管理
     */
    @Override
    public MdmLayer selectMdmLayerById(Long layerId)
    {
        return mdmLayerMapper.selectMdmLayerById(layerId);
    }

    /**
     * 查询分层管理列表
     *
     * @param mdmLayer 分层管理
     * @return 分层管理
     */
    @Override
    public List<MdmLayer> selectMdmLayerList(MdmLayer mdmLayer)
    {
        List<MdmLayer> mdmLayerList = mdmLayerMapper.selectMdmLayerList(mdmLayer);
        mdmLayerList.stream().forEach(layer -> {
            List<SysUser> sysUserList= layer.getUsers();
            if(sysUserList!=null && !sysUserList.isEmpty()){
                String strNames = "";
                for (int i = 0; i < sysUserList.size(); i++) {
                    strNames+=sysUserList.get(i).getUserName()+",";
                }
                layer.setUserNames(strNames.substring(0,strNames.length()-1));
            }
        });
        return mdmLayerList;
    }

    /**
     * 新增分层管理
     *
     * @param mdmLayer 分层管理
     * @return 结果
     */
    @Override
    public int insertMdmLayer(MdmLayer mdmLayer)
    {
        mdmLayer.setCreateTime(DateUtils.getNowDate());
        mdmLayer.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        mdmLayer.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        int rows = mdmLayerMapper.insertMdmLayer(mdmLayer);
        // 新增用户与主题关联
        insertUserLayer(mdmLayer);
        return rows;
    }

    /**
     * 修改分层管理
     *
     * @param mdmLayer 分层管理
     * @return 结果
     */
    @Override
    public int updateMdmLayer(MdmLayer mdmLayer)
    {
        mdmLayer.setUpdateTime(DateUtils.getNowDate());
        mdmLayer.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        mdmLayer.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        int rows = mdmLayerMapper.updateMdmLayer(mdmLayer);
        // 删除用户与主题关联
        mdmLayerMapper.deleteUserLayerByLayerId(mdmLayer.getLayerId());
        // 新增用户与主题关联
        insertUserLayer(mdmLayer);
        return rows;
    }

    /**
     * 批量删除分层管理
     *
     * @param layerIds 需要删除的分层管理ID
     * @return 结果
     */
    @Override
    public int deleteMdmLayerByIds(Long[] layerIds)
    {
        int rows =  mdmLayerMapper.deleteMdmLayerByIds(layerIds);
        // 删除用户与主题关联
        mdmLayerMapper.deleteUserLayer(layerIds);
        return rows;
    }

    /**
     * 删除分层管理信息
     *
     * @param layerId 分层管理ID
     * @return 结果
     */
    @Override
    public int deleteMdmLayerById(Long layerId)
    {
        int rows = mdmLayerMapper.deleteMdmLayerById(layerId);
        // 删除用户与主题关联
        mdmLayerMapper.deleteUserLayerByLayerId(layerId);
        return rows;
    }

    @Override
    public List<Integer> selectuserIdsByLayerId(Long layerid) {
        return mdmLayerMapper.selectuserIdsByLayerId(layerid);
    }

    /**
     * 新增分层负责人
     *
     * @param mdmLayer 分层管理
     */
    public void insertUserLayer(MdmLayer mdmLayer)
    {
        Long[] users = mdmLayer.getSysPersonIds();
        if (StringUtils.isNotNull(users))
        {
            // 新增分层责任人
            List<MdmUserLayer> list = new ArrayList<MdmUserLayer>();
            for (Long userId : users)
            {
                MdmUserLayer mdmUserLayer = new MdmUserLayer();
                mdmUserLayer.setLayerId(mdmLayer.getLayerId());
                mdmUserLayer.setUserId(userId);
                list.add(mdmUserLayer);
            }
            if (list.size() > 0)
            {
                mdmLayerMapper.batchUserLayer(list);
            }
        }
    }

    /**
     * 导入分层数据
     *
     * @param layerList 分层数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importLayer(List<MdmLayer> layerList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(layerList) || layerList.size() == 0)
        {
            throw new CustomException("导分层数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (MdmLayer mdmLayer : layerList)
        {
            try
            {
                //非空判断
                if(StringUtils.isEmpty(mdmLayer.getLayerName())){
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 名称不能为空 ",failureNum,mdmLayer.getLayerName());
                    failureMsg.append(msg);
                    continue;
                }
                if(StringUtils.isEmpty(mdmLayer.getLayerCode())){
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 编号不能为空 ",failureNum,mdmLayer.getLayerName());
                    failureMsg.append(msg);
                    continue;
                }
                if(StringUtils.isEmpty(mdmLayer.getStatus())){
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 状态不能为空 ",failureNum,mdmLayer.getLayerName());
                    failureMsg.append(msg);
                    continue;
                }
                if(StringUtils.isEmpty(mdmLayer.getUserNames())){
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 责任人不能为空 ",failureNum,mdmLayer.getLayerName());
                    failureMsg.append(msg);
                    continue;
                }

                // 验证编号是否存在
                String codeUnique= this.checkLayerCodeUniqueByName(mdmLayer);
                if(UserConstants.NOT_UNIQUE.equals(codeUnique)){
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 编号[{}]已存在",failureNum,mdmLayer.getLayerName(),mdmLayer.getLayerCode());
                    failureMsg.append(msg);
                    continue;
                }

                //验证责任人是否定义
                String[] userNames = mdmLayer.getUserNames().split(",");
                Long[] userIds = new Long[userNames.length];
                StringBuffer existentStr = new StringBuffer();
                Boolean flag = getaBoolean(userNames, userIds, existentStr);;
                if(flag){
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {}  责任人:[{}] 未定义",failureNum,mdmLayer.getLayerName(),existentStr.substring(0,existentStr.length()-1));
                    failureMsg.append(msg);
                    continue;
                }


                // 验证名称是否存在
                String nameUnique= this.checkLayerNameUnique(mdmLayer);

                if (UserConstants.UNIQUE.equals(nameUnique))
                {
                    mdmLayer.setSysPersonIds(userIds);
                    this.insertMdmLayer(mdmLayer);
                    successNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 导入成功",successNum,mdmLayer.getLayerName());
                    successMsg.append(msg);
                }
                else if (isUpdateSupport)
                {
                    mdmLayer.setSysPersonIds(userIds);
                    this.updateMdmLayer(mdmLayer);
                    successNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 更新成功",successNum,mdmLayer.getLayerName());
                    successMsg.append(msg);
                }else
                {
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、分层 {} 名称[{}]已存在",failureNum,mdmLayer.getLayerName(),mdmLayer.getLayerName());
                    failureMsg.append(msg);
                    continue;
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = StringUtils.format("<br/>{}、分层 {} 导入失败：",failureNum,mdmLayer.getLayerName());
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            String msg = StringUtils.format("很抱歉，导入失败！共 {} 条数据不正确，错误如下：",failureNum);
            failureMsg.insert(0,msg);
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            String msg = StringUtils.format("恭喜您，数据已全部导入成功！共 {} 条，数据如下：",successNum);
            successMsg.insert(0,msg);
        }
        return successMsg.toString();
    }

    private Boolean getaBoolean(String[] userNames, Long[] userIds, StringBuffer existentStr) {
        Boolean flag = false;
        for (int i = 0; i < userNames.length; i++) {
            Long userId = mdmLayerMapper.selectUserIdByName(userNames[i]);
            if (userId == null) {
                flag = true;
                existentStr.append(userNames[i] + ",");
            } else {
                userIds[i] = userId;
            }
        }
        return flag;
    }

    @Override
    public String checkLayerNameUnique(MdmLayer mdmLayer) {
        int count = mdmLayerMapper.checkLayerNameUnique(mdmLayer);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public String checkLayerCodeUnique(MdmLayer mdmLayer) {
        int count = mdmLayerMapper.checkLayerCodeUnique(mdmLayer);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }


    @Override
    public String checkLayerCodeUniqueByName(MdmLayer mdmLayer) {
        int count = mdmLayerMapper.checkLayerCodeUniqueByName(mdmLayer);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public List<MdmLayer> selectMdmLayerAll() {
        return mdmLayerMapper.selectMdmLayerAll();
    }
}
