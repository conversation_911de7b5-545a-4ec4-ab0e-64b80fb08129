package com.dqms.system.mapper;

import java.util.List;

import com.dqms.system.domain.SysUserTaskGroup;

/**
 * 任务分组权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-15
 */
public interface SysUserTaskGroupMapper 
{
    /**
     * 查询任务分组权限
     * 
     * @param userId 任务分组权限ID
     * @return 任务分组权限
     */
    public SysUserTaskGroup selectSysUserTaskGroupById(Long userId);

    /**
     * 查询任务分组权限列表
     * 
     * @param sysUserTaskGroup 任务分组权限
     * @return 任务分组权限集合
     */
    public List<SysUserTaskGroup> selectSysUserTaskGroupList(SysUserTaskGroup sysUserTaskGroup);

    /**
     * 新增任务分组权限
     * 
     * @param sysUserTaskGroup 任务分组权限
     * @return 结果
     */
    public int insertSysUserTaskGroup(SysUserTaskGroup sysUserTaskGroup);

    /**
     * 修改任务分组权限
     * 
     * @param sysUserTaskGroup 任务分组权限
     * @return 结果
     */
    public int updateSysUserTaskGroup(SysUserTaskGroup sysUserTaskGroup);

    /**
     * 删除任务分组权限
     * 
     * @param userId 任务分组权限ID
     * @return 结果
     */
    public int deleteSysUserTaskGroupById(Long userId);

    /**
     * 批量删除任务分组权限
     * 
     * @param userIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysUserTaskGroupByIds(Long[] userIds);
    
    /**
     * 批量新增用户任务分组
     *
     * @param userTaskGroupList 用户任务分组列表
     * @return 结果
     */
    public int batchUserTaskGroup(List<SysUserTaskGroup> userTaskGroupList);
}
