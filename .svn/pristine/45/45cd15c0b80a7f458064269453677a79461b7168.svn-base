<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.task.mapper.EtlTaskGroupRelationMapper">
    
    <resultMap type="EtlTaskGroupRelation" id="EtlTaskGroupRelationResult">
        <result property="taskGroupRelationId"    column="task_group_relation_id"    />
        <result property="srcTaskGroupId"    column="src_task_group_id"    />
        <result property="tarTaskGroupId"    column="tar_task_group_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEtlTaskGroupRelationVo">
        select task_group_relation_id, src_task_group_id, tar_task_group_id, create_by, update_by, create_id, update_id, create_time, update_time from etl_task_group_relation
    </sql>

    <select id="selectEtlTaskGroupRelationList" parameterType="EtlTaskGroupRelation" resultMap="EtlTaskGroupRelationResult">
        <include refid="selectEtlTaskGroupRelationVo"/>
        <where>  
            <if test="srcTaskGroupId != null "> and src_task_group_id = #{srcTaskGroupId}</if>
            <if test="tarTaskGroupId != null "> and tar_task_group_id = #{tarTaskGroupId}</if>
        </where>
    </select>
    
    <select id="selectEtlTaskGroupRelationById" parameterType="Long" resultMap="EtlTaskGroupRelationResult">
        <include refid="selectEtlTaskGroupRelationVo"/>
        where task_group_relation_id = #{taskGroupRelationId}
    </select>
        
    <insert id="insertEtlTaskGroupRelation" parameterType="EtlTaskGroupRelation" useGeneratedKeys="true" keyProperty="taskGroupRelationId">
        insert into etl_task_group_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="srcTaskGroupId != null">src_task_group_id,</if>
            <if test="tarTaskGroupId != null">tar_task_group_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="srcTaskGroupId != null">#{srcTaskGroupId},</if>
            <if test="tarTaskGroupId != null">#{tarTaskGroupId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEtlTaskGroupRelation" parameterType="EtlTaskGroupRelation">
        update etl_task_group_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcTaskGroupId != null">src_task_group_id = #{srcTaskGroupId},</if>
            <if test="tarTaskGroupId != null">tar_task_group_id = #{tarTaskGroupId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_group_relation_id = #{taskGroupRelationId}
    </update>

    <delete id="deleteEtlTaskGroupRelationById" parameterType="Long">
        delete from etl_task_group_relation where task_group_relation_id = #{taskGroupRelationId}
    </delete>

    <delete id="deleteEtlTaskGroupRelationByIds" parameterType="String">
        delete from etl_task_group_relation where task_group_relation_id in 
        <foreach item="taskGroupRelationId" collection="array" open="(" separator="," close=")">
            #{taskGroupRelationId}
        </foreach>
    </delete>
    
    <delete id="delTaskGroupRelationByNode"  parameterType="EtlTaskGroupRelation">
        delete from etl_task_group_relation where src_task_group_id = #{srcTaskGroupId} and tar_task_group_id = #{tarTaskGroupId}
    </delete>
</mapper>