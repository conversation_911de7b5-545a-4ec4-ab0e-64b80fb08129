package com.dqms.dic.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dic.domain.DicManualDataInstallRule;
import com.dqms.dic.service.IDicManualDataInstallRuleService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 字段规则Controller
 *
 * <AUTHOR>
 * @date 2022-06-28
 */
@RestController
@RequestMapping("/dic/dicManualDataInstallRule")
public class DicManualDataInstallRuleController extends BaseController
{
    @Autowired
    private IDicManualDataInstallRuleService dicManualDataInstallRuleService;

    /**
     * 查询字段规则列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DicManualDataInstallRule dicManualDataInstallRule)
    {
        startPage();
        List<DicManualDataInstallRule> list = dicManualDataInstallRuleService.selectDicManualDataInstallRuleList(dicManualDataInstallRule);
        return getDataTable(list);
    }

    /**
     * 导出字段规则列表
     */
    @Log(title = "字段规则", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DicManualDataInstallRule dicManualDataInstallRule)
    {
        List<DicManualDataInstallRule> list = dicManualDataInstallRuleService.selectDicManualDataInstallRuleList(dicManualDataInstallRule);
        ExcelUtil<DicManualDataInstallRule> util = new ExcelUtil<DicManualDataInstallRule>(DicManualDataInstallRule.class);
        return util.exportExcel(list, "dicManualDataInstallRule");
    }

    /**
     * 获取字段规则详细信息
     */
    @GetMapping(value = "/{manualDataInstallId}")
    public AjaxResult getInfo(@PathVariable("manualDataInstallId") Long manualDataInstallId)
    {
        return AjaxResult.success(dicManualDataInstallRuleService.selectDicManualDataInstallRuleById(manualDataInstallId));
    }

    /**
     * 新增字段规则
     */
    @Log(title = "字段规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DicManualDataInstallRule dicManualDataInstallRule)
    {
        return toAjax(dicManualDataInstallRuleService.insertDicManualDataInstallRule(dicManualDataInstallRule));
    }

    /**
     * 修改字段规则
     */
    @Log(title = "字段规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DicManualDataInstallRule dicManualDataInstallRule)
    {
        return toAjax(dicManualDataInstallRuleService.updateDicManualDataInstallRule(dicManualDataInstallRule));
    }

    /**
     * 删除字段规则
     */
    @Log(title = "字段规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{manualDataInstallIds}")
    public AjaxResult remove(@PathVariable Long[] manualDataInstallIds)
    {
        return toAjax(dicManualDataInstallRuleService.deleteDicManualDataInstallRuleByIds(manualDataInstallIds));
    }
}
