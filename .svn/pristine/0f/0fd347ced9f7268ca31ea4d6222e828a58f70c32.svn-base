<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手工数据" prop="manualDataId">
        <el-input
          v-model="queryParams.manualDataId"
          placeholder="请输入手工数据"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="字段" prop="propName">
        <el-input
          v-model="queryParams.propName"
          placeholder="请输入字段"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="注释" prop="propComment">
        <el-input
          v-model="queryParams.propComment"
          placeholder="请输入注释"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="字段类型" prop="propType">
        <el-input
          v-model="queryParams.propType"
          placeholder="请输入字段类型"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="组件类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择组件类型" clearable size="small">
          <el-option
            v-for="dict in typeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="占位提示" prop="placeholder">
        <el-input
          v-model="queryParams.placeholder"
          placeholder="请输入占位提示"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="组件宽度" prop="width">
        <el-input
          v-model="queryParams.width"
          placeholder="请输入组件宽度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="默认值" prop="defaultValue">
        <el-input
          v-model="queryParams.defaultValue"
          placeholder="请输入默认值"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="字段长度" prop="columnSize">
        <el-input
          v-model="queryParams.columnSize"
          placeholder="请输入字段长度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="标签宽度" prop="labelWidth">
        <el-input
          v-model="queryParams.labelWidth"
          placeholder="请输入标签宽度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="前缀" prop="prefix">
        <el-input
          v-model="queryParams.prefix"
          placeholder="请输入前缀"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="后缀" prop="postfix">
        <el-input
          v-model="queryParams.postfix"
          placeholder="请输入后缀"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="可否清空" prop="clearable">
        <el-select v-model="queryParams.clearable" placeholder="请选择可否清空" clearable size="small">
          <el-option
            v-for="dict in clearableOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="可否为空" prop="nullable">
        <el-select v-model="queryParams.nullable" placeholder="请选择可否为空" clearable size="small">
          <el-option
            v-for="dict in nullableOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否主键" prop="isKey">
        <el-select v-model="queryParams.isKey" placeholder="请选择是否主键" clearable size="small">
          <el-option
            v-for="dict in isKeyOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否查询" prop="isSearch">
        <el-select v-model="queryParams.isSearch" placeholder="请选择是否查询" clearable size="small">
          <el-option
            v-for="dict in isSearchOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否列表" prop="isTable">
        <el-select v-model="queryParams.isTable" placeholder="请选择是否列表" clearable size="small">
          <el-option
            v-for="dict in isTableOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模板位置" prop="place">
        <el-input
          v-model="queryParams.place"
          placeholder="请输入模板位置"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="关联维度" prop="relId">
        <el-input
          v-model="queryParams.relId"
          placeholder="请输入关联维度"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="关联表" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入关联表"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dic:dicManualDataInstall:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dic:dicManualDataInstall:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dic:dicManualDataInstall:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dic:dicManualDataInstall:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dicManualDataInstallList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="manualDataInstallId" />
      <el-table-column label="手工数据" align="center" prop="manualDataId" />
      <el-table-column label="字段" align="center" prop="propName" />
      <el-table-column label="注释" align="center" prop="propComment" />
      <el-table-column label="字段类型" align="center" prop="propType" />
      <el-table-column label="组件类型" align="center" prop="type" :formatter="typeFormat" />
      <el-table-column label="占位提示" align="center" prop="placeholder" />
      <el-table-column label="组件宽度" align="center" prop="width" />
      <el-table-column label="默认值" align="center" prop="defaultValue" />
      <el-table-column label="字段长度" align="center" prop="columnSize" />
      <el-table-column label="标签宽度" align="center" prop="labelWidth" />
      <el-table-column label="前缀" align="center" prop="prefix" />
      <el-table-column label="后缀" align="center" prop="postfix" />
      <el-table-column label="可否清空" align="center" prop="clearable" :formatter="clearableFormat" />
      <el-table-column label="可否为空" align="center" prop="nullable" :formatter="nullableFormat" />
      <el-table-column label="是否主键" align="center" prop="isKey" :formatter="isKeyFormat" />
      <el-table-column label="是否查询" align="center" prop="isSearch" :formatter="isSearchFormat" />
      <el-table-column label="是否列表" align="center" prop="isTable" :formatter="isTableFormat" />
      <el-table-column label="模板位置" align="center" prop="place" />
      <el-table-column label="关联维度" align="center" prop="relId" />
      <el-table-column label="关联表" align="center" prop="tableName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dic:dicManualDataInstall:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dic:dicManualDataInstall:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改模板配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="手工数据" prop="manualDataId">
          <el-input v-model="form.manualDataId" placeholder="请输入手工数据" clearable/>
        </el-form-item>
        <el-form-item label="字段" prop="propName">
          <el-input v-model="form.propName" placeholder="请输入字段" clearable/>
        </el-form-item>
        <el-form-item label="注释" prop="propComment">
          <el-input v-model="form.propComment" placeholder="请输入注释" clearable/>
        </el-form-item>
        <el-form-item label="字段类型" prop="propType">
          <el-input v-model="form.propType" placeholder="请输入字段类型" clearable/>
        </el-form-item>
        <el-form-item label="组件类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择组件类型" clearable>
            <el-option
              v-for="dict in typeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="占位提示" prop="placeholder">
          <el-input v-model="form.placeholder" placeholder="请输入占位提示" clearable/>
        </el-form-item>
        <el-form-item label="组件宽度" prop="width">
          <el-input v-model="form.width" placeholder="请输入组件宽度" clearable/>
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input v-model="form.defaultValue" placeholder="请输入默认值" clearable/>
        </el-form-item>
        <el-form-item label="字段长度" prop="columnSize">
          <el-input v-model="form.columnSize" placeholder="请输入字段长度" clearable/>
        </el-form-item>
        <el-form-item label="标签宽度" prop="labelWidth">
          <el-input v-model="form.labelWidth" placeholder="请输入标签宽度" clearable/>
        </el-form-item>
        <el-form-item label="前缀" prop="prefix">
          <el-input v-model="form.prefix" placeholder="请输入前缀" clearable/>
        </el-form-item>
        <el-form-item label="后缀" prop="postfix">
          <el-input v-model="form.postfix" placeholder="请输入后缀" clearable/>
        </el-form-item>
        <el-form-item label="可否清空" prop="clearable">
          <el-select v-model="form.clearable" placeholder="请选择可否清空" clearable>
            <el-option
              v-for="dict in clearableOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可否为空" prop="nullable">
          <el-select v-model="form.nullable" placeholder="请选择可否为空" clearable>
            <el-option
              v-for="dict in nullableOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否主键" prop="isKey">
          <el-select v-model="form.isKey" placeholder="请选择是否主键" clearable>
            <el-option
              v-for="dict in isKeyOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否查询" prop="isSearch">
          <el-select v-model="form.isSearch" placeholder="请选择是否查询" clearable>
            <el-option
              v-for="dict in isSearchOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否列表" prop="isTable">
          <el-select v-model="form.isTable" placeholder="请选择是否列表" clearable>
            <el-option
              v-for="dict in isTableOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板位置" prop="place">
          <el-input v-model="form.place" placeholder="请输入模板位置" clearable/>
        </el-form-item>
        <el-form-item label="关联维度" prop="relId">
          <el-input v-model="form.relId" placeholder="请输入关联维度" clearable/>
        </el-form-item>
        <el-form-item label="关联表" prop="tableName">
          <el-input v-model="form.tableName" placeholder="请输入关联表" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDicManualDataInstall, getDicManualDataInstall, delDicManualDataInstall, addDicManualDataInstall, updateDicManualDataInstall, exportDicManualDataInstall } from "@/api/dic/dicManualDataInstall";

export default {
  name: "DicManualDataInstall",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板配置表格数据
      dicManualDataInstallList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 组件类型字典
      typeOptions: [],
      // 可否清空字典
      clearableOptions: [],
      // 可否为空字典
      nullableOptions: [],
      // 是否主键字典
      isKeyOptions: [],
      // 是否查询字典
      isSearchOptions: [],
      // 是否列表字典
      isTableOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        manualDataId: null,
        propName: null,
        propComment: null,
        propType: null,
        type: null,
        placeholder: null,
        width: null,
        defaultValue: null,
        columnSize: null,
        labelWidth: null,
        prefix: null,
        postfix: null,
        clearable: null,
        nullable: null,
        isKey: null,
        isSearch: null,
        isTable: null,
        place: null,
        relId: null,
        tableName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        manualDataId: [
          { required: true, message: "手工数据不能为空", trigger: "blur" }
        ],
        propName: [
          { required: true, message: "字段不能为空", trigger: "blur" }
        ],
        propComment: [
          { required: true, message: "注释不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "组件类型不能为空", trigger: "change" }
        ],
        width: [
          { required: true, message: "组件宽度不能为空", trigger: "blur" }
        ],
        labelWidth: [
          { required: true, message: "标签宽度不能为空", trigger: "blur" }
        ],
        clearable: [
          { required: true, message: "可否清空不能为空", trigger: "change" }
        ],
        nullable: [
          { required: true, message: "可否为空不能为空", trigger: "change" }
        ],
        isKey: [
          { required: true, message: "是否主键不能为空", trigger: "change" }
        ],
        isSearch: [
          { required: true, message: "是否查询不能为空", trigger: "change" }
        ],
        isTable: [
          { required: true, message: "是否列表不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("dsm_dimension_show_type").then(response => {
      this.typeOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.clearableOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.nullableOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isKeyOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isSearchOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isTableOptions = response.data;
    });
  },
  methods: {
    /** 查询模板配置列表 */
    getList() {
      this.loading = true;
      listDicManualDataInstall(this.queryParams).then(response => {
        this.dicManualDataInstallList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 组件类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.type);
    },
    // 可否清空字典翻译
    clearableFormat(row, column) {
      return this.selectDictLabel(this.clearableOptions, row.clearable);
    },
    // 可否为空字典翻译
    nullableFormat(row, column) {
      return this.selectDictLabel(this.nullableOptions, row.nullable);
    },
    // 是否主键字典翻译
    isKeyFormat(row, column) {
      return this.selectDictLabel(this.isKeyOptions, row.isKey);
    },
    // 是否查询字典翻译
    isSearchFormat(row, column) {
      return this.selectDictLabel(this.isSearchOptions, row.isSearch);
    },
    // 是否列表字典翻译
    isTableFormat(row, column) {
      return this.selectDictLabel(this.isTableOptions, row.isTable);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        manualDataInstallId: null,
        manualDataId: null,
        propName: null,
        propComment: null,
        propType: null,
        type: null,
        placeholder: null,
        width: null,
        defaultValue: null,
        columnSize: null,
        labelWidth: null,
        prefix: null,
        postfix: null,
        clearable: null,
        nullable: null,
        isKey: null,
        isSearch: null,
        isTable: null,
        place: null,
        relId: null,
        tableName: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.manualDataInstallId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加模板配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const manualDataInstallId = row.manualDataInstallId || this.ids
      getDicManualDataInstall(manualDataInstallId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改模板配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.manualDataInstallId != null) {
            updateDicManualDataInstall(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDicManualDataInstall(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const manualDataInstallIds = row.manualDataInstallId || this.ids;
      this.$confirm('是否确认删除模板配置编号为"' + manualDataInstallIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDicManualDataInstall(manualDataInstallIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有模板配置数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDicManualDataInstall(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
