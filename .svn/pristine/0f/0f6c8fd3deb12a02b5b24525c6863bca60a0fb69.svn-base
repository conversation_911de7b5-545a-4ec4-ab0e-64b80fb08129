import request from '@/utils/request'

// 查询元数据快速注册日志列表
export function listMdmCollectFastLog(query) {
  return request({
    url: '/mdm/mdmCollectFastLog/list',
    method: 'get',
    params: query
  })
}

// 查询元数据快速注册日志详细
export function getMdmCollectFastLog(datasourceId) {
  return request({
    url: '/mdm/mdmCollectFastLog/' + datasourceId,
    method: 'get'
  })
}

// 新增元数据快速注册日志
export function addMdmCollectFastLog(data) {
  return request({
    url: '/mdm/mdmCollectFastLog',
    method: 'post',
    data: data
  })
}

// 修改元数据快速注册日志
export function updateMdmCollectFastLog(data) {
  return request({
    url: '/mdm/mdmCollectFastLog',
    method: 'put',
    data: data
  })
}

// 删除元数据快速注册日志
export function delMdmCollectFastLog(datasourceId) {
  return request({
    url: '/mdm/mdmCollectFastLog/' + datasourceId,
    method: 'delete'
  })
}

// 导出元数据快速注册日志
export function exportMdmCollectFastLog(query) {
  return request({
    url: '/mdm/mdmCollectFastLog/export',
    method: 'get',
    params: query
  })
}