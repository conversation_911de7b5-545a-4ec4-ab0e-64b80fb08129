package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmStandardSrcSystem;
import com.dqms.dsm.service.IDsmStandardSrcSystemService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 标准来源系统Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmStandardSrcSystem")
public class DsmStandardSrcSystemController extends BaseController
{
    @Autowired
    private IDsmStandardSrcSystemService dsmStandardSrcSystemService;

    /**
     * 查询标准来源系统列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcSystem:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        startPage();
        List<DsmStandardSrcSystem> list = dsmStandardSrcSystemService.selectDsmStandardSrcSystemList(dsmStandardSrcSystem);
        return getDataTable(list);
    }

    /**
     * 导出标准来源系统列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcSystem:export')")
    @Log(title = "标准来源系统", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        List<DsmStandardSrcSystem> list = dsmStandardSrcSystemService.selectDsmStandardSrcSystemList(dsmStandardSrcSystem);
        ExcelUtil<DsmStandardSrcSystem> util = new ExcelUtil<DsmStandardSrcSystem>(DsmStandardSrcSystem.class);
        return util.exportExcel(list, "dsmStandardSrcSystem");
    }

    /**
     * 获取标准来源系统详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcSystem:query')")
    @GetMapping(value = "/{standardId}")
    public AjaxResult getInfo(@PathVariable("standardId") Long standardId)
    {
        return AjaxResult.success(dsmStandardSrcSystemService.selectDsmStandardSrcSystemById(standardId));
    }

    /**
     * 新增标准来源系统
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcSystem:add')")
    @Log(title = "标准来源系统", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        return toAjax(dsmStandardSrcSystemService.insertDsmStandardSrcSystem(dsmStandardSrcSystem));
    }

    /**
     * 修改标准来源系统
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcSystem:edit')")
    @Log(title = "标准来源系统", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        return toAjax(dsmStandardSrcSystemService.updateDsmStandardSrcSystem(dsmStandardSrcSystem));
    }

    /**
     * 删除标准来源系统
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcSystem:remove')")
    @Log(title = "标准来源系统", businessType = BusinessType.DELETE)
	@DeleteMapping("/{standardIds}")
    public AjaxResult remove(@PathVariable Long[] standardIds)
    {
        return toAjax(dsmStandardSrcSystemService.deleteDsmStandardSrcSystemByIds(standardIds));
    }
}
