package com.dqms.dsm.domain;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 字典分类对象 dsm_dimension_class
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
public class DsmDimensionClass extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分类ID */
    private Long dimensionClassId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String className;

    /** 全路径 */
    @Excel(name = "全路径")
    private String classNameFull;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    /** 父菜单ID */
    private Long parentId;

    /** 显示顺序 */
    private Integer orderNum;

    /** 祖级列表 */
    private String ancestors;
    
    /** 子部门 */
    private List<DsmDimensionClass> children = new ArrayList<DsmDimensionClass>();

    public void setDimensionClassId(Long dimensionClassId)
    {
        this.dimensionClassId = dimensionClassId;
    }

    public Long getDimensionClassId()
    {
        return dimensionClassId;
    }
    public void setClassName(String className)
    {
        this.className = className;
    }

    public String getClassName()
    {
        return className;
    }
    public void setClassNameFull(String classNameFull)
    {
        this.classNameFull = classNameFull;
    }

    public String getClassNameFull()
    {
        return classNameFull;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public List<DsmDimensionClass> getChildren() {
		return children;
	}

	public void setChildren(List<DsmDimensionClass> children) {
		this.children = children;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public String getAncestors() {
		return ancestors;
	}

	public void setAncestors(String ancestors) {
		this.ancestors = ancestors;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dimensionClassId", getDimensionClassId())
            .append("parentId", getParentId())
            .append("ancestors", getAncestors())
            .append("className", getClassName())
            .append("classNameFull", getClassNameFull())
            .append("orderNum", getOrderNum())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
