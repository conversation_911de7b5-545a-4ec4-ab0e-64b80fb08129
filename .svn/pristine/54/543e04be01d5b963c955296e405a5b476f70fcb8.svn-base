package com.dqms.mdm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据实体对象 mdm_data_entity
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
public class MdmDataEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据实体编号 */
    private Long entityId;

    /** 所属元数据注册ID */
    @Excel(name = "所属元数据注册ID")
    private Long registryId;

    /** 表名 */
    @Excel(name = "表名")
    private String tableName;

    /** 表注释 */
    @Excel(name = "表注释")
    private String tableComment;

    /** SCHEMA */
    @Excel(name = "SCHEMA")
    private String tableSchema;

    /** SQL脚本 */
    @Excel(name = "SQL脚本")
    private String sqlScripts;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNo;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createId;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long updateId;

    private Boolean pushServer;

    private String systemName;
    
    private Long datasourceId;

    @Valid
    private List<MdmDataEntityProp> props;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public Boolean getPushServer() {
        return pushServer;
    }

    public void setPushServer(Boolean pushServer) {
        this.pushServer = pushServer;
    }

    public List<MdmDataEntityProp> getProps() {
        return props;
    }

    public void setProps(List<MdmDataEntityProp> props) {
        this.props = props;
    }

    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setRegistryId(Long registryId)
    {
        this.registryId = registryId;
    }

    public Long getRegistryId()
    {
        return registryId;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setTableComment(String tableComment)
    {
        this.tableComment = tableComment;
    }

    public String getTableComment()
    {
        return tableComment;
    }
    public void setTableSchema(String tableSchema)
    {
        this.tableSchema = tableSchema;
    }

    public String getTableSchema()
    {
        return tableSchema;
    }
    public void setSqlScripts(String sqlScripts)
    {
        this.sqlScripts = sqlScripts;
    }

    public String getSqlScripts()
    {
        return sqlScripts;
    }
    public void setVersionNo(String versionNo)
    {
        this.versionNo = versionNo;
    }

    public String getVersionNo()
    {
        return versionNo;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getDatasourceId() {
		return datasourceId;
	}

	public void setDatasourceId(Long datasourceId) {
		this.datasourceId = datasourceId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("entityId", getEntityId())
            .append("registryId", getRegistryId())
            .append("tableName", getTableName())
            .append("tableComment", getTableComment())
            .append("tableSchema", getTableSchema())
            .append("sqlScripts", getSqlScripts())
            .append("versionNo", getVersionNo())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
