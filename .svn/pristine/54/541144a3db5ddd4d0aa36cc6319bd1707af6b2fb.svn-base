package com.dqms.utils;

import java.util.Arrays;
import java.util.List;

import io.lettuce.core.ScriptOutputType;
import org.springframework.stereotype.Component;

import com.alibaba.druid.filter.config.ConfigTools;
import com.dqms.system.service.ISysConfigService;

@Component
public class PasswordEncryptUtils {

	private static String privateKey;
	private static String publicKey;
	private static ISysConfigService configService;
	public static final String key= "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIzyScSubADc/tpouIbG137jA4doUcTZE8UBA6pUGOIl/gqbyLsj+n4XsGbYKqocmWJySNPCDHvZmc8woKa2h+MCAwEAAQ==";

	/*
	 * static { configService = SpringUtils.getBean(SysConfigServiceImpl.class); }
	 *
	 * @PostConstruct public void initPassword() { String password =
	 * configService.selectConfigByKey("sys.vmwarePassword");
	 * if(StringUtil.isEmpty(privateKey) || StringUtil.isEmpty(publicKey)) { try {
	 * List<String> genkey = genkey(password); privateKey = genkey.get(0); publicKey
	 * = genkey.get(1); } catch (Exception e) { privateKey =
	 * "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAjPJJxK5sANz+2mi4hsbXfuMDh2hRxNkTxQEDqlQY4iX+CpvIuyP6fhewZtgqqhyZYnJI08IMe9mZzzCgpraH4wIDAQABAkBPMNqNrUp1ebWfVXNIFnSBkF8tN0mqBiV7bMR4cOPCO6z3I27YZZWGv//G+gb3+302cd/INTWe8bw+HPngx6qBAiEAwAeKicaukjrR1MRUCevXMLgXoxMIxiffZcX+0nlNu+ECIQC75lZhXsyMe3cXR4LSPLCUBVbO5XeP6Spqp53YXwncQwIgd8L8QwsXivmGLiU3qGxe6/KUQ082tKbfa9C9KTUgJuECIAejTU+X8uii/CxOE9tH7EWYEfdHDJ0k9Txwo9Hvyw7RAiEAvPbTa4ljz3L58DtWuKqRoP+JRKq3OSBl8GbMu6y8YJE=";
	 * publicKey =
	 * "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIzyScSubADc/tpouIbG137jA4doUcTZE8UBA6pUGOIl/gqbyLsj+n4XsGbYKqocmWJySNPCDHvZmc8woKa2h+MCAwEAAQ==";
	 * } } }
	 */

	public static String encrypt(String password,String privateKey) throws Exception {
		String cipherText = ConfigTools.encrypt(privateKey, password);
		return cipherText;
	}

	public static String decrypt(String cipherText,String publicKey) throws Exception {
		String password = ConfigTools.decrypt(publicKey, cipherText);
		return password;
	}

	public static List<String> genkey(String password) throws Exception {

		String[] arr = ConfigTools.genKeyPair(512);
        System.out.println("privateKey:" + arr[0]);
        System.out.println("publicKey:" + arr[1]);
        System.out.println("password:" + ConfigTools.encrypt(arr[0], password));
        List<String> keys = Arrays.asList(arr);
        return keys;
	}
	public static void main(String[] args) throws Exception {
        String[] arr = ConfigTools.genKeyPair(512);
        System.out.println("privateKey:" + arr[0]);
        System.out.println("publicKey:" + arr[1]);

        long now = System.currentTimeMillis();
        System.out.println(now);
//		String encrypt = encrypt("bhq100521","MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAjPJJxK5sANz+2mi4hsbXfuMDh2hRxNkTxQEDqlQY4iX+CpvIuyP6fhewZtgqqhyZYnJI08IMe9mZzzCgpraH4wIDAQABAkBPMNqNrUp1ebWfVXNIFnSBkF8tN0mqBiV7bMR4cOPCO6z3I27YZZWGv//G+gb3+302cd/INTWe8bw+HPngx6qBAiEAwAeKicaukjrR1MRUCevXMLgXoxMIxiffZcX+0nlNu+ECIQC75lZhXsyMe3cXR4LSPLCUBVbO5XeP6Spqp53YXwncQwIgd8L8QwsXivmGLiU3qGxe6/KUQ082tKbfa9C9KTUgJuECIAejTU+X8uii/CxOE9tH7EWYEfdHDJ0k9Txwo9Hvyw7RAiEAvPbTa4ljz3L58DtWuKqRoP+JRKq3OSBl8GbMu6y8YJE=");
//		System.out.println(encrypt);
//		String decrypt = decrypt(encrypt,key);
//		System.out.println(decrypt);
	}
}
