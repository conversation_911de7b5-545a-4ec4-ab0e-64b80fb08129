package com.dqms.dsm.service.impl;

import java.util.List;
import com.dqms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmDiscernMainMapper;
import com.dqms.dsm.domain.DsmDiscernMain;
import com.dqms.dsm.service.IDsmDiscernMainService;

/**
 * 落标主Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-01
 */
@Service
public class DsmDiscernMainServiceImpl implements IDsmDiscernMainService
{
    @Autowired
    private DsmDiscernMainMapper dsmDiscernMainMapper;

    /**
     * 查询落标主
     *
     * @param discernMainId 落标主ID
     * @return 落标主
     */
    @Override
    public DsmDiscernMain selectDsmDiscernMainById(Long discernMainId)
    {
        return dsmDiscernMainMapper.selectDsmDiscernMainById(discernMainId);
    }

    /**
     * 查询落标主列表
     *
     * @param dsmDiscernMain 落标主
     * @return 落标主
     */
    @Override
    public List<DsmDiscernMain> selectDsmDiscernMainList(DsmDiscernMain dsmDiscernMain)
    {
        return dsmDiscernMainMapper.selectDsmDiscernMainList(dsmDiscernMain);
    }

    /**
     * 新增落标主
     *
     * @param dsmDiscernMain 落标主
     * @return 结果
     */
    @Override
    public int insertDsmDiscernMain(DsmDiscernMain dsmDiscernMain)
    {
        dsmDiscernMain.setCreateTime(DateUtils.getNowDate());
        return dsmDiscernMainMapper.insertDsmDiscernMain(dsmDiscernMain);
    }

    /**
     * 修改落标主
     *
     * @param dsmDiscernMain 落标主
     * @return 结果
     */
    @Override
    public int updateDsmDiscernMain(DsmDiscernMain dsmDiscernMain)
    {
        return dsmDiscernMainMapper.updateDsmDiscernMain(dsmDiscernMain);
    }

    /**
     * 批量删除落标主
     *
     * @param discernMainIds 需要删除的落标主ID
     * @return 结果
     */
    @Override
    public int deleteDsmDiscernMainByIds(Long[] discernMainIds)
    {
        return dsmDiscernMainMapper.deleteDsmDiscernMainByIds();
    }

    /**
     * 删除落标主信息
     *
     * @param discernMainId 落标主ID
     * @return 结果
     */
    @Override
    public int deleteDsmDiscernMainById(Long discernMainId)
    {
        return dsmDiscernMainMapper.deleteDsmDiscernMainById(discernMainId);
    }
}
