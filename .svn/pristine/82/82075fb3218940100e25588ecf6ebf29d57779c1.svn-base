package com.dqms.dqm.domain.vo;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * <AUTHOR>
 * @date 2021/7/22
 */
public class DqmValidationRuleCateVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 检查规则ID */
    private Long validationRuleCateId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String validationName;

    /** 分类ID */
    private Long validationClassId;
    
    @Excel(name = "分类名称")
    private String validationRuleName;

    /** 数据源ID */
    private Long datasourceId;
    @Excel(name = "数据源")
    private String datasourceName;

    /** 类型 */
    @Excel(name = "类型",dictType="dqm_validation_type",combo={"一致性","有效性","唯一性","完整性","合理性","准确性"})
    private String validationRuleCateType;

    /** 系统ID */
    private Long systemId;
    @Excel(name = "应用系统")
    private String systemName;

    /** 字段名称 */
    @Excel(name = "字段名称")
    private String fieldName;

    /** 表名称 */
    @Excel(name = "表名称")
    private String tableName;

    /** 检查sql */
    @Excel(name = "检查sql")
    private String problemSql;

    /** 总数sql */
    @Excel(name = "总数sql")
    private String totalSql;

    @Excel(name = "阈值")
    private String threshold;

    /** 阈值判断 */
    @Excel(name = "阈值判断",dictType="dqm_threshold",combo={"≥",">","=","<","≤","!="})
    private String thresholdJudge;


    /** 级别 */
    @Excel(name = "级别",dictType="dqm_level",combo={"重要","一般","不重要"})
    private String level;


    /** 状态 */
    @Excel(name = "状态",dictType="sys_normal_disable",combo={"正常","停用"})
    private String status;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** 关联任务 */
    private Long taskId;

    /** 阈值数据源 */
    private Long thresholdDatasourceId;

    public void setValidationRuleCateId(Long validationRuleCateId)
    {
        this.validationRuleCateId = validationRuleCateId;
    }

    public String getValidationName() {
        return validationName;
    }

    public void setValidationName(String validationName) {
        this.validationName = validationName;
    }

    public Long getValidationRuleCateId()
    {
        return validationRuleCateId;
    }
    public void setValidationClassId(Long validationClassId)
    {
        this.validationClassId = validationClassId;
    }

    public Long getValidationClassId()
    {
        return validationClassId;
    }
    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }

    public String getValidationRuleCateType() {
        return validationRuleCateType;
    }

    public void setValidationRuleCateType(String validationRuleCateType) {
        this.validationRuleCateType = validationRuleCateType;
    }

    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setFieldName(String fieldName)
    {
        this.fieldName = fieldName;
    }

    public String getFieldName()
    {
        return fieldName;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setProblemSql(String problemSql)
    {
        this.problemSql = problemSql;
    }

    public String getProblemSql()
    {
        return problemSql;
    }
    public void setTotalSql(String totalSql)
    {
        this.totalSql = totalSql;
    }

    public String getTotalSql()
    {
        return totalSql;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setTaskId(Long taskId)
    {
        this.taskId = taskId;
    }

    public Long getTaskId()
    {
        return taskId;
    }

    public String getValidationRuleName() {
        return validationRuleName;
    }

    public void setValidationRuleName(String validationRuleName) {
        this.validationRuleName = validationRuleName;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getThresholdJudge() {
		return thresholdJudge;
	}

	public void setThresholdJudge(String thresholdJudge) {
		this.thresholdJudge = thresholdJudge;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

    public Long getThresholdDatasourceId() {
        return thresholdDatasourceId;
    }

    public void setThresholdDatasourceId(Long thresholdDatasourceId) {
        this.thresholdDatasourceId = thresholdDatasourceId;
    }

}
