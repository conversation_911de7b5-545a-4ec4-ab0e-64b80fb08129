package com.dqms.dqm.mapper;

import java.util.List;
import com.dqms.dqm.domain.DqmValidationSubscription;
import org.apache.ibatis.annotations.Param;

/**
 * 规则任务订阅Mapper接口
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
public interface DqmValidationSubscriptionMapper
{
    /**
     * 查询规则任务订阅
     *
     * @param validationSubscriptionId 规则任务订阅ID
     * @return 规则任务订阅
     */
    public DqmValidationSubscription selectDqmValidationSubscriptionById(Long validationSubscriptionId);

    /**
     * 查询规则任务订阅列表
     *
     * @param dqmValidationSubscription 规则任务订阅
     * @return 规则任务订阅集合
     */
    public List<DqmValidationSubscription> selectDqmValidationSubscriptionList(DqmValidationSubscription dqmValidationSubscription);

    /**
     * 新增规则任务订阅
     *
     * @param dqmValidationSubscription 规则任务订阅
     * @return 结果
     */
    public int insertDqmValidationSubscription(DqmValidationSubscription dqmValidationSubscription);

    /**
     * 修改规则任务订阅
     *
     * @param dqmValidationSubscription 规则任务订阅
     * @return 结果
     */
    public int updateDqmValidationSubscription(DqmValidationSubscription dqmValidationSubscription);

    /**
     * 删除规则任务订阅
     *
     * @param validationSubscriptionId 规则任务订阅ID
     * @return 结果
     */
    public int deleteDqmValidationSubscriptionById(Long validationSubscriptionId);

    /**
     * 批量删除规则任务订阅
     *
     * @param validationSubscriptionIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDqmValidationSubscriptionByIds(Long[] validationSubscriptionIds);

    public int deleteByValidationRuleCateIdAndCreateId(DqmValidationSubscription dqmValidationSubscription);
}

