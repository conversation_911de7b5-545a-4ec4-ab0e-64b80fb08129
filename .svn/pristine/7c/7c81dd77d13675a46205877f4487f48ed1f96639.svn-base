package com.dqms.dic.domain;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.dsm.domain.DsmMasterDataInstall;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 手工数据配置对象 dic_manual_data_define
 *
 * <AUTHOR>
 * @date 2022-06-28
 */
public class DicManualDataDefine extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long manualDataId;

    /** 数据名称 */
    @Excel(name = "数据名称")
    private String manualDataName;

    /** 数据源 */
    @Excel(name = "数据源")
    private String datasourceName;
    private Long datasourceId;

    /** 目标表 */
    @Excel(name = "目标表")
    private String tableName;

    /** 前置SQL */
    @Excel(name = "前置SQL")
    private String preSql;

    /** 后置任务 */
    @Excel(name = "后置任务")
    private String posTaskName;
    private Long posTaskId;

    /** 是否审批 */
    @Excel(name = "是否审批")
    private String isApply;

    /** 读写分离 */
    @Excel(name = "读写分离")
    private String isSeparation;

    /** 权限控制 */
    @Excel(name = "权限控制")
    private String isAuth;

    /** 统计周期 */
    @Excel(name = "统计周期")
    private String cycle;

    /** 最大行 */
    @Excel(name = "最大行")
    private Long maxLine;

    /** 最小行 */
    @Excel(name = "最小行")
    private Long minLine;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    private Integer pageNum;
    
    private Integer pageSize;

    /** 附件路径 */
    private String attachment;
    
    private List<DicManualDataInstall> dicManualDataInstalls;
    
    private int applyCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date upTime; 
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date apTime;
    
    private String prop;
    private String order;
    private Long userId;

    public void setManualDataId(Long manualDataId)
    {
        this.manualDataId = manualDataId;
    }

    public Long getManualDataId()
    {
        return manualDataId;
    }
    public void setManualDataName(String manualDataName)
    {
        this.manualDataName = manualDataName;
    }

    public String getManualDataName()
    {
        return manualDataName;
    }
    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setPreSql(String preSql)
    {
        this.preSql = preSql;
    }

    public String getPreSql()
    {
        return preSql;
    }
    public void setPosTaskId(Long posTaskId)
    {
        this.posTaskId = posTaskId;
    }

    public Long getPosTaskId()
    {
        return posTaskId;
    }
    public void setIsApply(String isApply)
    {
        this.isApply = isApply;
    }

    public String getIsApply()
    {
        return isApply;
    }
    public void setIsSeparation(String isSeparation)
    {
        this.isSeparation = isSeparation;
    }

    public String getIsSeparation()
    {
        return isSeparation;
    }
    public void setIsAuth(String isAuth)
    {
        this.isAuth = isAuth;
    }

    public String getIsAuth()
    {
        return isAuth;
    }
    public void setCycle(String cycle)
    {
        this.cycle = cycle;
    }

    public String getCycle()
    {
        return cycle;
    }
    public void setMaxLine(Long maxLine)
    {
        this.maxLine = maxLine;
    }

    public Long getMaxLine()
    {
        return maxLine;
    }
    public void setMinLine(Long minLine)
    {
        this.minLine = minLine;
    }

    public Long getMinLine()
    {
        return minLine;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public String getDatasourceName() {
		return datasourceName;
	}

	public void setDatasourceName(String datasourceName) {
		this.datasourceName = datasourceName;
	}

	public String getPosTaskName() {
		return posTaskName;
	}

	public void setPosTaskName(String posTaskName) {
		this.posTaskName = posTaskName;
	}

	public List<DicManualDataInstall> getDicManualDataInstalls() {
		return dicManualDataInstalls;
	}

	public void setDicManualDataInstalls(List<DicManualDataInstall> dicManualDataInstalls) {
		this.dicManualDataInstalls = dicManualDataInstalls;
	}

	
	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getAttachment() {
		return attachment;
	}

	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	public int getApplyCount() {
		return applyCount;
	}

	public void setApplyCount(int applyCount) {
		this.applyCount = applyCount;
	}

	public Date getUpTime() {
		return upTime;
	}

	public void setUpTime(Date upTime) {
		this.upTime = upTime;
	}

	public Date getApTime() {
		return apTime;
	}

	public void setApTime(Date apTime) {
		this.apTime = apTime;
	}

	public String getProp() {
		return prop;
	}

	public void setProp(String prop) {
		this.prop = prop;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("manualDataId", getManualDataId())
            .append("manualDataName", getManualDataName())
            .append("datasourceId", getDatasourceId())
            .append("tableName", getTableName())
            .append("preSql", getPreSql())
            .append("posTaskId", getPosTaskId())
            .append("isApply", getIsApply())
            .append("isSeparation", getIsSeparation())
            .append("isAuth", getIsAuth())
            .append("cycle", getCycle())
            .append("maxLine", getMaxLine())
            .append("minLine", getMinLine())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
