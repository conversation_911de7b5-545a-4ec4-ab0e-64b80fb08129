<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmModelEntityTempMapper">
    
    <resultMap type="DsmModelEntityTemp" id="DsmModelEntityTempResult">
        <result property="modelEntityId"    column="model_entity_id"    />
        <result property="modelEntityClassId"    column="model_entity_class_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableComment"    column="table_comment"    />
        <result property="tableSchema"    column="table_schema"    />
        <result property="versionNo"    column="version_no"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sqlScript"    column="sql_script"    />
        <result property="sqlChange"    column="sql_change"    />
        <result property="batchId"    column="batch_id"    />
        <result property="modelEntityTempId"    column="model_entity_temp_id"    />
        <result property="partitionType"    column="partition_type"    />
        <result property="partitionColType"    column="partition_col_type"    />
        <result property="bucketCol"    column="bucket_col"    />
        <result property="bucketNum"    column="bucket_num"    />
        <result property="createScript"    column="create_script"    />        
        <association property="modelEntityClass"    column="model_entity_class_id" javaType="DsmModelEntityClass" resultMap="DsmModelEntityClassResult" />
        <association property="sysDatasource"    column="datasource_id" javaType="SysDatasource" resultMap="SysDatasourceResult" />
        <collection  property="dsmModelEntityProps"    ofType="dsmModelEntityProp"  column="model_entity_temp_id"     select="getDsmModelEntityProps"/>
        <collection  property="dsmModelEntityShips"    ofType="dsmModelEntityShip"  column="model_entity_temp_id"     select="getDsmModelEntityShips"/>        
    </resultMap>
    <resultMap type="DsmModelEntityClass" id="DsmModelEntityClassResult">
        <result property="modelEntityClassId"    column="model_entity_class_id"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
    </resultMap>
    <resultMap type="DsmModelEntityProp" id="DsmModelEntityPropResult">
    	<result property="modelEntityPropTempId"    column="model_entity_prop_temp_id"    />
        <result property="modelEntityPropId"    column="model_entity_prop_id"    />
        <result property="modelEntityId"    column="model_entity_id"    />
        <result property="propName"    column="prop_name"    />
        <result property="propComment"    column="prop_comment"    />
        <result property="isPriKey"    column="is_pri_key"    />
        <result property="nullable"    column="nullable"    />
        <result property="columnSize"    column="column_size"    />
        <result property="decimalDigits"    column="decimal_digits"    />
        <result property="dataType"    column="data_type"    />
        <result property="defaultValue"    column="default_value"    />
    </resultMap>
    <resultMap type="DsmModelEntityShip" id="DsmModelEntityShipResult">
        <result property="modelEntityShipId"    column="model_entity_ship_id"    />
        <result property="srcModelEntityId"    column="src_model_entity_id"    />
        <result property="srcModelEntityPropId"    column="src_model_entity_prop_id"    />
        <result property="srcModelEntityPropName"    column="src_model_entity_prop_name"    />
        <result property="tarModelEntityId"    column="tar_model_entity_id"    />
        <result property="tarModelEntityPropId"    column="tar_model_entity_prop_id"    />
        <result property="tarModelEntityPropName"    column="tar_model_entity_prop_name"    />
        <result property="name"    column="name"    />
    </resultMap>    
    <resultMap type="SysDatasource" id="SysDatasourceResult">
        <result property="datasourceId"    column="datasource_id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="dbType"    column="db_type"    />
        <result property="systemId"    column="system_id"    />
        <result property="dsType" column="ds_type" />
    </resultMap> 
    
    <select id="getDsmModelEntityProps" resultMap="DsmModelEntityPropResult" parameterType="java.lang.String">
        select model_entity_prop_id, model_entity_id, prop_name, prop_comment, is_pri_key, nullable, column_size, decimal_digits, data_type, default_value
        from dsm_model_entity_prop_temp
        where  model_entity_id =#{themeId}
    </select>
    <select id="getDsmModelEntityShips" resultMap="DsmModelEntityShipResult" parameterType="java.lang.String">
        select ps.prop_name as src_model_entity_prop_name,pt.prop_name as tar_model_entity_prop_name,s.model_entity_ship_id, s.src_model_entity_id, s.src_model_entity_prop_id, s.tar_model_entity_id, s.tar_model_entity_prop_id,  s.name
 		from dsm_model_entity_ship_temp s 
 		left join dsm_model_entity_prop ps on s.src_model_entity_prop_id=ps.model_entity_prop_id 
 		left join dsm_model_entity_prop pt on s.tar_model_entity_prop_id=pt.model_entity_prop_id 
 		where  s.src_model_entity_id =#{themeId}
    </select>  
        
    <sql id="selectDsmModelEntityTempVo">
        select t.model_entity_id, t.model_entity_class_id, t.table_name, t.table_comment, t.table_schema, t.version_no, t.datasource_id, t.status, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time,
         t.update_time, t.sql_script, t.sql_change, t.batch_id, t.model_entity_temp_id ,c.class_name, c.class_name_full,d.datasource_id, d.name, d.code, d.db_type,d.ds_type, d.system_id,
         t.partition_type,t.partition_col_type,t.bucket_col,t.bucket_num,t.create_script
         from dsm_model_entity_temp t
        left join dsm_model_entity_class c on t.model_entity_class_id=c.model_entity_class_id
        left join sys_datasource d on t.datasource_id=d.datasource_id
    </sql>

    <select id="selectDsmModelEntityTempList" parameterType="DsmModelEntityTemp" resultMap="DsmModelEntityTempResult">
        <include refid="selectDsmModelEntityTempVo"/>
        <where>  
            <if test="modelEntityId != null "> and model_entity_id = #{modelEntityId}</if>
            <if test="modelEntityClassId != null "> and model_entity_class_id = #{modelEntityClassId}</if>
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="tableComment != null  and tableComment != ''"> and table_comment = #{tableComment}</if>
            <if test="tableSchema != null  and tableSchema != ''"> and table_schema = #{tableSchema}</if>
            <if test="versionNo != null  and versionNo != ''"> and version_no = #{versionNo}</if>
            <if test="datasourceId != null "> and datasource_id = #{datasourceId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            <if test="sqlScript != null  and sqlScript != ''"> and sql_script = #{sqlScript}</if>
            <if test="sqlChange != null  and sqlChange != ''"> and sql_change = #{sqlChange}</if>
            <if test="batchId != null  and batchId != ''"> and batch_id = #{batchId}</if>
        </where>
    </select>
    
    <select id="selectDsmModelEntityTempById" parameterType="Long" resultMap="DsmModelEntityTempResult">
        <include refid="selectDsmModelEntityTempVo"/>
        where model_entity_temp_id = #{modelEntityTempId}
    </select>
        
    <insert id="insertDsmModelEntityTemp" parameterType="DsmModelEntityTemp" useGeneratedKeys="true" keyProperty="modelEntityTempId">
        insert into dsm_model_entity_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelEntityId != null">model_entity_id,</if>
            <if test="modelEntityClassId != null">model_entity_class_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="tableComment != null">table_comment,</if>
            <if test="tableSchema != null">table_schema,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="datasourceId != null">datasource_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sqlScript != null">sql_script,</if>
            <if test="sqlChange != null">sql_change,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="partitionType != null">partition_type,</if>
            <if test="partitionColType != null">partition_col_type,</if>
            <if test="bucketCol != null">bucket_col,</if>
            <if test="bucketNum != null">bucket_num,</if>
            <if test="createScript != null">create_script,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelEntityId != null">#{modelEntityId},</if>
            <if test="modelEntityClassId != null">#{modelEntityClassId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="tableComment != null">#{tableComment},</if>
            <if test="tableSchema != null">#{tableSchema},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sqlScript != null">#{sqlScript},</if>
            <if test="sqlChange != null">#{sqlChange},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="partitionType != null">#{partitionType},</if>
            <if test="partitionColType != null">#{partitionColType},</if>
            <if test="bucketCol != null">#{bucketCol},</if>
            <if test="bucketNum != null">#{bucketNum},</if>
            <if test="createScript != null">#{createScript},</if>
         </trim>
    </insert>

    <update id="updateDsmModelEntityTemp" parameterType="DsmModelEntityTemp">
        update dsm_model_entity_temp
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelEntityId != null">model_entity_id = #{modelEntityId},</if>
            <if test="modelEntityClassId != null">model_entity_class_id = #{modelEntityClassId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="tableComment != null">table_comment = #{tableComment},</if>
            <if test="tableSchema != null">table_schema = #{tableSchema},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sqlScript != null">sql_script = #{sqlScript},</if>
            <if test="sqlChange != null">sql_change = #{sqlChange},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="partitionType != null">partition_type = #{partitionType},</if>
            <if test="partitionColType != null">partition_col_type = #{partitionColType},</if>
            <if test="bucketCol != null">bucket_col = #{bucketCol},</if>
            <if test="bucketNum != null">bucket_num = #{bucketNum},</if>
            <if test="createScript != null">create_script = #{createScript},</if>
        </trim>
        where model_entity_temp_id = #{modelEntityTempId}
    </update>

    <delete id="deleteDsmModelEntityTempById" parameterType="Long">
        delete from dsm_model_entity_temp where model_entity_temp_id = #{modelEntityTempId}
    </delete>

    <delete id="deleteDsmModelEntityTempByIds" parameterType="String">
        delete from dsm_model_entity_temp where model_entity_temp_id in 
        <foreach item="modelEntityTempId" collection="array" open="(" separator="," close=")">
            #{modelEntityTempId}
        </foreach>
    </delete>
</mapper>