import request from '@/utils/request'

// 查询检核规则列表
export function listDqmValidationRuleCate(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/list',
    method: 'get',
    params: query
  })
}

//查询检核规则列表
export function listDqmValidationRuleCateByUser(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/listByUser',
    method: 'get',
    params: query
  })
}

// 查询检核规则详细
export function getDqmValidationRuleCate(validationRuleCateId) {
  return request({
    url: '/dqm/dqmValidationRuleCate/' + validationRuleCateId,
    method: 'get'
  })
}

// 新增检核规则
export function addDqmValidationRuleCate(data) {
  return request({
    url: '/dqm/dqmValidationRuleCate',
    method: 'post',
    data: data
  })
}

// 修改检核规则
export function updateDqmValidationRuleCate(data) {
  return request({
    url: '/dqm/dqmValidationRuleCate',
    method: 'put',
    data: data
  })
}

// 删除检核规则
export function delDqmValidationRuleCate(validationRuleCateId) {
  return request({
    url: '/dqm/dqmValidationRuleCate/' + validationRuleCateId,
    method: 'delete'
  })
}

// 导出检核规则
export function exportDqmValidationRuleCate(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/export',
    method: 'get',
    params: query
  })
}

// 执行
export function executorDqmRuleCate(data) {
  return request({
    url: "/dqm/dqmValidationRuleCate/executorDqmRuleCate",
    method: "post",
    data: data
  });
}

// 订阅
export function getSubscription(validationRuleCateIds) {
  return request({
    url: "/dqm/dqmValidationRuleCate/addSubscription/" + validationRuleCateIds,
    method: "delete"
  });
}
// 订阅
export function subscriptionChange(validationRuleCateId, type) {
  const data = {
		  validationRuleCateId,
		  type
  }
  return request({
    url: "/dqm/dqmValidationRuleCate/subscriptionChange",
    method: 'put',
    data: data
  });
}
// 获取模板
export function getMould() {
  return request({
    url: "/dqm/dqmValidationRuleCate/getMould",
    method: "get"
  });
}
// 获取表
export function findEntityAndSystem(params) {
  return request({
    url: "/mdm/mdmDataEntityShip/findEntityAndSystem",
    method: "get",
    params
  });
}
// 获取字段
export function findAllPropAndEntityAndSystem(params) {
  return request({
    url: "/mdm/mdmDataEntityShip/findAllPropAndEntityAndSystem",
    method: "get",
    params
  });
}

// 获取条件
export function getConditionById(validationMouldId) {
  return request({
    url: "/dqm/dqmValidationRuleCate/getConditionById/" + validationMouldId.validationMouldId,
    method: "get"
    // params: validationMouldId
  });
}

export function dqmlist(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/dqmlist',
    method: 'get',
    params: query
  })
}
export function importTemplate(query){
  return request({
    url: '/dqm/dqmValidationRuleCate/exportDemo',
    method: 'get',
    params: query
  })
}
export function unDqmlist(query) {
  return request({
    url: '/dqm/dqmValidationRuleCate/unDqmlist',
    method: 'get',
    params: query
  })
}
