package com.dqms.api.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.dqms.api.domain.ApiDefine;
import com.dqms.api.domain.ApiDefineParam;
import com.dqms.api.enums.ApiConstants;
import com.dqms.api.service.IApiDefineService;
import com.dqms.common.annotation.Log;
import com.dqms.common.config.DqmsConfig;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.framework.web.service.TokenService;
import com.dqms.system.service.ISysConfigService;

/**
 * 接口管理Controller
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@RestController
@RequestMapping("/api/apiDefine")
public class ApiDefineController extends BaseController
{
    @Autowired
    private IApiDefineService apiDefineService;
    
    @Autowired
    private ISysConfigService sysConfigService;
    
    @Autowired
    private TokenService tokenService;


    /**
     * 查询接口管理列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApiDefine apiDefine)
    {
        startPage();
        List<ApiDefine> list = apiDefineService.selectApiDefineList(apiDefine);
        return getDataTable(list);
    }
    @PreAuthorize("@ss.hasPermi('api:apiDefine:list')")
    @GetMapping("/listHis")
    public TableDataInfo listHis(ApiDefine apiDefine)
    {
        startPage();
        List<ApiDefine> list = apiDefineService.selectApiDefineHisList(apiDefine);
        return getDataTable(list);
    }
    /**
     * 导出接口管理列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:export')")
    @Log(title = "接口管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ApiDefine apiDefine)
    {
        List<ApiDefine> list = apiDefineService.selectApiDefineList(apiDefine);
        ExcelUtil<ApiDefine> util = new ExcelUtil<ApiDefine>(ApiDefine.class);
        return util.exportExcel(list, "apiDefine");
    }

    /**
     * 获取接口管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:query')")
    @GetMapping(value = "/{defineId}")
    public AjaxResult getInfo(@PathVariable("defineId") Long defineId)
    {
        return AjaxResult.success(apiDefineService.selectApiDefineById(defineId));
    }

    /**
     * 新增接口管理
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:add')")
    @Log(title = "接口管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApiDefine apiDefine)
    {
    	apiDefine.setDefineNo(UUID.randomUUID().toString());
    	apiDefine.setVersion(1L);
        return toAjax(apiDefineService.insertApiDefine(apiDefine));
    }

    /**
     * 修改接口管理
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:edit')")
    @Log(title = "接口管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApiDefine apiDefine)
    {
        return toAjax(apiDefineService.updateApiDefine(apiDefine));
    }

    /**
     * 删除接口管理
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:remove')")
    @Log(title = "接口管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{defineIds}")
    public AjaxResult remove(@PathVariable Long[] defineIds)
    {
        return toAjax(apiDefineService.deleteApiDefineByIds(defineIds));
    }
    
    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:edit')")
    @Log(title = "接口管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody ApiDefine apiDefine)
    {
        return toAjax(apiDefineService.updateStatus(apiDefine));
    }
    
    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:edit')")
    @Log(title = "接口管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateApiDefineSystem")
    public AjaxResult updateApiDefineSystem(@RequestBody ApiDefine apiDefine)
    {
        return toAjax(apiDefineService.updateApiDefineSystem(apiDefine));
    }
    /**
     * 参数修改
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:edit')")
    @Log(title = "接口管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateApiDefineParam")
    public AjaxResult updateApiDefineParam(@RequestBody ApiDefine apiDefine)
    {
        return toAjax(apiDefineService.updateApiDefineParam(apiDefine));
    }
    /**
     * 修改接口输出
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefine:edit')")
    @Log(title = "接口管理", businessType = BusinessType.UPDATE)
    @PutMapping("/getData")
    public AjaxResult getData(@RequestBody ApiDefine apiDefine)
    {
    	Map<String,Object> map =new HashMap<>();
    	ApiDefine api = apiDefineService.selectApiDefineById(apiDefine.getDefineId());
	    	HttpClient httpClient = new HttpClient();
	        PostMethod postMethod = new PostMethod(DqmsConfig.getAddress()+"/apiTest");
	        postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"utf-8");
	        postMethod.addRequestHeader("accept", "*/*");
	        postMethod.addRequestHeader("connection", "Keep-Alive");
	        postMethod.addRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
	        String token = tokenService.getToken(ServletUtils.getRequest());
	        postMethod.addRequestHeader("Authorization", token);
	        postMethod.addParameter("api", apiDefine.getDefineCode());
	        
			postMethod.addParameter("system", "DQMS");
			postMethod.addParameter("token", "123456");
			postMethod.addParameter("page", "0");
			if(api.getDefineType().equals(ApiConstants.TASK_KAFKA)) {
				postMethod.addParameter("topic", api.getDefineSql());
			}
	        
	        Map<String,Object> params = new HashMap<String,Object>();
	        if(apiDefine.getApiDefineParams()!=null&&apiDefine.getApiDefineParams().size()>0) {
	        	for(ApiDefineParam param : apiDefine.getApiDefineParams()) {
	        		if(param.getDefualtValue()==null||param.getDefualtValue().equals("")) {
	        			continue;
	        		}
	        		if(param.getDefineParamType().equals(ApiConstants.INT)) {
	        			params.put(param.getDefineParamName(), Integer.parseInt(param.getDefualtValue()));
	        		}else if(param.getDefineParamType().equals(ApiConstants.DOUBLE)) {
	        			params.put(param.getDefineParamName(), Double.valueOf(param.getDefualtValue()));
	        		}else if(param.getDefineParamType().equals(ApiConstants.LONG)) {
	        			params.put(param.getDefineParamName(), Long.parseLong(param.getDefualtValue()));
	        		}else {
	        			params.put(param.getDefineParamName(), param.getDefualtValue());
	        		}
	        		
	        	}
	        	postMethod.addParameter("param",JSON.toJSONString(params));
	        }
	        
	        String res = "";
	        try {
	            int code = httpClient.executeMethod(postMethod);
	            if (code == 200){
	                res = postMethod.getResponseBodyAsString();
	                System.out.println(res);
	            }else {
	            	res = postMethod.getResponseBodyAsString();
	                System.out.println(res);
	            }
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    	if(api.getDefineType().equals(ApiConstants.TASK_JDBC)) {
	    		map.put("script","根据返回信息，通过系统或客户端直接调用即可");
	    	}else if(api.getDefineType().equals(ApiConstants.TASK_TCP)) {
	    		map.put("script","根据返回信息，通过系统或客户端直接调用即可");
	    	}else if(api.getDefineType().equals(ApiConstants.TASK_KAFKA)) {
	    		map.put("script", "HttpClient httpClient = new HttpClient();\r\n" + 
		    			"        PostMethod postMethod = new PostMethod(\""+DqmsConfig.getAddress()+"/api\");\r\n" + 
		    			"        postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,\"utf-8\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"accept\", \"*/*\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"connection\", \"Keep-Alive\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"Content-Type\", \"application/x-www-form-urlencoded;charset=utf-8\");\r\n" + 
		    			"        postMethod.addParameter(\"api\", \""+apiDefine.getDefineCode()+"\");\r\n" + 
		    			"        postMethod.addParameter(\"system\", \"请填写系统编号\");\r\n" + 
		    			"        postMethod.addParameter(\"token\", \"请填写系统令牌\");\r\n" + 
		    			"        postMethod.addParameter(\"pageSize\", \"数据行数：默认100\");\r\n" +     			
		    			"        postMethod.addParameter(\"topic\",\""+api.getDefineSql()+"\");\r\n" + 
		    			"        \r\n" + 
		    			"        String res = \"\";\r\n" + 
		    			"        try {\r\n" + 
		    			"            int code = httpClient.executeMethod(postMethod);\r\n" + 
		    			"            if (code == 200){\r\n" + 
		    			"                res = postMethod.getResponseBodyAsString();\r\n" + 
		    			"                System.out.println(res);\r\n" + 
		    			"            }\r\n" + 
		    			"        } catch (IOException e) {\r\n" + 
		    			"            e.printStackTrace();\r\n" + 
		    			"        }");
	    	}else  if(api.getDefineType().equals(ApiConstants.TASK_TASK)) {
		    	map.put("script", "HttpClient httpClient = new HttpClient();\r\n" + 
		    			"        PostMethod postMethod = new PostMethod(\""+DqmsConfig.getAddress()+"/api\");\r\n" + 
		    			"        postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,\"utf-8\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"accept\", \"*/*\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"connection\", \"Keep-Alive\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"Content-Type\", \"application/x-www-form-urlencoded;charset=utf-8\");\r\n" + 
		    			"        postMethod.addParameter(\"api\", \""+apiDefine.getDefineCode()+"\");\r\n" + 
		    			"        postMethod.addParameter(\"system\", \"请填写系统编号\");\r\n" + 
		    			"        postMethod.addParameter(\"token\", \"请填写系统令牌\");\r\n" + 
		    			"        postMethod.addParameter(\"param\",\""+JSON.toJSONString(params)+"\");\r\n" + 
		    			"        \r\n" + 
		    			"        String res = \"\";\r\n" + 
		    			"        try {\r\n" + 
		    			"            int code = httpClient.executeMethod(postMethod);\r\n" + 
		    			"            if (code == 200){\r\n" + 
		    			"                res = postMethod.getResponseBodyAsString();\r\n" + 
		    			"                System.out.println(res);\r\n" + 
		    			"            }\r\n" + 
		    			"        } catch (IOException e) {\r\n" + 
		    			"            e.printStackTrace();\r\n" + 
		    			"        }");
	    	}else {
		    	map.put("script", "HttpClient httpClient = new HttpClient();\r\n" + 
		    			"        PostMethod postMethod = new PostMethod(\""+DqmsConfig.getAddress()+"/api\");\r\n" + 
		    			"        postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,\"utf-8\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"accept\", \"*/*\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"connection\", \"Keep-Alive\");\r\n" + 
		    			"        postMethod.addRequestHeader(\"Content-Type\", \"application/x-www-form-urlencoded;charset=utf-8\");\r\n" + 
		    			"        postMethod.addParameter(\"api\", \""+apiDefine.getDefineCode()+"\");\r\n" + 
		    			"        postMethod.addParameter(\"system\", \"请填写系统编号\");\r\n" + 
		    			"        postMethod.addParameter(\"token\", \"请填写系统令牌\");\r\n" + 
		    			"        postMethod.addParameter(\"page\", \"数据页码：默认0\");\r\n" + 
		    			"        postMethod.addParameter(\"pageSize\", \"数据行数：默认100\");\r\n" +     			
		    			"        postMethod.addParameter(\"param\","+JSON.toJSONString(params)+");\r\n" + 
		    			"        \r\n" + 
		    			"        String res = \"\";\r\n" + 
		    			"        try {\r\n" + 
		    			"            int code = httpClient.executeMethod(postMethod);\r\n" + 
		    			"            if (code == 200){\r\n" + 
		    			"                res = postMethod.getResponseBodyAsString();\r\n" + 
		    			"                System.out.println(res);\r\n" + 
		    			"            }\r\n" + 
		    			"        } catch (IOException e) {\r\n" + 
		    			"            e.printStackTrace();\r\n" + 
		    			"        }");
	    	}
	    	map.put("data", res);
        return AjaxResult.success(map);
    }
    public static void main(String[] args) {
    	HttpClient httpClient = new HttpClient();
        PostMethod postMethod = new PostMethod("http://localhost:8080/api");
        postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"utf-8");
        postMethod.addRequestHeader("accept", "*/*");
        postMethod.addRequestHeader("connection", "Keep-Alive");
        postMethod.addRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        postMethod.addParameter("api", "TASK");
        postMethod.addParameter("system", "DQMS");
        postMethod.addParameter("token", "123456");
        postMethod.addParameter("page", "0");
        Map<String,Object> params = new HashMap<String,Object>();
        params.put("params", "客户号");
        postMethod.addParameter("param", JSON.toJSONString(params));
        String res = "";
        try {
            int code = httpClient.executeMethod(postMethod);
            if (code == 200){
                res = postMethod.getResponseBodyAsString();
                System.out.println(res);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    	
    	Map<String,Object> map =new HashMap<>();
    	map.put("script", "ajshdashas ajdlajsahdjashj");
    	map.put("data", res);
    	System.out.println(map);
	}
}
