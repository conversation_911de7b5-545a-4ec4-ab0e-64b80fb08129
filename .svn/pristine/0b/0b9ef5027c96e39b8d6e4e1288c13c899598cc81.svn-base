<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="关联任务" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入关联任务"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="调度名称" prop="scheduleName">
        <el-input
          v-model="queryParams.scheduleName"
          placeholder="请输入调度名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" filterable clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行方式" prop="execType">
        <el-select v-model="queryParams.execType" placeholder="请选择执行方式" filterable clearable size="small">
          <el-option
            v-for="dict in execTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['task:taskSchedule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single || only"
          @click="handleUpdate"
          v-hasPermi="['task:taskSchedule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple || only"
          @click="handleDelete"
          v-hasPermi="['task:taskSchedule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['task:taskSchedule:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
         <el-button
           type="info"
           plain
           icon="el-icon-upload2"
           size="mini"
           @click="handleImport"
           v-hasPermi="['task:taskSchedule:import']"
           >导入</el-button>
       </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>



    <el-table v-loading="loading" :data="taskScheduleList" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="关联任务" align="center" prop="etlTask.taskName"  width="200"/>
      <el-table-column label="调度名称" align="center" prop="scheduleName" width="200" />
      <el-table-column label="表达式" align="center" prop="expression"  width="200"/>
      <el-table-column label="下次计划时间" align="center" prop="nextValidTime"  width="200"/>
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat"  width="80"/>
      <el-table-column label="运行状态" align="center" prop="runStatus" :formatter="runStatusFormat"  width="80"/>
      <el-table-column label="后置任务链" align="center" prop="isLine" :formatter="isLineFormat"  width="90"/>
      <el-table-column label="后置任务组" align="center" prop="isGroup" :formatter="isGroupFormat"  width="90"/>
      <el-table-column label="是否并发" align="center" prop="isConcurrent" :formatter="isConcurrentFormat"  width="80"/>
      <el-table-column label="执行方式" align="center" prop="execType" :formatter="execTypeFormat"  width="220"/>
      <el-table-column label="翻牌日历" align="center" prop="taskCalendarClassName"  width="200"/>
      <el-table-column label="修改人" align="center" prop="updateBy"  width="120"/>
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['task:taskSchedule:edit']"
            v-if="scope.row.only=='Y'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['task:taskSchedule:remove']"
            v-if="scope.row.only=='Y'"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-caret-right"
            @click="handleRun(scope.row)"
            v-hasPermi="['task:taskSchedule:edit']"
            v-if="scope.row.only=='Y'"
          >执行</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改任务调度计划对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="950px" :before-close="handleClose" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="125px">
	      <el-row type="flex" justify="start" align="top"  >
	      	<el-col :span="12">
		        <el-form-item label="关联任务" prop="taskId">
		            <el-select
					    v-model="form.taskId"
					    filterable
					    remote
					    reserve-keyword
					    placeholder="请输入任务名称"
					    :remote-method="remoteMethod"
					    :loading="loading"  style="width:100%">
					    <el-option
					      v-for="item in taskOptions"
					      :key="item.taskId"
					      :label="item.taskName"
					      :value="item.taskId">
					    </el-option>
					  </el-select>
		        </el-form-item>
	        </el-col>
		    <el-col :span="12">
		        <el-form-item label="调度名称" prop="scheduleName">
		          <el-input v-model="form.scheduleName" placeholder="请输入调度名称" clearable/>
		        </el-form-item>
	        </el-col>
	     </el-row>
	     <el-row type="flex" justify="start" align="top"  >
	      	<el-col :span="24">
		        <el-form-item label="表达式" prop="expression">
		          <el-input v-model="form.expression" placeholder="请输入表达式" clearable/>
		        </el-form-item>
	        </el-col>
<!-- 	        <el-col :span="12">
		        <el-form-item label="前置调度" prop="preScheduleId">
		        	<el-select
					    v-model="form.preScheduleId"
					    filterable
					    remote
					    reserve-keyword
					    placeholder="请输入任务名称"
					    :remote-method="remotePreScheduleMethod"
					    :loading="loading"  style="width:100%">
					    <el-option
					      v-for="item in preScheduleOptions"
					      :key="item.scheduleId"
					      :label="item.scheduleName"
					      :value="item.scheduleId">
					    </el-option>
					  </el-select>
		        </el-form-item>
		    </el-col> -->
	     </el-row>
	     <el-row type="flex" justify="start" align="top"  >
	      	<el-col :span="12">
		        <el-form-item label="后置任务链">
		          <el-radio-group v-model="form.isLine">
		            <el-radio
		              v-for="dict in isLineOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue"
		            >{{dict.dictLabel}}</el-radio>
		          </el-radio-group>
		        </el-form-item>
	        </el-col>
		    <el-col :span="12">
		        <el-form-item label="后置任务组">
		          <el-radio-group v-model="form.isGroup">
		            <el-radio
		              v-for="dict in isGroupOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue"
		            >{{dict.dictLabel}}</el-radio>
		          </el-radio-group>
		        </el-form-item>
		      </el-col>
	     </el-row>
	     <el-row type="flex" justify="start" align="top"  >
	      	<el-col :span="12">
		        <el-form-item label="是否并发">
		          <el-radio-group v-model="form.isConcurrent">
		            <el-radio
		              v-for="dict in isConcurrentOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue">
		              {{dict.dictLabel}}
		            </el-radio>
		          </el-radio-group>
		        </el-form-item>
        	</el-col>
		    <el-col :span="12">
		        <el-form-item label="状态">
		          <el-radio-group v-model="form.status">
		            <el-radio
		              v-for="dict in statusOptions"
		              :key="dict.dictValue"
		              :label="dict.dictValue"
		            >{{dict.dictLabel}}</el-radio>
		          </el-radio-group>
		        </el-form-item>
		      </el-col>
	     </el-row>
	     <el-row type="flex" justify="start" align="top"  >
			<el-col :span="12">
		       <el-form-item label="执行方式" prop="execType" >
		          <el-select
                    v-model="form.execType"
                    placeholder="请选择执行方式 "
                    filterable
                    clearable
                    style="width:100%"
                    @change="execTypeOptionsChange"
              >
		            <el-option
		              v-for="dict in execTypeOptions"
		              :key="dict.dictValue"
		              :label="dict.dictLabel"
		              :value="dict.dictValue"
		            ></el-option>
		          </el-select>
		        </el-form-item>
		    </el-col>
		    <el-col :span="12">
		        <el-form-item label="翻牌日历" prop="taskCalendarClassId">
		          <treeselect v-model="form.taskCalendarClassId" :options="taskCalendarClassIdOptions" :disable-branch-nodes="true" :show-count="true"  placeholder="请选择翻牌日历" />
		        </el-form-item>
			</el-col>
	     </el-row>
	     <el-row type="flex" justify="start" align="top"  >
			<el-col :span="12">
		       <el-form-item label="交易日校验方式" prop="dateCheckType" >
		          <el-select
                    v-model="form.dateCheckType"
                    placeholder="请选择日期校验方式 "
                    :disabled="dateCheckTypeDis"
                    filterable
                    clearable
                    style="width:100%"
              >
		            <el-option
		              v-for="dict in dateCheckTypeOptions"
		              :key="dict.dictValue"
		              :label="dict.dictLabel"
		              :value="dict.dictValue"
		            ></el-option>
		          </el-select>
		        </el-form-item>
		    </el-col>
		    <el-col :span="12">
		        <el-form-item label="第几交易日" prop="dateCheckNum">
		          <el-input-number v-model="form.dateCheckNum" :min="0" :max="366" label="交易日天数" style="width:100%"></el-input-number>
		        </el-form-item>
			</el-col>
	     </el-row>
	     <el-row type="flex" justify="start" align="top"  >
	      	<el-col :span="24">
		        <el-form-item label="调度说明" prop="remark">
		          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
		        </el-form-item>
        	</el-col>
	     </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 主题导入对话框 -->
        <el-dialog
          :title="upload.title"
          :visible.sync="upload.open"
          width="400px"
          append-to-body
        >
          <el-upload
            ref="upload"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url + '?updateSupport=' + upload.updateSupport"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              <el-checkbox
                v-model="upload.updateSupport"
              />是否更新已经存在的任务调度关系,以<span style="font-weight: bold"
                >名称</span
              >为主键更新
              <el-link type="info" style="font-size:12px" @click="importTemplate"
                >下载模板</el-link
              >
            </div>
            <div class="el-upload__tip" style="color:#ff0000" slot="tip">
              提示：仅允许导入“xls”或“xlsx”格式文件！
            </div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </el-dialog>

  </div>
</template>

<script>
import { listTaskSchedule,listTaskScheduleByPage, getTaskSchedule, delTaskSchedule, addTaskSchedule, updateTaskSchedule, exportTaskSchedule,runJob,exportdemo } from "@/api/task/taskSchedule";
import { listTask} from "@/api/task/task";
import { treeselect } from "@/api/task/taskCalendarClass";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "TaskSchedule",
  components: {
	  Treeselect
  },
  data() {
    return {
      dateCheckTypeDis:false,
       upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/task/taskSchedule/importEtlTaskSchedule"
        },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,

      only:true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务调度计划表格数据
      taskScheduleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态字典
      statusOptions: [],
      // 运行状态字典
      runStatusOptions: [],
      // 后置任务链字典
      isLineOptions: [],
      // 后置任务组字典
      isGroupOptions: [],
      // 是否并发字典
      isConcurrentOptions: [],
      // 是否临时字典
      isTempOptions: [],
      // 执行方式字典
      execTypeOptions: [],
      // 关联任务
      taskOptions: [],
      //前置调度
      preScheduleOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      dateCheckTypeOptions: [],
      taskCalendarClassIdOptions: [],
      defaultProps: {
          children: "children",
          label: "label"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        scheduleName: null,
        expression: null,
        status: null,
        runStatus: null,
        isLine: null,
        isGroup: null,
        isConcurrent: null,
        isTemp: null,
        execType: null,
        preScheduleId: null,
        taskCalendarClassId: null,
      },

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskId: [
          { required: true, message: "关联任务不能为空", trigger: "blur" }
        ],
        scheduleName: [
          { required: true, message: "调度名称不能为空", trigger: "blur" }
        ],
        expression: [
          { required: true, message: "表达式不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        runStatus: [
          { required: true, message: "运行状态不能为空", trigger: "blur" }
        ],
        isLine: [
          { required: true, message: "后置任务链不能为空", trigger: "blur" }
        ],
        isGroup: [
          { required: true, message: "后置任务组不能为空", trigger: "blur" }
        ],
        isConcurrent: [
          { required: true, message: "是否并发不能为空", trigger: "blur" }
        ],
        isTemp: [
          { required: true, message: "是否临时不能为空", trigger: "blur" }
        ],
        execType: [

        ],
        dateCheckType: [
          { required: true, message: "交易日校验方式不能为空", trigger: "blur" }
        ]
      },
    };
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getDicts("sys_job_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("etl_task_run_status").then(response => {
      this.runStatusOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isLineOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isGroupOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isConcurrentOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isTempOptions = response.data;
    });
    this.getDicts("etl_task_schedule_exec_type").then(response => {
      this.execTypeOptions = response.data;
    });
    this.getDicts("etl_task_schedule_date_check").then(response => {
      this.dateCheckTypeOptions = response.data;
    });
  },
  methods: {
    // 请选择执行方式改变的时候
    execTypeOptionsChange(val){
      var canSelect = [
                        'FJYR-QZRR',
                        'FJYR-QJYR',
                        'FJYR-DQR'
                      ]
      if(canSelect.includes(val)){
        this.form.dateCheckType = 'N'
        this.dateCheckTypeDis = true
      }else{
        this.dateCheckTypeDis = false
      }
    },
    /** 查询任务调度计划列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listTaskScheduleByPage(this.queryParams).then(response => {
        this.taskScheduleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getTreeselect() {
        treeselect().then(response => {
          this.taskCalendarClassIdOptions = response.data;
        });
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 运行状态字典翻译
    runStatusFormat(row, column) {
      return this.selectDictLabel(this.runStatusOptions, row.runStatus);
    },
    // 后置任务链字典翻译
    isLineFormat(row, column) {
      return this.selectDictLabel(this.isLineOptions, row.isLine);
    },
    // 后置任务组字典翻译
    isGroupFormat(row, column) {
      return this.selectDictLabel(this.isGroupOptions, row.isGroup);
    },
    // 是否并发字典翻译
    isConcurrentFormat(row, column) {
      return this.selectDictLabels(this.isConcurrentOptions, row.isConcurrent);
    },
    // 是否临时字典翻译
    isTempFormat(row, column) {
      return this.selectDictLabel(this.isTempOptions, row.isTemp);
    },
    // 执行方式字典翻译
    execTypeFormat(row, column) {
      return this.selectDictLabel(this.execTypeOptions, row.execType);
    },
    // 执行方式字典翻译
    dateCheckTypeFormat(row, column) {
      return this.selectDictLabel(this.dateCheckTypeOptions, row.dateCheckType);
    },
    handleClose(done) {
      this.dateCheckTypeDis = false;
      done();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        taskScheduleId: null,
        taskId: null,
        scheduleName: null,
        expression: null,
        status: "0",
        runStatus: "0",
        isLine: "Y",
        isGroup: "Y",
        isConcurrent: "N",
        isTemp: "N",
        execType: null,
        preScheduleId: null,
        taskCalendarClassId: null,
        year: null,
        mon: null,
        remark: null,
        day: null,
        min: null,
        sec: null,
        week: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null,
        dateCheckType:"N",
        dateCheckNum:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.taskScheduleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
      this.only=false;
      const that=this;
      selection.map(item => {if(item.only=="N"){that.only=true;}})
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务调度计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const taskScheduleId = row.taskScheduleId || this.ids[0]
      var taskName = this.taskScheduleList.filter(el=>el.taskScheduleId==taskScheduleId)
      getTaskSchedule(taskScheduleId).then(response => {
        this.taskOptions =[{
          taskName: taskName[0].etlTask.taskName,
          taskId: response.data.taskId
        }]
        this.form = response.data;
        this.preScheduleOptions.push(response.data.etlTask);
        this.open = true;
        this.title = "修改任务调度计划";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.taskScheduleId != null) {
            updateTaskSchedule(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTaskSchedule(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taskScheduleIds = row.taskScheduleId || this.ids;
      this.$confirm('是否确认删除任务调度计划编号为"' + taskScheduleIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delTaskSchedule(taskScheduleIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有任务调度计划数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportTaskSchedule(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
     /** 下载模板操作 */
        importTemplate() {
          exportdemo().then(response => {
            this.download(response.msg);
          });
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
          this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
          this.upload.open = false;
          this.upload.isUploading = false;
          this.$refs.upload.clearFiles();
          this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
          this.getList();
        },
        // 提交上传文件
        submitFileForm() {
          this.$refs.upload.submit();
        },
        /** 导入按钮操作 */
        handleImport() {
          this.upload.title = "任务调度计划导入";
          this.upload.open = true;
        },
    // 模糊搜索
    remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
            let taskParams = {taskName:query}
            listTask(taskParams).then(response => {
	          this.taskOptions = response.rows;
	        });
          }, 200);
        } else {
          this.taskOptions = [];
        }
    },
    remotePreScheduleMethod(query) {
        if (query !== '') {
            this.loading = true;
            setTimeout(() => {
              this.loading = false;
              let taskScheduleParams = {scheduleName:query}
              listTaskScheduleByPage(taskScheduleParams).then(response => {
  	          this.preScheduleOptions = response.rows;
  	        });
            }, 200);
          } else {
            this.preScheduleOptions = [];
          }
    },
    /* 立即执行一次 */
    handleRun(row) {
      this.$confirm('确认要立即执行一次"' + row.scheduleName + '"任务吗?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return runJob(row.jobId);
        }).then(() => {
          this.msgSuccess("执行成功");
        })
    }
  }
};
</script>
