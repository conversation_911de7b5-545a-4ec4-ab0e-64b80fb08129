import request from '@/utils/request'
import * as echarts from 'echarts';

// 查询标准对标列表
export function listDsmCheck(query) {
  return request({
    url: '/dsm/dsmCheck/list',
    method: 'get',
    params: query
  })
}

// 查询标准对标详细
export function getDsmCheck(checkId) {
  return request({
    url: '/dsm/dsmCheck/' + checkId,
    method: 'get'
  })
}

// 新增标准对标
export function addDsmCheck(data) {
  return request({
    url: '/dsm/dsmCheck',
    method: 'post',
    data: data
  })
}

// 修改标准对标
export function updateDsmCheck(data) {
  return request({
    url: '/dsm/dsmCheck',
    method: 'put',
    data: data
  })
}

// 删除标准对标
export function delDsmCheck(checkId) {
  return request({
    url: '/dsm/dsmCheck/' + checkId,
    method: 'delete'
  })
}

// 导出标准对标
export function exportDsmCheck(query) {
  return request({
    url: '/dsm/dsmCheck/export',
    method: 'get',
    params: query
  })
}


export function getRunWcl(value_) {
var chartDom = document.getElementById('runWcl');
    	  var myChart = echarts.init(chartDom);
    	  var option;

		option = {
		    series: [{
		        type: 'gauge',
		        radius: '100%',
		        axisLine: {
		            lineStyle: {
		                width: 5
		            }
		        },
		        pointer: {
		            itemStyle: {
		                color: 'auto'
		            }
		        },
		        axisTick: {
		            distance: -30,
		            length: 8,
		            lineStyle: {
		                color: '#fff',
		                width: 2
		            }
		        },
		        splitLine: {
		            distance: -30,
		            length: 30,
		            lineStyle: {
		                color: '#fff',
		                width: 4
		            }
		        },
		        axisLabel: {
		            color: 'auto',
		            distance: 0,
		            fontSize: 5
		        },
		        detail: {
		            valueAnimation: true,
		            formatter: '{value} %',
		            color: 'auto'
		        },
		        data: [{
		            value: value_
		        }]
		    }]
		};

    	  option && myChart.setOption(option);
}