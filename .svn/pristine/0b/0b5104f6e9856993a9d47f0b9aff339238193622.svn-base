<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.needs.mapper.NesNeedsHandleMapper">
    
    <resultMap type="NesNeedsHandle" id="NesNeedsHandleResult">
        <result property="needsHandleId"    column="needs_handle_id"    />
        <result property="needsId"    column="needs_id"    />
        <result property="remark"    column="remark"    />
        <result property="scale"    column="scale"    />
        <result property="attachment"    column="attachment"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="planDate"    column="plan_date"    />
    </resultMap>

    <sql id="selectNesNeedsHandleVo">
        select needs_handle_id, needs_id, remark, scale, attachment, create_by, create_id, create_time,plan_date from nes_needs_handle
    </sql>

    <select id="selectNesNeedsHandleList" parameterType="NesNeedsHandle" resultMap="NesNeedsHandleResult">
        <include refid="selectNesNeedsHandleVo"/>
        <where>  
            <if test="needsId != null "> and needs_id = #{needsId}</if>
            <if test="scale != null "> and scale = #{scale}</if>
        </where>
    </select>
    
    <select id="selectNesNeedsHandleById" parameterType="Long" resultMap="NesNeedsHandleResult">
        <include refid="selectNesNeedsHandleVo"/>
        where needs_handle_id = #{needsHandleId}
        order by level ,needs_handle_id desc
    </select>
        
    <insert id="insertNesNeedsHandle" parameterType="NesNeedsHandle" useGeneratedKeys="true" keyProperty="needsHandleId">
        insert into nes_needs_handle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="needsId != null">needs_id,</if>
            <if test="remark != null">remark,</if>
            <if test="scale != null">scale,</if>
            <if test="attachment != null">attachment,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="planDate != null">plan_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="needsId != null">#{needsId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="scale != null">#{scale},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="planDate != null">#{planDate},</if>
         </trim>
    </insert>

    <update id="updateNesNeedsHandle" parameterType="NesNeedsHandle">
        update nes_needs_handle
        <trim prefix="SET" suffixOverrides=",">
            <if test="needsId != null">needs_id = #{needsId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
        </trim>
        where needs_handle_id = #{needsHandleId}
    </update>

    <delete id="deleteNesNeedsHandleById" parameterType="Long">
        delete from nes_needs_handle where needs_handle_id = #{needsHandleId}
    </delete>

    <delete id="deleteNesNeedsHandleByIds" parameterType="String">
        delete from nes_needs_handle where needs_handle_id in 
        <foreach item="needsHandleId" collection="array" open="(" separator="," close=")">
            #{needsHandleId}
        </foreach>
    </delete>
</mapper>