package com.dqms.dic.service.impl;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ip.IpUtils;
import com.dqms.dic.domain.DicFileDefine;
import com.dqms.dic.domain.DicFileTask;
import com.dqms.dic.mapper.DicFileDefineMapper;
import com.dqms.dic.mapper.DicFileTaskMapper;
import com.dqms.dic.service.IDicFileTaskService;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.enums.EtlConstants;
import com.dqms.task.service.IEtlTaskInstanceService;

/**
 * 文件数据补录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-14
 */
@Service
public class DicFileTaskServiceImpl implements IDicFileTaskService
{
    @Autowired
    private DicFileTaskMapper dicFileTaskMapper;
    
    @Autowired
    private DicFileDefineMapper dicFileDefineMapper;
    
    @Autowired
    private IEtlTaskInstanceService etlTaskInstanceService;
    

    /**
     * 查询文件数据补录
     *
     * @param fileTaskId 文件数据补录ID
     * @return 文件数据补录
     */
    @Override
    public DicFileTask selectDicFileTaskById(Long fileTaskId)
    {
        return dicFileTaskMapper.selectDicFileTaskById(fileTaskId);
    }

    /**
     * 查询文件数据补录列表
     *
     * @param dicFileTask 文件数据补录
     * @return 文件数据补录
     */
    @Override
    public List<DicFileTask> selectDicFileTaskList(DicFileTask dicFileTask)
    {
        return dicFileTaskMapper.selectDicFileTaskList(dicFileTask);
    }

    /**
     * 新增文件数据补录
     *
     * @param dicFileTask 文件数据补录
     * @return 结果
     */
    @Override
    public int insertDicFileTask(DicFileTask dicFileTask)
    {
    	DicFileDefine d =dicFileDefineMapper.selectDicFileDefineById(dicFileTask.getFileDefineId());
    	EtlTaskInstance etlTaskInstance = new EtlTaskInstance();
    	etlTaskInstance.setTaskIds(new Long[] {d.getTaskId()});
    	etlTaskInstance.setRunType(new String[] {"LINE","GROUP"});
    	etlTaskInstance.setLoadDate(dicFileTask.getAttachment());
    	String batchId=UUID.randomUUID().toString();
    	etlTaskInstance.setBatchId(batchId);
    	etlTaskInstance.setIp(IpUtils.getHostIp());
    	etlTaskInstanceService.insertEtlTaskInstanceBySd(etlTaskInstance);
    	dicFileTask.setCreateTime(DateUtils.getNowDate());
    	dicFileTask.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dicFileTask.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	dicFileTask.setStatus(EtlConstants.TASK_CALENDER_STATUS_FINISHED);
        return dicFileTaskMapper.insertDicFileTask(dicFileTask);
    }

    /**
     * 修改文件数据补录
     *
     * @param dicFileTask 文件数据补录
     * @return 结果
     */
    @Override
    public int updateDicFileTask(DicFileTask dicFileTask)
    {
        return dicFileTaskMapper.updateDicFileTask(dicFileTask);
    }

    /**
     * 批量删除文件数据补录
     *
     * @param fileTaskIds 需要删除的文件数据补录ID
     * @return 结果
     */
    @Override
    public int deleteDicFileTaskByIds(Long[] fileTaskIds)
    {
        return dicFileTaskMapper.deleteDicFileTaskByIds(fileTaskIds);
    }

    /**
     * 删除文件数据补录信息
     *
     * @param fileTaskId 文件数据补录ID
     * @return 结果
     */
    @Override
    public int deleteDicFileTaskById(Long fileTaskId)
    {
        return dicFileTaskMapper.deleteDicFileTaskById(fileTaskId);
    }
}
