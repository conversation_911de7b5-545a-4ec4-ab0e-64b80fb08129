package com.dqms.dsc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsc.mapper.DscEntityPropSystemMapper;
import com.dqms.dsc.domain.DscEntityPropSystem;
import com.dqms.dsc.service.IDscEntityPropSystemService;

/**
 * 字段应用系统Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@Service
public class DscEntityPropSystemServiceImpl implements IDscEntityPropSystemService
{
    @Autowired
    private DscEntityPropSystemMapper dscEntityPropSystemMapper;

    /**
     * 查询字段应用系统
     *
     * @param entityPropId 字段应用系统ID
     * @return 字段应用系统
     */
    @Override
    public DscEntityPropSystem selectDscEntityPropSystemById(Long entityPropId)
    {
        return dscEntityPropSystemMapper.selectDscEntityPropSystemById(entityPropId);
    }

    /**
     * 查询字段应用系统列表
     *
     * @param dscEntityPropSystem 字段应用系统
     * @return 字段应用系统
     */
    @Override
    public List<DscEntityPropSystem> selectDscEntityPropSystemList(DscEntityPropSystem dscEntityPropSystem)
    {
        return dscEntityPropSystemMapper.selectDscEntityPropSystemList(dscEntityPropSystem);
    }

    /**
     * 新增字段应用系统
     *
     * @param dscEntityPropSystem 字段应用系统
     * @return 结果
     */
    @Override
    public int insertDscEntityPropSystem(DscEntityPropSystem dscEntityPropSystem)
    {
        return dscEntityPropSystemMapper.insertDscEntityPropSystem(dscEntityPropSystem);
    }

    /**
     * 修改字段应用系统
     *
     * @param dscEntityPropSystem 字段应用系统
     * @return 结果
     */
    @Override
    public int updateDscEntityPropSystem(DscEntityPropSystem dscEntityPropSystem)
    {
        return dscEntityPropSystemMapper.updateDscEntityPropSystem(dscEntityPropSystem);
    }

    /**
     * 批量删除字段应用系统
     *
     * @param entityPropIds 需要删除的字段应用系统ID
     * @return 结果
     */
    @Override
    public int deleteDscEntityPropSystemByIds(Long[] entityPropIds)
    {
        return dscEntityPropSystemMapper.deleteDscEntityPropSystemByIds(entityPropIds);
    }

    /**
     * 删除字段应用系统信息
     *
     * @param entityPropId 字段应用系统ID
     * @return 结果
     */
    @Override
    public int deleteDscEntityPropSystemById(Long entityPropId)
    {
        return dscEntityPropSystemMapper.deleteDscEntityPropSystemById(entityPropId);
    }
}
