<template>
  <div class="app-container">
    <el-row :gutter="20">
	    <el-col :span="4" :xs="24">
	      <div class="head-container">
	        <el-input
	          v-model="className"
	          placeholder="请输入分类名称"
	          clearable
	          size="small"
	          prefix-icon="el-icon-search"
	          style="margin-bottom: 20px"
	        />
	      </div>
	      <div class="head-container">
	        <el-tree
	          :data="modelEntityClassIdOptions"
	          :props="defaultProps"
	          :expand-on-click-node="false"
	          :filter-node-method="filterNode"
	          ref="tree"
	          default-expand-all
	          @node-click="handleTreeNodeClick"
	        />
	      </div>
	    </el-col>
	    <el-col :span="20" :xs="24" style="border-left: 1px solid #304156;">
	      <el-tabs type="border-card" ref="tabs" v-model="activeName">
		   <el-tab-pane label="视图" name="first">
		   	<div id="scalingToolBar" style="position:absolute;padding-top:10;right:0;height:100;cursor:pointer;z-index: 99;width:100%;">
		   <el-row type="flex" justify="start" align="top">
		   	<el-col :span="14" style="padding:0px 15px;">
		   	<el-slider v-model="zoomIndex" :step="1" @input="zoomTo()" show-stops></el-slider>
		   	</el-col>
	    	<el-col :span="4" >
	    	<el-input size="mini" v-model="queryParams.name" placeholder="请输入关键字" style="width:200px;"/>
	    	</el-col>
	    	<el-col :span="6" >
	    	<el-tooltip class="item" effect="dark" content="根据任务名称进行居中定位" placement="bottom">
	    	<el-button type="primary" size="mini" @click="findTo()"><i class="el-icon-search"></i>查询</el-button>
	    	</el-tooltip>
	    	<el-tooltip class="item" effect="dark" content="根据元数据进行批量导入" placement="bottom">
	    	<el-button type="primary" size="mini" @click="handleOpenImp"><i class="el-icon-menu"></i>导入</el-button>
	    	</el-tooltip>
	    	<el-tooltip class="item" effect="dark" content="添加物理表" placement="bottom">
	    	<el-button type="primary" size="mini" @click="handleAdd"><i class="el-icon-menu"></i>新增</el-button>
	    	</el-tooltip>	  
	    	</el-col>
		   </el-row>
			</div>
		    <div id="container" style="position: relative;"></div>
	       </el-tab-pane>
	       <el-tab-pane label="审核中" name="second">
			    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
			      <el-form-item label="数据源" prop="datasourceId">
			          <el-select v-model="queryParams.datasourceId" placeholder="请选择数据源" style="width:100%" clearable>
			            <el-option
			              v-for="item in dataSourceOptions"
			              :key="item.datasourceId"
			              :label="item.name"
			              :value="item.datasourceId"
			            ></el-option>
			          </el-select>
			      </el-form-item>			    
			      <el-form-item label="表名" prop="tableName">
			        <el-input
			          v-model="queryParams.tableName"
			          placeholder="请输入表名"
			          clearable
			          size="small"
			          @keyup.enter.native="handleQuery"
			
			        />
			      </el-form-item>
			      <el-form-item label="注释" prop="tableComment">
			        <el-input
			          v-model="queryParams.tableComment"
			          placeholder="请输入注释"
			          clearable
			          size="small"
			          @keyup.enter.native="handleQuery"
			
			        />
			      </el-form-item>
			      <el-form-item label="SCHEMA" prop="tableSchema">
			        <el-input
			          v-model="queryParams.tableSchema"
			          placeholder="请输入SCHEMA"
			          clearable
			          size="small"
			          @keyup.enter.native="handleQuery"
			
			        />
			      </el-form-item>
			      <el-form-item>
			        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
			        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
			      </el-form-item>
			    </el-form>
			
			    <el-row :gutter="10" class="mb8">
			      <el-col :span="1.5">
			        <el-button
			          type="primary"
			          plain
			          icon="el-icon-plus"
			          size="mini"
			          @click="handleAdd"
			          v-hasPermi="['dsm:dsmModelEntity:add']"
			        >新增</el-button>
			      </el-col>
			      <el-col :span="1.5">
			        <el-button
			          type="danger"
			          plain
			          icon="el-icon-delete"
			          size="mini"
			          :disabled="multiple"
			          @click="handleDelete"
			          v-hasPermi="['dsm:dsmModelEntity:remove']"
			        >删除</el-button>
			      </el-col>
			      <el-col :span="1.5">
			        <el-button
			          type="warning"
			          plain
			          icon="el-icon-download"
			          size="mini"
			          @click="handleApply"
			          v-hasPermi="['dsm:dsmModelEntity:export']"
			        >审核</el-button>
			      </el-col>
			      <el-col :span="1.5">
			        <el-button
			          type="warning"
			          plain
			          icon="el-icon-download"
			          size="mini"
			          @click="handleExport"
			          v-hasPermi="['dsm:dsmModelEntity:export']"
			        >导出SQL</el-button>
			      </el-col>			      
			      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
			    </el-row>
			
			    <el-table v-loading="loading" :data="dsmModelEntityTempList" @selection-change="handleSelectionChange">
			      <el-table-column type="selection" width="55" align="center" />
			      <el-table-column label="数据源" align="center" prop="sysDatasource.name" />
			      <el-table-column label="模型主题" align="center" prop="modelEntityClass.classNameFull" />
			      <el-table-column label="SCHEMA" align="center" prop="tableSchema" />
			      <el-table-column label="表名" align="center" prop="tableName" />
			      <el-table-column label="注释" align="center" prop="tableComment" />
			      <el-table-column label="状态" align="center" prop="status"  :formatter="statusFormat" />
			      <el-table-column label="修改人" align="center" prop="updateBy" />
			      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
			        <template slot-scope="scope">
			          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
			        </template>
			      </el-table-column>
			      <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
			        <template slot-scope="scope">
			          <el-button
			            size="mini"
			            type="text"
			            icon="el-icon-edit"
			            @click="handleUpdate(scope.row,'TEMP')"
			            v-hasPermi="['dsm:dsmModelEntity:edit']"
			          >修改</el-button>
			          <el-button
			            size="mini"
			            type="text"
			            icon="el-icon-delete"
			            @click="handleDelete(scope.row)"
			            v-hasPermi="['dsm:dsmModelEntity:remove']"
			          >删除</el-button>
			          <el-button
			            size="mini"
			            type="text"
			            icon="el-icon-guide"
			            @click="showSql(scope.row)"
			            v-hasPermi="['dsm:dsmModelEntity:edit']"
			          >SQL</el-button>
			        </template>
			      </el-table-column>
			    </el-table>
			
			    <pagination
			      v-show="total>0"
			      :total="total"
			      :page.sync="queryParams.pageNum"
			      :limit.sync="queryParams.pageSize"
			      @pagination="getList"
			    />

		      </el-tab-pane>
		    </el-tabs>	
	      </el-col>
	    </el-row>    
    	        	    
    <!-- 添加或修改模型实例对话框 -->
    <el-dialog :visible.sync="open" width="1200px"  append-to-body>
 	      <el-tabs type="border-card" ref="tabs" v-model="formActiveName" >
		   <el-tab-pane label="基本信息" name="form" style="height:90%;overflow-y:auto;overflow-x:hidden;">   
		      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
		        <el-row type="flex" justify="start" align="top"  >
		        	<el-col :span="12">
				        <el-form-item label="模型主题" prop="modelEntityClassId">
				          <treeselect v-model="form.modelEntityClassId" :options="modelEntityClassIdOptions" :disable-branch-nodes="true" :show-count="true"  placeholder="请选择模型主题"  size="mini"/>
				        </el-form-item>
			        </el-col>
		        	<el-col :span="12">
		        		<el-form-item label="数据源" prop="datasourceId">          	      
				          <el-select v-model="form.datasourceId" placeholder="请选择数据源" style="width:100%" size="mini" clearable>
				            <el-option
				              v-for="item in dataSourceOptions"
				              :key="item.datasourceId"
				              :label="item.name"
				              :value="item.datasourceId"
				            ></el-option>
				          </el-select>	
			           </el-form-item>		        
			        </el-col>
			    </el-row>	
		        <el-row type="flex" justify="start" align="top"  >	  
		        	<el-col :span="8">          	      
				        <el-form-item label="SCHEMA" prop="tableSchema">
				          <el-input v-model="form.tableSchema" placeholder="请输入SCHEMA" size="mini" clearable/>
				        </el-form-item>		        
			        </el-col>
		        	<el-col :span="8">				        
				        <el-form-item label="表名" prop="tableName">
				          <el-input v-model="form.tableName" placeholder="请输入表名" size="mini" clearable/>
				        </el-form-item>		        
			        </el-col>
		        	<el-col :span="8">		        
				        <el-form-item label="注释" prop="tableComment">
				          <el-input v-model="form.tableComment" placeholder="请输入注释" size="mini" clearable/>
				        </el-form-item>
				    </el-col> 			        		        
			    </el-row>	
			    <el-row type="flex" justify="start" align="top"  >	
	              <el-form-item label="分区方式" prop="partitionType">
	                <el-select v-model="form.partitionType" placeholder="请选择分区方式" size="mini" clearable @change="(value)=>partitionChanged(value)">
	                  <el-option
	                    v-for="dict in partitionTypeOptions"
	                    :key="dict.dictValue"
	                    :label="dict.dictLabel"
	                    :value="dict.dictValue"
	                  ></el-option>
	                </el-select>
	              </el-form-item>
	              <el-form-item label="分区类型" prop="partitionColType">
	                <el-select v-model="form.partitionColType" placeholder="请选择分区字段类型" size="mini" clearable>
	                  <el-option
	                    v-for="dict in partitionColTypeOptions"
	                    :key="dict.dictValue"
	                    :label="dict.dictLabel"
	                    :value="dict.dictValue"
	                  ></el-option>
	                </el-select>
	              </el-form-item>	
	              <el-form-item label="分桶字段" prop="bucketCol">
	                <el-input v-model="form.bucketCol" placeholder="请输入分桶字段"  size="mini" clearable/>
	              </el-form-item>
	              <el-form-item label="分桶数量" prop="bucketNum">
	                <el-input v-model="form.bucketNum" placeholder="请输入分桶数量" size="mini" clearable/>
	              </el-form-item>		    
			    </el-row>	  	        
			       
			    <el-table v-loading="loading" :data="dsmModelEntityPropList" @selection-change="handleSelectionChange" height="300">
			    <el-table-column align="left" label="字段">
			      <el-table-column type="selection" width="55" align="center" />
			      <el-table-column label="名称" align="center" prop="propName" >
			      	 <template slot-scope="scope">
		               <el-input   v-model="scope.row.propName" size="mini"/>
		             </template>
		          </el-table-column>
			      <el-table-column label="注释" align="center" prop="propComment" >
			      	 <template slot-scope="scope">
		               <el-input   v-model="scope.row.propComment" size="mini"/>
		             </template>
		          </el-table-column>
			      <el-table-column label="类型" align="center" prop="dataType" >
		            <template slot-scope="scope">
		              <el-select v-model="scope.row.dataType"  size="mini">
		                <el-option v-for="dict in dataTypeOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
		              </el-select>
		            </template>
		          </el-table-column>          
			      <el-table-column label="是否主键" align="center" prop=isPriKey >
		            <template slot-scope="scope">
		              <el-select v-model="scope.row.isPriKey"  size="mini">
		                <el-option v-for="dict in isPriKeyOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
		              </el-select>
		            </template>
		          </el-table-column>          
			      <el-table-column label="是否必填" align="center" prop="nullable" >
		            <template slot-scope="scope">
		              <el-select v-model="scope.row.nullable"   size="mini">
		                <el-option v-for="dict in nullableOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
		              </el-select>
		            </template>
		          </el-table-column>
			      <el-table-column label="列大小" align="center" prop="columnSize" >
		            <template slot-scope="scope">
		              <el-input   v-model="scope.row.columnSize"  size="mini"/>
		            </template>
		          </el-table-column>
			      <el-table-column label="小数位数" align="center" prop="decimalDigits" >
			      	 <template slot-scope="scope">
		               <el-input   v-model="scope.row.decimalDigits"  size="mini"/>
		             </template>
		          </el-table-column>
			      <el-table-column label="默认值" align="center" prop="defaultValue" >
		            <template slot-scope="scope">
		              <el-input   v-model="scope.row.defaultValue"  size="mini"/>
		            </template>
		          </el-table-column>          
			      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
		            <template slot="header" slot-scope="scope">
		              <el-button
		                slot="append"
		                icon="el-icon-share"
		                @click="addParam"
		                size="mini"
		                type="text"
		                >添加</el-button
		              >
		            </template>
			        <template slot-scope="scope">
			          <el-button
			            size="mini"
			            type="text"
			            icon="el-icon-delete"
			            @click="delParam(scope.row, scope.$index)"
			          >删除</el-button>
			        </template>
			      </el-table-column>
			    </el-table-column>
			    </el-table>
		
			    <el-table v-loading="loading" :data="dsmModelEntityShipList" @selection-change="handleSelectionChange" height="200">
			    <el-table-column align="left" label="逻辑关系">
			      <el-table-column type="selection" width="55" align="center" />
			      <el-table-column label="名称" align="center" prop="name" >
			      	 <template slot-scope="scope">
		               <el-input   v-model="scope.row.name"  size="mini"/>
		             </template>
		          </el-table-column>
			      <el-table-column label="字段" align="center" prop="srcModelEntityPropName" >
		            <template slot-scope="scope">
		              <el-select v-model="scope.row.srcModelEntityPropName"  size="mini">
		                <el-option v-for="dict in dsmModelEntityPropList" :key="dict.propName" :label="dict.propName" :value="dict.propName" ></el-option>
		              </el-select>
		            </template>
		          </el-table-column>          
			      <el-table-column label="引用字段" align="center" prop="tarModelEntityPropId" >
		            <template slot-scope="scope">
		              <el-select v-model="scope.row.tarModelEntityPropId" size="mini">
		                <el-option v-for="dict in keyOptions" :key="dict.modelEntityPropId" :label="dict.propName" :value="dict.modelEntityPropId" ></el-option>
		              </el-select>
		            </template>
		          </el-table-column>
			      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
		            <template slot="header" slot-scope="scope">
		              <el-button
		                slot="append"
		                icon="el-icon-share"
		                @click="addKey"
		                size="mini"
		                type="text"
		                >添加</el-button
		              >
		            </template>
			        <template slot-scope="scope">
			          <el-button
			            size="mini"
			            type="text"
			            icon="el-icon-delete"
			            @click="delKey(scope.row, scope.$index)"
			          >删除</el-button>
			        </template>
			      </el-table-column>
			    </el-table-column>
			    </el-table>	    
			    	    
		      </el-form>
		      </el-tab-pane>
		      <el-tab-pane label="脚本信息" name="script">
		      	<el-col :span="12">
			         <div id="sqlQueryModelCol" style="height:585px;">
			          <textarea
			            ref="myCm"
			            class="sqlTextarea"
			            id="sqlQueryModelText"
			            name="sqlQueryModelText"
			          ></textarea>
			        </div>
		      	<el-row :gutter="20" style="text-align: right;padding-right:20px;padding-top:5px;right:0;height:100;cursor:pointer;z-index: 99;">
			      	<el-button type="primary" size="mini" @click="handleRestart(true)">执行</el-button>
			      	<el-button type="primary" size="mini" @click="sqlFormat">格式化</el-button> 
			      	<el-button type="primary" size="mini" @click="handleCheck(true)">校验</el-button>
		      	</el-row>
		        </el-col>
		        <el-col :span="12">
		            <el-table :data="infoData" style="width: 100%;" height="585">
		              <el-table-column
		                v-for="(item, key, index) in infoData[0]"
		                :key="index"
		                :prop="key"
		                :label="key"
		                align="left"
		                header-align="center"
		                :min-width="
		                  key.length * 12 < info[key]
		                    ? info[key] > 500
		                      ? 500
		                      : info[key]
		                    : key.length * 12
		                "
		              >
		              </el-table-column>
		            </el-table>		        
		        </el-col> 
		      </el-tab-pane>
		    </el-tabs>		
		    <div slot="footer" class="dialog-footer">
		       <el-button type="primary" @click="submitForm">确 定</el-button>
		       <el-button @click="cancel">取 消</el-button>
		    </div>      
    </el-dialog>
    
    <el-dialog :visible.sync="openImp" width="1200px"  append-to-body>
        <el-form :model="queryParamsImp" ref="queryFormImp" :inline="true" label-width="70px">
	      <el-form-item label="名称" prop="regName">
	        <el-input
	          v-model="queryParamsImp.regName"
	          placeholder="请输入表代码/脚本名称"
	          clearable
	          size="small"
	          @keyup.enter.native="handleQuery"
	        />
	      </el-form-item>
	      <el-form-item label="数据源" prop="datasourceId">
	        <el-select v-model="queryParamsImp.datasourceId" placeholder="请选择数据源" clearable size="small" filterable>
	          <el-option
	            v-for="item in dataSourceOptions"
	            :key="item.datasourceId"
	            :label="item.name"
	            :value="item.datasourceId"
	          ></el-option>
	        </el-select>
	      </el-form-item>
	      <el-form-item label="所属系统" prop="systemId">
	        <el-select v-model="queryParamsImp.systemId" placeholder="请选择所属系统" clearable size="small" filterable>
	          <el-option
	            v-for="item in systemOptions"
	            :key="item.systemId"
	            :label="item.name"
	            :value="item.systemId"
	          ></el-option>
	        </el-select>
	      </el-form-item>
	      <el-form-item>
	        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryImp">搜索</el-button>
	        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
	      </el-form-item>
	    </el-form>
    <el-table v-loading="loading" border :data="mdmRegistryList">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="名称" width="260" align="center" prop="regName" />
      <el-table-column label="中文注释" align="center"  prop="regAnt"  width="225"/>
      <el-table-column label="数据源" align="center"   prop="datasourceName"  width="205"/>
      <el-table-column label="所属系统" align="center"  prop="systemName"  width="205"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleImp(scope.row)"
          >引入</el-button>
         </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="totalImp>0"
      :total="totalImp"
      :page.sync="queryParamsImp.pageNum"
      :limit.sync="queryParamsImp.pageSize"
      @pagination="getList"
    />	    
    </el-dialog>
  </div>
</template>

<script>
import { listDsmModelEntity, getDsmModelEntity, delDsmModelEntity, addDsmModelEntity, updateDsmModelEntity,exportDsmModelEntity ,showShipMap ,impDsmModelEntity} from "@/api/dsm/dsmModelEntity";
import { listDsmModelEntityTemp, getDsmModelEntityTemp, delDsmModelEntityTemp, addDsmModelEntityTemp, updateDsmModelEntityTemp, exportDsmModelEntityTemp,applyDsmModelEntityTemp } from "@/api/dsm/dsmModelEntityTemp";
import { listDsmModelEntityProp, getDsmModelEntityProp, delDsmModelEntityProp, addDsmModelEntityProp, updateDsmModelEntityProp, exportDsmModelEntityProp } from "@/api/dsm/dsmModelEntityProp";
import { listDsmModelEntityShip, getDsmModelEntityShip, delDsmModelEntityShip, addDsmModelEntityShip, updateDsmModelEntityShip, exportDsmModelEntityShip } from "@/api/dsm/dsmModelEntityShip";
import { listDatasource,listDatasourceAll,findTablesByDS,excuteSql,checkSql  ,listSystem} from "@/api/basic/datasource";
import { listMdmRegistry } from '@/api/mdm/mdmRegistry'
import { treeselect } from "@/api/dsm/dsmModelEntityClass";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getToken } from "@/utils/auth";
import G6 from "@antv/g6";
import CodeMirror from "codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/merge/merge.js";
import "codemirror/addon/merge/merge.css";
import "codemirror/addon/hint/show-hint.css";
import "codemirror/addon/hint/sql-hint.js";
import { codemirror } from "vue-codemirror";
import "codemirror/theme/monokai.css";
import "codemirror/theme/idea.css";
import "codemirror/mode/sql/sql.js";
import "codemirror/addon/hint/show-hint.js";
import "codemirror/addon/display/autorefresh.js";
import DiffMatchPatch from "diff-match-patch";
import sqlFormatter from "sql-formatter";
window.diff_match_patch = DiffMatchPatch;
window.DIFF_DELETE = -1;
window.DIFF_INSERT = 1;
window.DIFF_EQUAL = 0;
export default {
  name: "DsmModelEntity",
  components: {
	  Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模型实例表格数据
      dsmModelEntityList: [],
      dsmModelEntityTempList: [],
      modelEntityClassIdOptions:[],
      dataSourceOptions: [],
      systemOptions:[],
      // 分区方式字典
      partitionTypeOptions: [],
      // 分区字段类型字典
      partitionColTypeOptions: [],
      className:"",
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      zoomIndex:50,
   	  // 模型字段表格数据
      dsmModelEntityPropList: [],
      dataTypeOptions: [
    	  {"dictValue":"char","dictLabel":"char"},
    	  {"dictValue":"varchar2","dictLabel":"varchar2"},
    	  {"dictValue":"long","dictLabel":"long"},
    	  {"dictValue":"number","dictLabel":"number"},
    	  {"dictValue":"date","dictLabel":"date"},
    	  {"dictValue":"timestamp","dictLabel":"timestamp"},
    	  {"dictValue":"binary_double","dictLabel":"binary_double"},
    	  {"dictValue":"binary_float","dictLabel":"binary_float"},
    	  {"dictValue":"blob","dictLabel":"blob"},
    	  {"dictValue":"clob","dictLabel":"clob"},
    	  {"dictValue":"interval day to second","dictLabel":"interval day to second"},
    	  {"dictValue":"interval year to month","dictLabel":"interval year to month"},
    	  {"dictValue":"long raw","dictLabel":"long raw"},
    	  {"dictValue":"nclob","dictLabel":"nclob"},
    	  {"dictValue":"nvarchar2","dictLabel":"nvarchar2"},
    	  {"dictValue":"raw","dictLabel":"raw"},
    	  {"dictValue":"timestamp with local time zone","dictLabel":"timestamp with local time zone"},
    	  {"dictValue":"timestamp with time zone","dictLabel":"timestamp with time zone"},
    	  ],
      isPriKeyOptions: [],
      nullableOptions: [],
      dsmModelEntityShipList: [],
      statusOptions: [],
      keyOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10000,
        modelEntityClassId: null,
        datasourceId: null,
        tableName: null,
        tableComment: null,
        tableSchema: null,
        versionNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      graphA: undefined,
      g6graph: undefined,
      nodes: [],
      edges: [],
      activeName: "first",
      formActiveName: "form",
    //SQL编辑器
      table: {}, // 用于自定义列表宽度
      info: [], // 用于自定义列表宽度s
      idbLexicon: {},
      isRealTimeTip: false, // 是否是实时的提示
      activeName: "first",
      sqlInfo: [],
      codeEditor: null,
      input:20,
      defaultProps: {
        icon: "iconOne",
        label: "label",
        isLeaf: "leaf"
      },
      infoData: [],
      totalImp: 0,
      openImp: false,
      titleImp: "",
      mdmRegistryList: [],
      queryParamsImp: {
        pageNum: 1,
        pageSize: 10,
        regName: null,
        metaType: "1",
        datasourceId: null,
        systemId: null,
        themeId: null,
        layerId: null,
        acqStatus:"2"
      }
    };
  },
  watch: {
    className(val) {
      this.$refs.tree.filter(val);
    },
	'form.type': {
         handler: function(newValue, oldValue) {
			if(newValue=='SCRIPT'){
				this.ifRequired = true
			}else{
				this.ifRequired = false
			}
         },
		 immediate:true
     }
  },
  created() {
    this.getTreeselect();
    this.getDicts("sys_yes_no").then(response => {
        this.isPriKeyOptions = response.data;
        this.nullableOptions = response.data;
    });
    this.getDicts("mdm_apply_status").then(response => {
      this.statusOptions = response.data;
    });   
    this.getDicts("partition_type").then(response => {
      this.partitionTypeOptions = response.data;
    });
    this.getDicts("partition_col_type").then(response => {
      this.partitionColTypeOptions = response.data;
    });
    this.getSystem();
    this.getDataSource();
  },
  mounted(){
	this.getList();
	this.getListTemp();
  },
  methods: {
    /** 查询模型实例列表 */
    getList() {
      this.loading = true;
      listDsmModelEntity(this.queryParams).then(response => {
    	  this.keyOptions=[];
        this.dsmModelEntityList = response.rows;
        this.total = response.total;
        this.loading = false;
    	if(this.g6graph){
      		this.nodes=[];
      		this.edges=[];
      		this.g6graph.clear();
      		this.g6graph.destroy();
      	  }
    	for (var i = 0; i < this.dsmModelEntityList.length; i++) {
    		this.nodes.push({
            id: this.dsmModelEntityList[i].modelEntityId.toString(),
            label: this.dsmModelEntityList[i].tableName,
            attrs: this.dsmModelEntityList[i].dsmModelEntityProps
          });
    		for(var j = 0; j < this.dsmModelEntityList[i].dsmModelEntityProps.length; j++){
    			if(this.dsmModelEntityList[i].dsmModelEntityProps[j].isPriKey=='Y'){
    				this.keyOptions.push({modelEntityPropId:this.dsmModelEntityList[i].dsmModelEntityProps[j].modelEntityPropId,propName:this.dsmModelEntityList[i].dsmModelEntityProps[j].propName});	
    			}
    		}
    		for(var j = 0; j < this.dsmModelEntityList[i].dsmModelEntityShips.length; j++){
        		this.edges.push({
                    source: this.dsmModelEntityList[i].dsmModelEntityShips[j].srcModelEntityId.toString(),
                    target: this.dsmModelEntityList[i].dsmModelEntityShips[j].tarModelEntityId.toString(),
                    sourceKey: this.dsmModelEntityList[i].dsmModelEntityShips[j].srcModelEntityPropName,
                    targetKey: this.dsmModelEntityList[i].dsmModelEntityShips[j].tarModelEntityPropName,
                    label: this.dsmModelEntityList[i].dsmModelEntityShips[j].name
                  });
    		}
    	}
        this.initGraph();
      });

    },
    getListTemp() {
      this.loading = true;
      listDsmModelEntityTemp(this.queryParams).then(response => {
        this.dsmModelEntityTempList = response.rows;
        this.total = response.total;
        this.loading = false;
      });

    },
    getSystem() {
      listSystem().then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },    
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },   
    // 前置数据源字典翻译
    datasourceFormat(row, column) {
      return this.selectLabel(this.dataSourceOptions, row.datasourceId,'datasourceId','name');
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then(response => {
        this.modelEntityClassIdOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleTreeNodeClick(data) {
      this.queryParams.modelEntityClassId = data.id;
      this.getList();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        modelEntityId: null,
        modelEntityClassId: null,
        tableName: null,
        tableComment: null,
        tableSchema: null,
        versionNo: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null,
        datasourceId: null,
        partitionType: "N",
        partitionColType: null,
        bucketCol: null,
        bucketNum: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getListTemp();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.modelEntityTempId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加模型实例";
      this.initCode();
    },
    /** 修改按钮操作 */
    handleUpdate(row,type) {
      this.reset();
	      if(type!='TEMP'){
	    	  const modelEntityId = row.modelEntityId || this.ids
	          getDsmModelEntity(modelEntityId).then(response => {
	            this.open = true;
	            this.form = response.data;
	            this.form.datasourceId=Number(response.data.datasourceId);
	            this.dsmModelEntityPropList = response.data.dsmModelEntityProps;
	            this.dsmModelEntityShipList = response.data.dsmModelEntityShips;
	            this.title = "修改模型实例";
	            this.$nextTick(()=>{this.initCode();})
	          }); 
	      }else{
	    	  const modelEntityTempId = row.modelEntityTempId || this.ids
	    	  getDsmModelEntityTemp(modelEntityTempId).then(response => {
	            this.open = true;
	            this.form = response.data;
	            console.log(response.data);
	            this.form.datasourceId=Number(response.data.datasourceId);
	            this.dsmModelEntityPropList = response.data.dsmModelEntityProps;
	            this.dsmModelEntityShipList = response.data.dsmModelEntityShips;
	            this.title = "修改模型实例";
	            this.$nextTick(()=>{this.initCode(this.form.sqlScript);})
	          }); 
	      }
      
    
    },
    initCode(sqlScript){
        // 实例初始化
        const targetF = document.getElementById("sqlQueryModelCol");
        targetF.innerHTML = '<textarea id="sqlQueryModelText" name="sqlQueryModelText" class="sqlTextarea"/>';
        const target = document.getElementById("sqlQueryModelText");
        this.codeEditor = CodeMirror.fromTextArea(target, {
          lineNumbers: true, //显示行号
          styleActiveLine: true,
          matchBrackets: true,
          mode: "text/x-sql",
          connect: "align",
          theme: "monokai",
          autoCloseBrackets: true,
          autoRefresh: true,
          readOnly: false,
          hintOptions: {
            completeSingle: false,
            tables: this.idbLexicon
          },
          extraKeys: {
            "Ctrl-Space": editor => {
              editor.showHint();
            }
          }
        });
        this.codeEditor.setValue(sqlScript);
        this.codeEditor.on("keypress", editor => {
          const __Cursor = editor.getDoc().getCursor();
          const __Token = editor.getTokenAt(__Cursor);
          if (
            __Token.type &&
            __Token.type !== "string" &&
            __Token.type !== "punctuation" &&
            __Token.string.indexOf(".") === -1
          ) {
            // 把输入的关键字统一变成大写字母
            editor.replaceRange(
              __Token.string.toUpperCase(),
              {
                line: __Cursor.line,
                ch: __Token.start
              },
              {
                line: __Cursor.line,
                ch: __Token.end
              },
              __Token.string
            );
          }
          editor.showHint();
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
       	   this.form.dsmModelEntityProps = [];
       		this.form.sqlScript =this.getValue();
              for (var i = 0; i < this.dsmModelEntityPropList.length; i++) {
                this.form.dsmModelEntityProps.push({
                	 propName: this.dsmModelEntityPropList[i].propName,
                	 propComment: this.dsmModelEntityPropList[i].propComment,
                	 dataType: this.dsmModelEntityPropList[i].dataType,
                	 isPriKey: this.dsmModelEntityPropList[i].isPriKey,
                	 nullable: this.dsmModelEntityPropList[i].nullable,
                	 columnSize: this.dsmModelEntityPropList[i].columnSize,
                	 decimalDigits: this.dsmModelEntityPropList[i].decimalDigits,
                	 defaultValue: this.dsmModelEntityPropList[i].defaultValue
                });
             }
           this.form.dsmModelEntityShips = [];
             for (var i = 0; i < this.dsmModelEntityShipList.length; i++) {
               this.form.dsmModelEntityShips.push({
            	   name: this.dsmModelEntityShipList[i].name,
            	   srcModelEntityPropName: this.dsmModelEntityShipList[i].srcModelEntityPropName,
            	   tarModelEntityPropId: this.dsmModelEntityShipList[i].tarModelEntityPropId
               });
             }
          if (this.form.modelEntityTempId != null) {
        	updateDsmModelEntityTemp(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getListTemp();
            });
          } else {
            addDsmModelEntityTemp(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getListTemp();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const modelEntityTempIds = row.modelEntityTempId || this.ids;
      this.$confirm('是否确认删除模型实例编号为"' + modelEntityTempIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDsmModelEntityTemp(modelEntityTempIds);
        }).then(() => {
          this.getListTemp();
          this.msgSuccess("删除成功");
        })
    },
    handleApply(row) {
       const modelEntityTempIds = row.modelEntityTempId || this.ids;
       this.$confirm('是否确认审核模型实例编号为"' + modelEntityTempIds + '"的数据项?', "警告", {
           confirmButtonText: "确定",
           cancelButtonText: "取消",
           type: "warning"
         }).then(function() {
           return applyDsmModelEntityTemp(modelEntityTempIds);
         }).then(() => {
           this.getListTemp();
           this.msgSuccess("审核成功");
         })
     },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有模型实例数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDsmModelEntityTemp(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    addParam() {
   	 let j = { "propName": "","propComment": "","dataType":"varchar2", "isPriKey": "N","nullable":"N", "columnSize": "","decimalDigits": "", "defualtValue": ""};
   	 this.dsmModelEntityPropList.push(j);
    },
    delParam(row, index) {
   	 this.dsmModelEntityPropList.splice(index, 1);
    },
    addKey() {
   	 let j = { "name": "","srcModelEntityPropId": "","tarModelEntityPropId":""};
   	 this.dsmModelEntityShipList.push(j);
    },
    delKey(row, index) {
   	 this.dsmModelEntityShipList.splice(index, 1);
    },
    initGraph() {
        this.g6graph=showShipMap("container",this.nodes,this.edges);
    },
  	zoomTo(){
    	  this.g6graph.zoomTo(this.zoomIndex/100);
  	},
	findTo(){
		var name = this.queryParams.name;
		this.g6graph.findAll('node', node => {
			if( node.get('model').label == name){
				console.log(node.get('model').x+"="+node.get('model').y);
				this.g6graph.updateItem(node, {
					  style: {stroke: 'red'},
				  });
				this.g6graph.focusItem(node);
				// 5秒后自动回复
				let _this=this
				setTimeout( function(){
					_this.g6graph.updateItem(node, {
						  style: {stroke: '#72CC4A',},
					  });
				}, 5 * 1000 );
			};
			});
	},
    handleRestart() {
	      this.runLoading = true
	      let sqlScript =
	        this.getSelection() != "" ? this.getSelection() : this.getValue();
	      if (sqlScript == null || sqlScript == "") {
	        this.$message.warning("请输入脚本后再点击运行");
	        return;
	      }
	      if (this.form.datasourceId == null) {
	        this.$message.warning("请选择数据源后再点击运行");
	        return;
	      }
	      this.tableData = [];
	      this.infoData = [];
	      excuteSql(this.form.datasourceId, sqlScript, this.input)
	        .then(res => {
	          this.tableData = res.data;
	          this.tableData.map((el, index) => {
	            Object.entries(el).map((el2, index2) => {
	              var width = this.getTextWidth(el2[1].toString());
	              if (!this.table[el2[0]] || width > this.table[el2[0]]) {
	                this.table[el2[0]] = width;
	              }
	            });
	          });
	          this.infoData = res.info;
	          this.infoData.map((el, index) => {
	            Object.entries(el).map((el2, index2) => {
	              var width = this.getTextWidth(el2[1].toString());
	              if (!this.info[el2[0]] || width > this.info[el2[0]]) {
	                this.info[el2[0]] = width;
	              }
	            });
	          });
	          if (this.tableData.length != 0 && this.infoData.length != 0) {
	            this.activeName = "first";
	          }
	          if (this.tableData.length == 0 && this.infoData.length != 0) {
	            this.activeName = "second";
	          }
	          if (this.tableData.length != 0 && this.infoData.length == 0) {
	            this.activeName = "first";
	          }
	          this.paginationInfo.total = this.tableData.length
	          this.tableData2 = this.tableData.slice(0,this.pageSize)
	          this.$nextTick(()=>{
	             document.body.scrollTop=document.documentElement.scrollTop = 800
	          })
	        })
	        .catch(() => {
	          let data = [];
	        })
	        .finally(()=>{
	          this.runLoading = false
	        })
	    },
	    handleCheck() {
		      this.runLoading = true
		      let sqlScript =
		        this.getSelection() != "" ? this.getSelection() : this.getValue();
		      if (sqlScript == null || sqlScript == "") {
		        this.$message.warning("请输入脚本后再点击运行");
		        return;
		      }
		      if (this.form.datasourceId == null) {
		        this.$message.warning("请选择数据源后再点击运行");
		        return;
		      }
		      this.tableData = [];
		      this.infoData = [];
		      checkSql(this.form.datasourceId, sqlScript, this.input)
		        .then(res => {
		          this.tableData = res.data;
		          this.tableData.map((el, index) => {
		            Object.entries(el).map((el2, index2) => {
		              var width = this.getTextWidth(el2[1].toString());
		              if (!this.table[el2[0]] || width > this.table[el2[0]]) {
		                this.table[el2[0]] = width;
		              }
		            });
		          });
		          
		          this.infoData = res.info;
		          console.log(res.info);
		          this.infoData.map((el, index) => {
		            Object.entries(el).map((el2, index2) => {
		              var width = this.getTextWidth(el2[1].toString());
		              if (!this.info[el2[0]] || width > this.info[el2[0]]) {
		                this.info[el2[0]] = width;
		              }
		            });
		          });
		          if (this.tableData.length != 0 && this.infoData.length != 0) {
		            this.activeName = "first";
		          }
		          if (this.tableData.length == 0 && this.infoData.length != 0) {
		            this.activeName = "second";
		          }
		          if (this.tableData.length != 0 && this.infoData.length == 0) {
		            this.activeName = "first";
		          }
		          this.paginationInfo.total = this.tableData.length
		          this.tableData2 = this.tableData.slice(0,this.pageSize)
		          this.$nextTick(()=>{
		             document.body.scrollTop=document.documentElement.scrollTop = 800
		          })
		        })
		        .catch(() => {
		          let data = [];
		        })
		        .finally(()=>{
		          this.runLoading = false
		        })
		    },
	    // 设置value
	    setValue(val) {
	      this.codeEditor.setValue(val);
	    },
	    // 获取value
	    getValue() {
	      return this.codeEditor.getValue();
	    },
	    // 获取选中内容
	    getSelection() {
	      return this.codeEditor.getSelection();
	    },
	    getTextWidth(str) {
          var width = 0;
          var html = document.createElement("span");
          html.innerText = str;
          html.className = "getTextWidth";
          document.querySelector("body").appendChild(html);
          width = document.querySelector(".getTextWidth").offsetWidth;
          document.querySelector(".getTextWidth").remove();
          return width;
        },
        sqlFormat() {
        /*获取文本编辑器内容*/
          let sqlContent = "";
          sqlContent = this.codeEditor.getValue();
        /*将sql内容进行格式后放入编辑器中*/
          this.codeEditor.setValue(sqlFormatter.format(sqlContent));
        },
        showSql(row) {
          this.$notify({
       	    position: 'bottom-right',
       	    dangerouslyUseHTMLString: true,
            showClose: true,
            message: '<div style="overflow-y: auto;height:100%;white-space: pre-wrap;border: 1px solid #8d8c8c;padding: 5px;white-space: pre-wrap;word-wrap: break-word;">'+row.createScript+'</div>'
          });
        },
        handleOpenImp() {
	      this.openImp=true;
	      this.handleQueryImp();
	    },
	    handleQueryImp(){
	      this.loading = true;
	      listMdmRegistry(this.queryParamsImp).then(response => {
	        this.mdmRegistryList = response.rows;
	        this.totalImp = response.total;
	        this.loading = false;
	      });
	    },
	    handleCloseImp() {
	      this.openImp=false;
	    },
        handleImp(row) {
	      if(this.queryParams.modelEntityClassId==null){
	    	this.$message.warning("请选择分类后再导入！");
	        return;
	      }
	      impDsmModelEntity(row.regId,this.queryParams.modelEntityClassId).then(response => {
    		this.msgSuccess("修改成功");
          }); 
	    },
	    resetQuery() {
	      this.resetForm("queryFormImp");
	      this.handleQueryImp();
	    }
  },
  destroy() {
	    //注意，VUE此处必须清理，否则切换界面会越来越卡
	    this.g6graph.clear();
	    this.g6graph.destroy();
	  }
};
</script>
<style scoped>
>>> .CodeMirror pre.CodeMirror-line,
>>> .CodeMirror pre.CodeMirror-line-like {
  line-height: 22px;
}
>>> .CodeMirror {
  height:100% !important;
}
>>> .CodeMirror-merge,
.CodeMirror-merge .CodeMirror {
  height: 480px !important;
}
>>> .CodeMirror-merge-r-chunk {
  background: #38380d !important;
}

>>> .infinite-list-wrapper {
  height: calc(100vh - 84px);
}
>>> .el-tag--medium {
  margin-left: 5px;
}
>>> .loadingStatus {
  text-align: center;
  color: #303133;
  font-size: 14px;
}
.sqlTextarea {
  resize: none;
  width: 100%;
  height: calc(100vh - 432px);
  min-height: 400px;
  background: #f5f7fa;
}
>>> .head-container:last-child {
  overflow-y: scroll;
  height: calc(100vh - 180px);
  -ms-overflow-style: none;
  scrollbar-width: none;
}
>>> .head-container2::-webkit-scrollbar {
  height: 0 !important;
  width: 0px !important;
}
>>> .sqlBtn {
  margin-bottom: 1px;
}
>>> .spanStyle {
  white-space: nowrap;
  width: 90%;
  word-break: keep-all;
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
}
>>> .el-table th > .cell {
  padding: 0;
  text-align: center;
}
>>> .getTextWidth {
  font-size: 14px;
}
>>> .el-card__body{
  height:100%;
}
>>>.el-col-12{
  height:90%;
}
#sqlDamMdmAnalyseCol{
  height:100%;
}
>>>.el-card.is-always-shadow{
  height:100%;
}
.app-container{
  height:calc(100vh - 84px)
}
>>>.el-tabs,>>>.el-tab-pane{
  height:100%;
}
>>>.el-tabs__content{
  height:calc(100% - 24px);
}
.entity-container.fact {
  border: 1px solid #ced4de;
  height: 248px;
  width: 214px;
}
.entity-container {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border-radius: 2px;
  background-color: #fff;
}
.entity-container .content.fact {
  background-color: #ced4de;
}
.entity-container .content {
  margin: 1px;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.entity-container .content .head {
  width: calc(100% - 12px);
  height: 38px;
  margin-left: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.entity-container .content .head .type {
  padding-right: 8px;
}
.anticon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.anticon svg {
  display: inline-block;
}
svg:not(:root) {
  overflow: hidden;
}
.entity-container .content .head .more {
  cursor: pointer;
}
.entity-container .content .body {
  width: calc(100% - 12px);
  height: calc(100% - 42px);
  margin-left: 6px;
  margin-bottom: 6px;
  background-color: #fff;
  overflow: auto;
  cursor: pointer;
}
.entity-container .content .body .body-item {
  width: 100%;
  height: 28px;
  font-size: 12px;
  color: #595959;
  border-bottom: 1px solid rgba(206, 212, 222, 0.2);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.entity-container .content .body .body-item .name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 6px;
}
.entity-container .content .body .body-item .name .fk,
.entity-container .content .body .body-item .name .pk {
  width: 12px;
  font-family: "HelveticaNeue-CondensedBold";
  color: #ffd666;
  margin-right: 6px;
}
.entity-container .content .body .body-item .type {
  color: #bfbfbf;
  font-size: 8px;
  margin-right: 8px;
}
#scalingToolBar2{
  transition:all 2s ease;
  right:25%;
  top:10px;
}
#scalingToolBar2.right{
  right:0;
  top:0;
}
</style>
