package com.dqms.mdm.util.StrategyType;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.enums.TableInfo;
import com.dqms.common.utils.StringUtils;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.util.MetaDataStrategy;
import com.dqms.utils.JdbcTemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class PostgreSQLStrategy extends MetaDataStrategy {
    @Override
    public Map<String, Object> getTableInfo(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        log.info("这是PostgreSQL获取表结构方法");

        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Map<String, Object> m = new HashedMap();
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(conn.getCatalog(),mdmRegistry.getRegDir(), mdmRegistry.getRegName(), null);
            int i = 0;
            while (rs.next()) {
                log.info("---------[" + i++ + "]---------");
                String tableName = "";
                tableName = rs.getString("TABLE_NAME");
                String tableCat = rs.getString("TABLE_CAT");
                String tableSchem = rs.getString("TABLE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String tableType = rs.getString("TABLE_TYPE");
                String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                m.put(TableInfo.TABLE_NAME.getCode(), tableName);
                m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return m;
    }

    @Override
    public List<Map<String, Object>> getColumns(MdmRegistry mdmRegistry,SysDatasource sysDatasource) {

        log.info("这是PostgreSQL获取表字段方法");

        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        List<Map<String, Object>> columns = new ArrayList<Map<String, Object>>();
        Connection conn = null;
        ResultSet rs = null;
        try {
        	String regDir=mdmRegistry.getRegDir()==null?null:mdmRegistry.getRegDir().toLowerCase();
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getPrimaryKeys(null,  regDir, mdmRegistry.getRegName());
            List<String> primaryKeys = new ArrayList<String>();
            while (rs.next()) {
                primaryKeys.add(rs.getString("COLUMN_NAME"));
            }
            rs = dbmd.getColumns(null, regDir,  mdmRegistry.getRegName(), "%");
            getColumnsList(columns, rs, primaryKeys);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return columns;
    }

    @Override
    public Boolean checkTableExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Map<String, Object> m = new HashedMap();
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(conn.getCatalog(),mdmRegistry.getRegDir(), mdmRegistry.getRegName(), null);
            while (rs.next()) {
                //判断TABLE、VIEW使用正确
                String tableType = rs.getString("TABLE_TYPE");
                if(("TABLE".equalsIgnoreCase(tableType)&&Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.TABLE)
                        || ("VIEW".equalsIgnoreCase(tableType)&&Integer.parseInt(mdmRegistry.getMetaType())== MdmConstants.VIEW)
                ){
                    return true;
                }
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return false;
    }

    @Override
    public Boolean checkProcedureExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        return null;
    }


    @Override
    public List<Map<String, Object>> getTablesAndViews(SysDatasource sysDatasource,String catalog,String [] types) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(conn.getCatalog(),  null, null, new String[]{"TABLE"});
            while (rs.next()) {
                Map<String, Object> m = new HashedMap();
                log.info(rs.toString());
                String tableName = "";
                tableName = rs.getString("TABLE_NAME");
                String tableCat = rs.getString("TABLE_CAT");
                String tableSchem = rs.getString("TABLE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String tableType = rs.getString("TABLE_TYPE");
                String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                m.put("label", schema+'.'+tableName);
                m.put("tableName", tableName);
                m.put("icon", "iconThree");
                m.put("leaf", true);
                m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
                list.add(m);
            }
            return list;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getProcedures(SysDatasource sysDatasource, String catalog) {
        return null;
    }

    @Override
    public Map<String, Object> getProcedureInfo(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        return null;
    }

    @Override
    public Map excuteByLimit(SysDatasource sysDatasource, String sqlText,int pageSize) {
        return getExcuteMaps(sysDatasource, sqlText, DbType.postgresql,pageSize);
    }

    @Override
    public List<Map<String, Object>> quaryByPage(SysDatasource sysDatasource, String sqlText,int page,int size) {
        return getSqlPage(sysDatasource, sqlText, DbType.postgresql,page,size);
    }

    @Override
    public int getSqlCount(SysDatasource sysDatasource, String sqlText) {
        return getSqlCount(sysDatasource, sqlText, DbType.postgresql);
    }

    @Override
    public String getTableCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistr) {
    	return "postgresql-暂无";
    }

    @Override
    public String getProcedureCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistr) {
        return null;
    }

    @Override
    public String getViewCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        return null;
    }

    @Override
    public Map<String,List> getDatabaseInfos(SysDatasource sysDatasource,MdmRegistry mdmRegistry) {
        return null;
    }

    @Override
    public List<SQLStatement> parseStatements(SysDatasource sysDatasource, String sqlText) {
        return SQLUtils.parseStatements(sqlText, DbType.postgresql);
    }

    @Override
    public SchemaStatVisitor createSchemaStatVisitor(SysDatasource sysDatasource) {
        return SQLUtils.createSchemaStatVisitor( DbType.postgresql);
    }

	@Override
	public Boolean createUser(SysDatasource sysDatasource, String username, String passwd) {
        throw new RuntimeException("功能开发中，请选择其他接口");
	}

	@Override
	public Boolean grantBytable(SysDatasource sysDatasource, String username, String tables, String passwd,boolean flag) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Boolean revokeBytable(SysDatasource sysDatasource, String username, String tables, String passwd,boolean flag) {
		
		throw new RuntimeException("功能开发中，请选择其他接口");
	}
	
	@Override
	public Boolean dropUser(SysDatasource sysDatasource, String username) {
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		String sql ="DROP USER "+username;
		jd.execute(sql);
		return true;
	}

    @Override
    public String createSqlGeneration(DicDataExchange dicDataExchange,SysDatasource sysDatasource) {
        return null;
    }
    
	@Override
	public void createTableTemp(String tableName, SysDatasource sysDatasource) {
		String sql="create table IF NOT EXISTS "+tableName+"_temp like "+tableName;
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN batch_id VARCHAR(36) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN apply_status VARCHAR(10) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN create_type VARCHAR(10) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN creat_by VARCHAR(100) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN create_time VARCHAR(20) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN msg_ VARCHAR(2000) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN keys_ VARCHAR(200) NULL ";
		jd.execute(sql);
	}
}
