package com.dqms.dsc.service;

import java.util.List;
import com.dqms.dsc.domain.DscEncryptionDetail;

/**
 * 加密明细Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscEncryptionDetailService 
{
    /**
     * 查询加密明细
     * 
     * @param encryptionDetailId 加密明细ID
     * @return 加密明细
     */
    public DscEncryptionDetail selectDscEncryptionDetailById(Long encryptionDetailId);

    /**
     * 查询加密明细列表
     * 
     * @param dscEncryptionDetail 加密明细
     * @return 加密明细集合
     */
    public List<DscEncryptionDetail> selectDscEncryptionDetailList(DscEncryptionDetail dscEncryptionDetail);

    /**
     * 新增加密明细
     * 
     * @param dscEncryptionDetail 加密明细
     * @return 结果
     */
    public int insertDscEncryptionDetail(DscEncryptionDetail dscEncryptionDetail);

    /**
     * 修改加密明细
     * 
     * @param dscEncryptionDetail 加密明细
     * @return 结果
     */
    public int updateDscEncryptionDetail(DscEncryptionDetail dscEncryptionDetail);

    /**
     * 批量删除加密明细
     * 
     * @param encryptionDetailIds 需要删除的加密明细ID
     * @return 结果
     */
    public int deleteDscEncryptionDetailByIds(Long[] encryptionDetailIds);

    /**
     * 删除加密明细信息
     * 
     * @param encryptionDetailId 加密明细ID
     * @return 结果
     */
    public int deleteDscEncryptionDetailById(Long encryptionDetailId);
}
