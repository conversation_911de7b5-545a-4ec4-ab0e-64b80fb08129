package com.dqms.mdm.domain.vo;
import java.util.List;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmDataEntityShip;

/**
 * 数据实体对象 mdm_data_entity
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
public class MdmView extends BaseEntity
{
    private static final long serialVersionUID = 1L;
 
    private String name;
    private int zs;
    private int bs;
    private int st;
    private int jb;
    private int ccgc;
    private int bg;
    private int lb;
    private int wlb;
    private int wd;
    private int wwd;
    private int zhs;
    private int wzhs;
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public int getZs() {
		return zs;
	}
	public void setZs(int zs) {
		this.zs = zs;
	}
	public int getBs() {
		return bs;
	}
	public void setBs(int bs) {
		this.bs = bs;
	}
	public int getSt() {
		return st;
	}
	public void setSt(int st) {
		this.st = st;
	}
	public int getJb() {
		return jb;
	}
	public void setJb(int jb) {
		this.jb = jb;
	}
	public int getCcgc() {
		return ccgc;
	}
	public void setCcgc(int ccgc) {
		this.ccgc = ccgc;
	}
	public int getBg() {
		return bg;
	}
	public void setBg(int bg) {
		this.bg = bg;
	}
	public int getLb() {
		return lb;
	}
	public void setLb(int lb) {
		this.lb = lb;
	}
	public int getWlb() {
		return wlb;
	}
	public void setWlb(int wlb) {
		this.wlb = wlb;
	}
	public int getWd() {
		return wd;
	}
	public void setWd(int wd) {
		this.wd = wd;
	}
	public int getWwd() {
		return wwd;
	}
	public void setWwd(int wwd) {
		this.wwd = wwd;
	}
	public int getZhs() {
		return zhs;
	}
	public void setZhs(int zhs) {
		this.zhs = zhs;
	}
	public int getWzhs() {
		return wzhs;
	}
	public void setWzhs(int wzhs) {
		this.wzhs = wzhs;
	}

    
}
