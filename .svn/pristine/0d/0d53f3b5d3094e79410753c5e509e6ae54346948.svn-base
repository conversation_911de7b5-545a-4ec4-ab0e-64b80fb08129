package com.dqms.api.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 数据补录日志对象 api_template_log
 *
 * <AUTHOR>
 * @date 2021-08-26
 */
public class ApiTemplateLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long logId;

    /** 导入操作员 */
    @Excel(name = "导入操作员")
    private String importOperator;

    /** 模板名 */
    @Excel(name = "模板名")
    private String templateName;

    /** 模板id */
    private Long templateId;

    /** excel文件名 */
    @Excel(name = "excel文件名")
    private String exname;

    /** 导入结果 */
    @Excel(name = "导入结果")
    private String importResult;

    /** 导入日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "导入日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date importTime;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public void setImportOperator(String importOperator)
    {
        this.importOperator = importOperator;
    }

    public String getImportOperator()
    {
        return importOperator;
    }
    public void setTemplateName(String templateName)
    {
        this.templateName = templateName;
    }

    public String getTemplateName()
    {
        return templateName;
    }
    public void setTemplateId(Long templateId)
    {
        this.templateId = templateId;
    }

    public Long getTemplateId()
    {
        return templateId;
    }
    public void setExname(String exname)
    {
        this.exname = exname;
    }

    public String getExname()
    {
        return exname;
    }
    public void setImportResult(String importResult)
    {
        this.importResult = importResult;
    }

    public String getImportResult()
    {
        return importResult;
    }
    public void setImportTime(Date importTime)
    {
        this.importTime = importTime;
    }

    public Date getImportTime()
    {
        return importTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("importOperator", getImportOperator())
            .append("templateName", getTemplateName())
            .append("templateId", getTemplateId())
            .append("exname", getExname())
            .append("importResult", getImportResult())
            .append("importTime", getImportTime())
            .toString();
    }
}
