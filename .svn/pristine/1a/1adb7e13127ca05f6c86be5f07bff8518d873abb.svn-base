<template>
	<div id="containerDiv"><div id="container" style="position: relative;"></div></div>
</template>

<style>
	  .g6-minimap{
		position:absolute;width:200px;height:180px;text-align:right;margin-top:-115px
		}
	  .g6-minimap-container {
	    border: 2px solid #009999;position:absolute;width:200px;height:110px;text-align:right
	  }
	  .g6-minimap-viewport {
	    border: 2px solid rgb(25, 128, 255);
	  }
	   #contextMenu {
	    position: absolute;
	    list-style-type: none;
	    padding: 10px 8px;
	    left: -150px;
	    background-color: rgba(255, 255, 255, 0.9);
	    border: 1px solid #e2e2e2;
	    border-radius: 4px;
	    font-size: 12px;
	    color: #545454;
	  }
	  #contextMenu li {
	    cursor: pointer;
			list-style-type:none;
	    list-style: none;
	    margin-left: 0px;
	  }
	  #contextMenu li:hover {
	    color: #aaa;
	  }
	 .g6-tooltip {
	    border: 1px solid #e2e2e2;
	    border-radius: 4px;
	    font-size: 12px;
	    color: #000;
	    background-color: rgba(255, 255, 255, 0.9);
	    padding: 10px 8px;
	    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
	  }
	.detail-table{}
	.detail-table table{
		width:100%; 
		margin-top:10px;
		border-collapse:collapse;
		border-spacing:0;
	}
	.detail-table table td{padding:5px 8px; }
	.detail-table table .odd td{background-color:#f4f5f4;}
</style>
<script>
import G6 from '@antv/g6'
import backgroundImage from '@/assets/images/timg.jpg'
export default {
  name: "applicationView",
  components: {
  },
  data() {
    return {
    backgroundImage:"",
    graphA:undefined
    };
  },
  created() {

  },
  methods: {
  
  },
  destroyed () {
  	  //注意，VUE此处必须清理，否则切换界面会越来越卡
	  this.graphA.clear();
	  this.graphA.destroy();
  },
  mounted() {
  
     const colors = [
	  'rgb(64, 174, 247)',
	  'rgb(108, 207, 169)',
	  'rgb(157, 223, 125)',
	  'rgb(240, 198, 74)',
	  'rgb(221, 158, 97)',
	  'rgb(141, 163, 112)',
	  'rgb(115, 136, 220)',
	  'rgb(133, 88, 219)',
	  'rgb(203, 135, 226)',
	  'rgb(227, 137, 163)',
	];
	// custom the node
	

	// custom the edge
	G6.registerEdge(
	  'running-polyline',
	  {
	    afterDraw(cfg, group) {
	      const shape = group.get('children')[0];
	      const length = shape.getTotalLength();
	      let circleCount = Math.ceil(length / 20);
	      circleCount = circleCount === 0 ? 1 : circleCount;

	      const _loop = function _loop(i) {
	        const delay = Math.random() * 1000;
	        const start = shape.getPoint(i / circleCount);
	        const circle = group.addShape('circle', {
	          attrs: {
	            x: start.x,
	            y: start.y,
	            r: 0.8,
	            fill: '#A0F3AF',
	            shadowColor: '#fff',
	            shadowBlur: 30,
	          },
	          name: 'circle-shape',
	        });
	        circle.animate(
	          ratio => {
	            ratio += i / circleCount;
	            if (ratio > 1) {
	              ratio %= 1;
	            }
	            const tmpPoint = shape.getPoint(ratio);
	            return {
	              x: tmpPoint.x,
	              y: tmpPoint.y,
	            };
	          },
	          {
	            repeat: true,
	            duration: 10 * length,
	            easing: 'easeCubic',
	            delay,
	          }
	        );
	      };

	      for (let i = 0; i < circleCount; i++) {
	        _loop(i);
	      }
	    },
	  },
	  'cubic-horizontal'
	);
	const width = document.getElementById('container').scrollWidth;
	let height = document.body.clientHeight-110;
	const graph = new G6.Graph({
	  container: 'container',
	  width: width,
	  height: height+20,
	  modes: {
	    default: [
	      {
	        type: 'edge-tooltip',
	        formatText: function formatText(model) {
	          const text = model.class;
	          return "暂无";
	        },
	      },
	    ],
	  },
	  defaultNode: {
	    type: 'breath-node',
	    size: 10,
	    color:"green",
	    style: {
	      lineWidth: 0,
	      stroke: 'green',
	      fill: 'rgb(240, 223, 83)'
	    },
	    labelCfg: {
	        style: {
	          fill: '#EEEE00',
	          fontSize: 15
	        },
	        position: 'top',
	      }
	  },
	  defaultEdge: {
	    type: 'running-polyline',
	    size: 2,
	    color: 'rgb(14,142,63)',
	    style: {
	      opacity: 0.4,
	      stroke: 'green',
	      lineAppendWidth: 3,
	    },
	    labelCfg: {
	        style: {
	          fill: '#32CD32',
	          fontSize: 10
	        }
	      }
	  },
	});
	
	const graphSize = [700, 500];
	const nodes = [
	    {"id":"1","label":"数据采集平台","x":100,"y":400,style: {fill: 'blue',"font-color":"blue",color:"blue"}},
	    {"id":"2","label":"元数据中心","x":400,"y":400,style: {fill: 'yellow',"font-color":"yellow",color:"yellow"}},
	    {"id":"3","label":"数据标准平台","x":700,"y":300,style: {fill: 'yellow',"font-color":"yellow",color:"yellow"}},
	    {"id":"4","label":"质量检查平台","x":700,"y":400,style: {fill: 'yellow',"font-color":"yellow",color:"yellow"}},
	    {"id":"5","label":"数据模型管理","x":700,"y":500,style: {fill: 'yellow',"font-color":"yellow",color:"yellow"}},
	    {"id":"6","label":"数据安全管理","x":1000,"y":400,style: {fill: 'yellow',"font-color":"yellow",color:"yellow"}},
	    {"id":"7","label":"可视化平台","x":1300,"y":300,style: {fill: 'blue',"font-color":"blue",color:"blue"}},
	    {"id":"8","label":"数据服务平台","x":1300,"y":400,style: {fill: 'blue',"font-color":"blue",color:"blue"}},
	    {"id":"9","label":"数据交换平台","x":1300,"y":500,style: {fill: 'blue',"font-color":"blue",color:"blue"}},
	    {"id":"10","label":"资产平台","x":700,"y":700,style: {fill: 'red',"font-color":"red",color:"red"}},
//	    {"id":"11","label":"任务中心","x":800,"y":800,style: {fill: 'red',"font-color":"red",color:"red"}},
	    {"id":"12","label":"基础平台&任务中心","x":700,"y":100,style: {fill: 'red',"font-color":"red",color:"red"}}
		];
const edges = [{"source":"1","target":"2"},
			   {"source":"2","target":"3"},
			   {"source":"2","target":"4"},
			   {"source":"2","target":"5"},
			   {"source":"3","target":"6"},
			   {"source":"4","target":"6"},
			   {"source":"5","target":"6"},
			   {"source":"6","target":"7"},
			   {"source":"6","target":"8"},
			   {"source":"6","target":"9"},
			   {"source":"1","target":"10"},
			   {"source":"2","target":"10"},
			   {"source":"3","target":"10"},
			   {"source":"4","target":"10"},
			   {"source":"5","target":"10"},
			   {"source":"6","target":"10"},
			   {"source":"7","target":"10"},
			   {"source":"8","target":"10"},
			   {"source":"9","target":"10"},
			   {"source":"1","target":"12"},
			   {"source":"2","target":"12"},
			   {"source":"3","target":"12"},
			   {"source":"4","target":"12"},
			   {"source":"5","target":"12"},
			   {"source":"6","target":"12"},
			   {"source":"7","target":"12"},
			   {"source":"8","target":"12"},
			   {"source":"9","target":"12"}];
    const data = {
			  nodes: nodes,
			  edges: edges
			};
    graph.data(data);
    graph.render();
	graph.get('container').style.background = '#08284d';
	graph.get('container').style.backgroundImage ='url("'+backgroundImage+'")';
	graph.get('container').style.backgroundSize = 'auto 100%';
		graph.on('node:click', function (evt) {
	    const  target= evt.target;alert("跳转");
    	console.log(target); 
    });
    
    this.graphA=graph;
  }
};
</script>