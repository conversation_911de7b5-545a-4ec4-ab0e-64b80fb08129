import request from '@/utils/request'

// 查询代理机管理列表
export function listAgent(query) {
  return request({
    url: '/basic/agent/list',
    method: 'get',
    params: query
  })
}

// 查询代理机管理详细
export function getAgent(agentId) {
  return request({
    url: '/basic/agent/' + agentId,
    method: 'get'
  })
}

// 查询代理机连接状态
export function getStatus(agentId) {
  return request({
    url: '/basic/agent/getStatus/' + agentId,
    method: 'get'
  })
}

// 新增代理机管理
export function addAgent(data) {
  return request({
    url: '/basic/agent',
    method: 'post',
    data: data
  })
}

// 修改代理机管理
export function updateAgent(data) {
  return request({
    url: '/basic/agent',
    method: 'put',
    data: data
  })
}

// 删除代理机管理
export function delAgent(agentId) {
  return request({
    url: '/basic/agent/' + agentId,
    method: 'delete'
  })
}

// 导出代理机管理
export function exportAgent(query) {
  return request({
    url: '/basic/agent/export',
    method: 'get',
    params: query
  })
}