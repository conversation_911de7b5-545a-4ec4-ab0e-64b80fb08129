package com.dqms.basic.service;

import java.util.List;
import com.dqms.basic.domain.SysAgent;

/**
 * 代理机管理Service接口
 * 
 * <AUTHOR>
 * @date 2021-03-10
 */
public interface ISysAgentService 
{
    /**
     * 查询代理机管理
     * 
     * @param agentId 代理机管理ID
     * @return 代理机管理
     */
    public SysAgent selectSysAgentById(Long agentId);

    /**
     * 查询代理机管理列表
     * 
     * @param sysAgent 代理机管理
     * @return 代理机管理集合
     */
    public List<SysAgent> selectSysAgentList(SysAgent sysAgent);

    /**
     * 新增代理机管理
     * 
     * @param sysAgent 代理机管理
     * @return 结果
     */
    public int insertSysAgent(SysAgent sysAgent);

    /**
     * 修改代理机管理
     * 
     * @param sysAgent 代理机管理
     * @return 结果
     */
    public int updateSysAgent(SysAgent sysAgent);

    /**
     * 批量删除代理机管理
     * 
     * @param agentIds 需要删除的代理机管理ID
     * @return 结果
     */
    public int deleteSysAgentByIds(Long[] agentIds);

    /**
     * 删除代理机管理信息
     * 
     * @param agentId 代理机管理ID
     * @return 结果
     */
    public int deleteSysAgentById(Long agentId);
}
