import request from '@/utils/request'

// 查询文件补录配置列表
export function listDicFileDefine(query) {
  return request({
    url: '/dic/dicFileDefine/list',
    method: 'get',
    params: query
  })
}

export function listDicFileDefineAll(query) {
  return request({
    url: '/dic/dicFileDefine/listAll',
    method: 'get',
    params: query
  })
}

// 查询文件补录配置详细
export function getDicFileDefine(fileDefineId) {
  return request({
    url: '/dic/dicFileDefine/' + fileDefineId,
    method: 'get'
  })
}

// 新增文件补录配置
export function addDicFileDefine(data) {
  return request({
    url: '/dic/dicFileDefine',
    method: 'post',
    data: data
  })
}

// 修改文件补录配置
export function updateDicFileDefine(data) {
  return request({
    url: '/dic/dicFileDefine',
    method: 'put',
    data: data
  })
}

// 删除文件补录配置
export function delDicFileDefine(fileDefineId) {
  return request({
    url: '/dic/dicFileDefine/' + fileDefineId,
    method: 'delete'
  })
}

// 导出文件补录配置
export function exportDicFileDefine(query) {
  return request({
    url: '/dic/dicFileDefine/export',
    method: 'get',
    params: query
  })
}