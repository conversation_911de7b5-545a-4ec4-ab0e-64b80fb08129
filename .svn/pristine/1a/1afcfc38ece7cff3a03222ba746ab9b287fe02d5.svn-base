<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>com.dqms</groupId>
    <artifactId>dqms</artifactId>
    <version>1.0.0</version>

    <name>dqms</name>
    <description>数据治理平台</description>

    <properties>
        <dqms.version>1.0.0</dqms.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.4</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>2.9.2</swagger.version>
		<kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.3.0</pagehelper.boot.version>
        <fastjson.version>1.2.83</fastjson.version>
        <oshi.version>5.3.6</oshi.version>
        <jna.version>5.6.0</jna.version>
        <commons.io.version>2.5</commons.io.version>
        <commons.fileupload.version>1.3.3</commons.fileupload.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>1.7</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <elasticsearch>7.7.0</elasticsearch>
        <mysql.version>8.0.23</mysql.version>
        <log4j.version>2.17.0</log4j.version>
        <hadoop.version>3.1.1.3.1.4.0-315</hadoop.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

<!--            <dependency>-->
<!--                <groupId>mysql</groupId>-->
<!--                <artifactId>mysql-connector-java</artifactId>-->
<!--                <version>${mysql.version}</version>-->
<!--            </dependency>-->
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.2.13.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- swagger2-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- swagger2-UI-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!--文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!--velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!--验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.dqms</groupId>
                <artifactId>dqms-quartz</artifactId>
                <version>${dqms.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.dqms</groupId>
                <artifactId>dqms-generator</artifactId>
                <version>${dqms.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.dqms</groupId>
                <artifactId>dqms-framework</artifactId>
                <version>${dqms.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.dqms</groupId>
                <artifactId>dqms-system</artifactId>
                <version>${dqms.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.dqms</groupId>
                <artifactId>dqms-common</artifactId>
                <version>${dqms.version}</version>
            </dependency>

            <!-- 工作流-->
            <dependency>
                <groupId>com.dqms</groupId>
                <artifactId>dqms-activiti</artifactId>
                <version>${dqms.version}</version>
            </dependency>

            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>1.2.17</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>dqms-admin</module>
        <module>dqms-framework</module>
        <module>dqms-system</module>
        <module>dqms-quartz</module>
        <module>dqms-generator</module>
        <module>dqms-common</module>
		<module>dqms-activiti</module>
    </modules>
    <packaging>pom</packaging>


    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>


<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>public</id>-->
<!--            <name>aliyun nexus</name>-->
<!--            &lt;!&ndash;            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>&ndash;&gt;-->
<!--            <url>https://repository.cloudera.com/artifactory/cloudera-repos</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--        </repository>-->
<!--    </repositories>-->

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profileActive>local</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>docker</id>
            <properties>
                <profileActive>docker</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>lcdev</id>
            <properties>
                <profileActive>lcdev</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>lcuat</id>
            <properties>
                <profileActive>lcuat</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>lcuatapi</id>
            <properties>
                <profileActive>lcuatapi</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>lcprod</id>
            <properties>
                <profileActive>lcprod</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>lcprodapi</id>
            <properties>
                <profileActive>lcprodapi</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>gjhbdev</id>
            <properties>
                <profileActive>gjhbdev</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>junit</id>
            <properties>
                <profileActive>junit</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>gfqh</id>
            <properties>
                <profileActive>gfqh</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>gfqhapi</id>
            <properties>
                <profileActive>gfqhapi</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        <profile>
            <id>paqh</id>
            <properties>
                <profileActive>paqh</profileActive>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
    </profiles>
    <repositories>
                <repository>
                    <id>public</id>
                    <name>aliyun nexus</name>
        <!--            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>-->
                    <url>https://repository.cloudera.com/artifactory/cloudera-repos</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
        <repository>
            <id>nexus</id>
            <name>local private nexus</name>
            <url> https://repo.hortonworks.com/content/repositories/releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

</project>
