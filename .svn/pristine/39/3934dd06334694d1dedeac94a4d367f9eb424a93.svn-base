package com.dqms.dqm.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dqm.domain.DqmValidationClass;

/**
 * 检查分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-28
 */
public interface DqmValidationClassMapper 
{
    /**
     * 查询检查分类
     * 
     * @param validationClassId 检查分类ID
     * @return 检查分类
     */
    public DqmValidationClass selectDqmValidationClassById(Long validationClassId);
    public DqmValidationClass selectDqmValidationClassByName(DqmValidationClass dqmValidationClass);

    /**
     * 查询检查分类列表
     * 
     * @param dqmValidationClass 检查分类
     * @return 检查分类集合
     */
    public List<DqmValidationClass> selectDqmValidationClassList(DqmValidationClass dqmValidationClass);

    /**
     * 新增检查分类
     * 
     * @param dqmValidationClass 检查分类
     * @return 结果
     */
    public int insertDqmValidationClass(DqmValidationClass dqmValidationClass);

    /**
     * 修改检查分类
     * 
     * @param dqmValidationClass 检查分类
     * @return 结果
     */
    public int updateDqmValidationClass(DqmValidationClass dqmValidationClass);

    /**
     * 删除检查分类
     * 
     * @param validationClassId 检查分类ID
     * @return 结果
     */
    public int deleteDqmValidationClassById(Long validationClassId);

    /**
     * 批量删除检查分类
     * 
     * @param validationClassIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDqmValidationClassByIds(Long[] validationClassIds);
    /**
     * 根据ID查询所有子任务分类
     *
     * @param indexClassId 任务分类ID
     * @return 任务分类列表
     */
    public List<DqmValidationClass> selectChildrenClassById(Long validationClassId);
    public int updateClassChildren(@Param("dqmValidationClass") List<DqmValidationClass> dqmValidationClass);
    public int updateClassNameFullChildren(@Param("dqmValidationClass") List<DqmValidationClass> dqmValidationClass);
}
