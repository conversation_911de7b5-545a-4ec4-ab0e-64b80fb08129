import request from '@/utils/request'

// 查询接口权限列表
export function listApiDefineSystem(query) {
  return request({
    url: '/api/apiDefineSystem/list',
    method: 'get',
    params: query
  })
}
export function listApiDefineSystemAll(query) {
  return request({
    url: '/api/apiDefineSystem/listAll',
    method: 'get',
    params: query
  })
}
// 查询接口权限详细
export function getApiDefineSystem(defineId) {
  return request({
    url: '/api/apiDefineSystem/' + defineId,
    method: 'get'
  })
}

// 新增接口权限
export function addApiDefineSystem(data) {
  return request({
    url: '/api/apiDefineSystem',
    method: 'post',
    data: data
  })
}

// 修改接口权限
export function updateApiDefineSystem(data) {
  return request({
    url: '/api/apiDefineSystem',
    method: 'put',
    data: data
  })
}

// 删除接口权限
export function delApiDefineSystem(data) {
  return request({
    url: '/api/apiDefineSystem',
    method: 'delete',
    data: data
  })
}

// 导出接口权限
export function exportApiDefineSystem(query) {
  return request({
    url: '/api/apiDefineSystem/export',
    method: 'get',
    params: query
  })
}