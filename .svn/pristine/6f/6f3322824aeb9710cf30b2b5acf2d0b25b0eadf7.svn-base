package com.dqms.task.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.DictUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.task.domain.EtlTaskCalendarClass;
import com.dqms.task.domain.EtlTaskCalendarDetail;
import com.dqms.task.domain.vo.EtlTaskCalendarDetailVo;
import com.dqms.task.mapper.EtlTaskCalendarClassMapper;
import com.dqms.task.mapper.EtlTaskCalendarDetailMapper;
import com.dqms.task.service.IEtlTaskCalendarDetailService;

/**
 * 调度日历明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-01
 */
@Service
public class EtlTaskCalendarDetailServiceImpl implements IEtlTaskCalendarDetailService
{
    @Autowired
    private EtlTaskCalendarDetailMapper etlTaskCalendarDetailMapper;
    
    @Autowired
    private EtlTaskCalendarClassMapper etlTaskCalendarClassMapper;

    /**
     * 查询调度日历明细
     *
     * @param taskCalendarDetailId 调度日历明细ID
     * @return 调度日历明细
     */
    @Override
    public EtlTaskCalendarDetail selectEtlTaskCalendarDetailById(Long taskCalendarDetailId)
    {
        return etlTaskCalendarDetailMapper.selectEtlTaskCalendarDetailById(taskCalendarDetailId);
    }

    /**
     * 查询调度日历明细列表
     *
     * @param etlTaskCalendarDetail 调度日历明细
     * @return 调度日历明细
     */
    @Override
    public List<EtlTaskCalendarDetail> selectEtlTaskCalendarDetailList(EtlTaskCalendarDetail etlTaskCalendarDetail)
    {
        return etlTaskCalendarDetailMapper.selectEtlTaskCalendarDetailList(etlTaskCalendarDetail);
    }
    
    @Override
    public List<EtlTaskCalendarDetailVo> selectEtlTaskCalendarDetailVoList(EtlTaskCalendarDetail etlTaskCalendarDetail)
    {
        return etlTaskCalendarDetailMapper.selectEtlTaskCalendarDetailVoList(etlTaskCalendarDetail);
    }


    /**
     * 新增调度日历明细
     *
     * @param etlTaskCalendarDetail 调度日历明细
     * @return 结果
     */
    @Override
    public int insertEtlTaskCalendarDetail(EtlTaskCalendarDetail etlTaskCalendarDetail)
    {
        etlTaskCalendarDetail.setCreateTime(DateUtils.getNowDate());
        return etlTaskCalendarDetailMapper.insertEtlTaskCalendarDetail(etlTaskCalendarDetail);
    }

    /**
     * 修改调度日历明细
     *
     * @param etlTaskCalendarDetail 调度日历明细
     * @return 结果
     */
    @Override
    public int updateEtlTaskCalendarDetail(EtlTaskCalendarDetail etlTaskCalendarDetail)
    {
        etlTaskCalendarDetail.setUpdateTime(DateUtils.getNowDate());
        return etlTaskCalendarDetailMapper.updateEtlTaskCalendarDetail(etlTaskCalendarDetail);
    }

    /**
     * 批量删除调度日历明细
     *
     * @param taskCalendarDetailIds 需要删除的调度日历明细ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskCalendarDetailByIds(Long[] taskCalendarDetailIds)
    {
        return etlTaskCalendarDetailMapper.deleteEtlTaskCalendarDetailByIds(taskCalendarDetailIds);
    }

    /**
     * 删除调度日历明细信息
     *
     * @param taskCalendarDetailId 调度日历明细ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskCalendarDetailById(Long taskCalendarDetailId)
    {
        return etlTaskCalendarDetailMapper.deleteEtlTaskCalendarDetailById(taskCalendarDetailId);
    }
    
    /**
     * 查询当前可执行日期
     * 
     * @param TaskCalendarClassId 调度日历
     * @return LoadDate
     */
    @Override
    public EtlTaskCalendarDetail selectMinLoadDateByTaskCalendarClassId(Long taskCalendarClassId)
    {
        return etlTaskCalendarDetailMapper.selectMinLoadDateByTaskCalendarClassId(taskCalendarClassId);
    }
    
    /**
     * 导入调度日历明细信息
     * 
     * @param etlTaskCalendarDetailList 调度日历明细信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importEtlTaskCalendarDetail(List<EtlTaskCalendarDetailVo> etlTaskCalendarDetailList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(etlTaskCalendarDetailList) || etlTaskCalendarDetailList.size() == 0)
        {
            throw new CustomException("导入调度日历明细数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (EtlTaskCalendarDetailVo vo : etlTaskCalendarDetailList)
        {
            try
            {
                // 验证是否存在这个用户
            	EtlTaskCalendarDetail v = etlTaskCalendarDetailMapper.selectEtlTaskCalendarDetailByLoadDate(vo.getTaskCalendarClassId(),vo.getLoadDate());
            	EtlTaskCalendarDetail t =new EtlTaskCalendarDetail();
            	BeanUtils.copyBeanProp(t, vo);
            	String status=DictUtils.getDictValue("etl_task_calendar_status", vo.getStatusName());
            	if(StringUtils.isEmpty(status)){
            		failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、" + vo.getLoadDate() + " 状态名称未定义");
                    continue;
            	}
            	t.setStatus(status);
            	EtlTaskCalendarClass etlTaskCalendarClass = etlTaskCalendarClassMapper.selectEtlTaskCalendarClassByName(vo.getTaskCalendarClassName());
            	if(StringUtils.isNull(etlTaskCalendarClass)){
            		failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskCalendarClassName() + " 分类名称未定义");
                    continue;
            	}
            	t.setTaskCalendarClassId(etlTaskCalendarClass.getTaskCalendarClassId());
            	
                if (StringUtils.isNull(v))
                {
                    this.insertEtlTaskCalendarDetail(t);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、 " + vo.getLoadDate() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                	t.setTaskCalendarDetailId(v.getTaskCalendarDetailId());
                    this.updateEtlTaskCalendarDetail(t);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、 " + vo.getLoadDate() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getLoadDate() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + vo.getLoadDate() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
