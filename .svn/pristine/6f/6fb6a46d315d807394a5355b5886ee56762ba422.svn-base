package com.dqms.task.job.executor;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.service.IMdmRegistryService;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.job.MdmRunThread;
import com.dqms.task.service.IEtlTaskInstanceService;
import com.dqms.task.service.ITaskExecutor;
import com.dqms.utils.ThreadPoolUtils;

public class ETLMdmExecutor implements ITaskExecutor {
	private static final Logger log = LoggerFactory.getLogger(ETLMdmExecutor.class);
	
	public volatile static AtomicInteger flag=new AtomicInteger(0);
	
	public String execute(EtlTaskInstance ti) {
		int page =1;
		int allnum=0;
		List<MdmRegistry> list=getList(ti,page++);
		EtlTaskInstance etlTaskInstance = new EtlTaskInstance();
		etlTaskInstance.setTaskInstanceId(ti.getTaskInstanceId());
		etlTaskInstance.setBatchId(ti.getBatchId());
		if(list!=null&&list.size()>0) {
			while(list!=null&&list.size()>0) {
				allnum+=list.size();
				etlTaskInstance.setMsg("第"+(page-1)+"批次共"+list.size()+"个元数据准备执行！");
				SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
				for(MdmRegistry mdm :list) {
					flag.addAndGet(1);
					ThreadPoolUtils.addTask(-3L,0,new MdmRunThread(etlTaskInstance,mdm));
				}
				list=getList(ti,page++);
			}
			while(flag.get()!=0) {
				try {
					Thread.sleep(10000);
					flag.get();
					etlTaskInstance.setMsg("元数据共"+allnum+"条，还有"+flag.get()+"条未完成");
					SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}

		}else {
			log.info("【"+etlTaskInstance.getTaskInstanceId()+"】"+"无可执行子任务！");
			etlTaskInstance.setMsg("无可执行子任务！");
			SpringUtils.getBean(IEtlTaskInstanceService.class).updateEtlTaskInstance(etlTaskInstance);
		}
		return null;
	}
	
	public List<MdmRegistry> getList(EtlTaskInstance ti,int page) {
		List<MdmRegistry> list = null;
		if(ti.getEtlTask().getDatasourceId()==null) {
			if(page!=1) {
				return list;
			}
			list = SpringUtils.getBean(IMdmRegistryService.class).selectMdmRegistryListByTaskId(ti.getTaskId());
		}else {
			MdmRegistry mdmRegistry = new MdmRegistry();
			mdmRegistry.setDatasourceId(ti.getEtlTask().getDatasourceId());
			list = SpringUtils.getBean(IMdmRegistryService.class).selectMdmRegistryListByPage(ti.getEtlTask().getDatasourceId(),(page-1)*100,100);
		}
		return list;
	}
}
