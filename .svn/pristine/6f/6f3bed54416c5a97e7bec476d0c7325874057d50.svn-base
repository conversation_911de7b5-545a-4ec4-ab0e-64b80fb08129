package com.dqms.mdm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 元数据快速注册日志对象 mdm_collect_fast_log
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
public class MdmCollectFastLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据源 */
    @Excel(name = "数据源")
    private Long datasourceId;

    /** 所属系统 */
    @Excel(name = "所属系统")
    private Long systemId;

    /** 所属分层 */
    @Excel(name = "所属分层")
    private Long layerId;

    /** 所属主题 */
    @Excel(name = "所属主题")
    private Long themeId;

    /** 元类型 */
    @Excel(name = "元类型")
    private String metaType;

    /** 采集方式 */
    @Excel(name = "采集方式")
    private String acqMode;

    /** 采集结果状态 */
    @Excel(name = "采集结果状态")
    private String acqResultState;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMsg;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Excel(name = "采集结果说明")
    private String acqDesc;

    private String datasourceName;
    private String systemName;
    private String themeName;
    private String layerName;
    private String dsType;

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public String getLayerName() {
        return layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    public String getDsType() {
        return dsType;
    }

    public void setDsType(String dsType) {
        this.dsType = dsType;
    }

    public String getAcqDesc() {
        return acqDesc;
    }

    public void setAcqDesc(String acqDesc) {
        this.acqDesc = acqDesc;
    }

    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setLayerId(Long layerId)
    {
        this.layerId = layerId;
    }

    public Long getLayerId()
    {
        return layerId;
    }
    public void setThemeId(Long themeId)
    {
        this.themeId = themeId;
    }

    public Long getThemeId()
    {
        return themeId;
    }
    public void setMetaType(String metaType)
    {
        this.metaType = metaType;
    }

    public String getMetaType()
    {
        return metaType;
    }
    public void setAcqMode(String acqMode)
    {
        this.acqMode = acqMode;
    }

    public String getAcqMode()
    {
        return acqMode;
    }
    public void setAcqResultState(String acqResultState)
    {
        this.acqResultState = acqResultState;
    }

    public String getAcqResultState()
    {
        return acqResultState;
    }
    public void setErrorMsg(String errorMsg)
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg()
    {
        return errorMsg;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("datasourceId", getDatasourceId())
            .append("systemId", getSystemId())
            .append("layerId", getLayerId())
            .append("themeId", getThemeId())
            .append("metaType", getMetaType())
            .append("acqMode", getAcqMode())
            .append("acqResultState", getAcqResultState())
            .append("errorMsg", getErrorMsg())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("remark", getRemark())
            .toString();
    }
}
