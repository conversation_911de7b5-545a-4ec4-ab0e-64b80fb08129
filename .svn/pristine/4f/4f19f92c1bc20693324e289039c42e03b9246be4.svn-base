package com.dqms.task.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.task.domain.EtlTaskCalendarClass;
import com.dqms.task.domain.EtlTaskClass;
import com.dqms.task.service.IEtlTaskCalendarClassService;
import com.dqms.common.utils.poi.ExcelUtil;

/**
 * 调度日历管理Controller
 *
 * <AUTHOR>
 * @date 2021-04-01
 */
@RestController
@RequestMapping("/task/taskCalendarClass")
public class EtlTaskCalendarClassController extends BaseController
{
    @Autowired
    private IEtlTaskCalendarClassService etlTaskCalendarClassService;

    /**
     * 查询调度日历管理列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskCalendarClass:list')")
    @GetMapping("/list")
    public AjaxResult list(EtlTaskCalendarClass etlTaskCalendarClass)
    {
        List<EtlTaskCalendarClass> list = etlTaskCalendarClassService.selectEtlTaskCalendarClassList(etlTaskCalendarClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出调度日历管理列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskCalendarClass:export')")
    @Log(title = "调度日历管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EtlTaskCalendarClass etlTaskCalendarClass)
    {
        List<EtlTaskCalendarClass> list = etlTaskCalendarClassService.selectEtlTaskCalendarClassList(etlTaskCalendarClass);
        ExcelUtil<EtlTaskCalendarClass> util = new ExcelUtil<EtlTaskCalendarClass>(EtlTaskCalendarClass.class);
        return util.exportExcel(list, "taskCalendarClass");
    }

    /**
     * 获取调度日历管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('task:taskCalendarClass:query')")
    @GetMapping(value = "/{taskCalendarClassId}")
    public AjaxResult getInfo(@PathVariable("taskCalendarClassId") Long taskCalendarClassId)
    {
        return AjaxResult.success(etlTaskCalendarClassService.selectEtlTaskCalendarClassById(taskCalendarClassId));
    }

    /**
     * 新增调度日历管理
     */
    @PreAuthorize("@ss.hasPermi('task:taskCalendarClass:add')")
    @Log(title = "调度日历管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EtlTaskCalendarClass etlTaskCalendarClass)
    {
        return toAjax(etlTaskCalendarClassService.insertEtlTaskCalendarClass(etlTaskCalendarClass));
    }

    /**
     * 修改调度日历管理
     */
    @PreAuthorize("@ss.hasPermi('task:taskCalendarClass:edit')")
    @Log(title = "调度日历管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EtlTaskCalendarClass etlTaskCalendarClass)
    {
        return toAjax(etlTaskCalendarClassService.updateEtlTaskCalendarClass(etlTaskCalendarClass));
    }

    /**
     * 删除调度日历管理
     */
    @PreAuthorize("@ss.hasPermi('task:taskCalendarClass:remove')")
    @Log(title = "调度日历管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskCalendarClassIds}")
    public AjaxResult remove(@PathVariable Long[] taskCalendarClassIds)
    {
        return toAjax(etlTaskCalendarClassService.deleteEtlTaskCalendarClassByIds(taskCalendarClassIds));
    }
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(EtlTaskCalendarClass etlTaskCalendarClass)
    {
        List<EtlTaskCalendarClass> etlTaskCalendarClasss = etlTaskCalendarClassService.selectEtlTaskCalendarClassList(etlTaskCalendarClass);
        return AjaxResult.success(etlTaskCalendarClassService.buildEtlTaskCalendarClassTreeSelect(etlTaskCalendarClasss));
    }
}
