package com.dqms.dam.service.impl;

import java.util.List;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dam.mapper.DamAssetsEvaluateMapper;
import com.dqms.dam.domain.DamAssetsEvaluate;
import com.dqms.dam.service.IDamAssetsEvaluateService;
import com.dqms.framework.web.service.TokenService;

/**
 * 资产评价Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
@Service
public class DamAssetsEvaluateServiceImpl implements IDamAssetsEvaluateService
{
    @Autowired
    private DamAssetsEvaluateMapper damAssetsEvaluateMapper;

    @Autowired
    private TokenService tokenService;
    /**
     * 查询资产评价
     *
     * @param assetsEvaluateId 资产评价ID
     * @return 资产评价
     */
    @Override
    public DamAssetsEvaluate selectDamAssetsEvaluateById(Long assetsEvaluateId)
    {
        return damAssetsEvaluateMapper.selectDamAssetsEvaluateById(assetsEvaluateId);
    }

    /**
     * 查询资产评价列表
     *
     * @param damAssetsEvaluate 资产评价
     * @return 资产评价
     */
    @Override
    public List<DamAssetsEvaluate> selectDamAssetsEvaluateList(DamAssetsEvaluate damAssetsEvaluate)
    {
        return damAssetsEvaluateMapper.selectDamAssetsEvaluateList(damAssetsEvaluate);
    }

    /**
     * 新增资产评价
     *
     * @param damAssetsEvaluate 资产评价
     * @return 结果
     */
    @Override
    public int insertDamAssetsEvaluate(DamAssetsEvaluate damAssetsEvaluate)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	damAssetsEvaluate.setCreateTime(DateUtils.getNowDate());
    	damAssetsEvaluate.setCreateId(loginUser.getUser().getUserId());
    	damAssetsEvaluate.setCreateBy(loginUser.getUser().getNickName());
    	int i = damAssetsEvaluateMapper.insertDamAssetsEvaluate(damAssetsEvaluate);
    	damAssetsEvaluateMapper.updateDamAssetsGrade(damAssetsEvaluate);
        return i;
    }

    /**
     * 修改资产评价
     *
     * @param damAssetsEvaluate 资产评价
     * @return 结果
     */
    @Override
    public int updateDamAssetsEvaluate(DamAssetsEvaluate damAssetsEvaluate)
    {
        return damAssetsEvaluateMapper.updateDamAssetsEvaluate(damAssetsEvaluate);
    }

    /**
     * 批量删除资产评价
     *
     * @param assetsEvaluateIds 需要删除的资产评价ID
     * @return 结果
     */
    @Override
    public int deleteDamAssetsEvaluateByIds(Long[] assetsEvaluateIds)
    {
        return damAssetsEvaluateMapper.deleteDamAssetsEvaluateByIds(assetsEvaluateIds);
    }

    /**
     * 删除资产评价信息
     *
     * @param assetsEvaluateId 资产评价ID
     * @return 结果
     */
    @Override
    public int deleteDamAssetsEvaluateById(Long assetsEvaluateId)
    {
        return damAssetsEvaluateMapper.deleteDamAssetsEvaluateById(assetsEvaluateId);
    }
}
