package com.dqms.dam.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Treeselect树结构实体类
 *
*/
public class DamAssetsClassTreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DamAssetsClassTreeSelect> children;

    public DamAssetsClassTreeSelect()
    {

    }

    public DamAssetsClassTreeSelect(DamAssetsClass damAssetsClass)
    {
        this.id = damAssetsClass.getAssetsClassId();
        this.label = damAssetsClass.getClassName();
        this.children = damAssetsClass.getChildren().stream().map(DamAssetsClassTreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<DamAssetsClassTreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<DamAssetsClassTreeSelect> children)
    {
        this.children = children;
    }
}
