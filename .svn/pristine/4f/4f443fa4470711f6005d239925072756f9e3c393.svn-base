package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmDimensionDetailRel;

/**
 * 维度字典关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmDimensionDetailRelMapper 
{
    /**
     * 查询维度字典关系
     * 
     * @param souDimensionDetailId 维度字典关系ID
     * @return 维度字典关系
     */
    public DsmDimensionDetailRel selectDsmDimensionDetailRelById(Long souDimensionDetailId);

    /**
     * 查询维度字典关系列表
     * 
     * @param dsmDimensionDetailRel 维度字典关系
     * @return 维度字典关系集合
     */
    public List<DsmDimensionDetailRel> selectDsmDimensionDetailRelList(DsmDimensionDetailRel dsmDimensionDetailRel);
    public List<DsmDimensionDetailRel> selectDsmDimensionDetailUnRelList(DsmDimensionDetailRel dsmDimensionDetailRel);
    public List<DsmDimensionDetailRel> selectDsmDimensionDetailRelList_sou(DsmDimensionDetailRel dsmDimensionDetailRel);

    /**
     * 新增维度字典关系
     * 
     * @param dsmDimensionDetailRel 维度字典关系
     * @return 结果
     */
    public int insertDsmDimensionDetailRel(DsmDimensionDetailRel dsmDimensionDetailRel);

    /**
     * 修改维度字典关系
     * 
     * @param dsmDimensionDetailRel 维度字典关系
     * @return 结果
     */
    public int updateDsmDimensionDetailRel(DsmDimensionDetailRel dsmDimensionDetailRel);

    /**
     * 删除维度字典关系
     * 
     * @param souDimensionDetailId 维度字典关系ID
     * @return 结果
     */
    public int deleteDsmDimensionDetailRelById(Long souDimensionDetailId);
    public int deleteDsmDimensionDetailRelByPk(DsmDimensionDetailRel dsmDimensionDetailRel);

    /**
     * 批量删除维度字典关系
     * 
     * @param souDimensionDetailIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmDimensionDetailRelByIds(Long[] souDimensionDetailIds);
}
