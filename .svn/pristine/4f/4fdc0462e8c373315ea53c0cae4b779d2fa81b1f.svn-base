package com.dqms.dsm.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dsm.domain.DsmModelEntityClass;
import com.dqms.dsm.domain.DsmStandardClass;

/**
 * 模型主题Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-11-23
 */
public interface DsmModelEntityClassMapper 
{
    /**
     * 查询模型主题
     * 
     * @param modelEntityClassId 模型主题ID
     * @return 模型主题
     */
    public DsmModelEntityClass selectDsmModelEntityClassById(Long modelEntityClassId);

    /**
     * 查询模型主题列表
     * 
     * @param dsmModelEntityClass 模型主题
     * @return 模型主题集合
     */
    public List<DsmModelEntityClass> selectDsmModelEntityClassList(DsmModelEntityClass dsmModelEntityClass);

    /**
     * 新增模型主题
     * 
     * @param dsmModelEntityClass 模型主题
     * @return 结果
     */
    public int insertDsmModelEntityClass(DsmModelEntityClass dsmModelEntityClass);

    /**
     * 修改模型主题
     * 
     * @param dsmModelEntityClass 模型主题
     * @return 结果
     */
    public int updateDsmModelEntityClass(DsmModelEntityClass dsmModelEntityClass);

    /**
     * 删除模型主题
     * 
     * @param modelEntityClassId 模型主题ID
     * @return 结果
     */
    public int deleteDsmModelEntityClassById(Long modelEntityClassId);

    /**
     * 批量删除模型主题
     * 
     * @param modelEntityClassIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmModelEntityClassByIds(Long[] modelEntityClassIds);
    
    public DsmModelEntityClass selectDsmModelEntityClassByName(String modelEntityClassName);
    /**
     * 根据ID查询所有子任务分类
     *
     * @param deptId 任务分类ID
     * @return 任务分类列表
     */
    public List<DsmModelEntityClass> selectChildrenClassById(Long modelEntityClassId);
    public int updateClassChildren(@Param("dsmModelEntityClass") List<DsmModelEntityClass> dsmModelEntityClass);
    public int updateClassNameFullChildren(@Param("dsmModelEntityClass") List<DsmModelEntityClass> dsmModelEntityClass);
}
