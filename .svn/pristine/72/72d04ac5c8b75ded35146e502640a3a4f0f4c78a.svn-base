package com.dqms.task.service;

import java.util.List;
import java.util.Map;

import com.dqms.task.domain.EtlTaskInstanceHis;

/**
 * 任务监控历史Service接口
 * 
 * <AUTHOR>
 * @date 2021-03-15
 */
public interface IEtlTaskInstanceHisService 
{
    /**
     * 查询任务监控历史
     * 
     * @param taskInstanceHisId 任务监控历史ID
     * @return 任务监控历史
     */
    public EtlTaskInstanceHis selectEtlTaskInstanceHisById(Long taskInstanceHisId);

    /**
     * 查询任务监控历史列表
     * 
     * @param etlTaskInstanceHis 任务监控历史
     * @return 任务监控历史集合
     */
    public List<EtlTaskInstanceHis> selectEtlTaskInstanceHisList(EtlTaskInstanceHis etlTaskInstanceHis);

    /**
     * 新增任务监控历史
     * 
     * @param etlTaskInstanceHis 任务监控历史
     * @return 结果
     */
    public int insertEtlTaskInstanceHis(EtlTaskInstanceHis etlTaskInstanceHis);

    /**
     * 修改任务监控历史
     * 
     * @param etlTaskInstanceHis 任务监控历史
     * @return 结果
     */
    public int updateEtlTaskInstanceHis(EtlTaskInstanceHis etlTaskInstanceHis);

    /**
     * 批量删除任务监控历史
     * 
     * @param taskInstanceHisIds 需要删除的任务监控历史ID
     * @return 结果
     */
    public int deleteEtlTaskInstanceHisByIds(Long[] taskInstanceHisIds);

    /**
     * 删除任务监控历史信息
     * 
     * @param taskInstanceHisId 任务监控历史ID
     * @return 结果
     */
    public int deleteEtlTaskInstanceHisById(Long taskInstanceHisId);

    public Map<String , Object> getAssets();
}
