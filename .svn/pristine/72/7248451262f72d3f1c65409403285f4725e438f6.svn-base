package com.dqms.mdm.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.dqms.api.domain.ApiTableManager;
import com.dqms.api.mapper.ApiTableManagerMapper;
import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmDataEntityShip;
import com.dqms.mdm.domain.MdmDataEntityShipAnalysisRel;
import com.dqms.mdm.domain.vo.MdmDataEntityAnalyse;
import com.dqms.mdm.domain.vo.MdmDataEntityShipAnalyse;
import com.dqms.mdm.domain.vo.MdmDataEntityShipVo;
import com.dqms.mdm.domain.vo.MdmDataEntityVo;
import com.dqms.mdm.mapper.MdmDataEntityMapper;
import com.dqms.mdm.mapper.MdmDataEntityPropMapper;
import com.dqms.mdm.mapper.MdmDataEntityShipAnalysisRelMapper;
import com.dqms.mdm.mapper.MdmDataEntityShipMapper;
import com.dqms.mdm.service.IMdmDataEntityShipService;
import com.dqms.utils.StringUtils;
import com.dqms.utils.sql.GbColumnVo;
import com.dqms.utils.sql.GbHiveSchemaStatVisitor;
import com.dqms.utils.sql.GbOracleSchemaStatVisitor;
import com.dqms.utils.sql.GbSQLUtils;
import com.dqms.utils.sql.GbSchemaStatVisitor;
import com.dqms.utils.sql.GbSelectVo;
import com.dqms.utils.sql.GbSqlSchemaStatVisitor;
import com.dqms.utils.sql.GbTableStat;
import com.dqms.utils.sql.GbTableStat.Relationship;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据实体关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@Slf4j
@Service
public class MdmDataEntityShipServiceImpl implements IMdmDataEntityShipService
{
    @Autowired
    private MdmDataEntityShipMapper mdmDataEntityShipMapper;

    @Autowired
    private MdmDataEntityMapper mdmDataEntityMapper;

    @Autowired
    private MdmDataEntityPropMapper mdmDataEntityPropMapper;

	@Autowired
	private SysSystemMapper sysSystemMapper;

    @Autowired
    private MdmDataEntityShipAnalysisRelMapper mdmDataEntityShipAnalysisRelMapper;
    
    @Autowired
    private ApiTableManagerMapper apiTableManagerMapper;

    /**
     * 查询数据实体关系
     *
     * @param shipId 数据实体关系ID
     * @return 数据实体关系
     */
    @Override
    public MdmDataEntityShip selectMdmDataEntityShipById(Long shipId)
    {
        return mdmDataEntityShipMapper.selectMdmDataEntityShipById(shipId);
    }
    @Override
    public MdmDataEntityShip selectMdmDataEntityShipByRel(MdmDataEntityShip mdmDataEntityShip)
    {
        return mdmDataEntityShipMapper.selectMdmDataEntityShipByRel(mdmDataEntityShip);
    }
    /**
     * 查询数据实体关系列表
     *
     * @param mdmDataEntityShip 数据实体关系
     * @return 数据实体关系
     */
    @Override
    public List<MdmDataEntityShip> selectMdmDataEntityShipList(MdmDataEntityShip mdmDataEntityShip)
    {
    	if(mdmDataEntityShip.getTriggerType().equals(MdmConstants.ACQ_MODE_SD)){
    		return mdmDataEntityShipMapper.selectMdmDataEntityShipList(mdmDataEntityShip);
    	}else {
    		return mdmDataEntityShipMapper.selectMdmDataEntityShipListByRel(mdmDataEntityShip);
    	}
        
    }

    /**
     * 新增数据实体关系
     *
     * @param mdmDataEntityShip 数据实体关系
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMdmDataEntityShip(MdmDataEntityShip mdmDataEntityShip)
    {
    	mdmDataEntityShip.setTriggerType(MdmConstants.ACQ_MODE_SD);
    	mdmDataEntityShip.setCreateTime(DateUtils.getNowDate());
    	mdmDataEntityShip.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	mdmDataEntityShip.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	mdmDataEntityShip.setUpdateTime(DateUtils.getNowDate());
    	mdmDataEntityShip.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	mdmDataEntityShip.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	int i =0;
    	if(mdmDataEntityShip.getEntityId()==null) {
    		i = mdmDataEntityShipMapper.insertMdmDataEntityShip(mdmDataEntityShip);
    		mdmDataEntityShip.setTriggerType(MdmConstants.ACQ_MODE_SD);	
    	}else {
    		i = mdmDataEntityShipMapper.insertMdmDataEntityShip(mdmDataEntityShip);
    		mdmDataEntityShip.setTriggerType(MdmConstants.ACQ_MODE_ZD);
    		MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel = new MdmDataEntityShipAnalysisRel();
    		mdmDataEntityShipAnalysisRel.setShipId(mdmDataEntityShip.getShipId());
    		mdmDataEntityShipAnalysisRel.setEntityId(mdmDataEntityShip.getEntityId());
    		mdmDataEntityShipAnalysisRelMapper.insertMdmDataEntityShipAnalysisRel(mdmDataEntityShipAnalysisRel);
    	}
        return i;
    }

    /**
     * 修改数据实体关系
     *
     * @param mdmDataEntityShip 数据实体关系
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMdmDataEntityShip(MdmDataEntityShip mdmDataEntityShip)
    {
    	mdmDataEntityShip.setUpdateTime(DateUtils.getNowDate());
    	mdmDataEntityShip.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	mdmDataEntityShip.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	int i = 0;
    	if(mdmDataEntityShip.getEntityId()==null) {
    		mdmDataEntityShip.setTriggerType(MdmConstants.ACQ_MODE_SD);	
    		i = mdmDataEntityShipMapper.updateMdmDataEntityShip(mdmDataEntityShip);
    	}else {
    		mdmDataEntityShip.setTriggerType(MdmConstants.ACQ_MODE_ZD);
    		i = mdmDataEntityShipMapper.updateMdmDataEntityShip(mdmDataEntityShip);
    		MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel = new MdmDataEntityShipAnalysisRel();
    		mdmDataEntityShipAnalysisRel.setShipId(mdmDataEntityShip.getShipId());
    		mdmDataEntityShipAnalysisRel.setEntityId(mdmDataEntityShip.getEntityId());
    		mdmDataEntityShipAnalysisRelMapper.insertMdmDataEntityShipAnalysisRel(mdmDataEntityShipAnalysisRel);
    	}
        return i;
    }

    /**
     * 批量删除数据实体关系
     *
     * @param shipIds 需要删除的数据实体关系ID
     * @return 结果
     */
    @Override
    public int deleteMdmDataEntityShipByIds(Long[] shipIds)
    {
        return mdmDataEntityShipMapper.deleteMdmDataEntityShipByIds(shipIds);
    }

    /**
     * 删除数据实体关系信息
     *
     * @param shipId 数据实体关系ID
     * @return 结果
     */
    @Override
    public int deleteMdmDataEntityShipById(Long shipId)
    {
        return mdmDataEntityShipMapper.deleteMdmDataEntityShipById(shipId);
    }

    @Override
    public List<MdmDataEntity> selectEntityAll() {
        return mdmDataEntityMapper.selectAll();
    }

    @Override
    public List<MdmDataEntityVo> selectEntityTableAll(MdmDataEntity mdmDataEntity) {
    	if(mdmDataEntity.getTableName()!=null&&mdmDataEntity.getTableName().indexOf(".")!=-1) {
    		String[] names =mdmDataEntity.getTableName().split("\\.");
    		mdmDataEntity.setTableSchema(names[0]);
    		if(names.length==2) {
    			mdmDataEntity.setTableName(names[1]);
    		}else {
    			mdmDataEntity.setTableName(null);
    		}
    	}
        return mdmDataEntityMapper.selectAllTable(mdmDataEntity);
    }
    
    @Override
    public List<MdmDataEntityVo> selectEntityUnTableAll(MdmDataEntity mdmDataEntity) {
    	if(mdmDataEntity.getTableName()!=null&&mdmDataEntity.getTableName().indexOf(".")!=-1) {
    		String[] names =mdmDataEntity.getTableName().split("\\.");
    		mdmDataEntity.setTableSchema(names[0]);
    		if(names.length==2) {
    			mdmDataEntity.setTableName(names[1]);
    		}else {
    			mdmDataEntity.setTableName(null);
    		}
    	}
        return mdmDataEntityMapper.selectAllUnTable(mdmDataEntity);
    }

    @Override
    public List<MdmDataEntityProp> selectAllByEntityId(MdmDataEntityProp mdmDataEntityProp) {
        return mdmDataEntityPropMapper.selectMdmDataEntityPropList(mdmDataEntityProp);
    }
    
    @Override
    public List<MdmDataEntityProp> selectPropVoAllByEntityId(MdmDataEntityProp mdmDataEntityProp) {
    	if(mdmDataEntityProp.getPropName()!=null&&mdmDataEntityProp.getPropName().indexOf(".")!=-1) {
    		String[] names =mdmDataEntityProp.getPropName().split("\\.");
    		if(names.length==3) {
    			mdmDataEntityProp.setTableSchema(names[0]);
    			mdmDataEntityProp.setTableName(names[1]);
    			mdmDataEntityProp.setPropName(names[2]);
    		}else if(names.length==2) {
    			mdmDataEntityProp.setTableName(names[0]);
    			mdmDataEntityProp.setPropName(names[1]);
    		}
    	}
        return mdmDataEntityPropMapper.selectMdmDataEntityPropVoList(mdmDataEntityProp);
    }

	@Override
	public List<MdmDataEntity> selectEntityAndSystem(String name) {
		return mdmDataEntityMapper.selectEntityAndSystem(name);
	}

	@Override
	public List<MdmDataEntityProp> selectAllPropAndEntityAndSystem(String name) {
		return mdmDataEntityPropMapper.selectAllPropAndEntityAndSystem(name);
	}

	/**
     * 根据任务关系查询血缘或影响
     *
     * @param mdmDataEntityVo 数据实体关系
     * @return 数据实体关系集合
     */
    @Override
    public List<MdmDataEntityVo> findShipListByMdmDataEntityShip(MdmDataEntityVo mdmDataEntityVo)
    {
    	Map<String, String> etlTaskMap = new HashMap<String, String>();
    	List<MdmDataEntityVo> list = new ArrayList<MdmDataEntityVo>();
    	if(mdmDataEntityVo.getTarEntityPropIds()!=null&&mdmDataEntityVo.getTarEntityPropIds().length>0) {
    		Long[] tarEntityIds= mdmDataEntityVo.getTarEntityIds();
        	Long[] tarEntityPropIds= mdmDataEntityVo.getTarEntityPropIds();
    		list = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityPropIds(tarEntityIds, tarEntityPropIds);
    		selectShipTarPropList(list.get(0).getSrcEntityIds(),list.get(0).getSrcEntityPropIds(),list,etlTaskMap);
    	}else if(mdmDataEntityVo.getSrcEntityPropIds()!=null&&mdmDataEntityVo.getSrcEntityPropIds().length>0) {
    		Long[] srcEntityIds= mdmDataEntityVo.getSrcEntityIds();
        	Long[] srcEntityPropIds= mdmDataEntityVo.getSrcEntityPropIds();
    		list = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityPropIds(srcEntityIds, srcEntityPropIds);
    		selectShipSrcPropList(list.get(0).getTarEntityIds(),list.get(0).getTarEntityPropIds(),list,etlTaskMap);
    	}else if(mdmDataEntityVo.getTarEntityIds()!=null&&mdmDataEntityVo.getTarEntityIds().length>0) {
    		Long[] tarEntityIds= mdmDataEntityVo.getTarEntityIds();
        	Long[] tarEntityPropIds= mdmDataEntityVo.getTarEntityPropIds();
    		list = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityIds(tarEntityIds, tarEntityPropIds);
    		selectShipTarList(list.get(0).getSrcEntityIds(),list.get(0).getSrcEntityPropIds(),list,etlTaskMap);
    	}else if(mdmDataEntityVo.getSrcEntityIds()!=null&&mdmDataEntityVo.getSrcEntityIds().length>0) {
    		Long[] srcEntityIds= mdmDataEntityVo.getSrcEntityIds();
        	Long[] srcEntityPropIds= mdmDataEntityVo.getSrcEntityPropIds();
    		list = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityIds(srcEntityIds, srcEntityPropIds);
    		selectShipSrcList(list.get(0).getTarEntityIds(),list.get(0).getTarEntityPropIds(),list,etlTaskMap);
    	}

        return list;
    }
	/**
     * 查询血缘和影响全部关系
     *
     * @param mdmDataEntityVo 数据实体关系
     * @return 数据实体关系集合
     */   
    @Override
    public List<MdmDataEntityVo> findShipAllListByMdmDataEntityShip(MdmDataEntityVo mdmDataEntityVo)
    {
    	Map<String, String> etlTaskMap = new HashMap<String, String>();
    	List<MdmDataEntityVo> listL = new ArrayList<MdmDataEntityVo>();
    	mdmDataEntityVo.setSrcEntityIds(mdmDataEntityVo.getTarEntityIds());
    	mdmDataEntityVo.setSrcEntityPropIds(mdmDataEntityVo.getTarEntityPropIds());
    	if(mdmDataEntityVo.getTarEntityPropIds()!=null&&mdmDataEntityVo.getTarEntityPropIds().length>0) {
    		Long[] tarEntityIds= mdmDataEntityVo.getTarEntityIds();
        	Long[] tarEntityPropIds= mdmDataEntityVo.getTarEntityPropIds();
        	listL = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityPropIds(tarEntityIds, tarEntityPropIds);
    		selectShipTarPropList(listL.get(0).getSrcEntityIds(),listL.get(0).getSrcEntityPropIds(),listL,etlTaskMap);
    	}else if(mdmDataEntityVo.getTarEntityIds()!=null&&mdmDataEntityVo.getTarEntityIds().length>0) {
    		Long[] tarEntityIds= mdmDataEntityVo.getTarEntityIds();
        	Long[] tarEntityPropIds= mdmDataEntityVo.getTarEntityPropIds();
        	listL = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityIds(tarEntityIds, tarEntityPropIds);
    		selectShipTarList(listL.get(0).getSrcEntityIds(),listL.get(0).getSrcEntityPropIds(),listL,etlTaskMap);
    	}
    	List<MdmDataEntityVo> listR = new ArrayList<MdmDataEntityVo>();
    	if(mdmDataEntityVo.getSrcEntityPropIds()!=null&&mdmDataEntityVo.getSrcEntityPropIds().length>0) {
    		Long[] srcEntityIds= mdmDataEntityVo.getSrcEntityIds();
        	Long[] srcEntityPropIds= mdmDataEntityVo.getSrcEntityPropIds();
        	listR = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityPropIds(srcEntityIds, srcEntityPropIds);
    		selectShipSrcPropList(listR.get(0).getTarEntityIds(),listR.get(0).getTarEntityPropIds(),listR,etlTaskMap);
    	}else if(mdmDataEntityVo.getSrcEntityIds()!=null&&mdmDataEntityVo.getSrcEntityIds().length>0) {
    		Long[] srcEntityIds= mdmDataEntityVo.getSrcEntityIds();
        	Long[] srcEntityPropIds= mdmDataEntityVo.getSrcEntityPropIds();
        	listR = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityIds(srcEntityIds, srcEntityPropIds);
    		selectShipSrcList(listR.get(0).getTarEntityIds(),listR.get(0).getTarEntityPropIds(),listR,etlTaskMap);
    	}
    	int i=0;
    	if(listL==null||listL.size()==0) {
    		listL.addAll(listR);
    	}else if(listR!=null) {
    		for(MdmDataEntityVo vo : listR) {
        		if(i==0) {
        			List<MdmDataEntityShip> rels = listL.get(0).getRelations();
        			if(rels==null) {
        				rels = new ArrayList<>();
        			}
        			if(vo.getRelations()!=null) {
        				rels.addAll(vo.getRelations());
        			}
        		}else {
        			listL.add(vo);
        		}
        		i++;
        	}
    	}
    	List<MdmDataEntityVo> listA = new ArrayList<MdmDataEntityVo>();
    	for(MdmDataEntityVo vo : listL) {
    		vo.setEntryType("TABLE");
    		List<ApiTableManager> listApi = apiTableManagerMapper.selectApiTableManagerListByEntityId(Long.parseLong(vo.getEntityId()));
    		if(listApi!=null&&listApi.size()>0) {
    			for(ApiTableManager api : listApi) {
    				MdmDataEntityVo evo = new MdmDataEntityVo();
        			evo.setEntityId("API-TABLE"+api.getSystemId());
        			evo.setTableName(api.getSystemName());
        			List<MdmDataEntityShip> relations = new ArrayList<>();
        			MdmDataEntityShip ship=new MdmDataEntityShip();
        			ship.setSrcTableName(vo.getEntityId());
        			ship.setTarTableName(evo.getEntityId());
        			ship.setShipType("API-TABLE");
        			relations.add(ship);
        			evo.setRelations(relations);
        			List<MdmDataEntityProp> mdmDataEntityProps=new ArrayList<>();
        			MdmDataEntityProp prop =new MdmDataEntityProp();
        			prop.setPropId(0L);
        			prop.setPropName("No");
        			prop.setPropComment("No");
        			prop.setDataType("No");
        			mdmDataEntityProps.add(prop);
        			evo.setMdmDataEntityProps(mdmDataEntityProps);
        			evo.setEntryType("API-TABLE");
        			listA.add(evo);
    			}
    			
    		}
    	}
    	listL.addAll(listA);
    	
        return listL;
    }

    public void selectShipTarList(Long[] tarEntityIds,Long[] tarEntityPropIds ,List<MdmDataEntityVo> list,Map<String, String> etlTaskMap)
    {
    	if(tarEntityIds==null||tarEntityIds.length==0||tarEntityIds[0]==null) {
    		return;
    	}
    	List<MdmDataEntityVo> listL = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityIds(tarEntityIds, tarEntityPropIds);
    	if(listL!=null&&listL.size()>0) {
    		Map<String, String> map = new HashMap<String, String>();
    		for(MdmDataEntityVo t : listL) {
    			if(etlTaskMap.get(t.getEntityId())==null) {
    				list.add(t);
    				etlTaskMap.put(t.getEntityId(), "Y");
    				map.put(t.getEntityId(), t.getEntityId());
    				if(t.getSrcEntityIds()!=null&&t.getSrcEntityIds().length>0) {
    					selectShipTarList(t.getSrcEntityIds(),t.getSrcEntityPropIds(),list,etlTaskMap);
    				}
    			}
    		}
    	}
    }

    public void selectShipSrcList(Long[] srcEntityIds,Long[] srcEntityPropIds ,List<MdmDataEntityVo> list,Map<String, String> etlTaskMap)
    {
    	if(srcEntityIds==null||srcEntityIds.length==0||srcEntityIds[0]==null) {
    		return;
    	}
    	List<MdmDataEntityVo> listL = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityIds(srcEntityIds, srcEntityPropIds);
    	if(listL!=null&&listL.size()>0) {
    		Map<String, String> map = new HashMap<String, String>();
    		for(MdmDataEntityVo t : listL) {
    			if(etlTaskMap.get(t.getEntityId())==null) {
    				list.add(t);
    				etlTaskMap.put(t.getEntityId(), "Y");
    				map.put(t.getEntityId(), t.getEntityId());
    				if(t.getTarEntityIds()!=null&&t.getTarEntityIds().length>0) {
    				selectShipSrcList(t.getTarEntityIds(),t.getTarEntityPropIds(),list,etlTaskMap);
    				}
    			}
    		}
    	}
    }

    public void selectShipTarPropList(Long[] tarEntityIds,Long[] tarEntityPropIds ,List<MdmDataEntityVo> list,Map<String, String> etlTaskMap)
    {
    	if(tarEntityPropIds==null||tarEntityPropIds.length==0||tarEntityPropIds[0]==null) {
    		return;
    	}
    	List<MdmDataEntityVo> listL = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityPropIds(tarEntityIds, tarEntityPropIds);
    	if(listL!=null&&listL.size()>0) {
    		Map<String, String> map = new HashMap<String, String>();
    		for(MdmDataEntityVo t : listL) {
    			if(etlTaskMap.get(t.getEntityId())==null) {
    				list.add(t);
    				etlTaskMap.put(t.getEntityId(), "Y");
    				map.put(t.getEntityId(), t.getEntityId());
    				if(t.getSrcEntityPropIds()!=null&&t.getSrcEntityPropIds().length>0) {
    					selectShipTarList(t.getSrcEntityIds(),t.getSrcEntityPropIds(),list,etlTaskMap);
    				}
    			}
    		}
    	}
    }

    public void selectShipSrcPropList(Long[] srcEntityIds,Long[] srcEntityPropIds ,List<MdmDataEntityVo> list,Map<String, String> etlTaskMap)
    {
    	if(srcEntityPropIds==null||srcEntityPropIds.length==0||srcEntityPropIds[0]==null) {
    		return;
    	}
    	List<MdmDataEntityVo> listL = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityPropIds(srcEntityIds, srcEntityPropIds);
    	if(listL!=null&&listL.size()>0) {
    		Map<String, String> map = new HashMap<String, String>();
    		for(MdmDataEntityVo t : listL) {
    			if(etlTaskMap.get(t.getEntityId())==null) {
    				list.add(t);
    				etlTaskMap.put(t.getEntityId(), "Y");
    				map.put(t.getEntityId(), t.getEntityId());
    				if(t.getTarEntityPropIds()!=null&&t.getTarEntityPropIds().length>0) {
    				selectShipSrcList(t.getTarEntityIds(),t.getTarEntityPropIds(),list,etlTaskMap);
    				}
    			}
    		}
    	}
    }
    
    @Override
    public List<MdmDataEntityVo> findShipListByTheme(MdmDataEntityVo mdmDataEntityVo)
    {
    	List<MdmDataEntityVo> list = new ArrayList<MdmDataEntityVo>();
    	list = mdmDataEntityShipMapper.findShipListByTheme(mdmDataEntityVo.getThemeId());
        return list;
    }

	@Override
	public Map<String,Object> analyse(String sql, String type) {
		List<MdmDataEntityAnalyse> listL = new ArrayList<MdmDataEntityAnalyse>();
		String status="SUCCESS";
		if(StringUtils.isEmpty(sql)) {
			Map<String,Object> map =new HashMap<>();
	    	map.put("script", "");
	    	map.put("data", listL);
	    	map.put("status", status);
			return map;
		}
		DbType dbType = JdbcConstants.MYSQL;
		if(type.equals(MdmConstants.HIVE)) {
			dbType = JdbcConstants.HIVE;
		}else if(type.equals(MdmConstants.ORACLE)) {
			dbType = JdbcConstants.ORACLE;
		}
		
		StringBuffer shipS=new StringBuffer("");
		List<String> list = new ArrayList<>();
		try {
			sql=processSqlContent(sql, "utf-8");
			System.out.println(sql);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		sqlSplit(sql,list);
		sqlSplitCreate(sql, list);
		sqlSplitCreateView(sql, list);
		shipS.append("共发现"+list.size()+"个Insert和Create语句，准备开始解>\t\n");
		int i=1;
		
		for(String sqlScripts : list) {
			shipS.append("解析第"+(i++)+"个语句，SQL为"+sqlScripts+">>\t\n");
			if(sqlScripts.toUpperCase().indexOf(" START WITH ")!=-1) {
				shipS.append(" SQL中包含START WITH，跳过该语句！！！\t\n");
				continue;
			}
			try {
				
				sqlScripts=sqlScripts.replaceAll("\\\\", "");
		        List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sqlScripts, dbType);
		        for (SQLStatement sqlStatement : sqlStatements) {
		        	Map<Integer,GbTableStat.Column> map = new HashMap<Integer, GbTableStat.Column>();
		        	Map<String,MdmDataEntityAnalyse> tableMap = new HashMap<String, MdmDataEntityAnalyse>();
		        	Map<String,String> columnMap = new HashMap<String, String>();
		        	
		        	GbSchemaStatVisitor schemaStatVisitor = null;
		    		if(type.equals(MdmConstants.HIVE)) {
		    			dbType = JdbcConstants.HIVE;
		    			schemaStatVisitor = (GbHiveSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(dbType);
		    		}else if(type.equals(MdmConstants.ORACLE)) {
		    			dbType = JdbcConstants.ORACLE;
		    			schemaStatVisitor = (GbOracleSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(dbType);
		    		}else {
		    			schemaStatVisitor = (GbSqlSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(dbType);
		    		}
		            sqlStatement.accept(schemaStatVisitor);
		            
		            Map<GbTableStat.Name, GbTableStat> tables = schemaStatVisitor.getTables();
		            Collection<GbTableStat.Column> columns = schemaStatVisitor.getColumns();
		            Set<Relationship> relationships =schemaStatVisitor.getRelationships();
		            boolean flag=sqlScripts.toUpperCase().indexOf("CREATE TABLE ")!=-1;
		            Map<String,String> mm= schemaStatVisitor.getCreateColumns();
					  if (Objects.nonNull(tables)) { 
						  tables.forEach(((name, tableStat) -> {
							  MdmDataEntityAnalyse mdmDataEntityAnalyse = tableMap.get(name.getName().toLowerCase())==null?new MdmDataEntityAnalyse():tableMap.get(name.getName().toLowerCase());
							  List<MdmDataEntityProp> mdmDataEntityProps= tableMap.get(name.getName().toLowerCase())==null?new ArrayList<>():mdmDataEntityAnalyse.getMdmDataEntityProps();
							  List<MdmDataEntityShipAnalyse> relations = tableMap.get(name.getName().toLowerCase())==null?new ArrayList<MdmDataEntityShipAnalyse>():mdmDataEntityAnalyse.getRelations();
							  mdmDataEntityAnalyse.setTableName(name.getName().toLowerCase());
							  if(tableMap.get(name.getName().toLowerCase())==null) {
								  mdmDataEntityAnalyse.setMdmDataEntityProps(mdmDataEntityProps);
								  mdmDataEntityAnalyse.setRelations(relations);
								  listL.add(mdmDataEntityAnalyse);
								  tableMap.put(name.getName().toLowerCase(), mdmDataEntityAnalyse);
							  }
	
							  if(tableStat.getCreateCount() > 0 || tableStat.getInsertCount() > 0) {
								  shipS.append("目标表: table["+name.getName().toLowerCase()+"]\t\n");
								  columns.stream().filter(column -> Objects.equals(column.getTable().toLowerCase(), name.getName().toLowerCase())).forEach(column -> {
									  MdmDataEntityProp prop = new MdmDataEntityProp();
									  prop.setPropName(column.getName().toLowerCase());
									  prop.setPropComment(column.getName().toLowerCase());
									  if(columnMap.get(column.getTable().toLowerCase()+"."+column.getName().toLowerCase())==null) {
									  	mdmDataEntityProps.add(prop); 
									  	columnMap.put(column.getTable().toLowerCase()+"."+column.getName().toLowerCase(), "Y");
									  }
									  
									  shipS.append("目标字段: table["+column.getTable().toLowerCase()+"] column["+column.getName().toLowerCase()+"]\t\n");
									  map.put(map.size(), column);
								  }); 
								  
								  if(map==null||map.size()==0) {//createtable 类型语句目标没有指定字段
									  for (String key : mm.keySet()) { 
										  MdmDataEntityProp prop = new MdmDataEntityProp();
										  prop.setPropName(key.toLowerCase());
										  prop.setPropComment(key.toLowerCase());
										  if(columnMap.get(name.getName().toLowerCase()+"."+key.toLowerCase())==null) {
										  	mdmDataEntityProps.add(prop); 
										  	columnMap.put(name.getName().toLowerCase()+"."+key.toLowerCase(), "Y");
										  }
										  
										  shipS.append("目标字段: table["+name.getName().toLowerCase()+"] column["+key.toLowerCase()+"]\t\n");
										  GbTableStat.Column column = new GbTableStat.Column(name.getName().toLowerCase(),key.toLowerCase());
										  map.put(map.size(), column);
						            	} 
								  }
								  
							  } else if ( tableStat.getSelectCount() > 0) {
								  shipS.append("来源表: table["+name.getName().toLowerCase()+"]\t\n");
								  columns.stream().filter(column -> Objects.equals(column.getTable().toLowerCase(), name.getName().toLowerCase())).forEach(column -> {
									  List<GbColumnVo> clist=column.getGbColumnVo();
									  HashSet<String> names = new LinkedHashSet<String>();
									  for(GbColumnVo vo : clist) {
										  if(vo.getAlias()!=null) {
											  names.addAll(vo.getAlias());
										  }else {
											  names.add(vo.getName());  
										  }
									  }
									  HashSet<String> rel= getRelation(clist, names,shipS);
									  
									  shipS.append("来源字段: table["+column.getTable().toLowerCase()+"] column["+column.getName().toLowerCase()+"] 关系["+rel+"]\t\n");
									  
									  MdmDataEntityProp prop = new MdmDataEntityProp();
									  prop.setPropName(column.getName().toLowerCase());
									  prop.setPropComment(column.getName().toLowerCase());
									  mdmDataEntityProps.add(prop);
									  
									  if(rel!=null) {
										  for(Iterator it = rel.iterator(); it.hasNext();) {
											  String r=it.next().toString();
											  if(map.get(Integer.parseInt(r))!=null) {
												  MdmDataEntityShipAnalyse ship = new MdmDataEntityShipAnalyse();
												  ship.setSrcEntityId(column.getTable().toLowerCase());
												  ship.setSrcPropName(column.getName().toLowerCase());
												  ship.setTarEntityId(map.get(Integer.parseInt(r)).getTable().toLowerCase());
												  ship.setTarPropName(map.get(Integer.parseInt(r)).getName().toLowerCase());
												  ship.setShipType("2");
												  relations.add(ship); 
											  }
										  }  
									  }
								  }); 
							  } 
						  })); 
					  }
					  
					  for (Relationship ship : relationships) {
						  MdmDataEntityAnalyse mdmDataEntityAnalyseL = tableMap.get(ship.getLeft().getTable().toLowerCase());
						  MdmDataEntityAnalyse mdmDataEntityAnalyseR = tableMap.get(ship.getRight().getTable().toLowerCase());
						  if(mdmDataEntityAnalyseL!=null&&mdmDataEntityAnalyseR!=null) {
							  List<MdmDataEntityShipAnalyse> l = mdmDataEntityAnalyseL.getRelations();
							  if(l==null) {
								  l = new ArrayList<>();
							  }
							  MdmDataEntityShipAnalyse sh = new MdmDataEntityShipAnalyse();
							  sh.setSrcEntityId(ship.getLeft().getTable().toLowerCase());
							  sh.setSrcPropName(ship.getLeft().getName().toLowerCase());
							  sh.setTarEntityId(ship.getRight().getTable().toLowerCase());
							  sh.setTarPropName(ship.getRight().getName().toLowerCase());
							  sh.setShipType("1");
							  l.add(sh);  
						  }
					}
					 
		        }
			} catch (Exception e) {
				shipS.append(" SQL解析失败，错误信息为："+e.getMessage()+"！！！\t\n");
				status="FAILED";
				e.printStackTrace();
				continue;
			}
		}
		shipS.append("SQL血缘解析已完成。。。\t\n");
		Map<String,Object> map =new HashMap<>();
    	map.put("script", shipS);
    	map.put("data", listL);
    	map.put("status", status);
		return map;
	}

	@Override
	public Map<String,Object> analyseForTableRel(String sql, String type) {
		List<MdmDataEntityAnalyse> listL = new ArrayList<MdmDataEntityAnalyse>();
		String status="SUCCESS";
		if(StringUtils.isEmpty(sql)) {
			Map<String,Object> map =new HashMap<>();
	    	map.put("script", "");
	    	map.put("data", listL);
	    	map.put("status", status);
			return map;
		}
		DbType dbType = JdbcConstants.MYSQL;
		if(type.equals(MdmConstants.HIVE)) {
			dbType = JdbcConstants.HIVE;
		}else if(type.equals(MdmConstants.ORACLE)) {
			dbType = JdbcConstants.ORACLE;
		}
		
		StringBuffer shipS=new StringBuffer("");
		List<String> list = new ArrayList<>();
		try {
			sql=processSqlContent(sql, "utf-8");
			System.out.println(sql);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		sqlSplit(sql,list);
		sqlSplitCreate(sql, list);
		sqlSplitCreateView(sql, list);
		shipS.append("共发现"+list.size()+"个Insert和Create语句，准备开始解>\t\n");
		int i=1;
		
		for(String sqlScripts : list) {
			MdmDataEntityAnalyse mdmDataEntityAnalyse= new MdmDataEntityAnalyse();
			List<MdmDataEntityShipAnalyse> l = new  ArrayList<>();
			mdmDataEntityAnalyse.setRelations(l);
			listL.add(mdmDataEntityAnalyse);
			shipS.append("解析第"+(i++)+"个语句，SQL为"+sqlScripts+">>\t\n");
			if(sqlScripts.toUpperCase().indexOf(" START WITH ")!=-1) {
				shipS.append(" SQL中包含START WITH，跳过该语句！！！\t\n");
				continue;
			}
			try {
				
				sqlScripts=sqlScripts.replaceAll("\\\\", "");
		        List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sqlScripts, dbType);
		        for (SQLStatement sqlStatement : sqlStatements) {
		        	List<String> tableMap = new ArrayList< String>();
		        	GbSchemaStatVisitor schemaStatVisitor = null;
		    		if(type.equals(MdmConstants.HIVE)) {
		    			dbType = JdbcConstants.HIVE;
		    			schemaStatVisitor = (GbHiveSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(dbType);
		    		}else if(type.equals(MdmConstants.ORACLE)) {
		    			dbType = JdbcConstants.ORACLE;
		    			schemaStatVisitor = (GbOracleSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(dbType);
		    		}else {
		    			schemaStatVisitor = (GbSqlSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(dbType);
		    		}
		            sqlStatement.accept(schemaStatVisitor);
		            
		            Map<GbTableStat.Name, GbTableStat> tables = schemaStatVisitor.getTables();
		            Set<Relationship> relationships =schemaStatVisitor.getRelationships();
					  if (Objects.nonNull(tables)) { 
						  tables.forEach(((name, tableStat) -> {
							  if(tableStat.getCreateCount() > 0 || tableStat.getInsertCount() > 0) {
								  mdmDataEntityAnalyse.setTableName(name.getName().toLowerCase());
							  }else if ( tableStat.getSelectCount() > 0) {
								  tableMap.add(name.getName().toLowerCase());
							  }
	
						  })); 
					  }
					  
					  for (String ship : tableMap) {
						  MdmDataEntityShipAnalyse sh = new MdmDataEntityShipAnalyse();
						  sh.setSrcEntityId(ship);
						  sh.setTarEntityId(mdmDataEntityAnalyse.getTableName());
						  sh.setShipType("1");
						  l.add(sh); 
					}
					 
		        }
			} catch (Exception e) {
				shipS.append(" SQL解析失败，错误信息为："+e.getMessage()+"！！！\t\n");
				status="FAILED";
				e.printStackTrace();
				continue;
			}
		}
		shipS.append("SQL血缘解析已完成。。。\t\n");
		Map<String,Object> map =new HashMap<>();
    	map.put("script", shipS);
    	map.put("data", listL);
    	map.put("status", status);
		return map;
	}
	
    public static HashSet<String> getRelation(List<GbColumnVo> gbColumnVos,HashSet<String> names ,StringBuffer shipS) {
    	HashSet<String> list = new LinkedHashSet<String>();
    	if(gbColumnVos==null||gbColumnVos.size()==0) {
			  return null;
		}
    	for(GbColumnVo gbColumnVo : gbColumnVos) {
    		GbSelectVo gbSelectVo =gbColumnVo.getGbSelectVo();
    		GbSelectVo gbSelectVoP=gbSelectVo.getParent();
    		if(gbSelectVo.getParent()==null) {
    			list.addAll(gbColumnVo.getRelation());
				continue;
			}
    		List<GbColumnVo> clist = gbSelectVoP.getColumns();
    		if(clist==null) {
				gbSelectVoP=gbSelectVo.getParent().getParent();
    			clist = gbSelectVoP.getColumns();
    		}
	    		for(GbColumnVo cvo :clist) {
				  if(gbSelectVoP.getParent()==null) {
					  boolean flag=false;
					  for(Iterator it = names.iterator(); it.hasNext();) {
						  String sname=it.next().toString();
						  String tname=cvo.getName();
						  if(sname.indexOf(".")==-1&&tname.indexOf(".")!=-1) {
							  tname=tname.split("\\.")[1];
						  }else if(sname.indexOf(".")!=-1&&tname.indexOf(".")==-1){
							  sname=sname.split("\\.")[1];
						  }else if(sname.indexOf(".")!=-1){
							  sname=gbSelectVoP.getAlias()+"."+sname.split("\\.")[1];//gbSelectVo.getAlias()+"."+sname.split("\\.")[1];
						  }
						  if(sname.toLowerCase().equals(tname.toLowerCase())) {
							  flag=true;
							  break;
						  } 
						  
					  }
					  if(cvo.getRelation()!=null&&flag) {
						  list.addAll(cvo.getRelation());
					  }
				  }else {
					  List<GbColumnVo> vos = new ArrayList<GbColumnVo>();
					  HashSet<String> cls = new LinkedHashSet<String>();
					  vos.add(cvo);
					  boolean flag=false;
					  
					  for(Iterator it = names.iterator(); it.hasNext();) {
						  String sname=it.next().toString();
						  String tname=cvo.getName();
						  if(sname.indexOf(".")==-1&&tname.indexOf(".")!=-1) {
							  tname=tname.split("\\.")[1];
						  }else if(sname.indexOf(".")!=-1&&tname.indexOf(".")==-1){
							  sname=sname.split("\\.")[1];
						  }else if(sname.indexOf(".")!=-1){
							  sname=gbSelectVoP.getAlias()+"."+sname.split("\\.")[1];//gbSelectVo.getParent().getAlias()+"."+sname.split("\\.")[1];
						  }
						  System.out.println(sname.toLowerCase()+"="+tname.toLowerCase());
						  if(sname.toLowerCase().equals(tname.toLowerCase())) {
							  flag=true;
							  break;
						  } 
					  }
					  if(flag) {
						  if(cvo.getAlias()!=null) {
							  cls.addAll(cvo.getAlias());  
						  }else {
							  cls.add(cvo.getName());
						  }
					  }
					  list.addAll(getRelation(vos, cls ,shipS));
					  
				  }
						 
				 }
    	}
    	return list;
    }
	public String processSqlContent(String sql, String charSet)
			throws Exception {
		if (charSet == null) {
			charSet = "utf-8";
		}
		BufferedReader br = new BufferedReader( new InputStreamReader(new ByteArrayInputStream(sql.getBytes()), Charset.forName("utf8")));
		StringBuffer contentBuffer = new StringBuffer(5120);
		String temp = null;
		String tmpResult = null;
		boolean isStart = false;
		while ((temp = br.readLine()) != null) {
			//去空格和;
			//tmpResult = new String(temp.replaceAll(";", " ").replaceAll("\\s{2,}", " "));
			tmpResult = new String(temp);
			if (tmpResult != null) {
				
				if (isStart && tmpResult.indexOf("*/") != -1) {
					// */结束
					isStart = false;
					continue;
				} 
				//去除同一行/* */注释
				if(tmpResult.indexOf("/*")!=-1&&tmpResult.indexOf("*/")!=-1)
				{
					//最小匹配
					tmpResult=tmpResult.replaceAll("\\/\\*.*?\\*\\/", "");
				}else if (tmpResult.indexOf("/*") != -1&&tmpResult.indexOf("*/") == -1&&tmpResult.indexOf("--")==-1) {
					// /*开始
					isStart = true;
				}else if(tmpResult.indexOf("/*")!=-1&&tmpResult.indexOf("--")!=-1&&tmpResult.indexOf("--")<tmpResult.indexOf("/*"))
				{
					//同时存在--/*
					tmpResult=tmpResult.replaceAll("--.*", "");
				}
				// 去除同一行的--注释
				tmpResult = new String(tmpResult.replaceAll("--.*", ""));
			}
			if (!isStart) {
				//保留换行符
				contentBuffer.append(tmpResult).append("\r\n");
				//无换行符
				//contentBuffer.append(tmpResult);
			}
		}
		temp = contentBuffer.toString();
		//保留换行符
		//temp = new String(temp.replaceAll("\\s{2,}\\r\\n"," "));
		//无换行符
		//temp = new String(temp.replaceAll("\\s{2,}", " "));
		
		temp = new String(temp);
		return temp;
	}

	
  
    public void sqlSplit(String sql,List<String> list) {
    	if(sql==null) {
    		return;
    	}else if(sql.toUpperCase().indexOf("INSERT ")==-1||sql.indexOf(";")==-1) {
    		return;
    	}else if(sql.toUpperCase().indexOf("INSERT ")!=-1){
    		int index=sql.toUpperCase().indexOf("INSERT ");
    		String dyh="";
    		if(index>0) {
    			dyh=sql.trim().substring(index-1,index);	
    		}
    		
    		sql=sql.substring(index, sql.length());
    		String sqlB=sql;
    		if(sql.toUpperCase().indexOf(";")!=-1) {
    			sql=sql.substring(0, sql.toUpperCase().indexOf(";"));	
    		}else {
    			sql=sql.substring(0, sql.length());
    		}
        	if(!dyh.equals("'")&&!sql.toUpperCase().trim().endsWith(".DUAL")) {
        		list.add(sql);
        	}
        	if(sqlB.indexOf(";")!=-1&&sqlB.indexOf(";")<sqlB.length()-1) {
        		sqlB=sqlB.substring(sqlB.indexOf(";"), sqlB.length()-1);
        	}
			if(sqlB.indexOf(";")!=-1&&sqlB.indexOf(";")==sqlB.length()-1) {
				return;
			}
        	sqlSplit(sqlB, list);
    	}
    }
    public void sqlSplitCreate(String sql,List<String> list) {
    	if(sql==null) {
    		return;
    	}else if(sql.toUpperCase().indexOf("CREATE TABLE ")==-1||sql.indexOf(";")==-1) {
    		return;
    	}else{
    		int index=sql.toUpperCase().indexOf("CREATE TABLE ");
    		String dyh="";
    		if(index>0) {
    			dyh=sql.trim().substring(index-1,index);	
    		}
    		
    		sql=sql.substring(index, sql.length());
    		String sqlB=sql;
    		if(sql.toUpperCase().indexOf(";")!=-1) {
    			sql=sql.substring(0, sql.toUpperCase().indexOf(";"));	
    		}else {
    			sql=sql.substring(0, sql.length());
    		}
        	if(!dyh.equals("'")&&!sql.toUpperCase().trim().endsWith(".DUAL")) {
        		list.add(sql);
        	}
        	if(sqlB.indexOf(";")!=-1&&sqlB.indexOf(";")<sqlB.length()-1) {
        		sqlB=sqlB.substring(sqlB.indexOf(";"), sqlB.length()-1);
        	}
			if(sqlB.indexOf(";")!=-1&&sqlB.indexOf(";")==sqlB.length()-1) {
				return;
			}
			sqlSplitCreate(sqlB, list);
    	}
    }
    public void sqlSplitCreateView(String sql,List<String> list) {
    	if(sql==null) {
    		return;
    	}else if((sql.toUpperCase().indexOf("OR REPLACE VIEW")==-1&&sql.toUpperCase().indexOf("OR REPLACE FORCE VIEW")==-1)||sql.indexOf(";")==-1) {
    		return;
    	}else{
    		String sqlB=sql.toUpperCase().replaceAll("OR REPLACE VIEW", "TABLE");
    		sqlB=sql.toUpperCase().replaceAll("OR REPLACE FORCE VIEW", "TABLE");
    		list.add(sqlB);
    	}
    }
	@Override
	@Transactional
	public String importMdmDataEntityShipVo(List<MdmDataEntityShipVo> mdmDataEntityShipVoList, Boolean isUpdateSupport) {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		if (StringUtils.isNull(mdmDataEntityShipVoList) || mdmDataEntityShipVoList.size() == 0) {
			throw new CustomException("导入任务数据不能为空！");
		}
		List<MdmDataEntityShip> mdmDataEntityShipList=new ArrayList<MdmDataEntityShip>();
		int successNum = 0;
		int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		for (MdmDataEntityShipVo vo : mdmDataEntityShipVoList) {
			try {
				MdmDataEntityShipVo t = new MdmDataEntityShipVo();
				BeanUtils.copyBeanProp(t, vo);
				/*----------非空检查begain-------------------*/

				if (vo.getSrcSystemName() == null && vo.getSrcSystemName().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "源系统为空");
					continue;
				}
				if (vo.getSrcTableName() == null && vo.getSrcTableName().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "源表为空");
					continue;
				}
				if (vo.getSrcPropName() == null && vo.getSrcPropName().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "源表字段为空");
					continue;
				}
				if (vo.getTarSystemName() == null && vo.getTarSystemName().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "目标系统为空");
					continue;
				}
				if (vo.getTarTableName() == null && vo.getTarTableName().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "目标表为空");
					continue;
				}
				if (vo.getTarPropName() == null && vo.getTarPropName().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "目标表字段为空");
					continue;
				}
				if (vo.getShipType() == null && vo.getShipType().length() == 0) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "关系类型为空");
					continue;
				}



				/*  ----------非空检查end-------------------*/

				//验证源数据是否存在
				MdmDataEntityShip srcMdmDataEntityShip = new MdmDataEntityShip();
				SysSystem   srcSysSystemData = sysSystemMapper.getSysSystemByName(vo.getSrcSystemName());
					if(StringUtils.isNull(srcSysSystemData)){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、源系统" +vo.getSrcSystemName()+  " 未定义");
						continue;
					}
				MdmDataEntity  srcMdmDataEntity = new MdmDataEntity();
				if(vo.getSrcTableName().indexOf(".")==-1) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、源表表名填写不正确" +  "，例用户.表名");
					continue;
				}else {
					srcMdmDataEntity.setTableSchema(vo.getSrcTableName().split("\\.")[0]);
					srcMdmDataEntity.setTableName(vo.getSrcTableName().split("\\.")[1]);
				}
				
				srcMdmDataEntity.setSystemName(vo.getSrcSystemName());
				MdmDataEntity SrcMdmDataEntityData = mdmDataEntityMapper.selectMdmDataEntityByTableName(srcMdmDataEntity);
					if(StringUtils.isNull(SrcMdmDataEntityData)){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、源表" +vo.getSrcTableName()+  " 未定义");
						continue;
					}
				MdmDataEntityProp srcMdmDataEntityProp = new MdmDataEntityProp();
				srcMdmDataEntityProp.setSystemName(vo.getSrcSystemName());
				srcMdmDataEntityProp.setTableSchema(srcMdmDataEntity.getTableSchema());
				srcMdmDataEntityProp.setTableName(srcMdmDataEntity.getTableName());
				srcMdmDataEntityProp.setPropName(vo.getSrcPropName());
				MdmDataEntityProp srcMdmDataEntityPropData = mdmDataEntityPropMapper.getDataByOther(srcMdmDataEntityProp);
				if(StringUtils.isNull(srcMdmDataEntityPropData)){
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、源字段" +vo.getSrcPropName()+  " 未定义");
					continue;
				}

				//验证目标数据是否存在
				MdmDataEntityShip tarMdmDataEntityShip = new MdmDataEntityShip();
				SysSystem  tarsysSystemData = sysSystemMapper.getSysSystemByName(vo.getTarSystemName());
				if(StringUtils.isNull(tarsysSystemData)){
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、目标源系统" +vo.getTarSystemName()+  " 未定义");
					continue;
				}
				MdmDataEntity tarMdmDataEntity = new MdmDataEntity();
				if(vo.getTarTableName().indexOf(".")==-1) {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、目标表表名填写不正确"+  " ，例用户.表名");
					continue;
				}else {
					tarMdmDataEntity.setTableSchema(vo.getTarTableName().split("\\.")[0]);
					tarMdmDataEntity.setTableName(vo.getTarTableName().split("\\.")[1]);
				}
				tarMdmDataEntity.setSystemName(vo.getTarSystemName());
				MdmDataEntity tarMdmDataEntityData = mdmDataEntityMapper.selectMdmDataEntityByTableName(tarMdmDataEntity);
				if(StringUtils.isNull(tarMdmDataEntityData)){
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、目标表" +vo.getTarTableName()+  " 未定义");
					continue;
				}
				MdmDataEntityProp tarMdmDataEntityProp = new MdmDataEntityProp();
				tarMdmDataEntityProp.setSystemName(vo.getTarSystemName());
				tarMdmDataEntityProp.setTableSchema(tarMdmDataEntity.getTableSchema());
				tarMdmDataEntityProp.setTableName(tarMdmDataEntity.getTableName());
				tarMdmDataEntityProp.setPropName(vo.getTarPropName());
				MdmDataEntityProp tarMdmDataEntityPropData = mdmDataEntityPropMapper.getDataByOther(tarMdmDataEntityProp);
					if(StringUtils.isNull(tarMdmDataEntityPropData)){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、目标字段" +vo.getTarPropName()+  " 未定义");
						continue;
					}
				//插入数据
				MdmDataEntityShip mdmDataEntityShip = new MdmDataEntityShip();
				mdmDataEntityShip.setTriggerType(MdmConstants.ACQ_MODE_SD);
				mdmDataEntityShip.setSrcEntityId(SrcMdmDataEntityData.getEntityId());
				mdmDataEntityShip.setSrcEntityPropId(srcMdmDataEntityPropData.getPropId());
				mdmDataEntityShip.setTarEntityId(tarMdmDataEntityData.getEntityId());
				mdmDataEntityShip.setTarEntityPropId(tarMdmDataEntityPropData.getPropId());
				List<MdmDataEntityShip> mdmDataEntityShipDate = mdmDataEntityShipMapper.selectMdmDataEntityShipList(mdmDataEntityShip);
				mdmDataEntityShip.setShipType(vo.getShipType());
				mdmDataEntityShip.setTransferRule(vo.getTransferRule());
				mdmDataEntityShip.setShipName(vo.getShipName());
				if(mdmDataEntityShipDate==null||mdmDataEntityShipDate.size()==0){
					successNum++;
					mdmDataEntityShip.setCreateTime(DateUtils.getNowDate());
					mdmDataEntityShip.setCreateId(loginUser.getUser().getUserId());
					mdmDataEntityShip.setCreateBy(loginUser.getUsername());
					mdmDataEntityShip.setUpdateTime(DateUtils.getNowDate());
					mdmDataEntityShip.setUpdateId(loginUser.getUser().getUserId());
					mdmDataEntityShip.setUpdateBy(loginUser.getUsername());
					mdmDataEntityShipList.add(mdmDataEntityShip);
				}else{
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、该数据："+ vo.getSrcTableName()+"源表+"+vo.getSrcPropName()+"源字段+"+vo.getTarTableName()+"目标表+"+ vo.getTarPropName() + "目标字段已存在");
					continue;
				}
				
			} catch (Exception e) {
				failureNum++;
				failureMsg.append("<br/>" + failureNum + "、该数据："+ vo.getSrcTableName()+"源表+"+vo.getSrcPropName()+"源字段+"+vo.getTarTableName()+"目标表+"+ vo.getTarPropName() + "存在问题");

			}
		}


		if (failureNum > 0) {
			if (StringUtils.isNotNull(failureMsg)) {
				failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据失败，错误如下：");
				throw new CustomException(failureMsg.toString());
			} else {
				/*failureMsg.append("<br/>" + "系统错误，请联系管理员");*/
				throw new CustomException("系统错误，请联系管理员");
			}

		}
		if (StringUtils.isNotNull(successMsg)) {
			if (isUpdateSupport) {
				mdmDataEntityShipMapper.insertMdmDataEntityShipList(mdmDataEntityShipList);
				successMsg.insert(0, "恭喜您，数据更新成功！共 " + successNum + " 条");
			} else {
				mdmDataEntityShipMapper.insertMdmDataEntityShipList(mdmDataEntityShipList);
				successMsg.insert(0, "恭喜您，数据导入成功！共 " + successNum + " 条");
			}
		}
		return successMsg.toString();
	}

	@Override
	public int removeSource(MdmDataEntityShip mdmDataEntityShip)
	{
		return mdmDataEntityShipMapper.removeSource(mdmDataEntityShip);
	}
	
}
