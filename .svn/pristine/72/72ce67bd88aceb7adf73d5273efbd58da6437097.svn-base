package com.dqms.dqm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 质量检查规则知识库对象 dqm_knowledge_base
 *
 * <AUTHOR>
 * @date 2021-08-10
 */
@Document(indexName = "dqmknowledgebase")
public class DqmKnowledgeBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Id
    private Long knowledgeBaseId;

    /** 内容标题 */
    @Field(type = FieldType.Text,searchAnalyzer="ik_max_word",analyzer = "ik_max_word")
    private String knowledgeBaseName;

    /** 任务问题ID */
    private Long dqmValidationProblemId;

    /** 解决方案 */
    @Field(type = FieldType.Text,searchAnalyzer="ik_max_word",analyzer = "ik_max_word")
    private String knowledgeBaseContext;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** 处理进度 */
    private String handlingLoding;

    public void setKnowledgeBaseId(Long knowledgeBaseId)
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId()
    {
        return knowledgeBaseId;
    }
    public void setKnowledgeBaseName(String knowledgeBaseName)
    {
        this.knowledgeBaseName = knowledgeBaseName;
    }

    public String getKnowledgeBaseName()
    {
        return knowledgeBaseName;
    }
    public void setDqmValidationProblemId(Long dqmValidationProblemId)
    {
        this.dqmValidationProblemId = dqmValidationProblemId;
    }

    public Long getDqmValidationProblemId()
    {
        return dqmValidationProblemId;
    }
    public void setKnowledgeBaseContext(String knowledgeBaseContext)
    {
        this.knowledgeBaseContext = knowledgeBaseContext;
    }

    public String getKnowledgeBaseContext()
    {
        return knowledgeBaseContext;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setHandlingLoding(String handlingLoding)
    {
        this.handlingLoding = handlingLoding;
    }

    public String getHandlingLoding()
    {
        return handlingLoding;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("knowledgeBaseName", getKnowledgeBaseName())
            .append("dqmValidationProblemId", getDqmValidationProblemId())
            .append("knowledgeBaseContext", getKnowledgeBaseContext())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("handlingLoding", getHandlingLoding())
            .toString();
    }
}
