<template>
  <div class="app-container page-component__scroll" style="padding:0px;">
    <el-backtop target=".page-component__scroll"> </el-backtop>
    <el-row :gutter="20">
      <el-col :xs="24">
        <el-row style="text-align:center;margin:0 auto;margin-top: 60px;">
          <div style="margin-top: 15px;margin-bottom: 15px;">
            <el-link type="primary" style="font-size: 30px;"
              >知识库检索</el-link
            >
          </div>
          <div
            style="margin-top: 15px;width: 800px;text-align:center;margin:0 auto;"
          >
            <div style="display:inline;float:left;width: 590px;">
              <el-input
                placeholder="请输入内容"
                v-model="queryParams.knowledgeBaseContext"
                class="input-with-select"
                style="border: 1px solid #1890ff;border-radius:5px;"
                clearable
              >
              </el-input>
            </div>
            <div style="display:inline;">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="handleQuery"
                round
                >搜索</el-button
              >
            </div>
            <div style="display:inline;">
              <el-button icon="el-icon-refresh" @click="quertyReset" round
                >重置</el-button
              >
            </div>
          </div>
        </el-row>
        <el-row style="text-align:left;margin:0 auto;margin-top: 10px;">
          <div
            style="margin-top: 15px;"
            v-for="(item, index) in dqmKnowledgeBaseList"
            :key="'dqmKnowledgeBase' + index"
          >
            <el-col :span="20" :xs="20">
              <div style="padding: 14px;">
                <el-link
                  type="primary"
                  :underline="false"
                  style="font-size: 18px; margin-bottom: 10px;color: rgb(24, 144, 255);"
                  @click="shwoDetail(item)"
                  v-html="item.knowledgeBaseName"></el-link
                >
              </div>
            </el-col>
            <el-col :span="4" :xs="4" style="padding: 14px;">
              <div style="padding-right: 20px;text-align:right;width:150px;">
              <el-progress :text-inside="true" 
                            :stroke-width="15" 
                            :percentage="parseInt(item.handlingLoding)" 
                            status="warning"
              ></el-progress>
              </div>
            </el-col>
            <el-col :span="24" :xs="24">
              <div
                style="padding-left: 4px;font-size:14px;max-height:42px;line-height: 21px;overflow:hidden;text-overflow:ellipsis;display: block;word-wrap:break-word;text-indent:20px"
              v-html="item.knowledgeBaseContext">
              </div>
              <el-divider>
                <span style="color: #6B6C6D;font-size:12px;font-style: italic;" >By:{{ item.createBy }}</span >
                <span style="color: #6B6C6D;font-size:12px;font-style: italic;" >({{ item.createTime }})</span >
              
              </el-divider>
            </el-col>
          </div>
        </el-row>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改知识评价对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
    >
      <div>
        <el-descriptions
          title="基础信息"
          direction="vertical"
          :column="4"
          border
        >
          <el-descriptions-item label="规则名称">{{
            form.validationRuleName
          }}</el-descriptions-item>
          <el-descriptions-item label="源系统">{{
            form.systemName
          }}</el-descriptions-item>
          <el-descriptions-item label="数据源">{{
            form.datasourceName
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="margin-top: 15px">
        <el-descriptions
          title="问题信息"
          direction="vertical"
          :column="4"
          border
        >
          <el-descriptions-item label="阀值判断">
            <el-tag size="small">{{ form.thresholdJudge }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="阀值">{{
            form.threshold
          }}</el-descriptions-item>
          <el-descriptions-item label="检查SQL">
            {{ form.problemSql }}
          </el-descriptions-item>
          <el-descriptions-item label="错误数据">
            <el-tag size="small">{{ form.errorResult }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="margin-top: 15px">
        <el-descriptions
          title="方案信息"
          direction="vertical"
          :column="4"
          border
        >
          <el-descriptions-item label="方案">
            {{ form.knowledgeBaseContext }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
    <el-backtop target=".page-component__scroll"> </el-backtop>
  </div>
</template>

<script>
import {
  listDqmKnowledgeBase,
  getDqmKnowledgeBaseById
} from "@/api/dqm/dqmKnowledgeBase";

export default {
  name: "DqmKnowledgeBase",
  components: {},
  data() {
    return {
      size: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质量检查规则知识库表格数据
      dqmKnowledgeBaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        knowledgeBaseContext: null,
        knowledgeBaseType: null
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    shwoDetail(row) {
      getDqmKnowledgeBaseById(row.knowledgeBaseId).then(response => {
        this.form = response.data;
        this.title = row.knowledgeBaseName;
        this.open = true;
      });
    },
    // 表单重置
    quertyReset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        context: null,
        knowledgeBaseType: ""
      };
    },
    /** 查询质量检查规则知识库列表 */
    getList() {
      this.loading = true;
      listDqmKnowledgeBase(this.queryParams).then(response => {
        this.dqmKnowledgeBaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    }
  }
};
</script>
<style lang="scss" scoped>
.page-component__scroll {
  height: calc(100vh - 84px); // 不必是100vh，只需要是该容器显示的最大高度即可
  overflow-x: hidden;
}
</style>
