package com.dqms.dsm.service;

import java.util.List;
import com.dqms.dsm.domain.DsmModelEntityShipTemp;

/**
 * 模型关系Service接口
 * 
 * <AUTHOR>
 * @date 2021-12-29
 */
public interface IDsmModelEntityShipTempService 
{
    /**
     * 查询模型关系
     * 
     * @param modelEntityShipTempId 模型关系ID
     * @return 模型关系
     */
    public DsmModelEntityShipTemp selectDsmModelEntityShipTempById(Long modelEntityShipTempId);

    /**
     * 查询模型关系列表
     * 
     * @param dsmModelEntityShipTemp 模型关系
     * @return 模型关系集合
     */
    public List<DsmModelEntityShipTemp> selectDsmModelEntityShipTempList(DsmModelEntityShipTemp dsmModelEntityShipTemp);

    /**
     * 新增模型关系
     * 
     * @param dsmModelEntityShipTemp 模型关系
     * @return 结果
     */
    public int insertDsmModelEntityShipTemp(DsmModelEntityShipTemp dsmModelEntityShipTemp);

    /**
     * 修改模型关系
     * 
     * @param dsmModelEntityShipTemp 模型关系
     * @return 结果
     */
    public int updateDsmModelEntityShipTemp(DsmModelEntityShipTemp dsmModelEntityShipTemp);

    /**
     * 批量删除模型关系
     * 
     * @param modelEntityShipTempIds 需要删除的模型关系ID
     * @return 结果
     */
    public int deleteDsmModelEntityShipTempByIds(Long[] modelEntityShipTempIds);

    /**
     * 删除模型关系信息
     * 
     * @param modelEntityShipTempId 模型关系ID
     * @return 结果
     */
    public int deleteDsmModelEntityShipTempById(Long modelEntityShipTempId);
    public int deleteDsmModelEntityShipTempByDsmModelEntityId(Long modelEntityTempId);
}
