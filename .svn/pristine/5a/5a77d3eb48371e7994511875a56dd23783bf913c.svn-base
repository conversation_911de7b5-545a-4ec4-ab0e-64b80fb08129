package com.dqms.dqm.controller;

import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.dqm.domain.DqmValidationDetail;
import com.dqms.dqm.domain.DqmValidationTask;
import com.dqms.dqm.service.IDqmValidationDetailService;
import com.dqms.dqm.service.IDqmValidationTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 明细管理Controller
 *
 * <AUTHOR>
 * @date
 */
@RestController
@RequestMapping("/dqm/detail")
public class DqmValidationDetailController extends BaseController {
    @Autowired
    private IDqmValidationDetailService dqmValidationDetailService;
    @GetMapping(value = "/list")
    public TableDataInfo list(DqmValidationDetail dqmValidationDetail) throws Exception {
        startPage();
        List<Map<String,Object>> list = dqmValidationDetailService.selectDetailList(dqmValidationDetail);
        return getDataTable(list);
    }
}
