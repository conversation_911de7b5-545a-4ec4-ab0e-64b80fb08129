package com.dqms.dqm.service.impl;




import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.dqms.dqm.domain.DqmValidationDetail;
import com.dqms.dqm.mapper.DqmValidationDetailMapper;
import com.dqms.dqm.service.IDqmValidationDetailService;

@Service
public class DqmValidationDetailServiceImpl implements IDqmValidationDetailService {
    @Autowired
    private DqmValidationDetailMapper dqmValidationDetailMapper;
    @Override
    public int selectTableNameExit(String tableName) {
        return dqmValidationDetailMapper.selectTableNameExit(tableName);
    }
    /**
     * 添加问题明细数据
     * @param dqmValidationDetail
     */
    @Override
    public int insertDetailTable(DqmValidationDetail dqmValidationDetail) {
       return dqmValidationDetailMapper.insertDetailTable(dqmValidationDetail);
    }

    @Override
    public List<Map<String, Object>> selectDetailList(DqmValidationDetail dqmValidationDetail) throws Exception {
        String tableName = "detail_"+dqmValidationDetail.getValidationRuleCateId();
        dqmValidationDetail.setTableName(tableName);
        List<Map<String, Object>> data = new ArrayList<>();
        List<Map<String, Object>> list = dqmValidationDetailMapper.selectDetailList(dqmValidationDetail);
        for (Map<String, Object> map: list){
            Map<String,Object> line = new HashMap<>();
            String detailline = (String) map.get("detail");
            JSONObject jsonObject = JSONObject.parseObject(detailline);
            Iterator<String> iterator = jsonObject.keySet().iterator();
            while(iterator.hasNext()){
                String key = iterator.next().toString();
                String value= jsonObject.getString(key);
                line.put(key,value);
            }
            data.add(line);
        }
        return data;
    }
}
