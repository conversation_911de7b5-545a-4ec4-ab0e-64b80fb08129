package com.dqms.dqm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dqm.domain.DqmProblemHandling;
import com.dqms.dqm.service.IDqmProblemHandlingService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 检查规则任务问题处理进度Controller
 *
 * <AUTHOR>
 * @date 2021-07-28
 */
@RestController
@RequestMapping("/dqm/dqmProblemHandling")
public class DqmProblemHandlingController extends BaseController
{
    @Autowired
    private IDqmProblemHandlingService dqmProblemHandlingService;

    /**
     * 查询检查规则任务问题处理进度列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DqmProblemHandling dqmProblemHandling)
    {
        startPage();
        List<DqmProblemHandling> list = dqmProblemHandlingService.selectDqmProblemHandlingList(dqmProblemHandling);
        return getDataTable(list);
    }

    /**
     * 导出检查规则任务问题处理进度列表
     */
    @Log(title = "检查规则任务问题处理进度", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmProblemHandling dqmProblemHandling)
    {
        List<DqmProblemHandling> list = dqmProblemHandlingService.selectDqmProblemHandlingList(dqmProblemHandling);
        ExcelUtil<DqmProblemHandling> util = new ExcelUtil<DqmProblemHandling>(DqmProblemHandling.class);
        return util.exportExcel(list, "dqmProblemHandling");
    }

    /**
     * 获取检查规则任务问题处理进度详细信息
     */
    @GetMapping(value = "/{dqmProblemHandlingId}")
    public AjaxResult getInfo(@PathVariable("dqmProblemHandlingId") Long dqmProblemHandlingId)
    {
        return AjaxResult.success(dqmProblemHandlingService.selectDqmProblemHandlingById(dqmProblemHandlingId));
    }

    /**
     * 新增检查规则任务问题处理进度
     */
    @Log(title = "检查规则任务问题处理进度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DqmProblemHandling dqmProblemHandling)
    {
        return toAjax(dqmProblemHandlingService.insertDqmProblemHandling(dqmProblemHandling));
    }

    /**
     * 修改检查规则任务问题处理进度
     */
    @Log(title = "检查规则任务问题处理进度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmProblemHandling dqmProblemHandling)
    {
        return toAjax(dqmProblemHandlingService.updateDqmProblemHandling(dqmProblemHandling));
    }

    /**
     * 删除检查规则任务问题处理进度
     */
    @Log(title = "检查规则任务问题处理进度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dqmProblemHandlingIds}")
    public AjaxResult remove(@PathVariable Long[] dqmProblemHandlingIds)
    {
        return toAjax(dqmProblemHandlingService.deleteDqmProblemHandlingByIds(dqmProblemHandlingIds));
    }
}
