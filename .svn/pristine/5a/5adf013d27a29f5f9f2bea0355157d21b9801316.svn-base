package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmModelEntity;
import com.dqms.dsm.service.IDsmModelEntityService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 模型实例Controller
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@RestController
@RequestMapping("/dsm/dsmModelEntity")
public class DsmModelEntityController extends BaseController
{
    @Autowired
    private IDsmModelEntityService dsmModelEntityService;

    /**
     * 查询模型实例列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmModelEntity dsmModelEntity)
    {
        startPage();
        List<DsmModelEntity> list = dsmModelEntityService.selectDsmModelEntityList(dsmModelEntity);
        return getDataTable(list);
    }

    /**
     * 导出模型实例列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:export')")
    @Log(title = "模型实例", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmModelEntity dsmModelEntity)
    {
        List<DsmModelEntity> list = dsmModelEntityService.selectDsmModelEntityList(dsmModelEntity);
        ExcelUtil<DsmModelEntity> util = new ExcelUtil<DsmModelEntity>(DsmModelEntity.class);
        return util.exportExcel(list, "dsmModelEntity");
    }

    /**
     * 获取模型实例详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:query')")
    @GetMapping(value = "/{modelEntityId}")
    public AjaxResult getInfo(@PathVariable("modelEntityId") Long modelEntityId)
    {
        return AjaxResult.success(dsmModelEntityService.selectDsmModelEntityById(modelEntityId));
    }

    /**
     * 新增模型实例
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:add')")
    @Log(title = "模型实例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmModelEntity dsmModelEntity)
    {
        return toAjax(dsmModelEntityService.insertDsmModelEntity(dsmModelEntity));
    }

    /**
     * 修改模型实例
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:edit')")
    @Log(title = "模型实例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmModelEntity dsmModelEntity)
    {
        return toAjax(dsmModelEntityService.updateDsmModelEntity(dsmModelEntity));
    }

    /**
     * 修改模型实例
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:edit')")
    @Log(title = "模型实例", businessType = BusinessType.UPDATE)
    @PutMapping("/updateDsmModelEntityToApply")
    public AjaxResult updateDsmModelEntityToApply(@RequestBody DsmModelEntity dsmModelEntity)
    {
        return toAjax(dsmModelEntityService.updateDsmModelEntityToApply(dsmModelEntity));
    }
    /**
     * 删除模型实例
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelEntity:remove')")
    @Log(title = "模型实例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{modelEntityIds}")
    public AjaxResult remove(@PathVariable Long[] modelEntityIds)
    {
        return toAjax(dsmModelEntityService.deleteDsmModelEntityByIds(modelEntityIds));
    }
    
    @PutMapping(value = "/imp/{regId}/{modelEntityClassId}")
    public AjaxResult imp(@PathVariable("regId") Long regId,@PathVariable("modelEntityClassId") Long modelEntityClassId)
    {
        return AjaxResult.success(dsmModelEntityService.impDsmModelEntity(regId , modelEntityClassId));
    }
}
