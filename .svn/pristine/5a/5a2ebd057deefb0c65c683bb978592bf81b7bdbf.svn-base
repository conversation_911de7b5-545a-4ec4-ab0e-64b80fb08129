import request from '@/utils/request'

// 查询数据补录模版列表
export function listApiTemplateMapping(query) {
  return request({
    url: '/api/apiTemplateMapping/list',
    method: 'get',
    params: query
  })
}

// 查询数据补录模版详细
export function getApiTemplateMapping(id) {
  return request({
    url: '/api/apiTemplateMapping/' + id,
    method: 'get'
  })
}

// 新增数据补录模版
export function addApiTemplateMapping(data) {
  return request({
    url: '/api/apiTemplateMapping',
    method: 'post',
    data: data
  })
}

// 修改数据补录模版
export function updateApiTemplateMapping(data) {
  return request({
    url: '/api/apiTemplateMapping',
    method: 'put',
    data: data
  })
}

// 删除数据补录模版
export function delApiTemplateMapping(id) {
  return request({
    url: '/api/apiTemplateMapping/' + id,
    method: 'delete'
  })
}

// 导出数据补录模版
export function exportApiTemplateMapping(query) {
  return request({
    url: '/api/apiTemplateMapping/export',
    method: 'get',
    params: query
  })
}

// 添加模板映射查询表名
export function getTableNames() {
  return request({
    url: '/api/apiTemplateMapping/getTableNames',
    method: 'get'
  })
}

// 添加模板映射查询表名
export function getTableMapping(tableName) {
  return request({
    url: '/api/apiTemplateMapping/getTableMapping/'+tableName,
    method: 'get',
  })
}
