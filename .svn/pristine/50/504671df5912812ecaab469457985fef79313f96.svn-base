package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmDimensionDetaiTemp;
import com.dqms.dsm.service.IDsmDimensionDetaiTempService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 维度明细临时Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmDimensionDetaiTemp")
public class DsmDimensionDetaiTempController extends BaseController
{
    @Autowired
    private IDsmDimensionDetaiTempService dsmDimensionDetaiTempService;

    /**
     * 查询维度明细临时列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimensionDetaiTemp:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmDimensionDetaiTemp dsmDimensionDetaiTemp)
    {
        startPage();
        List<DsmDimensionDetaiTemp> list = dsmDimensionDetaiTempService.selectDsmDimensionDetaiTempList(dsmDimensionDetaiTemp);
        return getDataTable(list);
    }

    /**
     * 导出维度明细临时列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimensionDetaiTemp:export')")
    @Log(title = "维度明细临时", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmDimensionDetaiTemp dsmDimensionDetaiTemp)
    {
        List<DsmDimensionDetaiTemp> list = dsmDimensionDetaiTempService.selectDsmDimensionDetaiTempList(dsmDimensionDetaiTemp);
        ExcelUtil<DsmDimensionDetaiTemp> util = new ExcelUtil<DsmDimensionDetaiTemp>(DsmDimensionDetaiTemp.class);
        return util.exportExcel(list, "dsmDimensionDetaiTemp");
    }

    /**
     * 获取维度明细临时详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimensionDetaiTemp:query')")
    @GetMapping(value = "/{dimensionDetaiTempId}")
    public AjaxResult getInfo(@PathVariable("dimensionDetaiTempId") Long dimensionDetaiTempId)
    {
        return AjaxResult.success(dsmDimensionDetaiTempService.selectDsmDimensionDetaiTempById(dimensionDetaiTempId));
    }

    /**
     * 新增维度明细临时
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimensionDetaiTemp:add')")
    @Log(title = "维度明细临时", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmDimensionDetaiTemp dsmDimensionDetaiTemp)
    {
        return toAjax(dsmDimensionDetaiTempService.insertDsmDimensionDetaiTemp(dsmDimensionDetaiTemp));
    }

    /**
     * 修改维度明细临时
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimensionDetaiTemp:edit')")
    @Log(title = "维度明细临时", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmDimensionDetaiTemp dsmDimensionDetaiTemp)
    {
        return toAjax(dsmDimensionDetaiTempService.updateDsmDimensionDetaiTemp(dsmDimensionDetaiTemp));
    }

    /**
     * 删除维度明细临时
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimensionDetaiTemp:remove')")
    @Log(title = "维度明细临时", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dimensionDetaiTempIds}")
    public AjaxResult remove(@PathVariable Long[] dimensionDetaiTempIds)
    {
        return toAjax(dsmDimensionDetaiTempService.deleteDsmDimensionDetaiTempByIds(dimensionDetaiTempIds));
    }
}
