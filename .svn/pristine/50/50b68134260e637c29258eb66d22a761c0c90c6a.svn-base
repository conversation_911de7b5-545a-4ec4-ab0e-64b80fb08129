import request from "@/utils/request";
import * as echarts from "echarts";
// 查询检查规则任务管理列表
export function listTask(query) {
  return request({
    url: "/dqm/task/list",
    method: "get",
    params: query
  });
}

// 查询检查规则任务管理详细
export function getTask(dqmValidationTaskId) {
  return request({
    url: "/dqm/task/" + dqmValidationTaskId,
    method: "get"
  });
}

// 新增检查规则任务管理
export function addTask(data) {
  return request({
    url: "/dqm/dqmValidationTask",
    method: "post",
    data: data
  });
}

// 修改检查规则任务管理
export function updateTask(data) {
  return request({
    url: "/dqm/task",
    method: "put",
    data: data
  });
}

// 删除检查规则任务管理
export function delTask(dqmValidationTaskId) {
  return request({
    url: "/dqm/task/" + dqmValidationTaskId,
    method: "delete"
  });
}

// 查询最近30天规则任务趋势
export function getExeCuteState30(validationRuleCateId) {
  return request({
    url: "/dqm/task/getExeCuteState30/" + validationRuleCateId,
    method: "get"
  });
}
// 查询最近30天规则任务错误结果趋势
export function getErrorRestule30(validationRuleCateId) {
  return request({
    url: "/dqm/task/getErrorRestule30/" + validationRuleCateId,
    method: "get"
  });
}
// 导出检查规则任务管理
export function exportTask(query) {
  return request({
    url: "/dqm/task/export",
    method: "get",
    params: query
  });
}
export function getEcharsXQ(data1, data2) {
  var chartDom = document.getElementById("echarsXQ");
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    title: {
      text: "近30天规则检核日志分布"
    },
    xAxis: {
      type: "category",
      data: data1,
      axisLabel: {
        interval: 0,
        rotate: 40
      }
    },
    tooltip: { trigger: "axis" },
    yAxis: {
      type: "value"
    },
    series: [
      {
        data: data2,
        type: "bar",
        showBackground: true,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)"
        }
      }
    ]
  };
  option && myChart.setOption(option);
}
