import request from '@/utils/request'

// 查询任务关系列表
export function listTaskRelation(query) {
  return request({
    url: '/task/taskRelation/list',
    method: 'get',
    params: query
  })
}
export function listTaskInstanceRelation(query) {
  return request({
    url: '/task/taskRelation/listInstance',
    method: 'get',
    params: query
  })
}

// 查询任务关系详细
export function getTaskRelation(taskRelationId) {
  return request({
    url: '/task/taskRelation/' + taskRelationId,
    method: 'get'
  })
}

// 新增任务关系
export function addTaskRelation(data) {
  return request({
    url: '/task/taskRelation',
    method: 'post',
    data: data
  })
}

// 修改任务关系
export function updateTaskRelation(data) {
  return request({
    url: '/task/taskRelation',
    method: 'put',
    data: data
  })
}

// 删除任务关系
export function delTaskRelation(taskRelationId) {
  return request({
    url: '/task/taskRelation/' + taskRelationId,
    method: 'delete'
  })
}

// 导出任务关系
export function exportTaskRelation(query) {
  return request({
    url: '/task/taskRelation/export',
    method: 'get',
    params: query
  })
}

// 导出任务关系
export function exportEtlTaskRelationList(query) {
  return request({
    url: '/task/taskRelation/exportEtlTaskRelationList',
    method: 'get',
    params: query
  })
}
//下载任务导入关系模板
export function importTemplateRel() {
  return request({
    url: '/task/taskRelation/importTemplate',
    method: 'get'
  })
}
//删除任务关系
export function delTaskRelationByNode(data) {
  return request({
    url: '/task/taskRelation/delTaskRelationByNode',
    method: 'post',
    data: data
  })
}

export function toLoopCheck(taskRelationId) {
  return request({
    url: '/task/taskRelation/loopCheck/'+taskRelationId,
    method: 'put'
  })
}
