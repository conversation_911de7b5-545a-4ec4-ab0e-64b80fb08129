package com.dqms.task.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import com.dqms.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.framework.web.service.TokenService;
import com.dqms.task.domain.EtlTaskCalendarClass;
import com.dqms.task.domain.EtlTaskCalendarClassTreeSelect;
import com.dqms.task.mapper.EtlTaskCalendarClassMapper;
import com.dqms.task.service.IEtlTaskCalendarClassService;

import javax.management.RuntimeErrorException;

/**
 * 调度日历管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-01
 */
@Service
public class EtlTaskCalendarClassServiceImpl implements IEtlTaskCalendarClassService
{
    @Autowired
    private EtlTaskCalendarClassMapper etlTaskCalendarClassMapper;


    @Autowired
    private TokenService tokenService;
    /**
     * 查询调度日历管理
     *
     * @param taskCalendarClassId 调度日历管理ID
     * @return 调度日历管理
     */
    @Override
    public EtlTaskCalendarClass selectEtlTaskCalendarClassById(Long taskCalendarClassId)
    {
        return etlTaskCalendarClassMapper.selectEtlTaskCalendarClassById(taskCalendarClassId);
    }

    /**
     * 查询调度日历管理列表
     *
     * @param etlTaskCalendarClass 调度日历管理
     * @return 调度日历管理
     */
    @Override
    public List<EtlTaskCalendarClass> selectEtlTaskCalendarClassList(EtlTaskCalendarClass etlTaskCalendarClass)
    {
        return etlTaskCalendarClassMapper.selectEtlTaskCalendarClassList(etlTaskCalendarClass);
    }

    /**
     * 新增调度日历管理
     *
     * @param etlTaskCalendarClass 调度日历管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEtlTaskCalendarClass(EtlTaskCalendarClass etlTaskCalendarClass)
    {
    	EtlTaskCalendarClass info = etlTaskCalendarClassMapper.selectEtlTaskCalendarClassById(etlTaskCalendarClass.getParentId());
    	if(info!=null) {
    		etlTaskCalendarClass.setAncestors(info.getAncestors() + "," + etlTaskCalendarClass.getParentId());
    		etlTaskCalendarClass.setTaskCalendarClassNameFull(info.getTaskCalendarClassNameFull() + "/" + etlTaskCalendarClass.getTaskCalendarClassName());
    	}else {
    		etlTaskCalendarClass.setAncestors("0");
    		etlTaskCalendarClass.setTaskCalendarClassNameFull(etlTaskCalendarClass.getTaskCalendarClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	etlTaskCalendarClass.setCreateTime(DateUtils.getNowDate());
    	etlTaskCalendarClass.setCreateId(loginUser.getUser().getUserId());
    	etlTaskCalendarClass.setCreateBy(loginUser.getUser().getNickName());
    	etlTaskCalendarClass.setUpdateTime(DateUtils.getNowDate());
    	etlTaskCalendarClass.setUpdateId(loginUser.getUser().getUserId());
    	etlTaskCalendarClass.setUpdateBy(loginUser.getUser().getNickName());
        return etlTaskCalendarClassMapper.insertEtlTaskCalendarClass(etlTaskCalendarClass);
    }

    /**
     * 修改调度日历管理
     *
     * @param etlTaskCalendarClass 调度日历管理
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEtlTaskCalendarClass(EtlTaskCalendarClass etlTaskCalendarClass)
    {
        if(etlTaskCalendarClass.getTaskCalendarClassId().equals(etlTaskCalendarClass.getParentId())){
            throw new RuntimeErrorException(null, "父分类不能是本身！");
        }
    	EtlTaskCalendarClass newC = etlTaskCalendarClassMapper.selectEtlTaskCalendarClassById(etlTaskCalendarClass.getParentId());
    	EtlTaskCalendarClass oldC = etlTaskCalendarClassMapper.selectEtlTaskCalendarClassById(etlTaskCalendarClass.getTaskCalendarClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String ancestorsFlag = oldC.getAncestors() + "," + etlTaskCalendarClass.getTaskCalendarClassId();
             if(newC.getAncestors().contains(ancestorsFlag)){
                 throw new RuntimeErrorException(null, "父分类不能是本身下级！");
             }
             String newAncestors = newC.getAncestors() + "," + etlTaskCalendarClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newTaskClassNameFull = newC.getTaskCalendarClassNameFull() + "/" + etlTaskCalendarClass.getTaskCalendarClassName();
             String oldTaskClassNameFull = oldC.getTaskCalendarClassNameFull();
             etlTaskCalendarClass.setAncestors(newAncestors);
             etlTaskCalendarClass.setTaskCalendarClassNameFull(newTaskClassNameFull);
             updateEtlTaskCalendarClassChildren(etlTaskCalendarClass.getTaskCalendarClassId(), newAncestors, oldAncestors);
             updateEtlTaskCalendarClassNameFullChildren(etlTaskCalendarClass.getTaskCalendarClassId(), newTaskClassNameFull, oldTaskClassNameFull);
         }else if(newC==null){
             String newAncestors = "0";
             String oldAncestors = oldC.getAncestors();
             String newTaskClassNameFull = etlTaskCalendarClass.getTaskCalendarClassName();
             String oldTaskClassNameFull = oldC.getTaskCalendarClassNameFull();
             etlTaskCalendarClass.setAncestors(newAncestors);
             etlTaskCalendarClass.setTaskCalendarClassNameFull(newTaskClassNameFull);
             updateEtlTaskCalendarClassChildren(etlTaskCalendarClass.getTaskCalendarClassId(), newAncestors, oldAncestors);
             updateEtlTaskCalendarClassNameFullChildren(etlTaskCalendarClass.getTaskCalendarClassId(), newTaskClassNameFull, oldTaskClassNameFull);
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	etlTaskCalendarClass.setUpdateTime(DateUtils.getNowDate());
    	etlTaskCalendarClass.setUpdateId(loginUser.getUser().getUserId());
    	etlTaskCalendarClass.setUpdateBy(loginUser.getUser().getNickName());
        return etlTaskCalendarClassMapper.updateEtlTaskCalendarClass(etlTaskCalendarClass);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateEtlTaskCalendarClassChildren(Long taskCalendarClassId, String newAncestors, String oldAncestors)
    {
        List<EtlTaskCalendarClass> children = etlTaskCalendarClassMapper.selectChildrenEtlTaskCalendarClassById(taskCalendarClassId);
        for (EtlTaskCalendarClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	etlTaskCalendarClassMapper.updateEtlTaskCalendarClassChildren(children);
        }
    }
    @Transactional
    public void updateEtlTaskCalendarClassNameFullChildren(Long taskCalendarClassId, String newTaskCalendarClassNameFull, String oldTaskCalendarClassNameFull)
    {
        List<EtlTaskCalendarClass> children = etlTaskCalendarClassMapper.selectChildrenEtlTaskCalendarClassById(taskCalendarClassId);
        for (EtlTaskCalendarClass child : children)
        {
            child.setTaskCalendarClassNameFull(child.getTaskCalendarClassNameFull().replace(oldTaskCalendarClassNameFull, newTaskCalendarClassNameFull));
        }
        if (children.size() > 0)
        {
        	etlTaskCalendarClassMapper.updateEtlTaskCalendarClassNameFullChildren(children);
        }
    }
    /**
     * 批量删除调度日历管理
     *
     * @param taskCalendarClassIds 需要删除的调度日历管理ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskCalendarClassByIds(Long[] taskCalendarClassIds)
    {
        for(Long taskCalendarClassId : taskCalendarClassIds){
            List<EtlTaskCalendarClass> etlTaskCalendarClasses = etlTaskCalendarClassMapper.selectChildrenEtlTaskCalendarClassById(taskCalendarClassId);
            if(etlTaskCalendarClasses!=null && etlTaskCalendarClasses.size()>0){
                throw new CustomException("请先删除子分类");
            }
        }
        return etlTaskCalendarClassMapper.deleteEtlTaskCalendarClassByIds(taskCalendarClassIds);
    }

    /**
     * 删除调度日历管理信息
     *
     * @param taskCalendarClassId 调度日历管理ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskCalendarClassById(Long taskCalendarClassId)
    {
        List<EtlTaskCalendarClass> etlTaskCalendarClasses = etlTaskCalendarClassMapper.selectChildrenEtlTaskCalendarClassById(taskCalendarClassId);
        if(etlTaskCalendarClasses!=null && etlTaskCalendarClasses.size()>0){
            throw new CustomException("请先删除子分类");
        }
        return etlTaskCalendarClassMapper.deleteEtlTaskCalendarClassById(taskCalendarClassId);
    }
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<EtlTaskCalendarClassTreeSelect> buildEtlTaskCalendarClassTreeSelect(List<EtlTaskCalendarClass> etlTaskCalendarClasss)
    {
        List<EtlTaskCalendarClass> etlTaskCalendarClassTrees = buildEtlTaskCalendarClassTree(etlTaskCalendarClasss);
        return etlTaskCalendarClassTrees.stream().map(EtlTaskCalendarClassTreeSelect::new).collect(Collectors.toList());
    }
    
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<EtlTaskCalendarClass> buildEtlTaskCalendarClassTree(List<EtlTaskCalendarClass> etlTaskCalendarClasss)
    {
        List<EtlTaskCalendarClass> returnList = new ArrayList<EtlTaskCalendarClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (EtlTaskCalendarClass etlTaskCalendarClass : etlTaskCalendarClasss)
        {
            tempList.add(etlTaskCalendarClass.getTaskCalendarClassId());
        }
        for (Iterator<EtlTaskCalendarClass> iterator = etlTaskCalendarClasss.iterator(); iterator.hasNext();)
        {
        	EtlTaskCalendarClass etlTaskCalendarClass = (EtlTaskCalendarClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(etlTaskCalendarClass.getParentId()))
            {
                recursionFn(etlTaskCalendarClasss, etlTaskCalendarClass);
                returnList.add(etlTaskCalendarClass);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = etlTaskCalendarClasss;
        }
        return returnList;
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<EtlTaskCalendarClass> list, EtlTaskCalendarClass t)
    {
        // 得到子节点列表
        List<EtlTaskCalendarClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (EtlTaskCalendarClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<EtlTaskCalendarClass> getChildList(List<EtlTaskCalendarClass> list, EtlTaskCalendarClass t)
    {
        List<EtlTaskCalendarClass> tlist = new ArrayList<EtlTaskCalendarClass>();
        Iterator<EtlTaskCalendarClass> it = list.iterator();
        while (it.hasNext())
        {
        	EtlTaskCalendarClass n = (EtlTaskCalendarClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getTaskCalendarClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<EtlTaskCalendarClass> list, EtlTaskCalendarClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
