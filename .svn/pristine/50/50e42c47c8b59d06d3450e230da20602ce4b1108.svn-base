package com.dqms.common.enums;

public enum DbType {

    MYS<PERSON>("MYSQL", "MYSQL"),
    <PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
    DB2("DB2", "DB2"),
    HIVE2("HIVE2","HIVE2"),
    PostgreSQL("PostgreSQL","PostgreSQL"),
    IMPALA("IMPALA","IMPALA"),
    PRESTO("PRESTO","PRESTO"),
    SQLSERVER("SQLSERVER","SQLSERVER"),
    CLICKHOUSE("CLICKHOUSE","CLICKHOUSE"),
	RAPIDSDB("RAPIDSDB","RAPIDSDB"),
    DM("DM","DM");

    private final String code;
    private final String info;

    DbType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
