package com.dqms.dqm.mapper;

import com.dqms.dqm.domain.DqmValidationDetail;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
@Repository
public interface DqmValidationDetailMapper {

    public int selectTableNameExit(String tableName);
    /**
     * 调用存储过程创建表
     */
    public void createTableDetail(Map<String,Object> tableName);

    /**
     * 添加问题明细数据
     * @param dqmValidationDetail
     */
    int insertDetailTable(DqmValidationDetail dqmValidationDetail);

    /**
     * 查询问题明细列表数据
     */
    List<Map<String, Object>> selectDetailList(DqmValidationDetail dqmValidationDetail);
}
