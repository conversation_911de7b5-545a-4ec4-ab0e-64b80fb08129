package com.dqms.dsm.controller;

import java.util.List;
import java.util.UUID;

import com.dqms.dsm.domain.DsmDimension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsm.domain.DsmStandard;
import com.dqms.dsm.domain.vo.DsmStandardVo;
import com.dqms.dsm.service.IDsmStandardService;
import com.dqms.system.service.ISysConfigService;

/**
 * 基础标准Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmStandard")
public class DsmStandardController extends BaseController
{
    @Autowired
    private IDsmStandardService dsmStandardService;

    @Autowired
    public ISysConfigService configService;
    /**
     * 查询基础标准列表
     */
   // @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmStandard dsmStandard)
    {
        startPage();
        List<DsmStandard> list = dsmStandardService.selectDsmStandardList(dsmStandard);
        return getDataTable(list);
    }

    @GetMapping("/listByNo")
    public TableDataInfo listByNo(DsmStandard dsmStandard)
    {
        startPage();
        List<DsmStandard> list = dsmStandardService.selectDsmStandardListByNo(dsmStandard);
        return getDataTable(list);
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<DsmStandardVo> util = new ExcelUtil<>(DsmStandardVo.class);
        return util.importTemplateExcel("基础标准");
    }
    /**
     * 导出基础标准列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:export')")
    @Log(title = "基础标准", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmStandardVo dsmStandardVo)
    {
        List<DsmStandardVo> list = dsmStandardService.selectDsmStandardVoList(dsmStandardVo);
        ExcelUtil<DsmStandardVo> util = new ExcelUtil<DsmStandardVo>(DsmStandardVo.class);
        return util.exportExcel(list, "dsmStandardVo");
    }
    @Log(title = "导入基础标准", businessType = BusinessType.IMPORT )
    @PreAuthorize("@ss.hasPermi('task:task:export')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        //获取模版实体
        ExcelUtil<DsmStandardVo> util = new ExcelUtil<>(DsmStandardVo.class);
        //获取excel中数据植入实体集合
        List<DsmStandardVo> dsmStandardList = util.importExcel(file.getInputStream());
        //做数据校验
        String message = dsmStandardService.importDsmStandard(dsmStandardList, updateSupport);
        return AjaxResult.success(message);
    }
    /**
     * 获取基础标准详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:query')")
    @GetMapping(value = "/{standardId}")
    public AjaxResult getInfo(@PathVariable("standardId") Long standardId)
    {
        return AjaxResult.success(dsmStandardService.selectDsmStandardById(standardId));
    }
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:query')")
    @GetMapping(value = "/getVoById/{standardId}")
    public AjaxResult getVoById(@PathVariable("standardId") Long standardId)
    {
        return AjaxResult.success(dsmStandardService.selectDsmStandardVoById(standardId));
    }

    /**
     * 新增基础标准
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:add')")
    @Log(title = "基础标准", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmStandard dsmStandard)
    {
        dsmStandard.setVersion(1L);
        dsmStandard.setStandardNo(UUID.randomUUID().toString());
        return toAjax(dsmStandardService.insertDsmStandard(dsmStandard,null));
    }

    /**
     * 修改基础标准
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:edit')")
    @Log(title = "基础标准", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmStandard dsmStandard)
    {
        return toAjax(dsmStandardService.updateDsmStandard(dsmStandard));
    }
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:updateStatus')")
    @Log(title = "基础标准", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateDsmDimensionStatus(@RequestBody  DsmStandard dsmStandard)
    {
        return toAjax(dsmStandardService.updatedsmStandardStatus(dsmStandard));
    }
    /**
     * 删除基础标准
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandard:remove')")
    @Log(title = "基础标准", businessType = BusinessType.DELETE)
	@DeleteMapping("/{standardIds}")
    public AjaxResult remove(@PathVariable Long[] standardIds)
    {
        return toAjax(dsmStandardService.deleteDsmStandardByIds(standardIds));
    }

}
