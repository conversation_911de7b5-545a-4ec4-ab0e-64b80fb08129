package com.dqms.api.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.api.domain.ApiDefineColumn;
import com.dqms.api.service.IApiDefineColumnService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 接口输出Controller
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@RestController
@RequestMapping("/api/apiDefineColumn")
public class ApiDefineColumnController extends BaseController
{
    @Autowired
    private IApiDefineColumnService apiDefineColumnService;

    /**
     * 查询接口输出列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineColumn:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApiDefineColumn apiDefineColumn)
    {
        startPage();
        List<ApiDefineColumn> list = apiDefineColumnService.selectApiDefineColumnList(apiDefineColumn);
        return getDataTable(list);
    }

    /**
     * 导出接口输出列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineColumn:export')")
    @Log(title = "接口输出", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ApiDefineColumn apiDefineColumn)
    {
        List<ApiDefineColumn> list = apiDefineColumnService.selectApiDefineColumnList(apiDefineColumn);
        ExcelUtil<ApiDefineColumn> util = new ExcelUtil<ApiDefineColumn>(ApiDefineColumn.class);
        return util.exportExcel(list, "apiDefineColumn");
    }

    /**
     * 获取接口输出详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineColumn:query')")
    @GetMapping(value = "/{defineColumnId}")
    public AjaxResult getInfo(@PathVariable("defineColumnId") Long defineColumnId)
    {
        return AjaxResult.success(apiDefineColumnService.selectApiDefineColumnById(defineColumnId));
    }

    /**
     * 新增接口输出
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineColumn:add')")
    @Log(title = "接口输出", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApiDefineColumn apiDefineColumn)
    {
        return toAjax(apiDefineColumnService.insertApiDefineColumn(apiDefineColumn));
    }

    /**
     * 修改接口输出
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineColumn:edit')")
    @Log(title = "接口输出", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApiDefineColumn apiDefineColumn)
    {
        return toAjax(apiDefineColumnService.updateApiDefineColumn(apiDefineColumn));
    }

    /**
     * 删除接口输出
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineColumn:remove')")
    @Log(title = "接口输出", businessType = BusinessType.DELETE)
	@DeleteMapping("/{defineColumnIds}")
    public AjaxResult remove(@PathVariable Long[] defineColumnIds)
    {
        return toAjax(apiDefineColumnService.deleteApiDefineColumnByIds(defineColumnIds));
    }
    
}
