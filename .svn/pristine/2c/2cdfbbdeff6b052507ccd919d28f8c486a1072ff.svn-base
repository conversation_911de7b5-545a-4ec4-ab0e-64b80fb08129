package com.dqms.dqm.service;

import java.util.List;
import java.util.Map;

import com.dqms.dqm.domain.DqmValidationRuleCate;
import com.dqms.dqm.domain.vo.DqmValidationRuleCateVo;

/**
 * 检核规则Service接口
 *
 * <AUTHOR>
 * @date 2021-07-21
 */
public interface IDqmValidationRuleCateService
{
    /**
     * 查询检核规则
     *
     * @param validationRuleCateId 检核规则ID
     * @return 检核规则
     */
    public DqmValidationRuleCate selectDqmValidationRuleCateById(Long validationRuleCateId);

    /**
     * 查询检核规则列表
     *
     * @param dqmValidationRuleCate 检核规则
     * @return 检核规则集合
     */
    public List<DqmValidationRuleCate> selectDqmValidationRuleCateList(DqmValidationRuleCate dqmValidationRuleCate);
    public List<DqmValidationRuleCateVo> selectDqmValidationRuleCateVoList(DqmValidationRuleCate dqmValidationRuleCate);
    public List<DqmValidationRuleCate> selectDqmValidationRuleCateListByUser(DqmValidationRuleCate dqmValidationRuleCate);

    /**
     * 新增检核规则
     *
     * @param dqmValidationRuleCate 检核规则
     * @return 结果
     */
    public int insertDqmValidationRuleCate(DqmValidationRuleCate dqmValidationRuleCate);

    /**
     * 修改检核规则
     *
     * @param dqmValidationRuleCate 检核规则
     * @return 结果
     */
    public int updateDqmValidationRuleCate(DqmValidationRuleCate dqmValidationRuleCate);

    /**
     * 批量删除检核规则
     *
     * @param validationRuleCateIds 需要删除的检核规则ID
     * @return 结果
     */
    public int deleteDqmValidationRuleCateByIds(Long[] validationRuleCateIds);

    /**
     * 删除检核规则信息
     *
     * @param validationRuleCateId 检核规则ID
     * @return 结果
     */
    public int deleteDqmValidationRuleCateById(Long validationRuleCateId);
    /**
     *  执行
     * @param dqmValidationRuleCate param
     * @return result
     */
    public int executorDqmValidationRuleCate(DqmValidationRuleCate dqmValidationRuleCate,String exeType,String loadDate);

    /**
     * 任务中心查询绑定的任务
     *
     * @param taskId任务IDs
     * @return 结果
     */
    public List<DqmValidationRuleCate>  selectDqmValidationRuleCateListByTaskId(Long taskId);
    public List<DqmValidationRuleCate>  selectDqmValidationRuleCateListByPage(Long datasourceId,int page ,int size);
    /**
     * 任务中心查询未绑定的任务
     *
     * @param regIds 源系统注册ID
     * @return 结果
     */
    public List<DqmValidationRuleCate> selectUnDqmValidationRuleCateListByTaskId(DqmValidationRuleCate dqmValidationRuleCate);
    /**
     * 任务中心添加绑定任务
     *
     * @param taskId 任务IDs regIds 源系统注册ID
     * @return 结果
     */
    public int addDqmValidationRuleCateAndTaskRel(Long taskId,Long[] regIds);
    /**
     * 任务中心删除绑定任务
     *
     * @param taskId 任务IDs
     * @return 结果
     */
    public int delDqmValidationRuleCateAndTaskRel(Long taskId);

    /**
     * 规则与系统分布结构
     * @return
     */
    public List<Map<String,Object>> getRuleCateSystem(DqmValidationRuleCate dqmValidationRuleCate);

    /**
     * 规则主题分布结构
     * @return
     */
    public List<Map<String,Object>> getRuleCateType(DqmValidationRuleCate dqmValidationRuleCate);
    
    /**
     * 规则与状态分布结构
     * @return
     */
    public List<Map<String,Object>> getRuleCateStatus(DqmValidationRuleCate dqmValidationRuleCate);
    /**
     * 导入数据质量检查规则
     * @return
     */
    public String importDqmValidationRuleCate(List<DqmValidationRuleCateVo> dqmValidationRuleCateVoList,boolean updateSupport);
    public List<DqmValidationRuleCateVo> exportDqmValidationRuleCateVoList(DqmValidationRuleCate dqmValidationRuleCate);


}
