<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dic.mapper.DicManualDataInstallRuleMapper">
    
    <resultMap type="DicManualDataInstallRule" id="DicManualDataInstallRuleResult">
        <result property="manualDataInstallId"    column="manual_data_install_id"    />
        <result property="manualDataRuleId"    column="manual_data_rule_id"    />
    </resultMap>

    <sql id="selectDicManualDataInstallRuleVo">
        select manual_data_install_id, manual_data_rule_id from dic_manual_data_install_rule
    </sql>

    <select id="selectDicManualDataInstallRuleList" parameterType="DicManualDataInstallRule" resultMap="DicManualDataInstallRuleResult">
        <include refid="selectDicManualDataInstallRuleVo"/>
        <where>  
            <if test="manualDataInstallId != null "> and manual_data_install_id = #{manualDataInstallId}</if>
            <if test="manualDataRuleId != null "> and manual_data_rule_id = #{manualDataRuleId}</if>
        </where>
    </select>
    
    <select id="selectDicManualDataInstallRuleById" parameterType="Long" resultMap="DicManualDataInstallRuleResult">
        <include refid="selectDicManualDataInstallRuleVo"/>
        where manual_data_install_id = #{manualDataInstallId}
    </select>
        
    <insert id="insertDicManualDataInstallRule" parameterType="DicManualDataInstallRule">
        insert into dic_manual_data_install_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="manualDataInstallId != null">manual_data_install_id,</if>
            <if test="manualDataRuleId != null">manual_data_rule_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="manualDataInstallId != null">#{manualDataInstallId},</if>
            <if test="manualDataRuleId != null">#{manualDataRuleId},</if>
         </trim>
    </insert>

    <update id="updateDicManualDataInstallRule" parameterType="DicManualDataInstallRule">
        update dic_manual_data_install_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="manualDataRuleId != null">manual_data_rule_id = #{manualDataRuleId},</if>
        </trim>
        where manual_data_install_id = #{manualDataInstallId}
    </update>

    <delete id="deleteDicManualDataInstallRuleById" parameterType="Long">
        delete from dic_manual_data_install_rule where manual_data_install_id = #{manualDataInstallId}
    </delete>

    <delete id="deleteDicManualDataInstallRuleByDefineId" parameterType="Long">
        delete from dic_manual_data_install_rule where manual_data_install_id in (select manual_data_install_id from dic_manual_data_install where manual_data_id = #{manualDataId})
    </delete>
    
    <delete id="deleteDicManualDataInstallRuleByIds" parameterType="String">
        delete from dic_manual_data_install_rule where manual_data_install_id in 
        <foreach item="manualDataInstallId" collection="array" open="(" separator="," close=")">
            #{manualDataInstallId}
        </foreach>
    </delete>
</mapper>