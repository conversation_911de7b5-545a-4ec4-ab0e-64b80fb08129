<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dam.mapper.DamAssetsSubscribeMapper">
    
    <resultMap type="DamAssetsSubscribe" id="DamAssetsSubscribeResult">
        <result property="assetsSubscribeId"    column="assets_subscribe_id"    />
        <result property="damAssetsId"    column="dam_assets_id"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="subscribeType"    column="subscribe_type"    />
        <result property="userName"    column="user_name"    />
    </resultMap>

    <sql id="selectDamAssetsSubscribeVo">
        select t.assets_subscribe_id, t.dam_assets_id, t.create_id, t.create_time, t.subscribe_type,u.user_name from dam_assets_subscribe t left join sys_user u on t.create_id=u.user_id
    </sql>

    <select id="selectDamAssetsSubscribeList" parameterType="DamAssetsSubscribe" resultMap="DamAssetsSubscribeResult">
        <include refid="selectDamAssetsSubscribeVo"/>
        <where>  
            <if test="damAssetsId != null "> and t.dam_assets_id = #{damAssetsId}</if>
            <if test="createId != null "> and t.create_id = #{createId}</if>
        </where>
        order by t.assets_subscribe_id desc
    </select>
    
    <select id="selectDamAssetsSubscribeById" parameterType="Long" resultMap="DamAssetsSubscribeResult">
        <include refid="selectDamAssetsSubscribeVo"/>
        where t.assets_subscribe_id = #{assetsSubscribeId}
    </select>
        
    <insert id="insertDamAssetsSubscribe" parameterType="DamAssetsSubscribe" useGeneratedKeys="true" keyProperty="assetsSubscribeId">
        insert into dam_assets_subscribe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="damAssetsId != null">dam_assets_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="subscribeType != null">subscribe_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="damAssetsId != null">#{damAssetsId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="subscribeType != null">#{subscribeType},</if>
         </trim>
    </insert>

    <update id="updateDamAssetsSubscribe" parameterType="DamAssetsSubscribe">
        update dam_assets_subscribe
        <trim prefix="SET" suffixOverrides=",">
            <if test="damAssetsId != null">dam_assets_id = #{damAssetsId},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="subscribeType != null">subscribe_type = #{subscribeType},</if>
        </trim>
        where assets_subscribe_id = #{assetsSubscribeId}
    </update>

    <delete id="deleteDamAssetsSubscribeById" parameterType="Long">
        delete from dam_assets_subscribe where assets_subscribe_id = #{assetsSubscribeId}
    </delete>

    <delete id="deleteDamAssetsSubscribeByIds" parameterType="String">
        delete from dam_assets_subscribe where assets_subscribe_id in 
        <foreach item="assetsSubscribeId" collection="array" open="(" separator="," close=")">
            #{assetsSubscribeId}
        </foreach>
    </delete>
</mapper>