package com.dqms.dsm.domain.vo;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 维度字典对象 dsm_dimension
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
public class DsmDimensionDetailVo2 extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 维度ID */
    private Long dimensionId;

    /** 全路径 */
    private String classNameFull;

    /** 分类名称 */
    private String className;

    /** 系统 */
    @Excel(name = "系统")
    private String systemName;
    private Long systemId;

    /** 数据源 */
    private String datasourceName;
    private Long datasourceId;

    /** 名称 */
    private String dimensionName;

    /** 编码 */
    @Excel(name = "类型编码")
    private String dimensionCode;

    /** 类型 */
    private String dimensionType;

    /** 展示方式 */
    private String showType;

    /** 数据类型 */
    private String dataType;

    /** 状态 */
    private String status;

    /** 同步SQL */
    private String execSql;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    /** 分类ID */
    private Long dimensionClassId;

    /** 同步状态 */
    private String taskStatus;
    
    /** 同步信息 */
    private String taskMsg;
    
    /** 关联任务 */
    private String taskId;
    
    /** 引用*/
    private Long[] propIds;
    
    private String discernFlag;
    
    private String checkFlag;

    /** 维度明细ID */
    private Long dimensionDetailId;

    /** 编码 */
    @Excel(name = "字典编码")
    private String detailCode;

    /** 名称 */
    @Excel(name = "字典名称")
    private String detailName;

    /** 录入方式 */
    private String operateType;

    /** 状态 */
    private String detailStatus;


    public void setDimensionId(Long dimensionId)
    {
        this.dimensionId = dimensionId;
    }

    public Long getDimensionId()
    {
        return dimensionId;
    }
    public void setDimensionName(String dimensionName)
    {
        this.dimensionName = dimensionName;
    }

    public String getDimensionName()
    {
        return dimensionName;
    }
    public void setDimensionCode(String dimensionCode)
    {
        this.dimensionCode = dimensionCode;
    }

    public String getDimensionCode()
    {
        return dimensionCode;
    }
    public void setDimensionType(String dimensionType)
    {
        this.dimensionType = dimensionType;
    }

    public String getDimensionType()
    {
        return dimensionType;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }
    public void setShowType(String showType)
    {
        this.showType = showType;
    }

    public String getShowType()
    {
        return showType;
    }
    public void setDataType(String dataType)
    {
        this.dataType = dataType;
    }

    public String getDataType()
    {
        return dataType;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setExecSql(String execSql)
    {
        this.execSql = execSql;
    }

    public String getExecSql()
    {
        return execSql;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getDimensionClassId() {
		return dimensionClassId;
	}

	public void setDimensionClassId(Long dimensionClassId) {
		this.dimensionClassId = dimensionClassId;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getClassNameFull() {
		return classNameFull;
	}

	public void setClassNameFull(String classNameFull) {
		this.classNameFull = classNameFull;
	}

	public String getTaskStatus() {
		return taskStatus;
	}

	public void setTaskStatus(String taskStatus) {
		this.taskStatus = taskStatus;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getTaskMsg() {
		return taskMsg;
	}

	public void setTaskMsg(String taskMsg) {
		this.taskMsg = taskMsg;
	}

	public Long[] getPropIds() {
		return propIds;
	}

	public void setPropIds(Long[] propIds) {
		this.propIds = propIds;
	}

	public String getDiscernFlag() {
		return discernFlag;
	}

	public void setDiscernFlag(String discernFlag) {
		this.discernFlag = discernFlag;
	}

	public String getCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public Long getDimensionDetailId() {
        return dimensionDetailId;
    }

    public void setDimensionDetailId(Long dimensionDetailId) {
        this.dimensionDetailId = dimensionDetailId;
    }

    public String getDetailName() {
        return detailName;
    }

    public void setDetailName(String detailName) {
        this.detailName = detailName;
    }

    public String getDetailCode() {
        return detailCode;
    }

    public void setDetailCode(String detailCode) {
        this.detailCode = detailCode;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getDetailStatus() {
        return detailStatus;
    }

    public void setDetailStatus(String detailStatus) {
        this.detailStatus = detailStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dimensionId", getDimensionId())
            .append("dimensionName", getDimensionName())
            .append("dimensionCode", getDimensionCode())
            .append("dimensionType", getDimensionType())
            .append("systemId", getSystemId())
            .append("datasourceId", getDatasourceId())
            .append("showType", getShowType())
            .append("dataType", getDataType())
            .append("status", getStatus())
            .append("execSql", getExecSql())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())

            .append("dimensionDetailId", getDimensionDetailId())
            .append("detailName", getDetailName())
            .append("detailCode", getDetailCode())
            .append("operateType", getOperateType())
            .append("detailStatus", getDetailStatus())
            .toString();
    }
}
