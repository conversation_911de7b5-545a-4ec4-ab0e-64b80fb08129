{"name": "bpmn-js-example-properties-panel", "version": "0.0.0", "description": "A bpmn-js modeler + properties panel example", "main": "app/index.js", "scripts": {"all": "grunt", "dev": "grunt auto-build"}, "repository": {"type": "git", "url": "https://github.com/bpmn-io/bpmn-js-examples"}, "keywords": ["bpmnjs-example"], "author": {"name": "<PERSON>", "url": "https://github.com/nikku"}, "contributors": [{"name": "bpmn.io contributors", "url": "https://github.com/bpmn-io"}], "license": "MIT", "devDependencies": {"esmify": "^2.1.1", "grunt": "^1.0.4", "grunt-browserify": "^5.3.0", "grunt-contrib-connect": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-less": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "load-grunt-tasks": "^5.0.0", "stringify": "^5.2.0"}, "dependencies": {"bpmn-js": "^7.0.0", "bpmn-js-properties-panel": "^0.32.0", "camunda-bpmn-moddle": "^4.0.1", "diagram-js": "^5.0.0", "jquery": "^3.4.1", "min-dash": "^3.5.0", "x2js": "^3.4.0"}}