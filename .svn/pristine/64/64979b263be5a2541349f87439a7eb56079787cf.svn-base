package com.dqms.dqm.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.utils.DateUtils;
import com.dqms.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dqm.mapper.DqmValidationProblemMapper;
import com.dqms.dqm.domain.DqmValidationProblem;
import com.dqms.dqm.service.IDqmValidationProblemService;

/**
 * 检查规则任务问题管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@Service
public class DqmValidationProblemServiceImpl implements IDqmValidationProblemService
{
    @Autowired
    private DqmValidationProblemMapper dqmValidationProblemMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询检查规则任务问题管理
     *
     * @param dqmValidationProblemId 检查规则任务问题管理ID
     * @return 检查规则任务问题管理
     */
    @Override
    public DqmValidationProblem selectDqmValidationProblemById(Integer dqmValidationProblemId)
    {
        return dqmValidationProblemMapper.selectDqmValidationProblemById(dqmValidationProblemId);
    }

    /**
     * 查询检查规则任务问题管理列表
     *
     * @param dqmValidationProblem 检查规则任务问题管理
     * @return 检查规则任务问题管理
     */
    @DataScope(systemAlias = "dvrc")
    @Override
    public List<DqmValidationProblem> selectDqmValidationProblemList(DqmValidationProblem dqmValidationProblem)
    {
        return dqmValidationProblemMapper.selectDqmValidationProblemList(dqmValidationProblem);
    }

    /**
     * 新增检查规则任务问题管理
     *
     * @param dqmValidationProblem 检查规则任务问题管理
     * @return 结果
     */
    @Override
    public int insertDqmValidationProblem(DqmValidationProblem dqmValidationProblem)
    {
        dqmValidationProblem.setCreateTime(DateUtils.getNowDate());
        dqmValidationProblem.setHandleResult("0");
        return dqmValidationProblemMapper.insertDqmValidationProblem(dqmValidationProblem);
    }

    /**
     * 修改检查规则任务问题管理
     *
     * @param dqmValidationProblem 检查规则任务问题管理
     * @return 结果
     */
    @Override
    public int updateDqmValidationProblem(DqmValidationProblem dqmValidationProblem)
    {
        dqmValidationProblem.setUpdateTime(DateUtils.getNowDate());
        return dqmValidationProblemMapper.updateDqmValidationProblem(dqmValidationProblem);
    }

    /**
     * 批量删除检查规则任务问题管理
     *
     * @param dqmValidationProblemIds 需要删除的检查规则任务问题管理ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationProblemByIds(Integer[] dqmValidationProblemIds)
    {
        return dqmValidationProblemMapper.deleteDqmValidationProblemByIds(dqmValidationProblemIds);
    }

    /**
     * 删除检查规则任务问题管理信息
     *
     * @param dqmValidationProblemId 检查规则任务问题管理ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationProblemById(Integer dqmValidationProblemId)
    {
        return dqmValidationProblemMapper.deleteDqmValidationProblemById(dqmValidationProblemId);
    }

    @DataScope(systemAlias = "dvrc")
    @Override
    public List<Map<Object, Object>> getProblemSumForDate(DqmValidationProblem dqmValidationProblem) {
        Integer dateType=Integer.parseInt(dqmValidationProblem.getParams().get("dateType").toString());
        String startDate="";
        String endDate="";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        if(dateType == 2){
            c.setTime(new Date());
            endDate =format.format(c.getTime());
            c.add(Calendar.YEAR, -1);
            Date m = c.getTime();
            startDate = format.format(m);
        }else{
            c.setTime(new Date());
            endDate =format.format(c.getTime());
            c.add(Calendar.MONTH, -1);
            Date m = c.getTime();
            startDate = format.format(m);
        }
        dqmValidationProblem.getParams().put("startDate",startDate);
        dqmValidationProblem.getParams().put("endDate",endDate);
        return dqmValidationProblemMapper.getProblemSumForDate(dqmValidationProblem);
    }

    @DataScope(systemAlias = "dvrc")
    @Override
    public List<Map> getProblemHandleForDate(DqmValidationProblem dqmValidationProblem) {
        return dqmValidationProblemMapper.getProblemHandleForDate(dqmValidationProblem);
    }

    @DataScope(systemAlias = "dvrc")
    @Override
    public List<DqmValidationProblem> getProblemHandleForNo(DqmValidationProblem dqmValidationProblem) {
        return dqmValidationProblemMapper.getProblemHandleForNo(dqmValidationProblem);
    }

    @Override
    public List<Map> getSystemScore(DqmValidationProblem dqmValidationProblem) {
        Integer dateType=Integer.parseInt(dqmValidationProblem.getParams().get("dateType").toString());
        String startDate="";
        String endDate="";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        if(dateType == 2){
            c.setTime(new Date());
            endDate =format.format(c.getTime());
            c.add(Calendar.YEAR, -1);
            Date m = c.getTime();
            startDate = format.format(m);
        }else{
            c.setTime(new Date());
            endDate =format.format(c.getTime());
            c.add(Calendar.MONTH, -1);
            Date m = c.getTime();
            startDate = format.format(m);
        }
        dqmValidationProblem.getParams().put("startDate",startDate);
        dqmValidationProblem.getParams().put("endDate",endDate);

        String[] systemScore = sysConfigService.selectConfigByKey("dqm_problem_systemScore").split(",");
        dqmValidationProblem.getParams().put("systemScore1",systemScore[0].split(":")[1]);
        dqmValidationProblem.getParams().put("systemScoreThreshold1",Double.parseDouble(systemScore[0].split(":")[0])/100);
        dqmValidationProblem.getParams().put("systemScore2",systemScore[1].split(":")[1]);
        dqmValidationProblem.getParams().put("systemScoreThreshold2",Double.parseDouble(systemScore[1].split(":")[0])/100);
        dqmValidationProblem.getParams().put("systemScore3",systemScore[2].split(":")[1]);
        dqmValidationProblem.getParams().put("systemScoreThreshold3",Double.parseDouble(systemScore[2].split(":")[0])/100);
        return dqmValidationProblemMapper.getSystemScore(dqmValidationProblem);
    }
}

