import request from '@/utils/request'

// 查询接口日志列表
export function listApiDefineHis(query) {
  return request({
    url: '/api/apiDefineHis/list',
    method: 'get',
    params: query
  })
}

// 查询接口日志详细
export function getApiDefineHis(defineHisId) {
  return request({
    url: '/api/apiDefineHis/' + defineHisId,
    method: 'get'
  })
}

// 新增接口日志
export function addApiDefineHis(data) {
  return request({
    url: '/api/apiDefineHis',
    method: 'post',
    data: data
  })
}

// 修改接口日志
export function updateApiDefineHis(data) {
  return request({
    url: '/api/apiDefineHis',
    method: 'put',
    data: data
  })
}

// 删除接口日志
export function delApiDefineHis(defineHisId) {
  return request({
    url: '/api/apiDefineHis/' + defineHisId,
    method: 'delete'
  })
}

// 导出接口日志
export function exportApiDefineHis(query) {
  return request({
    url: '/api/apiDefineHis/export',
    method: 'get',
    params: query
  })
}