package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmQuotaCurrencyRel;

/**
 * 指标与币种Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-03
 */
public interface DsmQuotaCurrencyRelMapper 
{
    /**
     * 查询指标与币种
     * 
     * @param quotaId 指标与币种ID
     * @return 指标与币种
     */
    public DsmQuotaCurrencyRel selectDsmQuotaCurrencyRelById(Long quotaId);

    /**
     * 查询指标与币种列表
     * 
     * @param dsmQuotaCurrencyRel 指标与币种
     * @return 指标与币种集合
     */
    public List<DsmQuotaCurrencyRel> selectDsmQuotaCurrencyRelList(DsmQuotaCurrencyRel dsmQuotaCurrencyRel);

    /**
     * 新增指标与币种
     * 
     * @param dsmQuotaCurrencyRel 指标与币种
     * @return 结果
     */
    public int insertDsmQuotaCurrencyRel(DsmQuotaCurrencyRel dsmQuotaCurrencyRel);

    /**
     * 修改指标与币种
     * 
     * @param dsmQuotaCurrencyRel 指标与币种
     * @return 结果
     */
    public int updateDsmQuotaCurrencyRel(DsmQuotaCurrencyRel dsmQuotaCurrencyRel);

    /**
     * 删除指标与币种
     * 
     * @param quotaId 指标与币种ID
     * @return 结果
     */
    public int deleteDsmQuotaCurrencyRelById(Long quotaId);

    /**
     * 批量删除指标与币种
     * 
     * @param quotaIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmQuotaCurrencyRelByIds(Long[] quotaIds);
}
