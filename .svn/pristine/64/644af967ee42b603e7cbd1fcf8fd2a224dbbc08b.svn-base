import request from '@/utils/request'

// 查询维度明细临时列表
export function listDsmDimensionDetaiTemp(query) {
  return request({
    url: '/dsm/dsmDimensionDetaiTemp/list',
    method: 'get',
    params: query
  })
}

// 查询维度明细临时详细
export function getDsmDimensionDetaiTemp(dimensionDetaiTempId) {
  return request({
    url: '/dsm/dsmDimensionDetaiTemp/' + dimensionDetaiTempId,
    method: 'get'
  })
}

// 新增维度明细临时
export function addDsmDimensionDetaiTemp(data) {
  return request({
    url: '/dsm/dsmDimensionDetaiTemp',
    method: 'post',
    data: data
  })
}

// 修改维度明细临时
export function updateDsmDimensionDetaiTemp(data) {
  return request({
    url: '/dsm/dsmDimensionDetaiTemp',
    method: 'put',
    data: data
  })
}

// 删除维度明细临时
export function delDsmDimensionDetaiTemp(dimensionDetaiTempId) {
  return request({
    url: '/dsm/dsmDimensionDetaiTemp/' + dimensionDetaiTempId,
    method: 'delete'
  })
}

// 导出维度明细临时
export function exportDsmDimensionDetaiTemp(query) {
  return request({
    url: '/dsm/dsmDimensionDetaiTemp/export',
    method: 'get',
    params: query
  })
}