package com.dqms;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.service.ISysDatasourceService;
import com.dqms.utils.JdbcTemplateUtils;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ApplicationRunnerDsImpl implements ApplicationRunner {

    @Autowired
    private ISysDatasourceService sysDatasourceService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
		/*
		 * List<SysDatasource> list = sysDatasourceService.selectSysDatasourceAll();
		 * for(SysDatasource ds : list) {
		 * 
		 * if(ds.getDbType()!=null&&ds.getDsType().equals("1")) {
		 * 
		 * try { JdbcTemplateUtils.addDataSourceMap(ds); } catch (Exception e) {
		 * log.info(ds.getName()+"数据源初始化失败"+e.getMessage()); }
		 * 
		 * } }
		 */
        log.info("数据源初始化完毕");
    }
}
