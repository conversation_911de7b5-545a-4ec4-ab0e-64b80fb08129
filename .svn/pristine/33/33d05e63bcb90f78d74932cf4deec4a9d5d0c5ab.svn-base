package com.dqms.task.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 任务结果订阅对象 etl_task_subscribe
 *
 * <AUTHOR>
 * @date 2021-04-02
 */
public class EtlTaskSubscribe extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订阅ID */
    private Long taskSubscribeId;

    /** 用户 */
    @Excel(name = "用户")
    private String userName;
    private Long userId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;
    private Long taskId;
    
    private String taskClassName;
    private Long taskClassId;
    
    private String typeName;

    /** 订阅成功 */
    @Excel(name = "订阅成功")
    private String successFlag;
    private String successFlagName;

    /** 订阅失败 */
    @Excel(name = "订阅失败")
    private String errorFlag;
    private String errorFlagName;

    /** 邮箱推送 */
    @Excel(name = "邮箱推送")
    private String emailFlag;
    private String emailFlagName;

    /** 短信推送 */
    @Excel(name = "短信推送")
    private String smsFlag;
    private String smsFlagName;

    /** 站内通知 */
    @Excel(name = "站内通知")
    private String noticeFlag;
    private String noticeFlagName;
    
    private Long[] taskIds;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    private String type;
    
    /** 任务分组 */
    private Long taskGroupId;

    /*订阅全部任务查询条件*/
    private String oldSuccessFlag;
    private String oldErrorFlag;
    private String oldEmailFlag;
    private String oldSmsFlag;
    private String oldNoticeFlag;
    
    public Long getTaskClassId() {
		return taskClassId;
	}

	public void setTaskClassId(Long taskClassId) {
		this.taskClassId = taskClassId;
	}

	public void setTaskSubscribeId(Long taskSubscribeId)
    {
        this.taskSubscribeId = taskSubscribeId;
    }

    public Long getTaskSubscribeId()
    {
        return taskSubscribeId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setTaskId(Long taskId)
    {
        this.taskId = taskId;
    }

    public Long getTaskId()
    {
        return taskId;
    }
    public void setSuccessFlag(String successFlag)
    {
        this.successFlag = successFlag;
    }

    public String getSuccessFlag()
    {
        return successFlag;
    }
    public void setErrorFlag(String errorFlag)
    {
        this.errorFlag = errorFlag;
    }

    public String getErrorFlag()
    {
        return errorFlag;
    }
    public void setEmailFlag(String emailFlag)
    {
        this.emailFlag = emailFlag;
    }

    public String getEmailFlag()
    {
        return emailFlag;
    }
    public void setSmsFlag(String smsFlag)
    {
        this.smsFlag = smsFlag;
    }

    public String getSmsFlag()
    {
        return smsFlag;
    }
    public void setNoticeFlag(String noticeFlag)
    {
        this.noticeFlag = noticeFlag;
    }

    public String getNoticeFlag()
    {
        return noticeFlag;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public String getTaskClassName() {
		return taskClassName;
	}

	public void setTaskClassName(String taskClassName) {
		this.taskClassName = taskClassName;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSuccessFlagName() {
		return successFlagName;
	}

	public void setSuccessFlagName(String successFlagName) {
		this.successFlagName = successFlagName;
	}

	public String getErrorFlagName() {
		return errorFlagName;
	}

	public void setErrorFlagName(String errorFlagName) {
		this.errorFlagName = errorFlagName;
	}

	public String getEmailFlagName() {
		return emailFlagName;
	}

	public void setEmailFlagName(String emailFlagName) {
		this.emailFlagName = emailFlagName;
	}

	public String getSmsFlagName() {
		return smsFlagName;
	}

	public void setSmsFlagName(String smsFlagName) {
		this.smsFlagName = smsFlagName;
	}

	public String getNoticeFlagName() {
		return noticeFlagName;
	}

	public void setNoticeFlagName(String noticeFlagName) {
		this.noticeFlagName = noticeFlagName;
	}

	public Long[] getTaskIds() {
		return taskIds;
	}

	public void setTaskIds(Long[] taskIds) {
		this.taskIds = taskIds;
	}

	public Long getTaskGroupId() {
		return taskGroupId;
	}

	public void setTaskGroupId(Long taskGroupId) {
		this.taskGroupId = taskGroupId;
	}

    public String getOldSuccessFlag() {
        return oldSuccessFlag;
    }

    public void setOldSuccessFlag(String oldSuccessFlag) {
        this.oldSuccessFlag = oldSuccessFlag;
    }

    public String getOldErrorFlag() {
        return oldErrorFlag;
    }

    public void setOldErrorFlag(String oldErrorFlag) {
        this.oldErrorFlag = oldErrorFlag;
    }

    public String getOldEmailFlag() {
        return oldEmailFlag;
    }

    public void setOldEmailFlag(String oldEmailFlag) {
        this.oldEmailFlag = oldEmailFlag;
    }

    public String getOldSmsFlag() {
        return oldSmsFlag;
    }

    public void setOldSmsFlag(String oldSmsFlag) {
        this.oldSmsFlag = oldSmsFlag;
    }

    public String getOldNoticeFlag() {
        return oldNoticeFlag;
    }

    public void setOldNoticeFlag(String oldNoticeFlag) {
        this.oldNoticeFlag = oldNoticeFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskSubscribeId", getTaskSubscribeId())
            .append("userId", getUserId())
            .append("taskId", getTaskId())
            .append("successFlag", getSuccessFlag())
            .append("errorFlag", getErrorFlag())
            .append("emailFlag", getEmailFlag())
            .append("smsFlag", getSmsFlag())
            .append("noticeFlag", getNoticeFlag())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
