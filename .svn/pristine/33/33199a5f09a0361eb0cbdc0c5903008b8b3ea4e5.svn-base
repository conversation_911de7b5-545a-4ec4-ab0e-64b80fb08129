package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmStandardTarSystem;
import com.dqms.dsm.service.IDsmStandardTarSystemService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 标准应用系统Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmStandardTarSystem")
public class DsmStandardTarSystemController extends BaseController
{
    @Autowired
    private IDsmStandardTarSystemService dsmStandardTarSystemService;

    /**
     * 查询标准应用系统列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarSystem:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmStandardTarSystem dsmStandardTarSystem)
    {
        startPage();
        List<DsmStandardTarSystem> list = dsmStandardTarSystemService.selectDsmStandardTarSystemList(dsmStandardTarSystem);
        return getDataTable(list);
    }

    /**
     * 导出标准应用系统列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarSystem:export')")
    @Log(title = "标准应用系统", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmStandardTarSystem dsmStandardTarSystem)
    {
        List<DsmStandardTarSystem> list = dsmStandardTarSystemService.selectDsmStandardTarSystemList(dsmStandardTarSystem);
        ExcelUtil<DsmStandardTarSystem> util = new ExcelUtil<DsmStandardTarSystem>(DsmStandardTarSystem.class);
        return util.exportExcel(list, "dsmStandardTarSystem");
    }

    /**
     * 获取标准应用系统详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarSystem:query')")
    @GetMapping(value = "/{standardId}")
    public AjaxResult getInfo(@PathVariable("standardId") Long standardId)
    {
        return AjaxResult.success(dsmStandardTarSystemService.selectDsmStandardTarSystemById(standardId));
    }

    /**
     * 新增标准应用系统
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarSystem:add')")
    @Log(title = "标准应用系统", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmStandardTarSystem dsmStandardTarSystem)
    {
        return toAjax(dsmStandardTarSystemService.insertDsmStandardTarSystem(dsmStandardTarSystem));
    }

    /**
     * 修改标准应用系统
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarSystem:edit')")
    @Log(title = "标准应用系统", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmStandardTarSystem dsmStandardTarSystem)
    {
        return toAjax(dsmStandardTarSystemService.updateDsmStandardTarSystem(dsmStandardTarSystem));
    }

    /**
     * 删除标准应用系统
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarSystem:remove')")
    @Log(title = "标准应用系统", businessType = BusinessType.DELETE)
	@DeleteMapping("/{standardIds}")
    public AjaxResult remove(@PathVariable Long[] standardIds)
    {
        return toAjax(dsmStandardTarSystemService.deleteDsmStandardTarSystemByIds(standardIds));
    }
}
