package com.dqms.dsc.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 脱敏明细对象 dsc_desensitization_detail
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscDesensitizationDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private Long desensitizationDetailId;

    /** 脱敏ID */
    @Excel(name = "脱敏ID")
    private Long desensitizationId;

    /** 实体ID */
    @Excel(name = "实体ID")
    private Long entityId;

    /** 字段ID */
    @Excel(name = "字段ID")
    private Long propId;

    /** 脱敏类型 */
    @Excel(name = "脱敏类型")
    private String runType;
    
    private String propName;
    private String propComment;

    public void setDesensitizationDetailId(Long desensitizationDetailId)
    {
        this.desensitizationDetailId = desensitizationDetailId;
    }

    public Long getDesensitizationDetailId()
    {
        return desensitizationDetailId;
    }
    public void setDesensitizationId(Long desensitizationId)
    {
        this.desensitizationId = desensitizationId;
    }

    public Long getDesensitizationId()
    {
        return desensitizationId;
    }
    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setPropId(Long propId)
    {
        this.propId = propId;
    }

    public Long getPropId()
    {
        return propId;
    }
    public void setRunType(String runType)
    {
        this.runType = runType;
    }

    public String getRunType()
    {
        return runType;
    }

    public String getPropName() {
		return propName;
	}

	public void setPropName(String propName) {
		this.propName = propName;
	}

	public String getPropComment() {
		return propComment;
	}

	public void setPropComment(String propComment) {
		this.propComment = propComment;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("desensitizationDetailId", getDesensitizationDetailId())
            .append("desensitizationId", getDesensitizationId())
            .append("entityId", getEntityId())
            .append("propId", getPropId())
            .append("runType", getRunType())
            .toString();
    }
}
