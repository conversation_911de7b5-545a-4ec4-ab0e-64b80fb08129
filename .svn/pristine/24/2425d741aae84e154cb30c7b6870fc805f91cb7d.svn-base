package com.dqms.dsm.service;

import java.util.List;
import com.dqms.dsm.domain.DsmCheckMain;

/**
 * 对标主表Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-01
 */
public interface IDsmCheckMainService 
{
    /**
     * 查询对标主表
     * 
     * @param checkMainId 对标主表ID
     * @return 对标主表
     */
    public DsmCheckMain selectDsmCheckMainById(Long checkMainId);

    /**
     * 查询对标主表列表
     * 
     * @param dsmCheckMain 对标主表
     * @return 对标主表集合
     */
    public List<DsmCheckMain> selectDsmCheckMainList(DsmCheckMain dsmCheckMain);

    /**
     * 新增对标主表
     * 
     * @param dsmCheckMain 对标主表
     * @return 结果
     */
    public int insertDsmCheckMain(DsmCheckMain dsmCheckMain);

    /**
     * 修改对标主表
     * 
     * @param dsmCheckMain 对标主表
     * @return 结果
     */
    public int updateDsmCheckMain(DsmCheckMain dsmCheckMain);

    /**
     * 批量删除对标主表
     * 
     * @param checkMainIds 需要删除的对标主表ID
     * @return 结果
     */
    public int deleteDsmCheckMainByIds(Long[] checkMainIds);

    /**
     * 删除对标主表信息
     * 
     * @param checkMainId 对标主表ID
     * @return 结果
     */
    public int deleteDsmCheckMainById(Long checkMainId);
}
