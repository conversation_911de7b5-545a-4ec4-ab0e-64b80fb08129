package com.dqms.task.controller;

import java.util.List;

import com.dqms.dsm.domain.DsmStandardMdmRel;
import com.dqms.task.domain.vo.EtlTaskScheduleVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.task.domain.EtlTaskSchedule;
import com.dqms.task.service.IEtlTaskScheduleService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.quartz.domain.SysJob;
import com.dqms.quartz.service.ISysJobService;
import com.dqms.quartz.util.CronUtils;
import com.dqms.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 任务调度计划Controller
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@RestController
@RequestMapping("/task/taskSchedule")
public class EtlTaskScheduleController extends BaseController
{
    @Autowired
    private IEtlTaskScheduleService etlTaskScheduleService;

    /**
     * 查询任务调度计划列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(EtlTaskSchedule etlTaskSchedule)
    {
        startPage();
        List<EtlTaskSchedule> list = etlTaskScheduleService.selectEtlTaskScheduleList(etlTaskSchedule);
        return getDataTable(list);
    }
    
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:list')")
    @GetMapping("/listByPage")
    public TableDataInfo listByPage(EtlTaskSchedule etlTaskSchedule)
    {
        startPage();
        List<EtlTaskSchedule> list = etlTaskScheduleService.selectEtlTaskScheduleListByPage(etlTaskSchedule);
        return getDataTable(list);
    }
    /**
     * 导出任务调度计划列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:export')")
    @Log(title = "任务调度计划", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EtlTaskSchedule etlTaskSchedule)
    {
        List<EtlTaskScheduleVo> list = etlTaskScheduleService.selectEtlTaskScheduleVoList(etlTaskSchedule);
        ExcelUtil<EtlTaskScheduleVo> util = new ExcelUtil<EtlTaskScheduleVo>(EtlTaskScheduleVo.class);
        return util.exportExcel(list, "taskSchedule");
    }




    /**
     * 获取任务调度计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:query')")
    @GetMapping(value = "/{taskScheduleId}")
    public AjaxResult getInfo(@PathVariable("taskScheduleId") Long taskScheduleId)
    {
        return AjaxResult.success(etlTaskScheduleService.selectEtlTaskScheduleById(taskScheduleId));
    }

    /**
     * 新增任务调度计划
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:add')")
    @Log(title = "任务调度计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EtlTaskSchedule etlTaskSchedule)
    {
    	if (!CronUtils.isValid(etlTaskSchedule.getExpression()))
        {
            return AjaxResult.error("cron表达式不正确");
        }
        return toAjax(etlTaskScheduleService.insertEtlTaskSchedule(etlTaskSchedule));
    }

    /**
     * 修改任务调度计划
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:edit')")
    @Log(title = "任务调度计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EtlTaskSchedule etlTaskSchedule)
    {
    	if (!CronUtils.isValid(etlTaskSchedule.getExpression()))
        {
            return AjaxResult.error("cron表达式不正确");
        }
        return toAjax(etlTaskScheduleService.updateEtlTaskSchedule(etlTaskSchedule));
    }

    /**
     * 删除任务调度计划
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:remove')")
    @Log(title = "任务调度计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskScheduleIds}")
    public AjaxResult remove(@PathVariable Long[] taskScheduleIds)
    {
        return toAjax(etlTaskScheduleService.deleteEtlTaskScheduleByIds(taskScheduleIds));
    }
    
    /**
     * 定时任务立即执行一次
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:edit')")
    @Log(title = "任务调度计划执行", businessType = BusinessType.UPDATE)
    @PutMapping("/run")
    public AjaxResult run(@RequestBody EtlTaskSchedule etlTaskSchedule) throws SchedulerException
    {
    	etlTaskScheduleService.run(etlTaskSchedule);
        return AjaxResult.success();
    }

    /**
     * 导入任务调度计划列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskSchedule:import')")
    @Log(title = "任务调度计划导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importEtlTaskSchedule")
    public AjaxResult importEtlTaskSchedule(@PathVariable MultipartFile file,boolean updateSupport)throws Exception
    {
        ExcelUtil<EtlTaskScheduleVo> util = new ExcelUtil<>(EtlTaskScheduleVo.class);
        //获取excel中的数值植入实体集合
        List<EtlTaskScheduleVo> etlTaskScheduleVoList = util.importExcel(file.getInputStream());

        String message = etlTaskScheduleService.importetlTaskSchedule(etlTaskScheduleVoList ,updateSupport);
        return AjaxResult.success(message);

    }

    @PreAuthorize("@ss.hasPermi('task:taskSchedule:export')")
    @Log(title = "任务调度计划模板下载", businessType = BusinessType.EXPORT)
    @GetMapping("/exportdemo")
    public AjaxResult exportdemo(EtlTaskScheduleVo etlTaskScheduleVo)
    {
        ExcelUtil<EtlTaskScheduleVo> util = new ExcelUtil<EtlTaskScheduleVo>(EtlTaskScheduleVo.class);
        return util.importTemplateExcel("任务调度计划模板");
    }


}
