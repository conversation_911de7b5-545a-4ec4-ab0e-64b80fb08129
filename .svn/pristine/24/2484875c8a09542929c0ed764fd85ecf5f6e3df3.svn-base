package com.dqms.system.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 任务分组权限对象 sys_user_task_group
 *
 * <AUTHOR>
 * @date 2021-12-15
 */
public class SysUserTaskGroup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 任务分组 */
    @Excel(name = "任务分组")
    private Long taskGroupId;

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setTaskGroupId(Long taskGroupId)
    {
        this.taskGroupId = taskGroupId;
    }

    public Long getTaskGroupId()
    {
        return taskGroupId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("taskGroupId", getTaskGroupId())
            .toString();
    }
}
