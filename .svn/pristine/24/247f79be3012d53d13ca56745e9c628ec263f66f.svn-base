package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmQuotaTagRel;

/**
 * 指标与标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-03
 */
public interface DsmQuotaTagRelMapper 
{
    /**
     * 查询指标与标签
     * 
     * @param quotaId 指标与标签ID
     * @return 指标与标签
     */
    public DsmQuotaTagRel selectDsmQuotaTagRelById(Long quotaId);

    /**
     * 查询指标与标签列表
     * 
     * @param dsmQuotaTagRel 指标与标签
     * @return 指标与标签集合
     */
    public List<DsmQuotaTagRel> selectDsmQuotaTagRelList(DsmQuotaTagRel dsmQuotaTagRel);

    /**
     * 新增指标与标签
     * 
     * @param dsmQuotaTagRel 指标与标签
     * @return 结果
     */
    public int insertDsmQuotaTagRel(DsmQuotaTagRel dsmQuotaTagRel);

    /**
     * 修改指标与标签
     * 
     * @param dsmQuotaTagRel 指标与标签
     * @return 结果
     */
    public int updateDsmQuotaTagRel(DsmQuotaTagRel dsmQuotaTagRel);

    /**
     * 删除指标与标签
     * 
     * @param quotaId 指标与标签ID
     * @return 结果
     */
    public int deleteDsmQuotaTagRelById(Long quotaId);

    /**
     * 批量删除指标与标签
     * 
     * @param quotaIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmQuotaTagRelByIds(Long[] quotaIds);
}
