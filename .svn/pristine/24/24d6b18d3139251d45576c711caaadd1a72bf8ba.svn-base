package com.dqms.api.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.dqms.api.domain.ApiDefine;
import com.dqms.api.domain.ApiDefineHis;

/**
 * 接口管理Service接口
 * 
 * <AUTHOR>
 * @date 2021-08-03
 */
public interface IApiDefineService 
{
    /**
     * 查询接口管理
     * 
     * @param defineId 接口管理ID
     * @return 接口管理
     */
    public ApiDefine selectApiDefineById(Long defineId);

    /**
     * 查询接口管理列表
     * 
     * @param apiDefine 接口管理
     * @return 接口管理集合
     */
    public List<ApiDefine> selectApiDefineList(ApiDefine apiDefine);
    public List<ApiDefine> selectApiDefineHisList(ApiDefine apiDefine);

    /**
     * 新增接口管理
     * 
     * @param apiDefine 接口管理
     * @return 结果
     */
    public int insertApiDefine(ApiDefine apiDefine);

    /**
     * 修改接口管理
     * 
     * @param apiDefine 接口管理
     * @return 结果
     */
    public int updateApiDefine(ApiDefine apiDefine);
    public int updateStatus(ApiDefine apiDefine);
    public int updateApiDefineSystem(ApiDefine apiDefine);
    public int updateApiDefineParam(ApiDefine apiDefine);

    /**
     * 批量删除接口管理
     * 
     * @param defineIds 需要删除的接口管理ID
     * @return 结果
     */
    public int deleteApiDefineByIds(Long[] defineIds);

    /**
     * 删除接口管理信息
     * 
     * @param defineId 接口管理ID
     * @return 结果
     */
    public int deleteApiDefineById(Long defineId);
    /**
     * 格局接口获取数据
     * 
     * @param defineId 接口管理ID
     * @return 结果
     */
    public Object getData(String page,String pageSize ,String param,String type,ApiDefineHis apiDefineHis,ApiDefine apiDefine);   
    public Object getJDBC(String param,String type,ApiDefineHis apiDefineHis,ApiDefine apiDefine);
    public Object getTCP(String param,String type,ApiDefineHis apiDefineHis,ApiDefine apiDefine);
    public Object getKAFKA(String page,String pageSize ,String param,String type,ApiDefineHis apiDefineHis,ApiDefine apiDefine);
    public Object getTASK(String param,String type,ApiDefineHis apiDefineHis,ApiDefine apiDefine);
    
}
