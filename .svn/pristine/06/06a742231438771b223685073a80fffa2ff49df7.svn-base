package com.dqms.dsc.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 字段应用系统对象 dsc_entity_prop_system
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscEntityPropSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 字段ID */
    private Long entityPropId;

    /** 系统ID */
    private Long systemId;

    public void setEntityPropId(Long entityPropId)
    {
        this.entityPropId = entityPropId;
    }

    public Long getEntityPropId()
    {
        return entityPropId;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("entityPropId", getEntityPropId())
            .append("systemId", getSystemId())
            .toString();
    }
}
