package com.dqms.task.service;

import java.util.List;

import com.dqms.task.domain.EtlTaskClass;
import com.dqms.task.domain.EtlTaskClassTreeSelect;

/**
 * 任务分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-03-11
 */
public interface IEtlTaskClassService 
{
    /**
     * 查询任务分类
     * 
     * @param taskClassId 任务分类ID
     * @return 任务分类
     */
    public EtlTaskClass selectEtlTaskClassById(Long taskClassId);

    /**
     * 查询任务分类列表
     * 
     * @param etlTaskClass 任务分类
     * @return 任务分类集合
     */
    public List<EtlTaskClass> selectEtlTaskClassList(EtlTaskClass etlTaskClass);

    /**
     * 新增任务分类
     * 
     * @param etlTaskClass 任务分类
     * @return 结果
     */
    public int insertEtlTaskClass(EtlTaskClass etlTaskClass);

    /**
     * 修改任务分类
     * 
     * @param etlTaskClass 任务分类
     * @return 结果
     */
    public int updateEtlTaskClass(EtlTaskClass etlTaskClass);

    /**
     * 批量删除任务分类
     * 
     * @param taskClassIds 需要删除的任务分类ID
     * @return 结果
     */
    public int deleteEtlTaskClassByIds(Long[] taskClassIds);

    /**
     * 删除任务分类信息
     * 
     * @param taskClassId 任务分类ID
     * @return 结果
     */
    public int deleteEtlTaskClassById(Long taskClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 树结构列表
     */
    public List<EtlTaskClass> buildEtlTaskClassTree(List<EtlTaskClass> etlTaskClasss);
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    public List<EtlTaskClassTreeSelect> buildEtlTaskClassTreeSelect(List<EtlTaskClass> etlTaskClasss);
}
