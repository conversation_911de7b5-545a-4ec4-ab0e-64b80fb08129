<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dam.mapper.DamAssetsEvaluateMapper">
    
    <resultMap type="DamAssetsEvaluate" id="DamAssetsEvaluateResult">
        <result property="assetsEvaluateId"    column="assets_evaluate_id"    />
        <result property="assetsId"    column="assets_id"    />
        <result property="grade"    column="grade"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectDamAssetsEvaluateVo">
        select assets_evaluate_id, assets_id, grade, remark, create_by, create_id, create_time from dam_assets_evaluate
    </sql>

    <select id="selectDamAssetsEvaluateList" parameterType="DamAssetsEvaluate" resultMap="DamAssetsEvaluateResult">
        <include refid="selectDamAssetsEvaluateVo"/>
        <where>  
            <if test="assetsId != null "> and assets_id = #{assetsId}</if>
            <if test="grade != null "> and grade = #{grade}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
    </select>
    
    <select id="selectDamAssetsEvaluateById" parameterType="Long" resultMap="DamAssetsEvaluateResult">
        <include refid="selectDamAssetsEvaluateVo"/>
        where assets_evaluate_id = #{assetsEvaluateId}
    </select>
        
    <insert id="insertDamAssetsEvaluate" parameterType="DamAssetsEvaluate" useGeneratedKeys="true" keyProperty="assetsEvaluateId">
        insert into dam_assets_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetsId != null">assets_id,</if>
            <if test="grade != null">grade,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetsId != null">#{assetsId},</if>
            <if test="grade != null">#{grade},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateDamAssetsEvaluate" parameterType="DamAssetsEvaluate">
        update dam_assets_evaluate
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetsId != null">assets_id = #{assetsId},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where assets_evaluate_id = #{assetsEvaluateId}
    </update>
    
    <update id="updateDamAssetsGrade" parameterType="DamAssetsEvaluate">
        UPDATE dam_assets d
          SET  d.grade = (SELECT ROUND(IFNULL(SUM(e.grade)/COUNT(e.assets_evaluate_id),0),1) FROM dam_assets_evaluate e WHERE e.assets_id = #{assetsId})
        WHERE d.dam_assets_id = #{assetsId} 
    </update>

    <delete id="deleteDamAssetsEvaluateById" parameterType="Long">
        delete from dam_assets_evaluate where assets_evaluate_id = #{assetsEvaluateId}
    </delete>

    <delete id="deleteDamAssetsEvaluateByIds" parameterType="String">
        delete from dam_assets_evaluate where assets_evaluate_id in 
        <foreach item="assetsEvaluateId" collection="array" open="(" separator="," close=")">
            #{assetsEvaluateId}
        </foreach>
    </delete>
</mapper>