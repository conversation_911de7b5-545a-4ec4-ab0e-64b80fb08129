package com.dqms.api.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 接口日志对象 api_define_his
 *
 * <AUTHOR>
 * @date 2021-08-08
 */
public class ApiDefineHis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 历史ID */
    private Long defineHisId;

    /** 接口ID */
    @Excel(name = "接口ID")
    private Long defineId;

    /** 系统ID */
    @Excel(name = "系统ID")
    private Long systemId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 返回信息 */
    private String masesge;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;
    
    private String systemName;
    private String defineType;
    private String defineCode;
    private String defineName;

    public void setDefineHisId(Long defineHisId)
    {
        this.defineHisId = defineHisId;
    }

    public Long getDefineHisId()
    {
        return defineHisId;
    }
    public void setDefineId(Long defineId)
    {
        this.defineId = defineId;
    }

    public Long getDefineId()
    {
        return defineId;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setMasesge(String masesge)
    {
        this.masesge = masesge;
    }

    public String getMasesge()
    {
        return masesge;
    }
    public void setIpAddress(String ipAddress)
    {
        this.ipAddress = ipAddress;
    }

    public String getIpAddress()
    {
        return ipAddress;
    }

    public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getDefineType() {
		return defineType;
	}

	public void setDefineType(String defineType) {
		this.defineType = defineType;
	}

	public String getDefineCode() {
		return defineCode;
	}

	public void setDefineCode(String defineCode) {
		this.defineCode = defineCode;
	}

	public String getDefineName() {
		return defineName;
	}

	public void setDefineName(String defineName) {
		this.defineName = defineName;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("defineHisId", getDefineHisId())
            .append("defineId", getDefineId())
            .append("systemId", getSystemId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("status", getStatus())
            .append("masesge", getMasesge())
            .append("ipAddress", getIpAddress())
            .toString();
    }
}