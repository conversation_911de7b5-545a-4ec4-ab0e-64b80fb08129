package com.dqms.dqm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 质量模板对象 dqm_validation_mould
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
public class DqmValidationMould extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    private Integer validationMouldId;

    /** 模板名称 */
    @Excel(name = "模板名称")
    private String templateName;

    /** 1为数据质量，2为数据标准 */
    @Excel(name = "规则主题",dictType="dqm_rule_object",combo={"数据质量","数据标准"})
    private String topic;

    /** 执行的sql */
    @Excel(name = "执行的sql")
    private String fillSql;

    /** 表名 */
    @Excel(name = "表名")
    private String tableName;

    /** 字段名 */
    @Excel(name = "字段名")
    private String fieldName;

    /** 条件 */
    @Excel(name = "条件")
    private String conditionName;

    /** 1为启用，2为禁用 */
    @Excel(name = "状态",dictType="sys_normal_disable",combo={"启用","停用"})
    private String state;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    @Excel(name = "说明")
    private String remark;
    public void setValidationMouldId(Integer validationMouldId)
    {
        this.validationMouldId = validationMouldId;
    }

    public Integer getValidationMouldId()
    {
        return validationMouldId;
    }
    public void setTemplateName(String templateName)
    {
        this.templateName = templateName;
    }

    public String getTemplateName()
    {
        return templateName;
    }
    public void setFillSql(String fillSql)
    {
        this.fillSql = fillSql;
    }

    public String getFillSql()
    {
        return fillSql;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setFieldName(String fieldName)
    {
        this.fieldName = fieldName;
    }

    public String getFieldName()
    {
        return fieldName;
    }
    public void setConditionName(String conditionName)
    {
        this.conditionName = conditionName;
    }

    public String getConditionName()
    {
        return conditionName;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }


    public String getRemark() {
        return remark;
    }


    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTopic() {
		return topic;
	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("validationMouldId", getValidationMouldId())
                .append("templateName", getTemplateName())
                .append("topic", getTopic())
                .append("fillSql", getFillSql())
                .append("tableName", getTableName())
                .append("fieldName", getFieldName())
                .append("conditionName", getConditionName())
                .append("state", getState())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("createId", getCreateId())
                .append("updateId", getUpdateId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
