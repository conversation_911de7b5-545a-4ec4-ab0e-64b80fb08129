package com.dqms.mdm.util.StrategyType;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.constant.DicConstants;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.enums.TableInfo;
import com.dqms.common.utils.DictUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.util.MetaDataStrategy;
import com.dqms.system.service.ISysConfigService;
import com.dqms.utils.DateUtils;
import com.dqms.utils.JdbcTemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class Hive2Strategy extends MetaDataStrategy {

    @Override
    public Map<String, Object> getTableInfo(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        log.info("这是Hive2获取表结构方法");

        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Map<String, Object> m = new HashedMap();
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(conn.getCatalog(),mdmRegistry.getRegDir().toLowerCase(), mdmRegistry.getRegName().toLowerCase(), null);
            int i = 0;
            while (rs.next()) {
                log.info("---------[" + i++ + "]---------");
                String tableName = "";
                tableName = rs.getString("TABLE_NAME");
                String tableCat = rs.getString("TABLE_CAT");
                String tableSchem = rs.getString("TABLE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String tableType = rs.getString("TABLE_TYPE");
                String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                m.put(TableInfo.TABLE_NAME.getCode(), tableName);
                m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return m;
    }

    @Override
    public List<Map<String, Object>> getColumns(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {

        log.info("这是Hive2获取表字段方法");

        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        List<Map<String, Object>> columns = new ArrayList<Map<String, Object>>();
        Connection conn = null;
        ResultSet rs = null;
        try {
        	String regDir=mdmRegistry.getRegDir()==null?null:mdmRegistry.getRegDir().toLowerCase();
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getPrimaryKeys(null,  regDir, mdmRegistry.getRegName().toLowerCase());
            List<String> primaryKeys = new ArrayList<String>();
            while (rs.next()) {
                primaryKeys.add(rs.getString("COLUMN_NAME"));
            }
            log.info("主键集合:{}",primaryKeys.toString());
            rs = dbmd.getColumns(null, regDir,  mdmRegistry.getRegName().toLowerCase(), "%");
            getColumnsList(columns, rs, primaryKeys);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return columns;
    }

    @Override
    public Boolean checkTableExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Map<String, Object> m = new HashedMap();
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(conn.getCatalog(),mdmRegistry.getRegDir().toLowerCase(), mdmRegistry.getRegName().toLowerCase(), null);
            while (rs.next()) {
                //判断TABLE、VIEW使用正确
                String tableType = rs.getString("TABLE_TYPE");
                if(("TABLE".equalsIgnoreCase(tableType)&&Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.TABLE)
                        || ("VIEW".equalsIgnoreCase(tableType)&&Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.VIEW)
                ){
                    return true;
                }
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return false;
    }

    @Override
    public Boolean checkProcedureExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getProcedures(conn.getCatalog(),mdmRegistry.getRegDir().toLowerCase(), mdmRegistry.getRegName().toLowerCase());
            while (rs.next()) {
                return true;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return false;
    }


    @Override
    public List<Map<String, Object>> getTablesAndViews(SysDatasource sysDatasource,String catalog,String [] types) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();

            List<String> catalogs = new ArrayList<>();
            if(catalog==null){
                List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();
                String sql = "show databases";//查看当前数据库列表
                JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
                dataList = jd.queryForList(sql);
                StringBuffer buffer = new StringBuffer();
                if(dataList.size()>0) {
                    for(Map<String, Object> data : dataList){
                        catalogs.add(data.get("database_name").toString());
                    }
                }
            }else{
                catalogs.add(catalog);
            }
            if(catalogs.size()>0){
                for(String catalogStr : catalogs){
                    rs = dbmd.getTables(conn.getCatalog(), catalogStr.toLowerCase(), null, types);
                    while (rs.next()) {
                        Map<String, Object> m = new HashedMap();
                        log.info(rs.toString());
                        String tableName = "";
                        tableName = rs.getString("TABLE_NAME");
                        String tableCat = rs.getString("TABLE_CAT");
                        String tableSchem = rs.getString("TABLE_SCHEM");
                        String remarks = rs.getString("REMARKS");
                        String tableType = rs.getString("TABLE_TYPE");
                        String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                        m.put("label", schema+'.'+tableName);
                        m.put("tableName", tableName);
                        m.put("icon", "iconThree");
                        m.put("leaf", true);
                        m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                        m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                        m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
                        list.add(m);
                    }
                }
            }else{//避免dataList获取失败
                rs = dbmd.getTables(conn.getCatalog(), null, null, types);
                while (rs.next()) {
                    Map<String, Object> m = new HashedMap();
                    log.info(rs.toString());
                    String tableName = "";
                    tableName = rs.getString("TABLE_NAME");
                    String tableCat = rs.getString("TABLE_CAT");
                    String tableSchem = rs.getString("TABLE_SCHEM");
                    String remarks = rs.getString("REMARKS");
                    String tableType = rs.getString("TABLE_TYPE");
                    String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                    m.put("label", schema+'.'+tableName);
                    m.put("tableName", tableName);
                    m.put("icon", "iconThree");
                    m.put("leaf", true);
                    m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                    m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                    m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
                    list.add(m);
                }
            }

            List<Map<String,Object>> newlist= new ArrayList<>();
            boolean viewFlag = Arrays.asList(types).contains("VIEW");
            boolean tableFlag = Arrays.asList(types).contains("TABLE");

            if(viewFlag&&tableFlag){
                newlist = list;
            }else{
                if(viewFlag){
                    List listView = list.stream()
                        .filter(map -> map.get(TableInfo.TABLE_TYPE.getCode()).toString().equalsIgnoreCase("VIEW"))
                        .collect(Collectors.toList());

                    newlist.addAll(listView);
                }
                if(tableFlag){
                    List listTable = list.stream()
                        .filter(map -> map.get(TableInfo.TABLE_TYPE.getCode()).toString().equalsIgnoreCase("TABLE"))
                        .collect(Collectors.toList());

                    newlist.addAll(listTable);
                }
            }
            return newlist;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getProcedures(SysDatasource sysDatasource, String catalog) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getProcedures(conn.getCatalog(), catalog.toLowerCase() , null);
            while (rs.next()) {
                Map<String, Object> m = new HashedMap();
                log.info(rs.toString());
                String procedureName = rs.getString("PROCEDURE_NAME");
                String procedureCat = rs.getString("PROCEDURE_CAT");
                String procedureSchema = rs.getString("PROCEDURE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String procedureType = rs.getString("PROCEDURE_TYPE");
                m.put(TableInfo.PROCEDURE_NAME.getCode(), procedureName);
                m.put(TableInfo.PROCEDURE_CAT.getCode(), procedureCat);
                m.put(TableInfo.PROCEDURE_SCHEMA.getCode(), procedureSchema);
                m.put(TableInfo.PROCEDURE_REMARKS.getCode(), remarks);
                m.put(TableInfo.PROCEDURE_TYPE.getCode(), procedureType);
                list.add(m);
            }
            return list;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }

    @Override
    public Map<String, Object> getProcedureInfo(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        Map<String, Object> m = new HashedMap();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getProcedures(conn.getCatalog(), mdmRegistry.getRegDir().toLowerCase() , mdmRegistry.getRegName().toLowerCase());
            while (rs.next()) {
                log.info(rs.toString());
                String procedureName = rs.getString("PROCEDURE_NAME");
                String procedureCat = rs.getString("PROCEDURE_CAT");
                String procedureSchema = rs.getString("PROCEDURE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String procedureType = rs.getString("PROCEDURE_TYPE");
                m.put(TableInfo.PROCEDURE_NAME.getCode(), procedureName);
                m.put(TableInfo.PROCEDURE_CAT.getCode(), procedureCat);
                m.put(TableInfo.PROCEDURE_SCHEMA.getCode(), procedureSchema);
                m.put(TableInfo.PROCEDURE_REMARKS.getCode(), remarks);
                m.put(TableInfo.PROCEDURE_TYPE.getCode(), procedureType);
            }
            return m;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }

    @Override
    public Map excuteByLimit(SysDatasource sysDatasource, String sqlText,int pageSize) {
         return getExcuteMaps(sysDatasource, sqlText, DbType.hive,pageSize);
    }

    @Override
    public List<Map<String, Object>> quaryByPage(SysDatasource sysDatasource, String sqlText,int page,int size) {
        return getSqlPage(sysDatasource, sqlText, DbType.hive,page,size);
    }

    @Override
    public int getSqlCount(SysDatasource sysDatasource, String sqlText) {
        return getSqlCount(sysDatasource, sqlText, DbType.hive);
    }

    @Override
    public String getTableCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        String sqlText=StringUtils.format("show create table {}.{}",mdmRegistry.getRegDir(),mdmRegistry.getRegName());
        List<Map<String, Object>>  dataList = new ArrayList<Map<String,Object>>();
    	JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
    	dataList = jd.queryForList(sqlText);
        StringBuffer buffer = new StringBuffer();
    	if(dataList.size()>0) {
    	    for(Map<String, Object> data : dataList){
                buffer.append(data.get("createtab_stmt").toString());
            }
    		return buffer.toString();
    	}else {
    		return "无";
    	}
    }

    @Override
    public String getProcedureCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        String sqlText = String.format("SELECT t.full_text FROM system.procedures_v t WHERE t.database_name = '%s' and t.procedure_name = '%s' ", mdmRegistry.getRegDir().toLowerCase(), mdmRegistry.getRegName().toLowerCase());
        List<Map<String, Object>>  dataList = new ArrayList<Map<String,Object>>();
        JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        dataList = jd.queryForList(sqlText);
        StringBuffer buffer = new StringBuffer();
        if(dataList.size()>0) {
            for(Map<String, Object> data : dataList){
                buffer.append(data.get("full_text").toString());
            }
            return buffer.toString();
        }else {
            return "无";
        }
    }

    @Override
    public String getViewCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        List<Map<String, Object>>  dataList = new ArrayList<Map<String,Object>>();
        String sqlText=StringUtils.format("show create table {}.{}",mdmRegistry.getRegDir(),mdmRegistry.getRegName());
        JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        dataList = jd.queryForList(sqlText);
        StringBuffer buffer = new StringBuffer();
        if(dataList.size()>0) {
            for(Map<String, Object> data : dataList){
                buffer.append(data.get("createtab_stmt").toString());
            }
            return buffer.toString();
        }else {
            return "无";
        }
    }

    @Override
    public Map<String,List> getDatabaseInfos(SysDatasource sysDatasource,MdmRegistry mdmRegistry) {
        Map<String,List> map = new HashMap<>();
        Boolean isPro = false;
        Boolean isTable = false;
        Boolean isView = false;
        for (Integer metaType : mdmRegistry.getMetaTypes()) {
            if(metaType== MdmConstants.TABLE){
                isTable = true;
            }
            if(metaType==MdmConstants.PROCEDURE){
                isPro = true;
            }
            if(metaType==MdmConstants.VIEW){
                isView = true;
            }
        }
        if(isTable){
            List<Map<String,Object>> list = getTablesAndViews(sysDatasource,mdmRegistry.getRegDir(),new String[]{"TABLE"});
            log.info("表list:{}",list.size());
            map.put("table",list);
        }
        if(isView){
            List<Map<String,Object>> list = getTablesAndViews(sysDatasource,mdmRegistry.getRegDir(),new String[]{"VIEW"});
            log.info("视图list:{}",list.size());
            map.put("view",list);
        }
        if(isPro){
            List<Map<String,Object>> list = getProcedures(sysDatasource,mdmRegistry.getRegDir());
            log.info("存储过程list:{}",list.size());
            map.put("procedures",list);
        }
        return map;
    }

    @Override
    public List<SQLStatement> parseStatements(SysDatasource sysDatasource, String sqlText) {
        return SQLUtils.parseStatements(sqlText, DbType.hive);
    }

    @Override
    public SchemaStatVisitor createSchemaStatVisitor(SysDatasource sysDatasource) {
        return SQLUtils.createSchemaStatVisitor( DbType.hive);
    }

	@Override
	public Boolean createUser(SysDatasource sysDatasource, String username, String passwd) {
		throw new RuntimeException("数据库不支持创建临时用户，请选择其他接口");
	}

	@Override
	public Boolean grantBytable(SysDatasource sysDatasource, String username, String tables, String passwd,boolean flag) {
		return null;
	}


	@Override
	public Boolean revokeBytable(SysDatasource sysDatasource, String username, String tables, String passwd,boolean flag) {
		
		throw new RuntimeException("功能开发中，请选择其他接口");
	}
	
	@Override
	public Boolean dropUser(SysDatasource sysDatasource, String username) {
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		String sql ="DROP USER "+username;
		jd.execute(sql);
		return true;
	}

    @Override
    public String createSqlGeneration(DicDataExchange sqlVo,SysDatasource sysDatasource) {
        String sql = "CREATE TABLE "+sqlVo.getTarSchema()+"."+sqlVo.getTarTableName()+"( \n";
        // fieldSqls 字段拼接
        StringBuffer fieldSqls = new StringBuffer("");
        List<MdmDataEntityProp>  props = sqlVo.getProps();
        //根据前端排序序号排序，便于生成字段语句
        Collections.sort(props, new Comparator<MdmDataEntityProp>() {
            @Override
            public int compare(MdmDataEntityProp o1, MdmDataEntityProp o2) {
                return o1.getOrderId().compareTo(o2.getOrderId());
            }
        });
        //存储主键语句
        List<String> priKeySqls = new ArrayList();
        List<MdmDataEntityProp> sqlVoProps = sqlVo.getProps();
        Boolean flag = false;
        for (int i = 0, sqlVoPropsSize = sqlVoProps.size(); i < sqlVoPropsSize; i++) {

            MdmDataEntityProp p = sqlVoProps.get(i);
            StringBuffer fieldSql = new StringBuffer("");
            //添加字段名和类型
            fieldSql.append(StringUtils.format("    {}  {}", p.getPropName(), p.getDataType()));
            //添加列大小和小数位
            if ((StringUtils.isNotNull(p.getColumnSize()) && p.getColumnSize() != 0) && (StringUtils.isNotNull(p.getDecimalDigits()) && p.getDecimalDigits() != 0)) {
                fieldSql.append(StringUtils.format("({},{})", p.getColumnSize(), p.getDecimalDigits()));
            } else if (StringUtils.isNotNull(p.getColumnSize()) && p.getColumnSize() != 0) {
                fieldSql.append(StringUtils.format("({})", p.getColumnSize()));
            }
            //拼接默认值
//            if(p.getDefaultValue().equals("")){
//                fieldSql.append("  DEFAULT ''");
//            }else{
//                fieldSql.append(StringUtils.format("  DEFAULT {}",p.getDefaultValue()));
//            }
            //拼接注释
            if (StringUtils.isNotEmpty(p.getPropComment()) && p.getPropComment().indexOf("?") == -1) {
                fieldSql.append(StringUtils.format("  COMMENT '{}'", p.getPropComment()));
            }
            //拼接主键
//            if (p.getIsPriKey().equals("Y")) {
//                priKeySqls.add(StringUtils.format("    ,constraint {}_pk\n" +
//                        "        primary key (id) disable novalidate\n", p.getPropName()));
//            }

            fieldSql.append(", \n");
//            if(i==sqlVo.getEltdateIndex()&&sqlVo.getEltdateIndex()!=0){
//                flag = true;
//                fieldSqls.append("    ETL_DATE INT COMMENT '采集日期', \n");
//            }
            fieldSqls.append(fieldSql);
        }

        //通过标识判断是第一次添加或者是在最后一位，如果是就添加到最后
//        if(flag==false){
//            fieldSqls.append("    ETL_DATE INT COMMENT '采集日期', \n");
//        }


        fieldSqls = new StringBuffer(fieldSqls.substring(0,fieldSqls.length()-3)+" \n");
//        //默认拼接采集日期字段
//        fieldSqls.append("   ");
        //字段最后拼接主键语句
        for (String priKeySql : priKeySqls) {
            fieldSqls.append(priKeySql);
        }
        fieldSqls.append(")\n");
        sql = sql+ fieldSqls;
        //拼接表注释
        String tableComment="";
        if(StringUtils.isNotNull(sqlVo.getTableComment())&&sqlVo.getTableComment().indexOf("?")==-1) {
            tableComment= StringUtils.format("COMMENT '{}'\n",sqlVo.getTableComment());
        }
        sql = sql+ tableComment;
        try {
            String initdate = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("acq.data.init_date");
            Date startDate = new SimpleDateFormat("yyyy-MM").parse(initdate);
            Date endDate = new SimpleDateFormat("yyyy-MM").parse(DateUtils.getDate());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            int startYear = calendar.get(Calendar.YEAR);
            calendar.setTime(endDate);
            int endYear = calendar.get(Calendar.YEAR);
            int endMonth = calendar.get(Calendar.MONTH)+1;
            //如果分区字段不是 无，拼接分区字段及类型
            if(!sqlVo.getPartitionType().equals(DicConstants.PARTITION_TYPE_N)){
                String partition="";
                String partitioncoltype = DictUtils.getDictLabel("partition_col_type",sqlVo.getPartitionColType());
                if(sqlVo.getPartitionType().equals(DicConstants.PARTITION_TYPE_D)) {
                    partition += String.format("PARTITIONED BY (BUS_DATE %s) ", partitioncoltype);
                }else if(sqlVo.getPartitionType().equals(DicConstants.PARTITION_TYPE_M)) {
                    partition += String.format("PARTITION BY RANGE(BUS_MONTH %s) \n", partitioncoltype);
                    partition += " ( \n";
                    for(int i=startYear;i<=endYear;i++) {
                            partition += String.format("  PARTITION BEFORE%d01 VALUES LESS THAN ('%d0200'),\n", i, i);
                        if(i!=endYear||endMonth>=2) {
                            partition += String.format("  PARTITION BEFORE%d02 VALUES LESS THAN ('%d0300'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=3) {
                            partition += String.format("  PARTITION BEFORE%d03 VALUES LESS THAN ('%d0400'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=4) {
                            partition += String.format("  PARTITION BEFORE%d04 VALUES LESS THAN ('%d0500'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=5) {
                            partition += String.format("  PARTITION BEFORE%d05 VALUES LESS THAN ('%d0600'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=6) {
                            partition += String.format("  PARTITION BEFORE%d06 VALUES LESS THAN ('%d0700'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=7) {
                            partition += String.format("  PARTITION BEFORE%d07 VALUES LESS THAN ('%d0800'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=8) {
                            partition += String.format("  PARTITION BEFORE%d08 VALUES LESS THAN ('%d0900'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=9) {
                            partition += String.format("  PARTITION BEFORE%d09 VALUES LESS THAN ('%d1000'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=10) {
                            partition += String.format("  PARTITION BEFORE%d10 VALUES LESS THAN ('%d1100'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=11) {
                            partition += String.format("  PARTITION BEFORE%d11 VALUES LESS THAN ('%d1200'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=12) {
                            partition += String.format("  PARTITION BEFORE%d12 VALUES LESS THAN ('%d0100'),\n", i, i + 1);
                        }
                    }
                    partition=partition.substring(0,partition.lastIndexOf(","))+"\n";
                    partition += " ) ";
                }else if(sqlVo.getPartitionType().equals(DicConstants.PARTITION_TYPE_Q)) {
                    partition += String.format("PARTITION BY RANGE(BUS_QUARTER %s) \n", partitioncoltype);
                    partition += " ( \n";
                    for(int i=startYear;i<=endYear;i++) {
                            partition += String.format("  PARTITION BEFORE%d01 VALUES LESS THAN ('%d0400'),\n", i, i);
                        if(i!=endYear||endMonth>=4) {
                            partition += String.format("  PARTITION BEFORE%d04 VALUES LESS THAN ('%d0700'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=7) {
                            partition += String.format("  PARTITION BEFORE%d07 VALUES LESS THAN ('%d1000'),\n", i, i);
                        }
                        if(i!=endYear||endMonth>=10) {
                            partition += String.format("  PARTITION BEFORE%d10 VALUES LESS THAN ('%d0100'),\n", i, i + 1);
                        }
                    }
                    partition=partition.substring(0,partition.lastIndexOf(","))+"\n";
                    partition += " ) ";
                }else if(sqlVo.getPartitionType().equals(DicConstants.PARTITION_TYPE_Y)) {
                    partition += String.format("PARTITION BY RANGE(BUS_YEAR %s) \n", partitioncoltype);
                    partition += " ( \n";
                    for(int i=startYear;i<=endYear;i++) {
                        partition +="  PARTITION BEFORE"+i+" VALUES LESS THAN ('"+(i+1)+"0100'),\n";
                    }
                    partition=partition.substring(0,partition.lastIndexOf(","))+"\n";
                    partition += " ) ";
                }
                sql = sql+ partition;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String tblpropertiesSql = "";
        //拼接分桶字段语句
        if(StringUtils.isNotEmpty(sqlVo.getBucketCol())) {
            sql += String.format("\nCLUSTERED BY (%s) INTO %d BUCKETS", sqlVo.getBucketCol(), sqlVo.getBucketNum());
            tblpropertiesSql += "\nTBLPROPERTIES('transactional'='true')";
        }
        //拼接建表语句后缀
        if(StringUtils.isNotEmpty(sysDatasource.getCreateTableSuffix())){
            sql += "\n"+sysDatasource.getCreateTableSuffix();
        }
        //拼接hdfs路径
//        if(StringUtils.isNotEmpty(sqlVo.getHdfsPath())){
//            sql += String.format("\nLOCATION '%s'", sqlVo.getHdfsPath());
//        }
        sql += tblpropertiesSql;
//        sql +=";";
        return sql;
    }

	@Override
	public void createTableTemp(String tableName, SysDatasource sysDatasource) {
		String sql="create table IF NOT EXISTS "+tableName+"_temp like "+tableName;
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD columns (batch_id string ,apply_status string  ,create_type string ,creat_by string ,create_time string,msg_ string,keys_ string) ";
		jd.execute(sql);
	}
}
