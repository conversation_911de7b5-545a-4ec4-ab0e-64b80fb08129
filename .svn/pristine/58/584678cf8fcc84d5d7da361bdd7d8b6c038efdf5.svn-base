package com.dqms.dqm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dqm.domain.DqmValidationProblem;
import com.dqms.dqm.service.IDqmValidationProblemService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 检查规则任务问题管理Controller
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@RestController
@RequestMapping("/dqm/problem")
public class DqmValidationProblemController extends BaseController
{
    @Autowired
    private IDqmValidationProblemService dqmValidationProblemService;

    /**
     * 查询检查规则任务问题管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DqmValidationProblem dqmValidationProblem)
    {
        startPage();
        List<DqmValidationProblem> list = dqmValidationProblemService.selectDqmValidationProblemList(dqmValidationProblem);
        return getDataTable(list);
    }

    /**
     * 导出检查规则任务问题管理列表
     */
    @Log(title = "检查规则任务问题管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmValidationProblem dqmValidationProblem)
    {
        List<DqmValidationProblem> list = dqmValidationProblemService.selectDqmValidationProblemList(dqmValidationProblem);
        ExcelUtil<DqmValidationProblem> util = new ExcelUtil<DqmValidationProblem>(DqmValidationProblem.class);
        return util.exportExcel(list, "problem");
    }

    /**
     * 获取检查规则任务问题管理详细信息
     */
    @GetMapping(value = "/{dqmValidationProblemId}")
    public AjaxResult getInfo(@PathVariable("dqmValidationProblemId") Integer dqmValidationProblemId)
    {
        return AjaxResult.success(dqmValidationProblemService.selectDqmValidationProblemById(dqmValidationProblemId));
    }

    /**
     * 新增检查规则任务问题管理
     */
    @Log(title = "检查规则任务问题管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DqmValidationProblem dqmValidationProblem)
    {
        return toAjax(dqmValidationProblemService.insertDqmValidationProblem(dqmValidationProblem));
    }

    /**
     * 修改检查规则任务问题管理
     */
    @Log(title = "检查规则任务问题管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmValidationProblem dqmValidationProblem)
    {
        return toAjax(dqmValidationProblemService.updateDqmValidationProblem(dqmValidationProblem));
    }

    /**
     * 删除检查规则任务问题管理
     */
    @Log(title = "检查规则任务问题管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dqmValidationProblemIds}")
    public AjaxResult remove(@PathVariable Integer[] dqmValidationProblemIds)
    {
        return toAjax(dqmValidationProblemService.deleteDqmValidationProblemByIds(dqmValidationProblemIds));
    }

    /**
     * 规则执行问题执行趋势
     */
    @GetMapping("/getProblemSumForDate/{dateType}")
    public AjaxResult getProblemSumForDate(@PathVariable("dateType")Integer dateType)
    {
        DqmValidationProblem dqmValidationProblem=new DqmValidationProblem();
        dqmValidationProblem.getParams().put("dateType",dateType);
        return AjaxResult.success(dqmValidationProblemService.getProblemSumForDate(dqmValidationProblem));
    }
    /**
     * 问题数据每日处理统计
     */
    @GetMapping("/getProblemHandleForDate")
    public AjaxResult getProblemHandleForDate(DqmValidationProblem dqmValidationProblem)
    {
        return AjaxResult.success(dqmValidationProblemService.getProblemHandleForDate(dqmValidationProblem));
    }

    /**
     * 待处理任务列表
     */
    @GetMapping("/getProblemHandleForNo")
    public AjaxResult getProblemHandleForNo(DqmValidationProblem dqmValidationProblem)
    {
        return AjaxResult.success(dqmValidationProblemService.getProblemHandleForNo(dqmValidationProblem));
    }
    /**
     * 待处理任务列表
     */
    @GetMapping("/getSystemScore/{dateType}")
    public AjaxResult getSystemScore(@PathVariable("dateType")Integer dateType)
    {
        DqmValidationProblem dqmValidationProblem=new DqmValidationProblem();
        dqmValidationProblem.getParams().put("dateType",dateType);
        return AjaxResult.success(dqmValidationProblemService.getSystemScore(dqmValidationProblem));
    }
}
