<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscEntityPropClassMapper">
    
    <resultMap type="DscEntityPropClass" id="DscEntityPropClassResult">
        <result property="entityPropClassId"    column="entity_prop_class_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="orderNum"    column="order_num"    />
        <result property="classRemit"    column="class_remit"    />
        <result property="guideLevel"    column="guide_level"    />
        <result property="enterpriseLevel"    column="enterprise_level"    />
        <result property="sensitivityLevel"    column="sensitivity_level"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="DscEntityPropClassVo" id="DscEntityPropClassVoResult">
        <result property="entityPropClassId"    column="entity_prop_class_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="orderNum"    column="order_num"    />
        <result property="classRemit"    column="class_remit"    />
        <result property="guideLevel"    column="guide_level"    />
        <result property="enterpriseLevel"    column="enterprise_level"    />
        <result property="sensitivityLevel"    column="sensitivity_level"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <sql id="selectDscEntityPropClassVo">
    SELECT
        entity_prop_class_id,
        parent_id,
        ancestors,
        class_name,
        class_name_full,
        order_num,
        class_remit,
        guide_level,
        enterprise_level,
        sensitivity_level,
        create_by,
        update_by,
        create_id,
        update_id,
        create_time,
        update_time
    FROM dsc_entity_prop_class
    </sql>
    <sql id="selectDscEntityPropClassVoRemove0">
    SELECT
        entity_prop_class_id,
        parent_id,
        ancestors,
        class_name,
        class_name_full,
        order_num,
        class_remit,
        guide_level,
        enterprise_level,
        sensitivity_level,
        create_by,
        update_by,
        create_id,
        update_id,
        create_time,
        update_time
    FROM
	(select * from dsc_entity_prop_class where entity_prop_class_id !=0) t
    </sql>

    <select id="selectDscEntityPropClassList" parameterType="DscEntityPropClass" resultMap="DscEntityPropClassResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="sensitivityLevel != null  and sensitivityLevel != ''"> and sensitivity_level = #{sensitivityLevel}</if>

            <if test="className != null  and className != ''"> and class_name_full like concat('%', #{className}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
        order by IFNULL(t.order_num, 9999)
    </select>
    <select id="selectDscEntityPropClassListVo" parameterType="dscEntityPropClassVo" resultMap="DscEntityPropClassVoResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="classNameFull != null  and classNameFull != ''"> and class_name_full = #{classNameFull}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
    </select>
    
    <select id="selectDscEntityPropClassById" parameterType="Long" resultMap="DscEntityPropClassResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        where entity_prop_class_id = #{entityPropClassId}
    </select>
    <select id="selectClassName" parameterType="DscEntityPropClass" resultMap="DscEntityPropClassResult">
       select distinct class_name from dsc_entity_prop_class where entity_prop_class_id !=0
    </select>
    <select id="selectClassNameFull" parameterType="DscEntityPropClass" resultMap="DscEntityPropClassResult">
       select  class_name_full from dsc_entity_prop_class where entity_prop_class_id !=0
    </select>

    <select id="selectDscEntityPropClassByClassNameFullvs" parameterType="DscEntityPropClass" resultMap="DscEntityPropClassResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        where class_name = #{classNameFull}  and ancestors = #{ancestors}
    </select>

    <select id="selectDscEntityPropClassByClassNameFullvss" parameterType="String" resultMap="DscEntityPropClassResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        where class_name = #{classNameFullvs}
    </select>

    <select id="selectDscEntityPropClassByClassNameFull" parameterType="DscEntityPropClass" resultMap="DscEntityPropClassResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        where class_name_full = #{classNameFull}
    </select>

    <select id="selectDscEntityProotpClassByClassName" parameterType="DscEntityPropClass" resultMap="DscEntityPropClassResult">
        <include refid="selectDscEntityPropClassVoRemove0"/>
        where class_name_full = #{className}
    </select>
        
    <insert id="insertDscEntityPropClass" parameterType="DscEntityPropClass" useGeneratedKeys="true" keyProperty="entityPropClassId">
        insert into dsc_entity_prop_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="className != null">class_name,</if>
            <if test="classNameFull != null">class_name_full,</if>
            <if test="orderNum != null">order_num,</if>

            <if test="classRemit != null and classRemit!=''">class_remit,</if>
            <if test="guideLevel != null and guideLevel!=''">guide_level,</if>
            <if test="enterpriseLevel != null and enterpriseLevel!=''">enterprise_level,</if>
            <if test="sensitivityLevel != null and sensitivityLevel!=''">sensitivity_level,</if>

            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="className != null">#{className},</if>
            <if test="classNameFull != null">#{classNameFull},</if>
            <if test="orderNum != null">#{orderNum},</if>

            <if test="classRemit != null and classRemit!=''">#{classRemit},</if>
            <if test="guideLevel != null and guideLevel!=''">#{guideLevel},</if>
            <if test="enterpriseLevel != null and enterpriseLevel!=''">#{enterpriseLevel},</if>
            <if test="sensitivityLevel != null and sensitivityLevel!=''">#{sensitivityLevel},</if>

            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDscEntityPropClass" parameterType="DscEntityPropClass">
        update dsc_entity_prop_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="classNameFull != null">class_name_full = #{classNameFull},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="classRemit != null and classRemit!=''">class_remit=#{classRemit},</if>
            <if test="guideLevel != null and guideLevel!=''">guide_level=#{guideLevel},</if>
            <if test="enterpriseLevel != null and enterpriseLevel!=''">enterprise_level=#{enterpriseLevel},</if>
            <if test="sensitivityLevel != null and sensitivityLevel!=''">sensitivity_level=#{sensitivityLevel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where entity_prop_class_id = #{entityPropClassId}
    </update>

    <delete id="deleteDscEntityPropClassById" parameterType="Long">
        delete from dsc_entity_prop_class where entity_prop_class_id = #{entityPropClassId}
    </delete>

    <delete id="deleteDscEntityPropClassByIds" parameterType="String">
        delete from dsc_entity_prop_class where entity_prop_class_id in 
        <foreach item="entityPropClassId" collection="array" open="(" separator="," close=")">
            #{entityPropClassId}
        </foreach>
    </delete>
    
       
    <select id="selectChildrenClassById" parameterType="Long" resultMap="DscEntityPropClassResult">
		select * from dsc_entity_prop_class where find_in_set(#{entityPropClassId}, ancestors)
	</select>
	
	<update id="updateClassChildren" parameterType="java.util.List">
	    update dsc_entity_prop_class set ancestors =
	    <foreach collection="dscEntityPropClass" item="item" index="index"
	        separator=" " open="case entity_prop_class_id" close="end">
	        when #{item.entityPropClassId} then #{item.ancestors}
	    </foreach>
	    where entity_prop_class_id in
	    <foreach collection="dscEntityPropClass" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.entityPropClassId}
	    </foreach>
	</update>
	
	<update id="updateClassNameFullChildren" parameterType="java.util.List">
	    update dsc_entity_prop_class set class_name_full =
	    <foreach collection="dscEntityPropClass" item="item" index="index"
	        separator=" " open="case entity_prop_class_id" close="end">
	        when #{item.entityPropClassId} then #{item.classNameFull}
	    </foreach>
	    where entity_prop_class_id in
	    <foreach collection="dscEntityPropClass" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.entityPropClassId}
	    </foreach>
	</update>


    <select id="getGrandList" parameterType="Long" resultMap="DscEntityPropClassResult">
        select
            t1.entity_prop_class_id,
            t1.class_name
        from dsc_entity_prop_class t1
        <where>
            entity_prop_class_id !=0
            and t1.parent_id=0
        </where>
    </select>
    <select id="getParentList" parameterType="Long" resultMap="DscEntityPropClassResult">
        select
            t2.entity_prop_class_id ,
            t2.class_name
        from dsc_entity_prop_class t2
        <where>
            t2.entity_prop_class_id !=0
            and t2.parent_id in(
                select
                    t1.entity_prop_class_id as grandId
                from dsc_entity_prop_class t1
                where t1.parent_id=0
            )
            <if test="entityPropClassId != null "> and t2.parent_id = #{entityPropClassId}</if>
        </where>
    </select>
    <select id="getEntityPropClassList" parameterType="Long" resultMap="DscEntityPropClassResult">
        select
            t3.entity_prop_class_id ,
            t3.class_name
        from dsc_entity_prop_class t3
        <where>
            t3.entity_prop_class_id !=0
	        and t3.parent_id in(
                select
                    t2.entity_prop_class_id as parentId
                from dsc_entity_prop_class t2
                where t2.parent_id in(
                    select
                        t1.entity_prop_class_id as grandId
                    from dsc_entity_prop_class t1
                    where t1.parent_id=0
                    )
	        )
	    <if test="entityPropClassId != null "> and t3.parent_id = #{entityPropClassId}</if>
        </where>


    </select>






</mapper>