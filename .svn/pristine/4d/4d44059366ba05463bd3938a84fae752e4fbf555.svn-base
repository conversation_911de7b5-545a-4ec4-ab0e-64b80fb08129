import request from '@/utils/request'

// 查询落标主列表
export function listDsmDiscernMain(query) {
  return request({
    url: '/dsm/dsmDiscernMain/list',
    method: 'get',
    params: query
  })
}

// 查询落标主详细
export function getDsmDiscernMain(discernMainId) {
  return request({
    url: '/dsm/dsmDiscernMain/' + discernMainId,
    method: 'get'
  })
}

// 新增落标主
export function addDsmDiscernMain(data) {
  return request({
    url: '/dsm/dsmDiscernMain',
    method: 'post',
    data: data
  })
}

// 修改落标主
export function updateDsmDiscernMain(data) {
  return request({
    url: '/dsm/dsmDiscernMain',
    method: 'put',
    data: data
  })
}

// 删除落标主
export function delDsmDiscernMain(discernMainId) {
  return request({
    url: '/dsm/dsmDiscernMain/' + discernMainId,
    method: 'delete'
  })
}

// 导出落标主
export function exportDsmDiscernMain(query) {
  return request({
    url: '/dsm/dsmDiscernMain/export',
    method: 'get',
    params: query
  })
}