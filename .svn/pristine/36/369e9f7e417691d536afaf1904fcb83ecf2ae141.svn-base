<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.mdm.mapper.MdmRegistryMapper">

    <resultMap type="MdmRegistry" id="MdmRegistryResult">
        <result property="regId"    column="reg_id"    />
        <result property="regName"    column="reg_name"    />
        <result property="regDir"    column="reg_dir"    />
        <result property="metaType"    column="meta_type"    />
        <result property="acqStatus"    column="acq_status"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="systemId"    column="system_id"    />
        <result property="themeId"    column="theme_id"    />
        <result property="regAnt"    column="reg_ant"    />
        <result property="layerId"    column="layer_id"    />
        <result property="isAutoAnalysis"    column="is_auto_analysis"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemName"    column="system_name"    />
        <result property="themeName"    column="theme_name"    />
        <result property="layerName"    column="layer_name"    />
        <result property="dsType"    column="ds_type"    />
        <result property="taskId"    column="task_id"    />
        <result property="expandFlag"    column="expand_flag"    />
        <result property="entityId"    column="entity_id"    />

    </resultMap>

    <sql id="selectMdmRegistryVo">
        select r.reg_id, r.reg_name, r.reg_dir, r.meta_type, r.acq_status, r.datasource_id, r.system_id, r.theme_id, r.reg_ant, r.layer_id, r.is_auto_analysis, r.is_enable,r.task_id,r.expand_flag, r.create_by, r.update_by, r.create_id, r.update_id, r.create_time, r.update_time
        ,d.name datasource_name
        ,d.ds_type ds_type
        ,s.name system_name
        ,t.theme_name theme_name
        ,l.layer_name layer_name
        ,e.entity_id
        from mdm_registry r
        left join mdm_data_entity e on e.registry_id = r.reg_id
        left join sys_datasource d on r.datasource_id = d.datasource_id
        left join sys_system s on r.system_id = s.system_id
        left join mdm_theme t on r.theme_id = t.theme_id
        left join mdm_layer l on r.layer_id=l.layer_id
    </sql>

    <select id="selectMdmRegistryList" parameterType="MdmRegistry" resultMap="MdmRegistryResult">
        <include refid="selectMdmRegistryVo"/>
        <where>
            <if test="regName != null  and regName != ''"> and r.reg_name like concat('%', #{regName}, '%')</if>
            <if test="metaType != null  and metaType != ''"> and r.meta_type = #{metaType}</if>
            <if test="datasourceId != null "> and r.datasource_id = #{datasourceId}</if>
            <if test="systemId != null "> and r.system_id = #{systemId}</if>
            <if test="acqStatus != null and acqStatus!=''"> and r.acq_status = #{acqStatus}</if>
            <if test="themeId != null "> and r.theme_id = #{themeId}</if>
            <if test="layerId != null "> and r.layer_id = #{layerId}</if>
            <if test="expandFlag != null "> and r.expand_flag = #{expandFlag}</if>
            and r.del_flag != '2'
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by r.reg_id desc
    </select>

    <select id="selectMdmRegistryListByPage" resultMap="MdmRegistryResult">
        <include refid="selectMdmRegistryVo"/>
        where r.del_flag != '2' and r.datasource_id = #{datasourceId}
        order by r.reg_id desc
        limit #{page}, #{size}
    </select>

    <select id="selectMdmRegistryById" parameterType="Long" resultMap="MdmRegistryResult">
        <include refid="selectMdmRegistryVo"/>
        where r.reg_id = #{regId}
    </select>

    <select id="getMdmRegistry" parameterType="MdmRegistry" resultMap="MdmRegistryResult">
        select *
        from mdm_registry  where reg_name = #{regName} and datasource_id = #{datasourceId} and reg_dir = #{regDir} AND del_flag != '2'
        <!-- <where>
             <if test="regName != null "> and r.reg_name = #{regName}</if>
             <if test="datasourceId != null "> and r.datasource_id = #{datasourceId}</if>
             <if test="systemId != null "> and r.system_id = #{systemId}</if>
         </where>-->
    </select>

    <insert id="insertMdmRegistry" parameterType="MdmRegistry" useGeneratedKeys="true" keyProperty="themeId">
        insert into mdm_registry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regId != null">reg_id,</if>
            <if test="regName != null">reg_name,</if>
            <if test="regDir != null">reg_dir,</if>
            <if test="metaType != null">meta_type,</if>
            <if test="acqStatus != null">acq_status,</if>
            <if test="datasourceId != null">datasource_id,</if>
            <if test="systemId != null">system_id,</if>
            <if test="themeId != null">theme_id,</if>
            <if test="regAnt != null">reg_ant,</if>
            <if test="layerId != null">layer_id,</if>
            <if test="isAutoAnalysis != null">is_auto_analysis,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="expandFlag != null">expand_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regId != null">#{regId},</if>
            <if test="regName != null">#{regName},</if>
            <if test="regDir != null">#{regDir},</if>
            <if test="metaType != null">#{metaType},</if>
            <if test="acqStatus != null">#{acqStatus},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="themeId != null">#{themeId},</if>
            <if test="regAnt != null">#{regAnt},</if>
            <if test="layerId != null">#{layerId},</if>
            <if test="isAutoAnalysis != null">#{isAutoAnalysis},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="expandFlag != null">#{expandFlag},</if>
        </trim>
    </insert>

    <update id="updateMdmRegistry" parameterType="MdmRegistry">
        update mdm_registry
        <trim prefix="SET" suffixOverrides=",">
            <if test="regName != null">reg_name = #{regName},</if>
            <if test="regDir != null">reg_dir = #{regDir},</if>
            <if test="metaType != null">meta_type = #{metaType},</if>
            <if test="acqStatus != null">acq_status = #{acqStatus},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="themeId != null">theme_id = #{themeId},</if>
            <if test="themeId == null">theme_id = null,</if>
            <if test="regAnt != null">reg_ant = #{regAnt},</if>
            <if test="layerId != null">layer_id = #{layerId},</if>
            <if test="isAutoAnalysis != null">is_auto_analysis = #{isAutoAnalysis},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="expandFlag != null">expand_flag = #{expandFlag},</if>
        </trim>
        <choose>
            <when test="regId != null  and regId != ''">
                where reg_id = #{regId}
            </when>
            <otherwise>
                where reg_name = #{regName}
            </otherwise>
        </choose>

    </update>

    <delete id="deleteMdmRegistryById" parameterType="Long">
        delete from mdm_registry where reg_id = #{regId}
    </delete>

    <update id="deleteMdmRegistryByIds" parameterType="String">
        update  mdm_registry  set del_flag = '2' where reg_id in
        <foreach item="regId" collection="array" open="(" separator="," close=")">
            #{regId}
        </foreach>
    </update>

    <select id="checkRegistryNameUnique" parameterType="MdmRegistry" resultType="int">
        select count(1) from mdm_registry where reg_name = #{regName}
        AND reg_dir = #{regDir,jdbcType=VARCHAR}
        AND datasource_id = #{datasourceId,jdbcType=NUMERIC}
        AND meta_type = #{metaType,jdbcType=NUMERIC}
        <if test="regId != null  and regId != ''"> and reg_id != #{regId}</if>
        and del_flag != '2'
        limit 1
    </select>

    <select id="selectMdmRegistryListByTaskId" resultMap="MdmRegistryResult">
        <include refid="selectMdmRegistryVo"/>
        <where> r.del_flag != '2' and r.is_enable='Y'
            <if test="taskId != null "> and r.task_id = #{taskId}</if>
        </where>
        order by r.reg_id desc
    </select>

    <select id="selectMdmRegistryListByRegIdsForTask" resultMap="MdmRegistryResult">
        <include refid="selectMdmRegistryVo"/>
        <where>
            r.del_flag != '2'
            and (r.task_id is not null and r.task_id != '')
            and reg_id in
            <foreach collection="regIds" item="regId" open="(" separator="," close=")">
                #{regId}
            </foreach>
        </where>
        order by r.reg_id desc
    </select>

    <select id="selectUnMdmRegistryListByTaskId" parameterType="MdmRegistry" resultMap="MdmRegistryResult">
        <include refid="selectMdmRegistryVo"/>
        <where>
            <if test="regName != null  and regName != ''"> and r.reg_name like concat('%', #{regName}, '%')</if>
            <if test="metaType != null  and metaType != ''"> and r.meta_type = #{metaType}</if>
            <if test="datasourceId != null "> and r.datasource_id = #{datasourceId}</if>
            <if test="systemId != null "> and r.system_id = #{systemId}</if>


            and (r.task_id is null OR r.task_id = '') and r.del_flag != '2'
            <if test="unIds != null ">
                and reg_id not in
                <foreach collection="unIds" item="unId" open="(" separator="," close=")">
                    #{unId}
                </foreach>
            </if>
        </where>
        order by r.reg_id desc
    </select>
    <update id="addMdmRegistryAndTaskRel">
        update  mdm_registry set task_id=#{taskId}
        where reg_id in
        <foreach collection="regIds" item="regId" open="(" separator="," close=")">
            #{regId}
        </foreach>
    </update>
    <update id="delMdmRegistryAndTaskRel" parameterType="Long">
       update  mdm_registry set task_id = null where task_id =#{taskId}
    </update>
    <insert id="insertBatch">
        insert into mdm_registry(reg_id,reg_name,reg_dir,
        meta_type,acq_status,reg_ant,
        datasource_id,system_id,theme_id,layer_id,
        is_auto_analysis,is_enable,expand_flag,
        create_id,update_id,task_id,
        create_by,create_time,
        update_by,update_time
        )
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.regId,jdbcType=NUMERIC},#{item.regName,jdbcType=VARCHAR},#{item.regDir,jdbcType=VARCHAR},
            #{item.metaType,jdbcType=VARCHAR},#{item.acqStatus,jdbcType=VARCHAR},#{item.regAnt,jdbcType=VARCHAR},
            #{item.datasourceId,jdbcType=NUMERIC},#{item.systemId,jdbcType=NUMERIC},#{item.themeId,jdbcType=NUMERIC},
            #{item.layerId,jdbcType=NUMERIC},#{item.isAutoAnalysis,jdbcType=VARCHAR},#{item.isEnable,jdbcType=VARCHAR},
            #{item.expandFlag,jdbcType=VARCHAR},#{item.createId,jdbcType=NUMERIC},#{item.updateId,jdbcType=NUMERIC},#{item.taskId,jdbcType=NUMERIC},
            #{item.createBy,jdbcType=VARCHAR},#{item.createTime,jdbcType=TIMESTAMP},#{item.updateBy,jdbcType=VARCHAR},#{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    
    <resultMap type="MdmView" id="MdmViewResult">
        <result property="name"    column="name"    />
        <result property="zs"    column="zs"    />
        <result property="bs"    column="bs"    />
        <result property="st"    column="st"    />
        <result property="jb"    column="jb"    />
        <result property="ccgc"    column="ccgc"    />
        <result property="bg"    column="bg"    />
        <result property="lb"    column="lb"    />
        <result property="wlb"    column="wlb"    />
        <result property="wd"    column="wd"    />
        <result property="wwd"    column="wwd"    />
        <result property="zhs"    column="zhs"    />
        <result property="wzhs"    column="wzhs"    />
    </resultMap>
    <select id="getView" resultMap="MdmViewResult">
        SELECT s.name,
			(SELECT COUNT(1) FROM mdm_registry t1 WHERE t1.system_id=t.system_id AND del_flag='0') AS zs,
			(SELECT COUNT(1) FROM mdm_registry t1 WHERE t1.system_id=t.system_id AND del_flag='0' AND t1.meta_type='1') bs,
			(SELECT COUNT(1) FROM mdm_registry t1 WHERE t1.system_id=t.system_id AND del_flag='0' AND t1.meta_type='2') st,
			(SELECT COUNT(1) FROM mdm_registry t1 WHERE t1.system_id=t.system_id AND del_flag='0' AND t1.meta_type='3') jb,
			(SELECT COUNT(1) FROM mdm_registry t1 WHERE t1.system_id=t.system_id AND del_flag='0' AND t1.meta_type='4') ccgc,
			(SELECT COUNT(1) FROM mdm_registry t1 LEFT JOIN mdm_data_monitor m ON m.reg_id=t1.reg_id WHERE t1.system_id=t.system_id AND m.app_status='1') bg,
			SUM( IF(p.index_id IS NULL ,0,1)) lb,
			SUM( IF(p.index_id IS NOT NULL ,0,1)) wlb,
			SUM( IF(p.dimension_id IS NULL ,0,1)) wd,
			SUM( IF(p.dimension_id IS NOT NULL ,0,1)) wwd,
			SUM( IF(p.prop_comment IS NULL ,0,1)) zhs,
			SUM( IF(p.prop_comment IS NOT NULL ,0,1)) wzhs
			FROM mdm_registry t 
			LEFT JOIN sys_system s ON t.system_id=s.system_id
			LEFT JOIN mdm_data_monitor m ON m.reg_id=t.reg_id AND m.app_status='1'
			LEFT JOIN mdm_data_entity e ON t.reg_id=e.registry_id
			LEFT JOIN mdm_data_entity_prop p ON p.entity_id = e.entity_id
			WHERE  del_flag='0'
			GROUP BY t.system_id

    </select>
</mapper>
