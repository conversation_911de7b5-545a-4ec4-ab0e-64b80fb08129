package com.dqms.mdm.domain.vo;
import java.util.List;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmDataEntityShip;

/**
 * 数据实体对象 mdm_data_entity
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
public class MdmDataEntityVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据实体编号 */
    private String entityId;

 
    private String tableName;

    /** 表注释 */
    private String tableComment;

    /** SCHEMA */
    private String tableSchema;

    
    private List<MdmDataEntityShip> relations;
    
    private List<MdmDataEntityProp> mdmDataEntityProps;
    
    private Long[] srcEntityIds;

    private Long[] srcEntityPropIds;

    private Long[] tarEntityIds;

    private Long[] tarEntityPropIds;

    /** SQL脚本 */
    @Excel(name = "SQL脚本")
    private String sqlScripts;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNo;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createId;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long updateId;

    private Boolean pushServer;
    
    private String systemName;
    
    private String sysDatasourceName;
    
    private Long registryId;
    
    private String sysDatasourceType;
    
    private String entryType;
    
    private Long themeId;

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getTableComment() {
		return tableComment;
	}

	public void setTableComment(String tableComment) {
		this.tableComment = tableComment;
	}

	public String getTableSchema() {
		return tableSchema;
	}

	public void setTableSchema(String tableSchema) {
		this.tableSchema = tableSchema;
	}

	public List<MdmDataEntityShip> getRelations() {
		return relations;
	}

	public void setRelations(List<MdmDataEntityShip> relations) {
		this.relations = relations;
	}

	public List<MdmDataEntityProp> getMdmDataEntityProps() {
		return mdmDataEntityProps;
	}

	public void setMdmDataEntityProps(List<MdmDataEntityProp> mdmDataEntityProps) {
		this.mdmDataEntityProps = mdmDataEntityProps;
	}

	public Long[] getSrcEntityIds() {
		if(this.srcEntityIds==null&&relations!=null&&relations.size()>0) {
			Long[]  ids = new Long[relations.size()];
			int i=0;
			for(MdmDataEntityShip t : relations) {
				if(t.getSrcEntityId()!=null) {
					ids[i++]=t.getSrcEntityId();	
				}
			}
			return ids;
		}
		return srcEntityIds;
	}

	public void setSrcEntityIds(Long[] srcEntityIds) {
		this.srcEntityIds = srcEntityIds;
	}

	public Long[] getSrcEntityPropIds() {
		if(this.srcEntityPropIds==null&&relations!=null&&relations.size()>0) {
			Long[]  ids = new Long[relations.size()];
			int i=0;
			for(MdmDataEntityShip t : relations) {
				if(t.getSrcEntityPropId()!=null) {
					ids[i++]=t.getSrcEntityPropId();	
				}
				
			}
			return ids;
		}
		return srcEntityPropIds;
	}

	public void setSrcEntityPropIds(Long[] srcEntityPropIds) {
		this.srcEntityPropIds = srcEntityPropIds;
	}

	public Long[] getTarEntityIds() {
		if(this.tarEntityIds==null&&relations!=null&&relations.size()>0) {
			Long[]  ids = new Long[relations.size()];
			int i=0;
			for(MdmDataEntityShip t : relations) {
				if(t.getTarEntityId()!=null) {
					ids[i++]=t.getTarEntityId();
				}
			}
			return ids;
		}
		return tarEntityIds;
	}

	public void setTarEntityIds(Long[] tarEntityIds) {
		this.tarEntityIds = tarEntityIds;
	}

	public Long[] getTarEntityPropIds() {
		if(this.tarEntityPropIds==null&&relations!=null&&relations.size()>0) {
			Long[]  ids = new Long[relations.size()];
			int i=0;
			for(MdmDataEntityShip t : relations) {
				if(t.getTarEntityPropId()!=null) {
					ids[i++]=t.getTarEntityPropId();	
				}
				
			}
			return ids;
		}
		return tarEntityPropIds;
	}

	public void setTarEntityPropIds(Long[] tarEntityPropIds) {
		this.tarEntityPropIds = tarEntityPropIds;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getSqlScripts() {
		return sqlScripts;
	}

	public void setSqlScripts(String sqlScripts) {
		this.sqlScripts = sqlScripts;
	}

	public String getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}

	public Boolean getPushServer() {
		return pushServer;
	}

	public void setPushServer(Boolean pushServer) {
		this.pushServer = pushServer;
	}

	public String getSysDatasourceName() {
		return sysDatasourceName;
	}

	public void setSysDatasourceName(String sysDatasourceName) {
		this.sysDatasourceName = sysDatasourceName;
	}

	public Long getRegistryId() {
		return registryId;
	}

	public void setRegistryId(Long registryId) {
		this.registryId = registryId;
	}

	public String getSysDatasourceType() {
		return sysDatasourceType;
	}

	public void setSysDatasourceType(String sysDatasourceType) {
		this.sysDatasourceType = sysDatasourceType;
	}

	public String getEntryType() {
		return entryType;
	}

	public void setEntryType(String entryType) {
		this.entryType = entryType;
	}

	public Long getThemeId() {
		return themeId;
	}

	public void setThemeId(Long themeId) {
		this.themeId = themeId;
	}
	
    
}
