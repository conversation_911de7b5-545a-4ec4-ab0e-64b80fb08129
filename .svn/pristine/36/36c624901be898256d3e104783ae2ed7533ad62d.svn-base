<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dqm.mapper.DqmProblemHandlingMapper">

    <resultMap type="DqmProblemHandling" id="DqmProblemHandlingResult">
        <result property="dqmProblemHandlingId"    column="dqm_problem_handling_id"    />
        <result property="dqmValidationProblemId"    column="dqm_validation_problem_id"    />
        <result property="handlingId"    column="handling_id"    />
        <result property="handlingBy"    column="handling_by"    />
        <result property="handlingTime"    column="handling_time"    />
        <result property="handlingLoding"    column="handling_loding"    />
        <result property="handlingMsg"    column="handling_msg"    />
    </resultMap>

    <sql id="selectDqmProblemHandlingVo">
        select t.dqm_problem_handling_id, t.dqm_validation_problem_id,t.handling_id, t.handling_by, t.handling_time, t.handling_loding, t.handling_msg from dqm_problem_handling t
        left join dqm_validation_problem dvp on t.dqm_validation_problem_id=dvp.dqm_validation_problem_id
        left join dqm_validation_rule_cate dvrc on dvp.validation_rule_cate_id=dvrc.validation_rule_cate_id
    </sql>

    <select id="selectDqmProblemHandlingList" parameterType="DqmProblemHandling" resultMap="DqmProblemHandlingResult">
        <include refid="selectDqmProblemHandlingVo"/>
        <where>
          <if test="dqmValidationProblemId !=null"> and t.dqm_validation_problem_id=#{dqmValidationProblemId}</if>
            <if test="handlingBy != null  and handlingBy != ''"> and t.handling_by = #{handlingBy}</if>
            <if test="handlingTime != null "> and t.handling_time = #{handlingTime}</if>
            <if test="handlingLoding != null  and handlingLoding != ''"> and t.handling_loding = #{handlingLoding}</if>
            <if test="handlingMsg != null  and handlingMsg != ''"> and t.handling_msg = #{handlingMsg}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.handling_time desc
    </select>

    <select id="selectDqmProblemHandlingById" parameterType="Long" resultMap="DqmProblemHandlingResult">
        <include refid="selectDqmProblemHandlingVo"/>
        where t.dqm_problem_handling_id = #{dqmProblemHandlingId}
    </select>

    <insert id="insertDqmProblemHandling" parameterType="DqmProblemHandling" useGeneratedKeys="true" keyProperty="dqmProblemHandlingId">
        insert into dqm_problem_handling
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dqmValidationProblemId != null">dqm_validation_problem_id,</if>
            <if test="handlingId != null">handling_id,</if>
            <if test="handlingBy != null and handlingBy != ''">handling_by,</if>
            <if test="handlingTime != null">handling_time,</if>
            <if test="handlingLoding != null">handling_loding,</if>
            <if test="handlingMsg != null">handling_msg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dqmValidationProblemId != null">#{dqmValidationProblemId},</if>

            <if test="handlingId != null and handlingId != ''">#{handlingId},</if>
            <if test="handlingBy != null and handlingBy != ''">#{handlingBy},</if>
            <if test="handlingTime != null">#{handlingTime},</if>
            <if test="handlingLoding != null">#{handlingLoding},</if>
            <if test="handlingMsg != null">#{handlingMsg},</if>
         </trim>
    </insert>

    <update id="updateDqmProblemHandling" parameterType="DqmProblemHandling">
        update dqm_problem_handling
        <trim prefix="SET" suffixOverrides=",">
            <if test="dqmValidationProblemId != null">dqm_validation_problem_id = #{dqmValidationProblemId},</if>
            <if test="handlingId != null and handlingId != ''">handling_id = #{handlingId},</if>
            <if test="handlingBy != null and handlingBy != ''">handling_by = #{handlingBy},</if>
            <if test="handlingTime != null">handling_time = #{handlingTime},</if>
            <if test="handlingLoding != null">handling_loding = #{handlingLoding},</if>
            <if test="handlingMsg != null">handling_msg = #{handlingMsg},</if>
        </trim>
        where dqm_problem_handling_id = #{dqmProblemHandlingId}
    </update>

    <delete id="deleteDqmProblemHandlingById" parameterType="Long">
        delete from dqm_problem_handling where dqm_problem_handling_id = #{dqmProblemHandlingId}
    </delete>

    <delete id="deleteDqmProblemHandlingByIds" parameterType="String">
        delete from dqm_problem_handling where dqm_problem_handling_id in
        <foreach item="dqmProblemHandlingId" collection="array" open="(" separator="," close=")">
            #{dqmProblemHandlingId}
        </foreach>
    </delete>
</mapper>
