package com.dqms.task.domain;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 调度日历明细对象 etl_task_calendar_detail
 *
 * <AUTHOR>
 * @date 2021-04-01
 */
public class EtlTaskCalendarDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日历明细 */
    private Long taskCalendarDetailId;

    /** 日历分类 */
    @Excel(name = "日历分类")
    private Long taskCalendarClassId;

    /** 执行参数 */
    @Excel(name = "执行参数")
    private String loadDate;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    /** 执行批次 */
    private String batchId;
    
    /** 执行参数*/
    @Excel(name = "执行参数")
    private String param;
    
    private EtlTaskCalendarClass taskCalendarClass;

    public void setTaskCalendarDetailId(Long taskCalendarDetailId)
    {
        this.taskCalendarDetailId = taskCalendarDetailId;
    }

    public Long getTaskCalendarDetailId()
    {
        return taskCalendarDetailId;
    }
    public void setTaskCalendarClassId(Long taskCalendarClassId)
    {
        this.taskCalendarClassId = taskCalendarClassId;
    }

    public Long getTaskCalendarClassId()
    {
        return taskCalendarClassId;
    }
    public void setLoadDate(String loadDate)
    {
        this.loadDate = loadDate;
    }

    public String getLoadDate()
    {
        return loadDate;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public EtlTaskCalendarClass getTaskCalendarClass() {
		return taskCalendarClass;
	}

	public void setTaskCalendarClass(EtlTaskCalendarClass taskCalendarClass) {
		this.taskCalendarClass = taskCalendarClass;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public String getParam() {
		return param;
	}

	public void setParam(String param) {
		this.param = param;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskCalendarDetailId", getTaskCalendarDetailId())
            .append("taskCalendarClassId", getTaskCalendarClassId())
            .append("loadDate", getLoadDate())
            .append("status", getStatus())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
