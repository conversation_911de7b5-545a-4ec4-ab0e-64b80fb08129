<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.task.mapper.EtlTaskGroupMapper">
    
    <resultMap type="EtlTaskGroup" id="EtlTaskGroupResult">
        <result property="taskGroupId"    column="task_group_id"    />
        <result property="taskGroupCode"    column="task_group_code"    />
        <result property="taskGroupName"    column="task_group_name"    />
        <result property="threadNum"    column="thread_num"    />
        <result property="axesx"    column="axesx"    />
        <result property="axesy"    column="axesy"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <collection  property="etlTaskGroupRelations"   javaType="java.util.List"        resultMap="EtlTaskGroupRelationResult" />
    </resultMap>

    <resultMap type="EtlTaskGroupRelation" id="EtlTaskGroupRelationResult">
        <result property="taskGroupRelationId"    column="task_group_relation_id"    />
        <result property="srcTaskGroupId"    column="src_task_group_id"    />
        <result property="tarTaskGroupId"    column="tar_task_group_id"    />
    </resultMap>
    
    <sql id="selectEtlTaskGroupVo">
        select t.task_group_id, t.task_group_code, t.task_group_name,t.thread_num, t.axesx, t.axesy, t.remark, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time 
        ,r.task_group_relation_id,r.src_task_group_id,r.tar_task_group_id
        from etl_task_group t left join etl_task_group_relation r on
        t.task_group_id = r.src_task_group_id
    </sql>

    <select id="selectEtlTaskGroupList" parameterType="EtlTaskGroup" resultMap="EtlTaskGroupResult">
        <include refid="selectEtlTaskGroupVo"/>
        <where>  
            <if test="taskGroupCode != null  and taskGroupCode != ''"> and task_group_code = #{taskGroupCode}</if>
            <if test="taskGroupName != null  and taskGroupName != ''"> and task_group_name like concat('%', #{taskGroupName}, '%')</if>
            <if test="createId != null  and createId != ''"> and create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''"> and update_id = #{updateId}</if>
        </where>
    </select>
    
    <select id="selectEtlTaskGroupById" parameterType="Long" resultMap="EtlTaskGroupResult">
        <include refid="selectEtlTaskGroupVo"/>
        where task_group_id = #{taskGroupId}
    </select>
    
    <select id="selectEtlTaskGroupByName" parameterType="String" resultMap="EtlTaskGroupResult">
        <include refid="selectEtlTaskGroupVo"/>
        where task_group_name = #{taskGroupName}
    </select>
        
    <insert id="insertEtlTaskGroup" parameterType="EtlTaskGroup" useGeneratedKeys="true" keyProperty="taskGroupId">
        insert into etl_task_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskGroupCode != null">task_group_code,</if>
            <if test="taskGroupName != null">task_group_name,</if>
            <if test="threadNum != null">thread_num,</if>
            <if test="axesx != null">axesx,</if>
            <if test="axesy != null">axesy,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskGroupCode != null">#{taskGroupCode},</if>
            <if test="taskGroupName != null">#{taskGroupName},</if>
            <if test="threadNum != null">#{threadNum},</if>
            <if test="axesx != null">#{axesx},</if>
            <if test="axesy != null">#{axesy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEtlTaskGroup" parameterType="EtlTaskGroup">
        update etl_task_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskGroupCode != null">task_group_code = #{taskGroupCode},</if>
            <if test="taskGroupName != null">task_group_name = #{taskGroupName},</if>
            <if test="threadNum != null">thread_num = #{threadNum},</if>
            <if test="axesx != null">axesx = #{axesx},</if>
            <if test="axesy != null">axesy = #{axesy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_group_id = #{taskGroupId}
    </update>

    <delete id="deleteEtlTaskGroupById" parameterType="Long">
        delete from etl_task_group where task_group_id = #{taskGroupId}
    </delete>

    <delete id="deleteEtlTaskGroupByIds" parameterType="String">
        delete from etl_task_group where task_group_id in 
        <foreach item="taskGroupId" collection="array" open="(" separator="," close=")">
            #{taskGroupId}
        </foreach>
    </delete>
    
    <select id="selectPosEtlGroupTask" parameterType="String" resultMap="EtlTaskGroupResult">
        select t.task_group_id, t.task_group_code, t.task_group_name,t.thread_num, t.axesx, t.axesy, t.remark, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time 
        from etl_task_group t
        WHERE 
        EXISTS (SELECT * FROM etl_task_group_relation po WHERE t.task_group_id=po.tar_task_group_id AND po.src_task_group_id in
		<foreach item="taskGroupId" collection="array" open="(" separator="," close=")">
            #{taskGroupId}
        </foreach>
		)
    </select>
</mapper>