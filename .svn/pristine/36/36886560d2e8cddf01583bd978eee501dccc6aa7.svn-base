package com.dqms.dsm.controller;

import java.io.IOException;
import java.util.List;

import com.dqms.dsm.domain.vo.DsmDimensionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsm.domain.DsmDimension;
import com.dqms.dsm.service.IDsmDimensionService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 维度字典Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmDimension")
public class DsmDimensionController extends BaseController
{
    @Autowired
    private IDsmDimensionService dsmDimensionService;

    /**
     * 查询维度字典列表
     */
   // @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmDimension dsmDimension)
    {
        startPage();
        List<DsmDimension> list = dsmDimensionService.selectDsmDimensionList(dsmDimension);
        return getDataTable(list);
    }
    @GetMapping("/forDetail")
    public TableDataInfo forDetail(DsmDimension dsmDimension)
    {
        startPage();
        List<DsmDimension> list = dsmDimensionService.forDetail(dsmDimension);
        return getDataTable(list);
    }
    /**
     * 导出维度字典列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:export')")
    @Log(title = "维度字典", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmDimensionVo dsmDimensionVo)
    {
        List<DsmDimensionVo> list = dsmDimensionService.selectDsmDimensionVoList(dsmDimensionVo);
        ExcelUtil<DsmDimensionVo> util = new ExcelUtil<DsmDimensionVo>(DsmDimensionVo.class);
        return util.exportExcel(list, "dsmDimension");
    }

    /**
     * 获取维度字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:query')")
    @GetMapping(value = "/{dimensionId}")
    public AjaxResult getInfo(@PathVariable("dimensionId") Long dimensionId)
    {
        return AjaxResult.success(dsmDimensionService.selectDsmDimensionById(dimensionId));
    }

    /**
     * 新增维度字典
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:add')")
    @Log(title = "维度字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmDimension dsmDimension)
    {
        return toAjax(dsmDimensionService.insertDsmDimension(dsmDimension));
    }

    /**
     * 修改维度字典
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:edit')")
    @Log(title = "维度字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmDimension dsmDimension)
    {
        return toAjax(dsmDimensionService.updateDsmDimension(dsmDimension));
    }
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:updateStatus')")
    @Log(title = "维度字典", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateDsmDimensionStatus(@RequestBody DsmDimension dsmDimension)
    {
        return toAjax(dsmDimensionService.updateDsmDimensionStatus(dsmDimension));
    }


    /**
     * 删除维度字典
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:remove')")
    @Log(title = "维度字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dimensionIds}")
    public AjaxResult remove(@PathVariable Long[] dimensionIds)
    {
        return toAjax(dsmDimensionService.deleteDsmDimensionByIds(dimensionIds));
    }
    
    /**
     * 同步字典明细
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:edit')")
    @Log(title = "同步字典明细", businessType = BusinessType.UPDATE)
    @PutMapping("/loadDsmDimension/{dimensionId}")
    public AjaxResult loadDsmDimension(@PathVariable Long dimensionId)
    {
        return toAjax(dsmDimensionService.loadDsmDimension(dimensionId));
    }

    /**
     * 批量同步字典明细
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:edit')")
    @Log(title = "批量同步字典明细", businessType = BusinessType.UPDATE)
    @PutMapping("/batchLoadDsmDimension/{dimensionIds}")
    public AjaxResult batchLoadDsmDimension(@PathVariable Long[] dimensionIds)
    {
        return toAjax(dsmDimensionService.batchLoadDsmDimension(dimensionIds));
    }

    /**
     * 审批字典明细
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:edit')")
    @Log(title = "审批字典明细", businessType = BusinessType.UPDATE)
    @PutMapping("/applyDsmDimension/{dimensionId}")
    public AjaxResult applyDsmDimension(@PathVariable Long dimensionId)
    {
        return toAjax(dsmDimensionService.applyDsmDimension(dimensionId));
    }
    
    /**
     * 忽略字典明细
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmDimension:edit')")
    @Log(title = "忽略字典明细", businessType = BusinessType.UPDATE)
    @PutMapping("/rejectDsmDimension/{dimensionId}")
    public AjaxResult rejectDsmDimension(@PathVariable Long dimensionId)
    {
        return toAjax(dsmDimensionService.rejectDsmDimension(dimensionId));
    }

    /**
     * 导入维度字典
     * @param file
     * @param updateSupport
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('dssm:dsmDimension:import')")
    @Log(title = "维度字典导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        //获取模版实体
        ExcelUtil<DsmDimensionVo> util = new ExcelUtil<>(DsmDimensionVo.class);
        //获取excel中数据植入实体集合
        List<DsmDimensionVo> dsmDimensionList = util.importExcel(file.getInputStream());
        //做数据校验
        String message = dsmDimensionService.importDsmDimension(dsmDimensionList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导出模板
     */
    @GetMapping("/exportTemplate")
    public AjaxResult exportTemplate()
    {
        ExcelUtil<DsmDimensionVo> util = new ExcelUtil<>(DsmDimensionVo.class);
        return util.importTemplateExcel("维度字典");
    }

}
