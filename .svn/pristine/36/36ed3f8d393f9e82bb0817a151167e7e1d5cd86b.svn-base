package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.dsm.domain.DsmModelEntityClass;
import com.dqms.dsm.domain.DsmModelEntityClassTreeSelect;
import com.dqms.dsm.mapper.DsmModelEntityClassMapper;
import com.dqms.dsm.service.IDsmModelEntityClassService;
import com.dqms.framework.web.service.TokenService;

/**
 * 模型主题Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@Service
public class DsmModelEntityClassServiceImpl implements IDsmModelEntityClassService
{
    @Autowired
    private DsmModelEntityClassMapper dsmModelEntityClassMapper;
    @Autowired
    private TokenService tokenService;
    /**
     * 查询模型主题
     *
     * @param modelEntityClassId 模型主题ID
     * @return 模型主题
     */
    @Override
    public DsmModelEntityClass selectDsmModelEntityClassById(Long modelEntityClassId)
    {
        return dsmModelEntityClassMapper.selectDsmModelEntityClassById(modelEntityClassId);
    }

    /**
     * 查询模型主题列表
     *
     * @param dsmModelEntityClass 模型主题
     * @return 模型主题
     */
    @Override
    public List<DsmModelEntityClass> selectDsmModelEntityClassList(DsmModelEntityClass dsmModelEntityClass)
    {
        return dsmModelEntityClassMapper.selectDsmModelEntityClassList(dsmModelEntityClass);
    }

    /**
     * 新增模型主题
     *
     * @param dsmModelEntityClass 模型主题
     * @return 结果
     */
    @Override
    public int insertDsmModelEntityClass(DsmModelEntityClass dsmModelEntityClass)
    {
    	DsmModelEntityClass info = dsmModelEntityClassMapper.selectDsmModelEntityClassById(dsmModelEntityClass.getParentId());
    	if(info!=null) {
    		dsmModelEntityClass.setAncestors(info.getAncestors() + "," + dsmModelEntityClass.getParentId());
    		dsmModelEntityClass.setClassNameFull(info.getClassNameFull() + "/" + dsmModelEntityClass.getClassName());
    	}else {
    		dsmModelEntityClass.setAncestors("0");
    		dsmModelEntityClass.setClassNameFull(dsmModelEntityClass.getClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmModelEntityClass.setCreateTime(DateUtils.getNowDate());
    	dsmModelEntityClass.setCreateId(loginUser.getUser().getUserId());
    	dsmModelEntityClass.setCreateBy(loginUser.getUser().getNickName());
    	dsmModelEntityClass.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntityClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmModelEntityClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmModelEntityClassMapper.insertDsmModelEntityClass(dsmModelEntityClass);
    }

    /**
     * 修改模型主题
     *
     * @param dsmModelEntityClass 模型主题
     * @return 结果
     */
    @Override
    public int updateDsmModelEntityClass(DsmModelEntityClass dsmModelEntityClass)
    {
    	DsmModelEntityClass newC = dsmModelEntityClassMapper.selectDsmModelEntityClassById(dsmModelEntityClass.getParentId());
    	DsmModelEntityClass oldC = dsmModelEntityClassMapper.selectDsmModelEntityClassById(dsmModelEntityClass.getModelEntityClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String newAncestors = newC.getAncestors() + "," + dsmModelEntityClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = newC.getClassNameFull() + "/" + dsmModelEntityClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             dsmModelEntityClass.setAncestors(newAncestors);
             dsmModelEntityClass.setClassNameFull(newClassNameFull);
             updateClassChildren(dsmModelEntityClass.getModelEntityClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dsmModelEntityClass.getModelEntityClassId(), newClassNameFull, oldClassNameFull);
         }else if(newC==null){
        	 dsmModelEntityClass.setAncestors("0");
        	 dsmModelEntityClass.setClassName(dsmModelEntityClass.getClassName());
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmModelEntityClass.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntityClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmModelEntityClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmModelEntityClassMapper.updateDsmModelEntityClass(dsmModelEntityClass);
    }

    /**
     * 批量删除模型主题
     *
     * @param modelEntityClassIds 需要删除的模型主题ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityClassByIds(Long[] modelEntityClassIds)
    {
        return dsmModelEntityClassMapper.deleteDsmModelEntityClassByIds(modelEntityClassIds);
    }

    /**
     * 删除模型主题信息
     *
     * @param modelEntityClassId 模型主题ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityClassById(Long modelEntityClassId)
    {
        return dsmModelEntityClassMapper.deleteDsmModelEntityClassById(modelEntityClassId);
    }
    
    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateClassChildren(Long taskClassId, String newAncestors, String oldAncestors)
    {
        List<DsmModelEntityClass> children = dsmModelEntityClassMapper.selectChildrenClassById(taskClassId);
        for (DsmModelEntityClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	dsmModelEntityClassMapper.updateClassChildren(children);
        }
    }
    @Transactional
    public void updateClassNameFullChildren(Long taskClassId, String newClassNameFull, String oldClassNameFull)
    {
        List<DsmModelEntityClass> children = dsmModelEntityClassMapper.selectChildrenClassById(taskClassId);
        for (DsmModelEntityClass child : children)
        {
            child.setClassNameFull(child.getClassNameFull().replace(oldClassNameFull, newClassNameFull));
        }
        if (children.size() > 0)
        {
        	dsmModelEntityClassMapper.updateClassNameFullChildren(children);
        }
    }
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<DsmModelEntityClassTreeSelect> buildDsmModelEntityClassTreeSelect(List<DsmModelEntityClass> dsmModelEntityClasss)
    {
        List<DsmModelEntityClass> dsmModelEntityClasssTrees = buildDsmModelEntityClassTree(dsmModelEntityClasss);
        return dsmModelEntityClasssTrees.stream().map(DsmModelEntityClassTreeSelect::new).collect(Collectors.toList());
    }
    
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<DsmModelEntityClass> buildDsmModelEntityClassTree(List<DsmModelEntityClass> dsmStandardClasss)
    {
        List<DsmModelEntityClass> returnList = new ArrayList<DsmModelEntityClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (DsmModelEntityClass dsmModelEntityClass : dsmStandardClasss)
        {
            tempList.add(dsmModelEntityClass.getModelEntityClassId());
        }
        for (Iterator<DsmModelEntityClass> iterator = dsmStandardClasss.iterator(); iterator.hasNext();)
        {
        	DsmModelEntityClass dsmModelEntityClass = (DsmModelEntityClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dsmModelEntityClass.getParentId()))
            {
                recursionFn(dsmStandardClasss, dsmModelEntityClass);
                returnList.add(dsmModelEntityClass);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = dsmStandardClasss;
        }
        return returnList;
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<DsmModelEntityClass> list, DsmModelEntityClass t)
    {
        // 得到子节点列表
        List<DsmModelEntityClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DsmModelEntityClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<DsmModelEntityClass> getChildList(List<DsmModelEntityClass> list, DsmModelEntityClass t)
    {
        List<DsmModelEntityClass> tlist = new ArrayList<DsmModelEntityClass>();
        Iterator<DsmModelEntityClass> it = list.iterator();
        while (it.hasNext())
        {
        	DsmModelEntityClass n = (DsmModelEntityClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getModelEntityClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DsmModelEntityClass> list, DsmModelEntityClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
