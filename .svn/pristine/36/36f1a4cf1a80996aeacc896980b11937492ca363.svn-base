<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.basic.mapper.SysDatasourceTypeMapper">

    <resultMap type="SysDatasourceType" id="SysDatasourceTypeResult">
        <result property="datasourceTypeId"    column="datasource_type_id"    />
        <result property="datasourceTypeCode"    column="datasource_type_code"    />
        <result property="datasourceTypeName"    column="datasource_type_name"    />
        <result property="datasourceTypeGroup"    column="datasource_type_group"    />
        <result property="datasourceTypeDrive"    column="datasource_type_drive"    />
    </resultMap>

    <sql id="selectSysDatasourceTypeVo">
        select datasource_type_id, datasource_type_code, datasource_type_name, datasource_type_group, datasource_type_drive from sys_datasource_type
    </sql>

    <select id="selectSysDatasourceTypeList" parameterType="SysDatasourceType" resultMap="SysDatasourceTypeResult">
        <include refid="selectSysDatasourceTypeVo"/>
        <where>
            <if test="datasourceTypeCode != null  and datasourceTypeCode != ''"> and datasource_type_code = #{datasourceTypeCode}</if>
            <if test="datasourceTypeName != null  and datasourceTypeName != ''"> and datasource_type_name like concat('%', #{datasourceTypeName}, '%')</if>
            <if test="datasourceTypeGroup != null  and datasourceTypeGroup != ''"> and datasource_type_group = #{datasourceTypeGroup}</if>
            <if test="datasourceTypeDrive != null  and datasourceTypeDrive != ''"> and datasource_type_drive = #{datasourceTypeDrive}</if>
        </where>
    </select>

    <select id="selectSysDatasourceTypeById" parameterType="Long" resultMap="SysDatasourceTypeResult">
        <include refid="selectSysDatasourceTypeVo"/>
        where datasource_type_id = #{datasourceTypeId}
    </select>
    
    <select id="selectSysDatasourceTypeByName" parameterType="String" resultMap="SysDatasourceTypeResult">
        <include refid="selectSysDatasourceTypeVo"/>
        where datasource_type_name = #{datasourceTypeName}
    </select>

    <select id="selectSysDatasourceTypeAll" resultMap="SysDatasourceTypeResult">
        <include refid="selectSysDatasourceTypeVo"/>
    </select>


    <insert id="insertSysDatasourceType" parameterType="SysDatasourceType" useGeneratedKeys="true" keyProperty="datasourceTypeId">
        insert into sys_datasource_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="datasourceTypeCode != null">datasource_type_code,</if>
            <if test="datasourceTypeName != null">datasource_type_name,</if>
            <if test="datasourceTypeGroup != null">datasource_type_group,</if>
            <if test="datasourceTypeDrive != null">datasource_type_drive,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="datasourceTypeCode != null">#{datasourceTypeCode},</if>
            <if test="datasourceTypeName != null">#{datasourceTypeName},</if>
            <if test="datasourceTypeGroup != null">#{datasourceTypeGroup},</if>
            <if test="datasourceTypeDrive != null">#{datasourceTypeDrive},</if>
         </trim>
    </insert>

    <update id="updateSysDatasourceType" parameterType="SysDatasourceType">
        update sys_datasource_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="datasourceTypeCode != null">datasource_type_code = #{datasourceTypeCode},</if>
            <if test="datasourceTypeName != null">datasource_type_name = #{datasourceTypeName},</if>
            <if test="datasourceTypeGroup != null">datasource_type_group = #{datasourceTypeGroup},</if>
            <if test="datasourceTypeDrive != null">datasource_type_drive = #{datasourceTypeDrive},</if>
        </trim>
        where datasource_type_id = #{datasourceTypeId}
    </update>

    <delete id="deleteSysDatasourceTypeById" parameterType="Long">
        delete from sys_datasource_type where datasource_type_id = #{datasourceTypeId}
    </delete>

    <delete id="deleteSysDatasourceTypeByIds" parameterType="String">
        delete from sys_datasource_type where datasource_type_id in
        <foreach item="datasourceTypeId" collection="array" open="(" separator="," close=")">
            #{datasourceTypeId}
        </foreach>
    </delete>
</mapper>
