package com.dqms.dsm.domain;
import java.util.List;
import java.util.stream.Collectors;

import com.dqms.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 主数据分类对象 dsm_master_data_class
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
public class DsmMasterDataClassTreeSelect extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;
    
    /** 节点名称 */
    private String type;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DsmMasterDataClassTreeSelect> children;

    public DsmMasterDataClassTreeSelect()
    {

    }

    public DsmMasterDataClassTreeSelect(DsmMasterDataClass dsmMasterDataClass)
    {
        this.id = dsmMasterDataClass.getMasterDataClassId();
        this.label = dsmMasterDataClass.getClassName();
        this.type = dsmMasterDataClass.getType();
        this.children = dsmMasterDataClass.getChildren().stream().map(DsmMasterDataClassTreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<DsmMasterDataClassTreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<DsmMasterDataClassTreeSelect> children)
    {
        this.children = children;
    }

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
    
    
}
