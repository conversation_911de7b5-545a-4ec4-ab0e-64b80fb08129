package com.dqms.api.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.alibaba.druid.util.JdbcConstants;
import com.dqms.api.domain.ApiDefine;
import com.dqms.api.domain.ApiDefineColumn;
import com.dqms.api.domain.ApiDefineParam;
import com.dqms.api.enums.ApiConstants;
import com.dqms.api.mapper.ApiDefineColumnMapper;
import com.dqms.api.mapper.ApiDefineMapper;
import com.dqms.api.service.IApiDefineColumnService;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.common.constant.MdmConstants;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.util.MetaDataContext;
import com.dqms.utils.sql.GbHiveSchemaStatVisitor;
import com.dqms.utils.sql.GbOracleSchemaStatVisitor;
import com.dqms.utils.sql.GbSQLUtils;
import com.dqms.utils.sql.GbSchemaStatVisitor;
import com.dqms.utils.sql.GbSqlSchemaStatVisitor;
import com.dqms.utils.sql.GbTableStat;
import com.dqms.utils.sql.GbTableStat.Column;
import com.dqms.utils.sql.GbTableStat.Name;

/**
 * 接口输出Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@Service
public class ApiDefineColumnServiceImpl implements IApiDefineColumnService
{
    @Autowired
    private ApiDefineColumnMapper apiDefineColumnMapper;
    
    @Autowired
    private ApiDefineMapper apiDefineMapper;

    @Autowired
    private MetaDataContext metaDataContext;

    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;
    /**
     * 查询接口输出
     *
     * @param defineColumnId 接口输出ID
     * @return 接口输出
     */
    @Override
    public ApiDefineColumn selectApiDefineColumnById(Long defineColumnId)
    {
        return apiDefineColumnMapper.selectApiDefineColumnById(defineColumnId);
    }

    /**
     * 查询接口输出列表
     *
     * @param apiDefineColumn 接口输出
     * @return 接口输出
     */
    @Override
    public List<ApiDefineColumn> selectApiDefineColumnList(ApiDefineColumn apiDefineColumn)
    {
    	List<ApiDefineColumn> list = apiDefineColumnMapper.selectApiDefineColumnList(apiDefineColumn);
    	if(list==null||list.size()==0) {
    		ApiDefine apiDefine = apiDefineMapper.selectApiDefineById(apiDefineColumn.getDefineId());
    		if(!apiDefine.getDefineType().equals(ApiConstants.TASK_DATA)) {
    			return list;
    		}
    		if(apiDefine.getDefineSql().indexOf("${WHERE}")>=0) {
    			apiDefine.setDefineSql(apiDefine.getDefineSql().replace("${WHERE}", ""));
    		}
    		if(apiDefine!=null&&apiDefine.getDefineSql()!=null&&apiDefine.getDefineSql().toLowerCase().startsWith("select")) {
    			list = getTables(apiDefine);
    		}else if(apiDefine!=null&&apiDefine.getDefineSql()!=null&&apiDefine.getDefineSql().indexOf(".")>0){
    			MdmRegistry mdmRegistry=new MdmRegistry();
    			mdmRegistry.setDatasourceId(apiDefine.getDatasourceId());
                String defineSql = apiDefine.getDefineSql();
                //defineSql presto将以cat.schem.table的形式出现
                int i = defineSql.lastIndexOf(".");
                String regDir = defineSql.substring(0,i);
                if(regDir.indexOf(".")!=-1) {
                	String[] regDirs= regDir.split("\\.");
                	regDir=regDirs[regDirs.length-1];
                }
                String regName = defineSql.substring(i+1);
    			mdmRegistry.setRegDir(regDir);
    			mdmRegistry.setRegName(regName);
    			List<Map<String, Object>> columnsList = metaDataContext.getColumns(mdmRegistry);
    			list = converMapToDataEntity(columnsList);
    		}
    	}
        return list;
    }

    private List<ApiDefineColumn> converMapToDataEntity( List<Map<String, Object>> columnsList){
        List<ApiDefineColumn> newMdmDataEntityPropList = new ArrayList<ApiDefineColumn>();
        for (int i = 0; i < columnsList.size(); i++) {
        	ApiDefineColumn column = new ApiDefineColumn();
            Map<String, Object> propMap = columnsList.get(i);
            String propName = propMap.get("name").toString();
            column.setDefineColumnName(propName);
            column.setRemark(propMap.get("remark").toString());
            String dataType = propMap.get("dataType").toString().toUpperCase();
            if(dataType.toUpperCase().indexOf("INT")>-1) {
            	column.setDefineColumnType(ApiConstants.INT);
            }else if(dataType.toUpperCase().indexOf("FLOAT")>-1) {
            	column.setDefineColumnType(ApiConstants.DOUBLE);
            }else if(dataType.toUpperCase().indexOf("DOUBLE")>-1||dataType.toUpperCase().indexOf("DECIMAL")>-1) {
            	column.setDefineColumnType(ApiConstants.DOUBLE);
            }else if(dataType.toUpperCase().indexOf("LONG")>-1) {
            	column.setDefineColumnType(ApiConstants.LONG);
            }else if(dataType.toUpperCase().indexOf("BOOLEAN")>-1) {
            	column.setDefineColumnType(ApiConstants.BOOLEAN);
            }else {
            	column.setDefineColumnType(ApiConstants.STRING);
            }
            column.setIsMust(ApiConstants.NO);
            newMdmDataEntityPropList.add(column);
        }
        return newMdmDataEntityPropList;
    }
    
    private List<ApiDefineColumn> getTables( ApiDefine apiDefine){
    	List<ApiDefineColumn> newMdmDataEntityPropList = new ArrayList<ApiDefineColumn>();
		SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(apiDefine.getDatasourceId());
        if (sysDatasource == null) {
            throw new RuntimeException("数据源不存在！");
        }
        List<SQLStatement> sqlStatements = metaDataContext.parseStatements(sysDatasource, apiDefine.getDefineSql());
        for (SQLStatement sqlStatement : sqlStatements) {
        	GbSchemaStatVisitor schemaStatVisitor = null;
    		if(sysDatasource.getSysDatasourceType().getDatasourceTypeCode().equals(MdmConstants.HIVE)) {
    			schemaStatVisitor = (GbHiveSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(JdbcConstants.HIVE);
    		}else if(sysDatasource.getSysDatasourceType().getDatasourceTypeCode().equals(MdmConstants.ORACLE)) {
    			schemaStatVisitor = (GbOracleSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(JdbcConstants.ORACLE);
    		}else {
    			schemaStatVisitor = (GbSqlSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(JdbcConstants.MYSQL);
    		}
            sqlStatement.accept(schemaStatVisitor);
            Map<Name, GbTableStat> tables = schemaStatVisitor.getTables();
            Collection<Column> columns = schemaStatVisitor.getColumns();
            if (Objects.nonNull(tables)) {
                tables.forEach(((name, tableStat) -> {
                    if (tableStat.getSelectCount() > 0) {
                        columns.stream().filter(column -> Objects.equals(column.getTable().toLowerCase(), name.getName().toLowerCase())).forEach(column -> {
                        	if(!column.getName().equals("*")) {
                        		ApiDefineColumn col = new ApiDefineColumn();
                        		col.setDefineColumnName(column.getTable()+"."+column.getName());
                        		col.setDefineColumnType(ApiConstants.STRING);
                        		col.setIsMust(ApiConstants.NO);
                                newMdmDataEntityPropList.add(col);
                        	}
                        });
                    }
                }));
            }
        }
       
        return newMdmDataEntityPropList;
    }
    /**
     * 新增接口输出
     *
     * @param apiDefineColumn 接口输出
     * @return 结果
     */
    @Override
    public int insertApiDefineColumn(ApiDefineColumn apiDefineColumn)
    {
        return apiDefineColumnMapper.insertApiDefineColumn(apiDefineColumn);
    }

    /**
     * 修改接口输出
     *
     * @param apiDefineColumn 接口输出
     * @return 结果
     */
    @Override
    public int updateApiDefineColumn(ApiDefineColumn apiDefineColumn)
    {
        return apiDefineColumnMapper.updateApiDefineColumn(apiDefineColumn);
    }

    /**
     * 批量删除接口输出
     *
     * @param defineColumnIds 需要删除的接口输出ID
     * @return 结果
     */
    @Override
    public int deleteApiDefineColumnByIds(Long[] defineColumnIds)
    {
        return apiDefineColumnMapper.deleteApiDefineColumnByIds(defineColumnIds);
    }

    /**
     * 删除接口输出信息
     *
     * @param defineColumnId 接口输出ID
     * @return 结果
     */
    @Override
    public int deleteApiDefineColumnById(Long defineColumnId)
    {
        return apiDefineColumnMapper.deleteApiDefineColumnById(defineColumnId);
    }
}
