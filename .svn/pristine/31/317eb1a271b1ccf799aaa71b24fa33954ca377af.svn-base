package com.dqms.dam.service.impl;

import java.util.List;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dam.mapper.DamMindMapper;
import com.dqms.dam.domain.DamMind;
import com.dqms.dam.service.IDamMindService;

/**
 * 我的脑图Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-01
 */
@Service
public class DamMindServiceImpl implements IDamMindService
{
    @Autowired
    private DamMindMapper damMindMapper;

    /**
     * 查询我的脑图
     *
     * @param mindId 我的脑图ID
     * @return 我的脑图
     */
    @Override
    public DamMind selectDamMindById(Long mindId)
    {
        return damMindMapper.selectDamMindById(mindId);
    }

    /**
     * 查询我的脑图列表
     *
     * @param damMind 我的脑图
     * @return 我的脑图
     */
    @Override
    public List<DamMind> selectDamMindList(DamMind damMind)
    {
        return damMindMapper.selectDamMindList(damMind);
    }

    /**
     * 新增我的脑图
     *
     * @param damMind 我的脑图
     * @return 结果
     */
    @Override
    public int insertDamMind(DamMind damMind)
    {
    	damMind.setCreateTime(DateUtils.getNowDate());
    	damMind.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	damMind.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	damMind.setUpdateTime(DateUtils.getNowDate());
    	damMind.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	damMind.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        return damMindMapper.insertDamMind(damMind);
    }

    /**
     * 修改我的脑图
     *
     * @param damMind 我的脑图
     * @return 结果
     */
    @Override
    public int updateDamMind(DamMind damMind)
    {
    	damMind.setUpdateTime(DateUtils.getNowDate());
    	damMind.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	damMind.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        return damMindMapper.updateDamMind(damMind);
    }

    /**
     * 批量删除我的脑图
     *
     * @param mindIds 需要删除的我的脑图ID
     * @return 结果
     */
    @Override
    public int deleteDamMindByIds(Long[] mindIds)
    {
        return damMindMapper.deleteDamMindByIds(mindIds);
    }

    /**
     * 删除我的脑图信息
     *
     * @param mindId 我的脑图ID
     * @return 结果
     */
    @Override
    public int deleteDamMindById(Long mindId)
    {
        return damMindMapper.deleteDamMindById(mindId);
    }
}
