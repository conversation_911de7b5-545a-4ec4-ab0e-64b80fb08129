package com.dqms.dsc.service;

import java.util.List;
import com.dqms.dsc.domain.DscEntityPropDept;

/**
 * 字段应用部门Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscEntityPropDeptService 
{
    /**
     * 查询字段应用部门
     * 
     * @param entityPropId 字段应用部门ID
     * @return 字段应用部门
     */
    public DscEntityPropDept selectDscEntityPropDeptById(Long entityPropId);

    /**
     * 查询字段应用部门列表
     * 
     * @param dscEntityPropDept 字段应用部门
     * @return 字段应用部门集合
     */
    public List<DscEntityPropDept> selectDscEntityPropDeptList(DscEntityPropDept dscEntityPropDept);

    /**
     * 新增字段应用部门
     * 
     * @param dscEntityPropDept 字段应用部门
     * @return 结果
     */
    public int insertDscEntityPropDept(DscEntityPropDept dscEntityPropDept);

    /**
     * 修改字段应用部门
     * 
     * @param dscEntityPropDept 字段应用部门
     * @return 结果
     */
    public int updateDscEntityPropDept(DscEntityPropDept dscEntityPropDept);

    /**
     * 批量删除字段应用部门
     * 
     * @param entityPropIds 需要删除的字段应用部门ID
     * @return 结果
     */
    public int deleteDscEntityPropDeptByIds(Long[] entityPropIds);

    /**
     * 删除字段应用部门信息
     * 
     * @param entityPropId 字段应用部门ID
     * @return 结果
     */
    public int deleteDscEntityPropDeptById(Long entityPropId);
}
