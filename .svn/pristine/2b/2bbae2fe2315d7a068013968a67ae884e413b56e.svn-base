package com.dqms.dsc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsc.mapper.DscSensitiveDataClassificationMapper;
import com.dqms.dsc.domain.DscSensitiveDataClassification;
import com.dqms.dsc.service.IDscSensitiveDataClassificationService;

/**
 * 敏感数据等级划分标准Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-30
 */
@Service
public class DscSensitiveDataClassificationServiceImpl implements IDscSensitiveDataClassificationService
{
    @Autowired
    private DscSensitiveDataClassificationMapper dscSensitiveDataClassificationMapper;

    /**
     * 查询敏感数据等级划分标准
     *
     * @param standardId 敏感数据等级划分标准ID
     * @return 敏感数据等级划分标准
     */
    @Override
    public DscSensitiveDataClassification selectDscSensitiveDataClassificationById(Long standardId)
    {
        return dscSensitiveDataClassificationMapper.selectDscSensitiveDataClassificationById(standardId);
    }

    /**
     * 查询敏感数据等级划分标准列表
     *
     * @param dscSensitiveDataClassification 敏感数据等级划分标准
     * @return 敏感数据等级划分标准
     */
    @Override
    public List<DscSensitiveDataClassification> selectDscSensitiveDataClassificationList(DscSensitiveDataClassification dscSensitiveDataClassification)
    {
        return dscSensitiveDataClassificationMapper.selectDscSensitiveDataClassificationList(dscSensitiveDataClassification);
    }

    /**
     * 新增敏感数据等级划分标准
     *
     * @param dscSensitiveDataClassification 敏感数据等级划分标准
     * @return 结果
     */
    @Override
    public int insertDscSensitiveDataClassification(DscSensitiveDataClassification dscSensitiveDataClassification)
    {
        return dscSensitiveDataClassificationMapper.insertDscSensitiveDataClassification(dscSensitiveDataClassification);
    }

    /**
     * 修改敏感数据等级划分标准
     *
     * @param dscSensitiveDataClassification 敏感数据等级划分标准
     * @return 结果
     */
    @Override
    public int updateDscSensitiveDataClassification(DscSensitiveDataClassification dscSensitiveDataClassification)
    {
        return dscSensitiveDataClassificationMapper.updateDscSensitiveDataClassification(dscSensitiveDataClassification);
    }

    /**
     * 批量删除敏感数据等级划分标准
     *
     * @param standardIds 需要删除的敏感数据等级划分标准ID
     * @return 结果
     */
    @Override
    public int deleteDscSensitiveDataClassificationByIds(Long[] standardIds)
    {
        return dscSensitiveDataClassificationMapper.deleteDscSensitiveDataClassificationByIds(standardIds);
    }

    /**
     * 删除敏感数据等级划分标准信息
     *
     * @param standardId 敏感数据等级划分标准ID
     * @return 结果
     */
    @Override
    public int deleteDscSensitiveDataClassificationById(Long standardId)
    {
        return dscSensitiveDataClassificationMapper.deleteDscSensitiveDataClassificationById(standardId);
    }
}
