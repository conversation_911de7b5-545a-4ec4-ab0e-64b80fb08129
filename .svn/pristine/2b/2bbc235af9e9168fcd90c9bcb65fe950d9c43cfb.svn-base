package com.dqms.dic.service;

import java.util.List;
import java.util.Map;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmRegistry;

/**
 * 数据交换配置Service接口
 *
 * <AUTHOR>
 * @date 2021-11-25
 */
public interface IDicDataExchangeService
{
    /**
     * 查询数据交换配置
     *
     * @param exchangeId 数据交换配置ID
     * @return 数据交换配置
     */
    public DicDataExchange selectDicDataExchangeById(Long exchangeId);

    /**
     * 查询数据交换配置列表
     *
     * @param dicDataExchange 数据交换配置
     * @return 数据交换配置集合
     */
    public List<DicDataExchange> selectDicDataExchangeList(DicDataExchange dicDataExchange);

    /**
     * 新增数据交换配置
     *
     * @param dicDataExchange 数据交换配置
     * @return 结果
     */
    public int insertDicDataExchange(DicDataExchange dicDataExchange);

    public int insertDicDataExchangeByExcle(DicDataExchange dicDataExchange);
    /**
     * 修改数据交换配置
     *
     * @param dicDataExchange 数据交换配置
     * @return 结果
     */
    public int updateDicDataExchange(DicDataExchange dicDataExchange);

    public int updateDicDataExchangeByExcle(DicDataExchange dicDataExchange);

    /**
     * 批量删除数据交换配置
     *
     * @param exchangeIds 需要删除的数据交换配置ID
     * @return 结果
     */
    public int deleteDicDataExchangeByIds(Long[] exchangeIds);

    /**
     * 删除数据交换配置信息
     *
     * @param exchangeId 数据交换配置ID
     * @return 结果
     */
    public int deleteDicDataExchangeById(Long exchangeId);

    List<MdmDataEntityProp> findPropByTableName(DicDataExchange dicDataExchange, SysDatasource tarSysDatasource);
    Map findPropInfos(DicDataExchange dicDataExchange);
    String sqlGeneration(DicDataExchange dicDataExchange);

    String jsonGeneration(DicDataExchange dicDataExchange);

    void syncJson(DicDataExchange dicDataExchange);

    public String importDicDataExchange(List<DicDataExchange> dicDataExchangeList, Boolean isUpdateSupport);

    public String checkDicDataExchangeNameUnique(DicDataExchange dicDataExchange);

    String exportSql(List<DicDataExchange> dicDataExchangeList);
    
    public int syncMdm(Long[] exchangeIds);
}
