package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 模型字段对象 dsm_model_entity_prop_temp
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
public class DsmModelEntityPropTemp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 字段ID */
    private Long modelEntityPropTempId;

    /** 实体ID */
    @Excel(name = "实体ID")
    private Long modelEntityId;

    /** 属性名称 */
    @Excel(name = "属性名称")
    private String propName;

    /** 属性注释 */
    @Excel(name = "属性注释")
    private String propComment;

    /** 是否主键 */
    @Excel(name = "是否主键")
    private String isPriKey;

    /** 可否为空 */
    @Excel(name = "可否为空")
    private String nullable;

    /** 列大小 */
    @Excel(name = "列大小")
    private Long columnSize;

    /** 小数位数 */
    @Excel(name = "小数位数")
    private Long decimalDigits;

    /** 数据类型 */
    @Excel(name = "数据类型")
    private String dataType;

    /** 默认值 */
    @Excel(name = "默认值")
    private String defaultValue;

    /** 排序ID */
    @Excel(name = "排序ID")
    private Long orderId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createId;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long updateId;

    /** 指标维度 */
    @Excel(name = "指标维度")
    private Long relId;
    
    private Long modelEntityPropId;

    public void setModelEntityPropTempId(Long modelEntityPropTempId)
    {
        this.modelEntityPropTempId = modelEntityPropTempId;
    }

    public Long getModelEntityPropTempId()
    {
        return modelEntityPropTempId;
    }

    public void setPropName(String propName)
    {
        this.propName = propName;
    }

    public String getPropName()
    {
        return propName;
    }
    public void setPropComment(String propComment)
    {
        this.propComment = propComment;
    }

    public String getPropComment()
    {
        return propComment;
    }
    public void setIsPriKey(String isPriKey)
    {
        this.isPriKey = isPriKey;
    }

    public String getIsPriKey()
    {
        return isPriKey;
    }
    public void setNullable(String nullable)
    {
        this.nullable = nullable;
    }

    public String getNullable()
    {
        return nullable;
    }
    public void setColumnSize(Long columnSize)
    {
        this.columnSize = columnSize;
    }

    public Long getColumnSize()
    {
        return columnSize;
    }
    public void setDecimalDigits(Long decimalDigits)
    {
        this.decimalDigits = decimalDigits;
    }

    public Long getDecimalDigits()
    {
        return decimalDigits;
    }
    public void setDataType(String dataType)
    {
        this.dataType = dataType;
    }

    public String getDataType()
    {
        return dataType;
    }
    public void setDefaultValue(String defaultValue)
    {
        this.defaultValue = defaultValue;
    }

    public String getDefaultValue()
    {
        return defaultValue;
    }
    public void setOrderId(Long orderId)
    {
        this.orderId = orderId;
    }

    public Long getOrderId()
    {
        return orderId;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setRelId(Long relId)
    {
        this.relId = relId;
    }

    public Long getRelId()
    {
        return relId;
    }

    public Long getModelEntityPropId() {
		return modelEntityPropId;
	}

	public void setModelEntityPropId(Long modelEntityPropId) {
		this.modelEntityPropId = modelEntityPropId;
	}

	public Long getModelEntityId() {
		return modelEntityId;
	}

	public void setModelEntityId(Long modelEntityId) {
		this.modelEntityId = modelEntityId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("modelEntityPropTempId", getModelEntityPropTempId())
            .append("propName", getPropName())
            .append("propComment", getPropComment())
            .append("isPriKey", getIsPriKey())
            .append("nullable", getNullable())
            .append("columnSize", getColumnSize())
            .append("decimalDigits", getDecimalDigits())
            .append("dataType", getDataType())
            .append("defaultValue", getDefaultValue())
            .append("orderId", getOrderId())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("relId", getRelId())
            .toString();
    }
}
