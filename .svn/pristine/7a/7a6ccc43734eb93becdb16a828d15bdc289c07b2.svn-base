package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmDiscern;

/**
 * 标准落标Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-31
 */
public interface DsmDiscernMapper 
{
    /**
     * 查询标准落标
     * 
     * @param discernId 标准落标ID
     * @return 标准落标
     */
    public DsmDiscern selectDsmDiscernById(Long discernId);

    /**
     * 查询标准落标列表
     * 
     * @param dsmDiscern 标准落标
     * @return 标准落标集合
     */
    public List<DsmDiscern> selectDsmDiscernList(DsmDiscern dsmDiscern);

    /**
     * 新增标准落标
     * 
     * @param dsmDiscern 标准落标
     * @return 结果
     */
    public int insertDsmDiscern(DsmDiscern dsmDiscern);

    /**
     * 修改标准落标
     * 
     * @param dsmDiscern 标准落标
     * @return 结果
     */
    public int updateDsmDiscern(DsmDiscern dsmDiscern);

    /**
     * 删除标准落标
     * 
     * @param discernId 标准落标ID
     * @return 结果
     */
    public int deleteDsmDiscernById(Long discernId);

    /**
     * 批量删除标准落标
     * 
     * @param discernIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmDiscernByIds();
}
