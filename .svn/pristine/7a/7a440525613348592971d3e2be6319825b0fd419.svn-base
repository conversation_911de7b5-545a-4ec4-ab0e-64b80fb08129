<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.api.mapper.ApiDefineMapper">
    
    <resultMap type="ApiDefine" id="ApiDefineResult">
        <result property="defineId"    column="define_id"    />
        <result property="defineType"    column="define_type"    />
        <result property="defineName"    column="define_name"    />
        <result property="defineCode"    column="define_code"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="version"    column="version"    />
        <result property="cacheTime"    column="cache_time"    />
        <result property="outType"    column="out_type"    />
        <result property="successMsg"    column="success_msg"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="remark"    column="remark"    />
        <result property="defineSql"    column="define_sql"    />
        <result property="createBy"    column="create_by"    />
        <result property="status"    column="status"    />
        <result property="updateBy"    column="update_by"    />
        <result property="startTime"    column="start_time"    />
        <result property="createId"    column="create_id"    />
        <result property="endTime"    column="end_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="defineNo"    column="define_no"    />
        <result property="listen"    column="listen"    />
        <result property="passwd"    column="passwd"    />
        <result property="usernm"    column="usernm"    />
        <collection  property="systems"   javaType="java.util.List"        resultMap="sysSystemResult" />
    </resultMap>
    
    <resultMap type="sysSystem" id="sysSystemResult">
        <result property="systemId"    column="system_id"    />
        <result property="name"    column="system_name"    />
        <result property="ipAddress"    column="ip_address"    />
    </resultMap>
    
    <sql id="selectApiDefineVo">
        select define_id, define_type, define_name, define_code, datasource_id, version, cache_time, out_type, success_msg, error_msg, remark, define_sql, create_by, status, update_by, start_time, create_id, end_time, update_id, create_time, update_time, define_no,listen,passwd,usernm 
        from api_define t 
    </sql>

    <select id="selectApiDefineList" parameterType="ApiDefine" resultMap="ApiDefineResult">
        <include refid="selectApiDefineVo"/>
        <where>  
        status in (0,1)
            <if test="defineType != null  and defineType != ''"> and define_type = #{defineType}</if>
            <if test="defineName != null  and defineName != ''"> and define_name like concat('%', #{defineName}, '%')</if>
            <if test="defineCode != null  and defineCode != ''"> and define_code = #{defineCode}</if>
            <if test="datasourceId != null "> and datasource_id = #{datasourceId}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="outType != null  and outType != ''"> and out_type = #{outType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="defineNo != null  and defineNo != ''"> and define_no = #{defineNo}</if>
            <if test="listen != null  and listen != ''"> and listen = #{listen}</if>
        </where>
    </select>
     <select id="selectApiDefineHisList" parameterType="ApiDefine" resultMap="ApiDefineResult">
        <include refid="selectApiDefineVo"/>
        <where>  
            <if test="defineType != null  and defineType != ''"> and define_type = #{defineType}</if>
            <if test="defineName != null  and defineName != ''"> and define_name like concat('%', #{defineName}, '%')</if>
            <if test="defineCode != null  and defineCode != ''"> and define_code = #{defineCode}</if>
            <if test="datasourceId != null "> and datasource_id = #{datasourceId}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="outType != null  and outType != ''"> and out_type = #{outType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="defineNo != null  and defineNo != ''"> and define_no = #{defineNo}</if>
            <if test="listen != null  and listen != ''"> and listen = #{listen}</if>            
        </where>
    </select>   
    <select id="selectApiDefineById" parameterType="Long" resultMap="ApiDefineResult">
        select t.define_id, t.define_type, t.define_name, t.define_code, t.datasource_id, t.version, t.cache_time, t.out_type, t.success_msg, t.error_msg, t.remark, t.define_sql,
         t.create_by, t.status, t.update_by, t.start_time, t.create_id, t.end_time, t.update_id, t.create_time, t.update_time, t.define_no ,t.listen,t.passwd,
         s.system_id,s.name as system_name,s.ip_address,t.usernm
        from api_define t 
        left join api_define_system r on t.define_id=r.define_id
        LEFT JOIN sys_system s ON r.system_id=s.system_id
        where t.define_id = #{defineId}
    </select>
        
    <insert id="insertApiDefine" parameterType="ApiDefine" useGeneratedKeys="true" keyProperty="defineId">
        insert into api_define
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="defineType != null and defineType != ''">define_type,</if>
            <if test="defineName != null and defineName != ''">define_name,</if>
            <if test="defineCode != null and defineCode != ''">define_code,</if>
            <if test="datasourceId != null">datasource_id,</if>
            <if test="version != null">version,</if>
            <if test="cacheTime != null">cache_time,</if>
            <if test="outType != null">out_type,</if>
            <if test="successMsg != null">success_msg,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="remark != null">remark,</if>
            <if test="defineSql != null">define_sql,</if>
            <if test="createBy != null">create_by,</if>
            <if test="status != null">status,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="startTime != null">start_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="endTime != null">end_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="defineNo != null">define_no,</if>
            <if test="listen != null">listen,</if>
            <if test="passwd != null">passwd,</if>
            <if test="usernm != null">usernm,</if>
            
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="defineType != null and defineType != ''">#{defineType},</if>
            <if test="defineName != null and defineName != ''">#{defineName},</if>
            <if test="defineCode != null and defineCode != ''">#{defineCode},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="version != null">#{version},</if>
            <if test="cacheTime != null">#{cacheTime},</if>
            <if test="outType != null">#{outType},</if>
            <if test="successMsg != null">#{successMsg},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="remark != null">#{remark},</if>
            <if test="defineSql != null">#{defineSql},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="status != null">#{status},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="defineNo != null">#{defineNo},</if>
            <if test="listen != null">#{listen},</if>
            <if test="passwd != null">#{passwd},</if>
            <if test="usernm != null">#{usernm},</if>
         </trim>
    </insert>

    <update id="updateApiDefine" parameterType="ApiDefine">
        update api_define
        <trim prefix="SET" suffixOverrides=",">
            <if test="defineType != null and defineType != ''">define_type = #{defineType},</if>
            <if test="defineName != null and defineName != ''">define_name = #{defineName},</if>
            <if test="defineCode != null and defineCode != ''">define_code = #{defineCode},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="cacheTime != null">cache_time = #{cacheTime},</if>
            <if test="outType != null">out_type = #{outType},</if>
            <if test="successMsg != null">success_msg = #{successMsg},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="defineSql != null">define_sql = #{defineSql},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="defineNo != null">define_no = #{defineNo},</if>
            <if test="listen != null">listen = #{listen},</if>
            <if test="passwd != null">passwd = #{passwd},</if>
            <if test="usernm != null">usernm = #{usernm},</if>
        </trim>
        where define_id = #{defineId}
    </update>

    <delete id="deleteApiDefineById" parameterType="Long">
        delete from api_define where define_id = #{defineId}
    </delete>

    <delete id="deleteApiDefineByIds" parameterType="String">
        delete from api_define where define_id in 
        <foreach item="defineId" collection="array" open="(" separator="," close=")">
            #{defineId}
        </foreach>
    </delete>
    <select id="selectApiDefineByNameOrCode" parameterType="ApiDefine" resultMap="ApiDefineResult">
        <include refid="selectApiDefineVo"/>
         <where>
             t.status != 2   and (define_name = #{defineName} or define_code = #{defineCode})
         </where>
    </select>
    <select id="selectApiDefineByName" parameterType="ApiDefine" resultMap="ApiDefineResult">
        <include refid="selectApiDefineVo"/>
        <where>
            t.status != 2   and define_name = #{defineName}
        </where>
    </select>
    <select id="selectApiDefineByCode" parameterType="ApiDefine" resultMap="ApiDefineResult">
        <include refid="selectApiDefineVo"/>
        <where>
            t.status != 2   and  define_code = #{defineCode}
        </where>
    </select>

</mapper>