<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dqm.mapper.DqmValidationSubscriptionMapper">

    <resultMap type="DqmValidationSubscription" id="DqmValidationSubscriptionResult">
        <result property="validationSubscriptionId"    column="validation_subscription_ID"    />
        <result property="validationRuleCateId"    column="VALIDATION_RULE_CATE_ID"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="emailFlag"    column="email_flag"    />
        <result property="smsFlag"    column="sms_flag"    />
    </resultMap>

    <sql id="selectDqmValidationSubscriptionVo">
        select t.validation_subscription_ID, t.VALIDATION_RULE_CATE_ID, t.create_by, t.create_id, t.create_time,t.email_flag,t.sms_flag 
        from dqm_validation_subscription t left join sys_user u on t.create_id=u.user_id
    </sql>

    <select id="selectDqmValidationSubscriptionList" parameterType="DqmValidationSubscription" resultMap="DqmValidationSubscriptionResult">
        <include refid="selectDqmValidationSubscriptionVo"/>
        <where>
            u.del_flag != '2'
            <if test="validationRuleCateId != null "> and VALIDATION_RULE_CATE_ID = #{validationRuleCateId}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="emailFlag != null  and emailFlag != ''"> and s.email_flag = #{emailFlag}</if>
            <if test="smsFlag != null  and smsFlag != ''"> and s.sms_flag = #{smsFlag}</if>
        </where>
    </select>

    <select id="selectDqmValidationSubscriptionById" parameterType="Integer" resultMap="DqmValidationSubscriptionResult">
        <include refid="selectDqmValidationSubscriptionVo"/>
        where validation_subscription_ID = #{validationSubscriptionId}
    </select>

    <insert id="insertDqmValidationSubscription" parameterType="DqmValidationSubscription" useGeneratedKeys="true" keyProperty="validationSubscriptionId">
        insert into dqm_validation_subscription
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="validationRuleCateId != null">VALIDATION_RULE_CATE_ID,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="emailFlag != null">email_flag,</if>
            <if test="smsFlag != null">sms_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="validationRuleCateId != null">#{validationRuleCateId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="emailFlag != null">#{emailFlag},</if>
            <if test="smsFlag != null">#{smsFlag},</if>
        </trim>
    </insert>

    <update id="updateDqmValidationSubscription" parameterType="DqmValidationSubscription">
        update dqm_validation_subscription
        <trim prefix="SET" suffixOverrides=",">
            <if test="validationRuleCateId != null">VALIDATION_RULE_CATE_ID = #{validationRuleCateId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="emailFlag != null">email_flag = #{emailFlag},</if>
            <if test="smsFlag != null">sms_flag = #{smsFlag},</if>
        </trim>
        where validation_subscription_ID = #{validationSubscriptionId}
    </update>

    <delete id="deleteDqmValidationSubscriptionById" parameterType="Integer">
        delete from dqm_validation_subscription where validation_subscription_ID = #{validationSubscriptionId}
    </delete>

    <delete id="deleteDqmValidationSubscriptionByIds" parameterType="String">
        delete from dqm_validation_subscription where validation_subscription_ID in
        <foreach item="validationSubscriptionId" collection="array" open="(" separator="," close=")">
            #{validationSubscriptionId}
        </foreach>
    </delete>

    <delete id="deleteByValidationRuleCateIdAndCreateId" parameterType="DqmValidationSubscription">
        delete from dqm_validation_subscription where validation_rule_cate_id = #{validationRuleCateId} and create_id=#{createId}
    </delete>

</mapper>
