import request from '@/utils/request'

// 查询模型主题列表
export function listDsmModelEntityClass(query) {
  return request({
    url: '/dsm/dsmModelEntityClass/list',
    method: 'get',
    params: query
  })
}

// 查询模型主题详细
export function getDsmModelEntityClass(modelEntityClassId) {
  return request({
    url: '/dsm/dsmModelEntityClass/' + modelEntityClassId,
    method: 'get'
  })
}

// 新增模型主题
export function addDsmModelEntityClass(data) {
  return request({
    url: '/dsm/dsmModelEntityClass',
    method: 'post',
    data: data
  })
}

// 修改模型主题
export function updateDsmModelEntityClass(data) {
  return request({
    url: '/dsm/dsmModelEntityClass',
    method: 'put',
    data: data
  })
}

// 删除模型主题
export function delDsmModelEntityClass(modelEntityClassId) {
  return request({
    url: '/dsm/dsmModelEntityClass/' + modelEntityClassId,
    method: 'delete'
  })
}

// 导出模型主题
export function exportDsmModelEntityClass(query) {
  return request({
    url: '/dsm/dsmModelEntityClass/export',
    method: 'get',
    params: query
  })
}
//查询任务分类下拉树结构
export function treeselect() {
  return request({
    url: '/dsm/dsmModelEntityClass/treeselect',
    method: 'get'
  })
}