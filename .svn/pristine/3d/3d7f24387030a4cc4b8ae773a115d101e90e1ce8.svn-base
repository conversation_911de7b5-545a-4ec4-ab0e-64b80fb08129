package com.dqms.web.controller.system;

import java.util.LinkedList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.constant.Constants;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.framework.manager.factory.AsyncFactory;
import com.dqms.framework.web.service.TokenService;
import com.dqms.system.domain.SysNotice;
import com.dqms.system.service.ISysConfigService;
import com.dqms.system.service.ISysNoticeService;

/**
 * 公告 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController
{
    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private TokenService tokenService;
    
    @Autowired
	private ISysConfigService configService;
    /**
     * 获取通知公告列表
     */
    @Log(title = "通知管理-查询通知", businessType = BusinessType.UPDATE)
    @GetMapping("/list")
    public TableDataInfo list(SysNotice notice)
    {
        startPage();
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }
    /**
     * 获取我的通知公告列表
     */
    @GetMapping("/mylist")
    public TableDataInfo mylist(SysNotice notice)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	notice.setReceiverId(loginUser.getUser().getUserId().toString());
        startPage();
        List<SysNotice> list = noticeService.selectMyNoticeList(notice);
        return getDataTable(list);
    }
    /**
     * 获取我的公告列表
     */
    @GetMapping("/mylistGg")
    public TableDataInfo mylistGg(SysNotice notice)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	notice.setReceiverId(loginUser.getUser().getUserId().toString());
        startPage();
        List<SysNotice> list = noticeService.selectMyNoticeGgList(notice);
        return getDataTable(list);
    }


    @GetMapping("/getMultiIndexDescription")
    public SysNotice getMultiIndexDescription()
    {
        return noticeService.getMultiIndexDescription();
    }

    @GetMapping("/getSingleIndexDescription")
    public SysNotice getSingleIndexDescription()
    {
        return noticeService.getSingleIndexDescription();
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId)
    {
        return AjaxResult.success(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "通知公告-添加通知", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysNotice notice)
    {
        notice.setCreateBy(SecurityUtils.getUsername());
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "通知公告-修改通知", businessType = BusinessType.UPDATE )
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysNotice notice)
    {
        notice.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     */
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        int ret=0;
        String log="";
        for (Long noticeId : noticeIds)
        {
            SysNotice sysNotice = noticeService.selectNoticeById(noticeId);
            log+="["+sysNotice.getNoticeTitle()+"]";
        }
        ret=noticeService.deleteNoticeByIds(noticeIds);
        return toAjax(ret);
    }

    /**
     * 修改指定公告的阅读状态
     * @return 结果
     */
    @PutMapping("/updateStatusById")
    public AjaxResult updateNoticeById(@RequestBody SysNotice sysNotice){
        int ret=0;
        String log="";
        SysNotice sysNotice2 = noticeService.selectNoticeById(sysNotice.getNoticeId());
        log+="["+sysNotice2.getNoticeTitle()+"]";
        ret=noticeService.updateNoticeStatusById(sysNotice.getNoticeId(),sysNotice.getReadStatus());
        return toAjax(ret);
    }
    /**
     * 修改指定公告的阅读状态-非消息类
     * @return 结果
     */
    @PutMapping("/updateNoticeStatusByFxx")
    public AjaxResult updateNoticeStatusByFxx(@RequestBody SysNotice sysNotice){
        int ret=0;
        String log="";
        SysNotice sysNotice2 = noticeService.selectNoticeById(sysNotice.getNoticeId());
        log+="["+sysNotice2.getNoticeTitle()+"]";
        ret=noticeService.updateNoticeStatusByFxx(sysNotice.getNoticeId(),sysNotice.getReadStatus(),tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId().toString());
        return toAjax(ret);
    }

    /**
     * 修改全部的公告阅读状态
     * @return 结果
     */
    @PutMapping("/updateStatusByIds")
    public AjaxResult updateNoticesByIds(){
        return toAjax(noticeService.updateNoticesByIds(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId().toString()));
    }

    /**
     * 查看个人所有的未读消息个数
     * @return 结果
     */
    @GetMapping("/selectNoticeByReceiverId")
    public int selectNoticeByReceiverId(){
        LoginUser loginUser =tokenService.getLoginUser(ServletUtils.getRequest());
        return  noticeService.selectNoticeByReceiverId(loginUser.getUser().getUserId().toString());
    }
}
