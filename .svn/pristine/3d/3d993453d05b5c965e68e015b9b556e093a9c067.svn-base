package com.dqms.task.controller;

import com.dqms.task.domain.SysSystemSubscribe;
import com.dqms.task.service.ISysSystemSubscribeService;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统结果订阅Controller
 *
 * <AUTHOR>
 * @date 2021-09-02
 */
@RestController
@RequestMapping("/task/systemSubscribe")
public class SysSystemSubscribeController extends BaseController
{
    @Autowired
    private ISysSystemSubscribeService sysSystemSubscribeService;

    /**
     * 查询系统结果订阅列表
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSystemSubscribe sysSystemSubscribe)
    {
        startPage();
        List<SysSystemSubscribe> list = sysSystemSubscribeService.selectSysSystemSubscribeList(sysSystemSubscribe);
        return getDataTable(list);
    }

    /**
     * 导出系统结果订阅列表
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:export')")
    @Log(title = "系统结果订阅", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SysSystemSubscribe sysSystemSubscribe)
    {
        List<SysSystemSubscribe> list = sysSystemSubscribeService.selectSysSystemSubscribeList(sysSystemSubscribe);
        ExcelUtil<SysSystemSubscribe> util = new ExcelUtil<SysSystemSubscribe>(SysSystemSubscribe.class);
        return util.exportExcel(list, "systemSubscribe");
    }

    /**
     * 获取系统结果订阅详细信息
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:query')")
    @GetMapping(value = "/{systemSubscribeId}")
    public AjaxResult getInfo(@PathVariable("systemSubscribeId") Long systemSubscribeId)
    {
        return AjaxResult.success(sysSystemSubscribeService.selectSysSystemSubscribeById(systemSubscribeId));
    }

    /**
     * 新增系统结果订阅
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:add')")
    @Log(title = "系统结果订阅", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSystemSubscribe sysSystemSubscribe)
    {
        return toAjax(sysSystemSubscribeService.insertSysSystemSubscribe(sysSystemSubscribe));
    }

    /**
     * 修改系统结果订阅
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:edit')")
    @Log(title = "系统结果订阅", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysSystemSubscribe sysSystemSubscribe)
    {
        return toAjax(sysSystemSubscribeService.updateSysSystemSubscribe(sysSystemSubscribe));
    }

    /**
     * 删除系统结果订阅
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:remove')")
    @Log(title = "系统结果订阅", businessType = BusinessType.DELETE)
	@DeleteMapping("/{systemSubscribeIds}")
    public AjaxResult remove(@PathVariable Long[] systemSubscribeIds)
    {
        return toAjax(sysSystemSubscribeService.deleteSysSystemSubscribeByIds(systemSubscribeIds));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('task:systemSubscribe:edit')")
    @Log(title = "系统结果订阅-修改状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysSystemSubscribe sysSystemSubscribe)
    {
        return toAjax(sysSystemSubscribeService.updateSysSystemSubscribeStatus(sysSystemSubscribe));
    }
}
