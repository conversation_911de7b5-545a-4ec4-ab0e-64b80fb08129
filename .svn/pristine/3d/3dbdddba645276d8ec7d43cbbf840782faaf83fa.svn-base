package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 指标与机构对象 dsm_quota_dept_rel
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
public class DsmQuotaDeptRel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标 */
    @Excel(name = "指标")
    private Long quotaId;

    /** 机构 */
    @Excel(name = "机构")
    private Long deptId;

    public void setQuotaId(Long quotaId)
    {
        this.quotaId = quotaId;
    }

    public Long getQuotaId()
    {
        return quotaId;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("quotaId", getQuotaId())
            .append("deptId", getDeptId())
            .toString();
    }
}
