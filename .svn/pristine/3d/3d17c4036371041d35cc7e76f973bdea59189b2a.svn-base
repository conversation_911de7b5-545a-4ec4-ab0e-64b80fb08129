<template>
  <div class="app-container">
    <div
      style="
        background: #293f54;
        height: 45px;
        line-height: 45px;
        color: #fff;
        padding-left: 30px;
        font-size: 17px;
        margin-bottom: 15px;
      "
    >
      数据标准查询
    </div>
    <el-row :gutter="20">
      <el-col :span="4" :xs="24" @click.native="model = false">
        <div
          style="
            background: #409eff;
            color: #fff;
            height: 30px;
            padding-left: 10px;
            margin-bottom: 10px;
          "
        >
          指标分类
        </div>
        <!--<div class="head-container">
          <el-input
            v-model="indexClassName"
            placeholder="请输入分类名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>-->
        <div class="head-container">
          <el-tree
            :data="indexClassIdOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleTreeNodeClick"
          />
        </div>
      </el-col>
      <el-col
        :span="20"
        :xs="24"
        style="border-left: 1px solid #304156; position: relative"
      >
        <div
          v-if="model"
          style="
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: -5px;
            background: #fff;
            z-index: 100;
            padding-left:15px;
          "
        >
          <h4>数据标准查询</h4>
          <p style="text-indent: 2em; line-height: 1.5em">
            数据标准是数据治理领域一个重要环节，同时也是数据质量管理、元数据管理工作的重要基础。基于大数据平台已入仓的系统数据，数据标准规范指标名称、口径定义，统一输出结果，使得指标能够反复使用，节省开发、管理成本。在此基础上数据标准查询提供可视查询页面，以业务的视角展现指标要素，便于业务人员浏览、查看指标数据标准，形成企业范围对数据的统一认识。
          </p>
          <h4>数据范围</h4>
          <p style="text-indent: 2em; line-height: 1.5em">
          当前已入仓源系统30个（集中交易柜台、法人清算、开户系统、投顾平台、PB量投、銘创PB、投教平台、O32、资管估值、资管TA、海益固收、三板做市、机构CRM、经纪CRM、CISP、FISP、净资本、异常交易、内控、市场风控、投行系统、聚源资讯、德勤数据、储宝宝、收益凭证、OA、财务系统、人力资源、统一用户、源动力），入仓源表978张，数据标准提供查询指标约600个。          </p>
          <h4>页面说明</h4>
          <p style="text-indent: 2em; line-height: 1.5em">
            指标名称：数据标准查询平台为查询指标的命名。
          </p>
          <p style="text-indent: 2em; line-height: 1.5em">
            所属应用：该指标第一个落地的应用。
          </p>
          <p style="text-indent: 2em; line-height: 1.5em">
            统计周期：该指标统计频度。
          </p>
          <p style="text-indent: 2em; line-height: 1.5em">
            业务条线：该指标当前主要服务的业务条线。
          </p>
          <p style="text-indent: 2em; line-height: 1.5em">
            是否落地：标注“是”的内容是指后台程序已经过加工计算，有明确应用，并连同业务部门进行过数据核对；标注“否”的是指上述内容未全部完成。
          </p>
        </div>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
        >
          <el-form-item label="名称/编码" prop="indexName">
            <el-input
              v-model="queryParams.indexName"
              placeholder="请输入指标名称/编码"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="指标类型" prop="indexType">
            <el-select
              v-model="queryParams.indexType"
              placeholder="请选择指标类型"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in indexTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="业务条线" prop="indexClassId">
            <treeselect
              v-model="queryParams.indexClassId"
              :options="indexClassIdOptions"
              :disable-branch-nodes="true"
              :show-count="true"
              placeholder="请选择业务条线"
            />
          </el-form-item>

          <el-form-item label="是否落地" prop="ifDown">
            <el-select
              v-model="queryParams.ifDown"
              placeholder="请选择是否落地"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in ifDownOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
          :refreshShow="true"
          :searchShow="true"
        ></right-toolbar>

        <el-table
          v-loading="loading"
          border
          :data="dsmIndexList"
          @selection-change="handleSelectionChange"
        >
         <!-- <el-table-column type="selection" width="55" align="center" />-->
          <el-table-column
            label="指标编码"
            align="center"
            prop="indexCode"
            width="90"
          />
          <el-table-column
            label="指标名称"
            align="center"
            prop="indexName"
            width="255"
          />
          <el-table-column
            label="业务条线"
            align="center"
            prop="indexClassName"
            width="130"
          />
          <el-table-column
            label="指标类型"
            align="center"
            prop="indexType"
            :formatter="indexTypeFormat"
          />
         <!-- <el-table-column
            label="指标状态"
            align="center"
            prop="status"
            :formatter="statusFormat"
          />-->
          <el-table-column
            label="所属应用"
            align="center"
            prop="systemName"
            width="130"
          />
          <el-table-column
            label="是否落地"
            align="center"
            prop="ifDown"
            :formatter="ifDownFormat"
          />
          <!--<el-table-column
            label="指标单位"
            align="center"
            prop="unit"
            :formatter="unitFormat"
          />-->

          <el-table-column
            label="统计周期"
            align="center"
            prop="cycle"
            :formatter="cycleFormat"
            width="155"
          />
          <el-table-column label="版本" align="center" prop="version" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="85"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-document"
                @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
      @before-close="closeDialog"
    >
      <dispatcher
        ref="dispatcher"
        v-if="open"
        :indexClassIdOptions="indexClassIdOptions"
        :indexTypeOptions="indexTypeOptions"
        :statusOptions="statusOptions"
        :unitOptions="unitOptions"
        :cycleOptions="cycleOptions"
        :ifDownOptions="ifDownOptions"
        :loading="loading"
        :iFDisable="iFDisable"
        @openChange="openChange"
        @changeiFDisable="changeiFDisable"
      ></dispatcher>
    </el-dialog>
  </div>
</template>

<script>
import Dispatcher from "../../dsm/dsmIndex/dispatcher";
import { listDatasource, getDatasource, delDatasource, addDatasource, updateDatasource, exportDatasource,listSystem,listDataSourceType,testDatasource } from "@/api/basic/datasource";
import {
selectDsmIndexListForShow,
  listDsmIndex,
  delDsmIndex,
  addDsmIndex,
  updateDsmIndex,
  exportDsmIndex,
  listDsmIndexRel,
  importTemplate,
  exportDsmIndexMdmRel,
} from "@/api/dsm/dsmIndex";

import { treeselect } from "@/api/dsm/dsmIndexClass";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import "@../../../public/UEditor/ueditor.config.js";
import "@../../../public/UEditor/ueditor.all.min.js";
import "@../../../public/UEditor/kityformula-plugin/addKityFormulaDialog.js";
import "@../../../public/UEditor/kityformula-plugin/getKfContent.js";
import "@../../../public/UEditor/kityformula-plugin/defaultFilterFix.js";
import G6 from "@antv/g6";
import { getToken } from "@/utils/auth";
import backgroundImage from "@/assets/images/timg.jpg";

export default {
  name: "DsmIndex",
  components: {
    Dispatcher,
    Treeselect,
  },
  provide(){
   return {
    reload: this.reload
   }
  },
  data() {
    return {
    isRouterAlive:true,
      model: true,
      iFDisable: "read",
      open: false,
      // 遮罩层
      loading: true,
      uploading: false,
      isfile: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 指标管理表格数据
      dsmIndexList: [],
      // 指标类型字典
      indexTypeOptions: [],
      // 指标状态字典
      statusOptions: [],
      // 指标单位字典
      unitOptions: [],
      // 统计周期字典
      cycleOptions: [],
      ifDownOptions: [],
      // 指标分类字典
      indexClassIdOptions: [],
      indexClassName: "",
      indexId: "",
      dsmMdmRelUnListSearch: "",
      balanceOpen: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      //关系图
      backgroundImage: "",
      g6graph: undefined,
      nodes: [],
      edges: [],
      drawer: false,
      layout: null,
      direction: "btt",

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        indexCode: null,
        indexName: null,
        indexType: null,
        status: null,
        attachment: null,
        definition: null,
        execSql: null,
        unit: null,
        cycle: null,
        version: null,
        indexClassId: null,
        ifDown: null
      },
      // 主题导入参数（维度字典明细）
      upload_dsmIndexAndMdm: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/dsm/dsmMdmRel/importDsmIndexMdmRelIndexIdUn",
      },
    };
  },
  watch: {
    indexClassName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getDicts("dsm_index_type").then((response) => {
      this.indexTypeOptions = response.data;
    });
    this.getDicts("dsm_standard_status").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("dsm_index_unit").then((response) => {
      this.unitOptions = response.data;
    });
    this.getDicts("nes_needs_rate").then((response) => {
      this.cycleOptions = response.data;
    });
   this.getDicts("sys_yes_no").then(response => {
      this.ifDownOptions = response.data;
    });
  },
  methods: {
    openChange(childData) {
      this.open = childData;
    },
    changeiFDisable(iFDisable) {
      this.iFDisable = iFDisable;
    },
    closeDialog(done) {
      this.$refs.dispatcher.closeDialog(done);
    },
    /** 查询指标管理列表 */
    getList() {
      this.loading = true;
      selectDsmIndexListForShow(this.queryParams).then((response) => {
        this.dsmIndexList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getTreeselect() {
      treeselect().then((response) => {
        this.indexClassIdOptions = response.data;
      });
    },
    indexClassIdFormat(row, column) {
      return this.selectLabel(
        this.indexClassIdOptions,
        row.indexClassId,
        "id",
        "label"
      );
    },
    // 指标类型字典翻译
    indexTypeFormat(row, column) {
      return this.selectDictLabel(this.indexTypeOptions, row.indexType);
    },
    // 指标状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 指标单位字典翻译
    unitFormat(row, column) {
      return this.selectDictLabel(this.unitOptions, row.unit);
    },
    // 统计周期字典翻译
    cycleFormat(row, column) {
      return this.selectDictLabel(this.cycleOptions, row.cycle);
    },
    // 数据类型字典翻译
    dataTypeFormat(row, column) {
      return this.selectDictLabel(this.dataTypeOptions, row.dataType);
    },
    // 是否主键字典翻译
    isPriKeyFormat(row, column) {
      return this.selectDictLabel(this.isPriKeyOptions, row.isPriKey);
    },
    // 是否落地字典翻译
    ifDownFormat(row, column) {
      return this.selectDictLabel(this.ifDownOptions, row.ifDown);
    },
    // 是否为空字典翻译
    nullableFormat(row, column) {
      return this.selectDictLabel(this.nullableOptions, row.nullable);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.indexClassId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.handleType= "ADD";
      this.$refs.dispatcher.handleType = "ADD";
      this.$refs.dispatcher.reset();
      this.$refs.dispatcher.open = true;
      this.$refs.dispatcher.title = "添加指标管理";
    },
    // 詳情
    handleDetail(row) {
      this.title = "指标管理详情";
      this.iFDisable = "read";
      this.open = true;
      this.$nextTick(() => {
        this.$refs.dispatcher.handle(row);
        this.$refs.dispatcher.handleType = "DETAIL";
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.dispatcher.handle(row);
      this.$refs.dispatcher.handleType = "UPDATA";
      this.$refs.dispatcher.iFDisable = "write";
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleTreeNodeClick(data) {
    if(data.label!='指标分类'){
        this.model = false;
        this.queryParams.indexClassId = data.id;
        this.getList();
      }else{
        this.getList();
            this.getTreeselect();
            this.getDicts("dsm_index_type").then((response) => {
              this.indexTypeOptions = response.data;
            });
            this.getDicts("dsm_standard_status").then((response) => {
              this.statusOptions = response.data;
            });
            this.getDicts("dsm_index_unit").then((response) => {
              this.unitOptions = response.data;
            });
            this.getDicts("nes_needs_rate").then((response) => {
              this.cycleOptions = response.data;
            });
           this.getDicts("sys_yes_no").then(response => {
              this.ifDownOptions = response.data;
            });
            this.model = true;
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexIds = row.indexId || this.ids;
      this.$confirm(
        '是否确认删除指标管理编号为"' + indexIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delDsmIndex(indexIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有指标管理数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportDsmIndex(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    /** 导入按钮操作 */
    handleImport(command) {
      if ("importIndex" == command) {
        //维度字典
        this.handleImportt();
      } else if ("importDsmIndexMdm" == command) {
        //维度字典明细
        this.importDsmIndexMdm_rel();
      }
    },

    handleImportt() {
      this.upload.title = "指标管理导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    //---指标标准元数据映射关系导入begin----------------------------------------------------------------
    importDsmIndexMdm_rel(type) {
      this.upload_dsmIndexAndMdm.title = "指标标准元数据映射关系导入";
      this.upload_dsmIndexAndMdm.open = true;
    },
    /** 下载模板操作 */
    exportDsmIndexMdmRel() {
      exportDsmIndexMdmRel().then((response) => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress_dsmIndexAndMdm(event, file, fileList) {
      this.upload_dsmIndexAndMdm.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess_dsmIndexAndMdm(response, file, fileList) {
      this.upload_dsmIndexAndMdm.open = false;
      this.upload_dsmIndexAndMdm.isUploading = false;
      this.$refs.upload_dsmIndexAndMdm.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm_upload_dsmIndexAndMdm() {
      this.$refs.upload_dsmIndexAndMdm.submit();
    },
    //---指标标准元数据映射关系导入end----------------------------------------------------------------

    handleDown(row, index) {
      this.attachmentDownload(row.attachment);
    },
    balanceCancel() {
      this.balanceOpen = false;
    },
    handleMap(row) {
      this.layout = null;
      this.drawer = true;
      this.nodes = [];
      this.edges = [];
      this.loading = true;
      const params = { indexId: row.indexId };
      listDsmIndexRel(params).then((response) => {
        console.log(response.rows);
        this.nodes = response.rows;
        this.loading = false;
        this.init(row.indexId);
      });
    },
    handleClose(done) {
      done();
    },
    init(indexId) {
      const container = document.getElementById("container");
      const width = document.getElementById("container").scrollWidth;
      let height = document.body.clientHeight;
      const graph = new G6.TreeGraph({
        container: "container",
        width,
        height,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                const data = item.get("model");
                data.collapsed = collapsed;
                return true;
              },
            },
            "drag-canvas",
            "zoom-canvas",
          ],
        },
        defaultNode: {
          size: 6,
          anchorPoints: [
            [0, 0.5],
            [1, 0.5],
          ],
        },
        defaultEdge: {
          type: "cubic-horizontal",
        },
        layout: {
          type: "mindmap",
          direction: "H",
          getHeight: () => {
            return 16;
          },
          getWidth: () => {
            return 16;
          },
          getVGap: () => {
            return 10;
          },
          getHGap: () => {
            return 50;
          },
          getSide: (d) => {
            if (d.data.type === "S") {
              return "left";
            }
            return "right";
          },
        },
      });

      let centerX = 0;
      graph.node(function (node) {
        if (node.id === indexId) {
          centerX = node.x;
        }

        return {
          label: node.label,
          labelCfg: {
            position:
              node.children && node.children.length > 0
                ? "left"
                : node.x > centerX
                ? "right"
                : "left",
            offset: 5,
          },
        };
      });

      graph.data(this.nodes[0]);
      graph.render();
      graph.fitView();
      this.g6graph = graph;
      if (typeof window !== "undefined")
        window.onresize = () => {
          if (!graph || graph.get("destroyed")) return;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          graph.changeSize(container.scrollWidth, container.scrollHeight);
        };
    },
    closeMap() {
      this.g6graph.clear();
      this.g6graph.destroy();
      this.drawer = false;
    },
  },
  destroyed() {
    this.g6graph.clear(); //注意，VUE此处必须清理，否则切换界面会越来越卡
    this.g6graph.destroy();
  },
};
</script>
<style>
.el-tree-node__content {
  height: 32px;
}
.g6-minimap {
  position: absolute;
  width: 200px;
  height: 180px;
  text-align: right;
  margin-top: -115px;
}
.g6-minimap-container {
  border: 2px solid #009999;
  position: absolute;
  width: 200px;
  height: 110px;
  text-align: right;
}
.g6-minimap-viewport {
  border: 2px solid rgb(25, 128, 255);
}
#contextMenu {
  position: absolute;
  list-style-type: none;
  padding: 10px 8px;
  left: -150px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e2e2e2;
  border-radius: 4px;
  font-size: 12px;
  color: #545454;
}
#contextMenu li {
  cursor: pointer;
  list-style-type: none;
  list-style: none;
  margin-left: 0px;
}
#contextMenu li:hover {
  color: #aaa;
}
.g6-tooltip {
  border: 1px solid #e2e2e2;
  border-radius: 4px;
  font-size: 12px;
  color: #000;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px 8px;
  box-shadow: rgb(174, 174, 174) 0px 0px 10px;
}

.detail-table {
}
.detail-table table {
  width: 100%;
  margin-top: 10px;
  border-collapse: collapse;
  border-spacing: 0;
}
.detail-table table td {
  padding: 5px 8px;
}
.detail-table table .odd td {
  background-color: #f4f5f4;
}
#contextMenu {
  position: absolute;
  list-style-type: none;
  padding: 10px 8px;
  left: -150px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e2e2e2;
  border-radius: 4px;
  font-size: 12px;
  color: #545454;
}
#contextMenu li {
  cursor: pointer;
  list-style-type: none;
  list-style: none;
  margin-left: 0px;
}
#contextMenu li:hover {
  color: #aaa;
}
</style>
<style scoped>
.vue-treeselect {
  width: 215px;
  height: 30px;
}
</style>
