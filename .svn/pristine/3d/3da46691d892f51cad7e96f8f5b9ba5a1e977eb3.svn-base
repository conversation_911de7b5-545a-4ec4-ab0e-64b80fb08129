package com.dqms.dic.mapper;

import java.util.List;
import com.dqms.dic.domain.DicFileDefine;

/**
 * 文件补录配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-04-14
 */
public interface DicFileDefineMapper 
{
    /**
     * 查询文件补录配置
     * 
     * @param fileDefineId 文件补录配置ID
     * @return 文件补录配置
     */
    public DicFileDefine selectDicFileDefineById(Long fileDefineId);

    /**
     * 查询文件补录配置列表
     * 
     * @param dicFileDefine 文件补录配置
     * @return 文件补录配置集合
     */
    public List<DicFileDefine> selectDicFileDefineList(DicFileDefine dicFileDefine);

    /**
     * 新增文件补录配置
     * 
     * @param dicFileDefine 文件补录配置
     * @return 结果
     */
    public int insertDicFileDefine(DicFileDefine dicFileDefine);

    /**
     * 修改文件补录配置
     * 
     * @param dicFileDefine 文件补录配置
     * @return 结果
     */
    public int updateDicFileDefine(DicFileDefine dicFileDefine);

    /**
     * 删除文件补录配置
     * 
     * @param fileDefineId 文件补录配置ID
     * @return 结果
     */
    public int deleteDicFileDefineById(Long fileDefineId);

    /**
     * 批量删除文件补录配置
     * 
     * @param fileDefineIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDicFileDefineByIds(Long[] fileDefineIds);
}
