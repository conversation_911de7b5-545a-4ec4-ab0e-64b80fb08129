import request from '@/utils/request'

// 查询业务术语定义列表
export function listDsmBusinessTerms(query) {
  return request({
    url: '/dsm/dsmBusinessTerms/list',
    method: 'get',
    params: query
  })
}

// 查询业务术语定义详细
export function getDsmBusinessTerms(termId) {
  return request({
    url: '/dsm/dsmBusinessTerms/' + termId,
    method: 'get'
  })
}

// 新增业务术语定义
export function addDsmBusinessTerms(data) {
  return request({
    url: '/dsm/dsmBusinessTerms',
    method: 'post',
    data: data
  })
}

// 修改业务术语定义
export function updateDsmBusinessTerms(data) {
  return request({
    url: '/dsm/dsmBusinessTerms',
    method: 'put',
    data: data
  })
}

// 删除业务术语定义
export function delDsmBusinessTerms(termId) {
  return request({
    url: '/dsm/dsmBusinessTerms/' + termId,
    method: 'delete'
  })
}

// 导出业务术语定义
export function exportDsmBusinessTerms(query) {
  return request({
    url: '/dsm/dsmBusinessTerms/export',
    method: 'get',
    params: query
  })
}

export function importTemplate(query){
  return request({
    url: '/dsm/dsmBusinessTerms/exportDemo',
    method: 'get',
    params: query
  })
}
