package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 指标与标签对象 dsm_quota_tag_rel
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
public class DsmQuotaTagRel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标ID */
    @Excel(name = "指标ID")
    private Long quotaId;

    /** 标签ID */
    @Excel(name = "标签ID")
    private Long tagId;

    public void setQuotaId(Long quotaId)
    {
        this.quotaId = quotaId;
    }

    public Long getQuotaId()
    {
        return quotaId;
    }
    public void setTagId(Long tagId)
    {
        this.tagId = tagId;
    }

    public Long getTagId()
    {
        return tagId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("quotaId", getQuotaId())
            .append("tagId", getTagId())
            .toString();
    }
}
