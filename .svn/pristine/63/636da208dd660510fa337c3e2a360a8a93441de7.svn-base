<template>
  <div class="app-container">
    <el-table v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
      :span-method="objectSpanMethod"
      border
      >
      <el-table-column label="一级分类" align="center" prop="grand" />
      <el-table-column label="二级分类" align="center" prop="parent" />
      <el-table-column label="三级分类" align="center" prop="className" />
      <el-table-column
                label="分类说明"
                align="center"
                prop="classRemit"
              >
              <template slot-scope="scope">
                <el-popover trigger="hover" placement="top" >
                  <p>{{
                      scope.row.classRemit
                    }}</p>
                  <div slot="reference" class="name-wrapper">
                    <el-tag size="medium" >{{
                      scope.row.classRemit
                    }}</el-tag>
                  </div>
                </el-popover>
              </template>
      </el-table-column>
      <el-table-column label="指引级别" align="center" :formatter="guideLevelFormat" prop="guideLevel" />
      <el-table-column label="企业级别" align="center" :formatter="enterpriseLevelFormat" prop="enterpriseLevel" />
      <el-table-column label="敏感级别" align="center" :formatter="sensitivityLevelFormat" prop="sensitivityLevel" />
    </el-table>

    <!--<pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />-->

    <!-- 添加或修改安全分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="安全分类ID" prop="entityPropClassId" v-show="false">
                  <el-input
                    v-model="form.entityPropClassId"
                    placeholder="请输入字段Id"
                    clearable
                  />
                </el-form-item>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="应用系统" prop="systemId">
              <el-select
                v-model="form.systemId"
                placeholder="请选择应用系统"
                clearable
              >
                <el-option
                  v-for="item in systemOptions"
                  :key="item.systemId"
                  :label="item.name"
                  :value="item.systemId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="所属部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入所属部门" clearable/>
        </el-form-item>
        <el-form-item label="开发部门" prop="developmentDepartment">
          <el-input v-model="form.developmentDepartment" placeholder="请输入开发部门" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { showClassLevel,
  getDscEntityPropSafeClass,
  delDscEntityPropSafeClass,
  addDscEntityPropSafeClass,
  updateDscEntityPropSafeClass,
  exportClass
} from "@/api/dsc/dscEntityPropSafeClass";
import { treeselect } from "@/api/dsc/dscEntityPropClass";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getToken } from "@/utils/auth";
import { listSystem } from "@/api/basic/system";
import {
  listDatasourceAll,
  listSystem as listSystem2
} from "@/api/basic/datasource";
import { treeselect as depttreeselect } from "@/api/system/dept";
import object from 'element-resize-detector/src/detection-strategy/object';

export default {
  name: "DscEntityPropSafeClass",
  components: {
  },
  data() {
    return {

      dataList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全分类表格数据
      DscEntityPropSafeClassList: [],
     //指引级别字典集合
      dscentityPropClassGuideLevelOptions:[],
      propSafeClassSensitivityLevelOptions:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //系统
      systemOptions:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        className: null,
        systemId: null,
        department: null,
        developmentDepartment: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getSystem();
     this.getDicts("dscentity_prop_class_guide_level").then(response => {
          this.dscentityPropClassGuideLevelOptions = response.data;
        });
          this.getDicts("prop_safe_class_sensitivity_level").then(response => {
                this.propSafeClassSensitivityLevelOptions = response.data;
              });
  },
  methods: {
integratedData (data) {
      let arrId = []
      data.forEach(i => {
        !arrId.includes(i.grand) ? arrId.push(i.grand) : arrId  // 把带有tName1的值先存到arrId中，待后面使用
      })
      let arrObj = []
      arrId.forEach(j => {
        arrObj.push({
          name: j,
          num: 0
        })
      })
      data.forEach(k => {
        arrObj.forEach(l => {
          k.parent === l.parent ? l.num ++ : l.num // 如果有相同的就会num++
        })
      })
      data.forEach(m => {
        arrObj.forEach((n, index) => {
          if (m.parent === n.parent) {
            if (arrId.includes(m.grand)) {
              m.mergeCol = n.num
              arrId.splice(arrId.indexOf(m.grand), 1)
            } else {
              m.mergeCol = 0
            }
          }
        })
      })
    },
     objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex==0) {
        return {
          rowspan: row.rowspan1,
          colspan: 1
        };
      }
      else if(columnIndex==1) {
        return {
          rowspan: row.rowspan2,
          colspan: 1
        };
      }
      else if(columnIndex==2) {
        return {
          rowspan: row.rowspan3,
          colspan: 1
        };
      }
    },
 handleSpan ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) { // 合并第一列
        return {
          rowspan: row.mergeCol === 0 ? 0 : row.mergeCol,
          colspan: row.mergeCol === 0 ? 0 : 1
        }
      } else if (columnIndex === 2) { // 合并第二列
        return {
          rowspan: row.mergeCol === 0 ? 0 : row.mergeCol,
          colspan: row.mergeCol === 0 ? 0 : 1
        }
      }
    },
  /** 获取应用系统 */
      getSystem() {
        listSystem2().then(response => {
          this.systemOptions = response.data;
        });
      },
 // 级别字典翻译
        guideLevelFormat(row, column) {
          return this.selectDictLabel(this.dscentityPropClassGuideLevelOptions, row.guideLevel);
        },
       enterpriseLevelFormat(row, column) {
                return this.selectDictLabel(this.dscentityPropClassGuideLevelOptions, row.enterpriseLevel);
              },
       sensitivityLevelFormat(row, column) {
                return this.selectDictLabel(this.propSafeClassSensitivityLevelOptions, row.sensitivityLevel);
              },
    /** 查询安全分类列表 */
    getList() {
      this.loading = true;
      showClassLevel(this.queryParams).then(response => {
        var sort ={}
        response.rows.map(el=>{
           if(sort[el.grand]){
              sort[el.grand].number++
              if(!sort[el.grand][el.parent]){
                sort[el.grand][el.parent] = {}
                sort[el.grand][el.parent].number = 1
              }else{
                sort[el.grand][el.parent].number++
              }

              if(!sort[el.grand][el.parent][el.className]){
                sort[el.grand][el.parent][el.className] = {}
                sort[el.grand][el.parent][el.className].number = 1
                sort[el.grand][el.parent][el.className].arr=[]
              }else{
                sort[el.grand][el.parent][el.className].number++
              }

              sort[el.grand][el.parent][el.className].arr.push(el)
           }else{
              sort[el.grand]={}
              sort[el.grand][el.parent]={}
              sort[el.grand][el.parent][el.className]={}
              sort[el.grand].number= 1
              sort[el.grand][el.parent].number= 1
              sort[el.grand][el.parent][el.className].number = 1
              let arr = sort[el.grand][el.parent][el.className].arr =[]
              arr.push(el)
           }
        })
        var dataList = []
        var indexL=0
        console.log(sort,'sort')
        Object.values(sort).forEach((el,index)=>{

          Object.values(el).forEach((el2,index2)=>{
            var el2_co = {...el2}
            delete el2_co.number
            Object.values(el2_co).forEach((el3,index3)=>{
              if(el3.arr){
                el3.arr.forEach((el4,index4)=>{
                  if(index2==0&&index3==0&&index4==0){
                    el4.rowspan1 = el.number
                  }else{
                    el4.rowspan1 = 0
                  }
                  if(index3==0&&index4==0){
                    el4.rowspan2 = el2.number
                  }else{
                    el4.rowspan2 = 0
                  }

                  if(index4==0){
                    el4.rowspan3 = el3.number
                  }else{
                    el4.rowspan3 = 0
                  }
                })
                dataList.push(...el3.arr)
              }
            })
          })
        })
        console.log(dataList)
        this.dataList = dataList
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        safeClassId: null,
        entityPropClassId: null,
        systemId: null,
        department: null,
        developmentDepartment: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.safeClassId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const safeClassId = row.safeClassId || this.ids;
      this.form.entityPropClassId = row.entityPropClassId;
      if(safeClassId!=null && safeClassId!=''){
      getDscEntityPropSafeClass(safeClassId).then(response => {
        var form = response.data;
         form.systemId = response.data.systemId;
         form.department = response.data.department;
         form.developmentDepartment = response.data.developmentDepartment;
         this.form = form
        this.open = true;
        this.title = "设置安全分类";
      });
      }else{
      this.open = true;
      this.title = "添加分级分类";
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.safeClassId != null) {
            updateDscEntityPropSafeClass(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDscEntityPropSafeClass(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      console.log(row)
      const safeClassIds = row.safeClassId || this.ids;
      this.$confirm('是否确认删除安全分类编号为"' + safeClassIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDscEntityPropSafeClass(safeClassIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有安全分类数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportClass(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
<style>
.el-popover{
  max-width:60%;
}
</style>
<style scoped>
>>> .cell .el-tag{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  border:0;
  background:#fff;
  color:#000;
  font-size:14px;
}
</style>
