<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.task.mapper.EtlTaskCalendarClassMapper">
    
    <resultMap type="EtlTaskCalendarClass" id="EtlTaskCalendarClassResult">
        <result property="taskCalendarClassId"    column="task_calendar_class_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="taskCalendarClassName"    column="task_calendar_class_name"    />
        <result property="taskCalendarClassNameFull"    column="task_calendar_class_name_full"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEtlTaskCalendarClassVo">
        select task_calendar_class_id, parent_id, ancestors, task_calendar_class_name, task_calendar_class_name_full, order_num, create_by, update_by, create_id, update_id, create_time, update_time from etl_task_calendar_class
    </sql>

    <select id="selectEtlTaskCalendarClassList" parameterType="EtlTaskCalendarClass" resultMap="EtlTaskCalendarClassResult">
        <include refid="selectEtlTaskCalendarClassVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="taskCalendarClassName != null  and taskCalendarClassName != ''"> and task_calendar_class_name like concat('%', #{taskCalendarClassName}, '%')</if>
            <if test="taskCalendarClassNameFull != null  and taskCalendarClassNameFull != ''"> and task_calendar_class_name_full like concat('%', #{taskCalendarClassNameFull}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
    </select>
    
    <select id="selectEtlTaskCalendarClassById" parameterType="Long" resultMap="EtlTaskCalendarClassResult">
        <include refid="selectEtlTaskCalendarClassVo"/>
        where task_calendar_class_id = #{taskCalendarClassId}
    </select>
    
    <select id="selectEtlTaskCalendarClassByName" parameterType="String" resultMap="EtlTaskCalendarClassResult">
        <include refid="selectEtlTaskCalendarClassVo"/>
        where task_calendar_class_name = #{taskCalendarClassName}
    </select>
        
    <insert id="insertEtlTaskCalendarClass" parameterType="EtlTaskCalendarClass" useGeneratedKeys="true" keyProperty="taskCalendarClassId">
        insert into etl_task_calendar_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="taskCalendarClassName != null and taskCalendarClassName != ''">task_calendar_class_name,</if>
            <if test="taskCalendarClassNameFull != null">task_calendar_class_name_full,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="taskCalendarClassName != null and taskCalendarClassName != ''">#{taskCalendarClassName},</if>
            <if test="taskCalendarClassNameFull != null">#{taskCalendarClassNameFull},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEtlTaskCalendarClass" parameterType="EtlTaskCalendarClass">
        update etl_task_calendar_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="taskCalendarClassName != null and taskCalendarClassName != ''">task_calendar_class_name = #{taskCalendarClassName},</if>
            <if test="taskCalendarClassNameFull != null">task_calendar_class_name_full = #{taskCalendarClassNameFull},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_calendar_class_id = #{taskCalendarClassId}
    </update>

    <delete id="deleteEtlTaskCalendarClassById" parameterType="Long">
        delete from etl_task_calendar_class where task_calendar_class_id = #{taskCalendarClassId}
    </delete>

    <delete id="deleteEtlTaskCalendarClassByIds" parameterType="String">
        delete from etl_task_calendar_class where task_calendar_class_id in 
        <foreach item="taskCalendarClassId" collection="array" open="(" separator="," close=")">
            #{taskCalendarClassId}
        </foreach>
    </delete>
    
        
    <select id="selectChildrenEtlTaskCalendarClassById" parameterType="Long" resultMap="EtlTaskCalendarClassResult">
		select * from etl_task_calendar_class where find_in_set(#{taskCalendarClassId}, ancestors)
	</select>
	
	<update id="updateEtlTaskCalendarClassChildren" parameterType="java.util.List">
	    update etl_task_calendar_class set ancestors =
	    <foreach collection="taskCalendarClasss" item="item" index="index"
	        separator=" " open="case task_calendar_class_id" close="end">
	        when #{item.taskCalendarClassId} then #{item.ancestors}
	    </foreach>
	    where task_calendar_class_id in
	    <foreach collection="taskCalendarClasss" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.taskCalendarClassId}
	    </foreach>
	</update>
	
	<update id="updateEtlTaskCalendarClassNameFullChildren" parameterType="java.util.List">
	    update etl_task_calendar_class set task_calendar_class_name_full =
	    <foreach collection="taskCalendarClasss" item="item" index="index"
	        separator=" " open="case task_calendar_class_id" close="end">
	        when #{item.taskCalendarClassId} then #{item.taskCalendarClassNameFull}
	    </foreach>
	    where task_calendar_class_id in
	    <foreach collection="taskCalendarClasss" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.taskCalendarClassId}
	    </foreach>
	</update>
</mapper>