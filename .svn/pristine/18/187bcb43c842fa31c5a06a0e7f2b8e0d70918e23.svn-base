package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmIndexIndex;
import com.dqms.dsm.service.IDsmIndexIndexService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 指标关系Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmIndexIndex")
public class DsmIndexIndexController extends BaseController
{
    @Autowired
    private IDsmIndexIndexService dsmIndexIndexService;

    /**
     * 查询指标关系列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexIndex:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmIndexIndex dsmIndexIndex)
    {
        startPage();
        List<DsmIndexIndex> list = dsmIndexIndexService.selectDsmIndexIndexList(dsmIndexIndex);
        return getDataTable(list);
    }

    /**
     * 导出指标关系列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexIndex:export')")
    @Log(title = "指标关系", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmIndexIndex dsmIndexIndex)
    {
        List<DsmIndexIndex> list = dsmIndexIndexService.selectDsmIndexIndexList(dsmIndexIndex);
        ExcelUtil<DsmIndexIndex> util = new ExcelUtil<DsmIndexIndex>(DsmIndexIndex.class);
        return util.exportExcel(list, "dsmIndexIndex");
    }

    /**
     * 获取指标关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexIndex:query')")
    @GetMapping(value = "/{indexIndexId}")
    public AjaxResult getInfo(@PathVariable("indexIndexId") Long indexIndexId)
    {
        return AjaxResult.success(dsmIndexIndexService.selectDsmIndexIndexById(indexIndexId));
    }

    /**
     * 新增指标关系
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexIndex:add')")
    @Log(title = "指标关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmIndexIndex dsmIndexIndex)
    {
        return toAjax(dsmIndexIndexService.insertDsmIndexIndex(dsmIndexIndex));
    }

    /**
     * 修改指标关系
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexIndex:edit')")
    @Log(title = "指标关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmIndexIndex dsmIndexIndex)
    {
        return toAjax(dsmIndexIndexService.updateDsmIndexIndex(dsmIndexIndex));
    }

    /**
     * 删除指标关系
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmIndexIndex:remove')")
    @Log(title = "指标关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{indexIndexIds}")
    public AjaxResult remove(@PathVariable Long[] indexIndexIds)
    {
        return toAjax(dsmIndexIndexService.deleteDsmIndexIndexByIds(indexIndexIds));
    }
}
