package com.dqms.dqm.service;


import java.util.List;

import com.dqms.dqm.domain.DqmValidationMould;
import com.dqms.dqm.domain.DqmValidationMouldParameter;

/**
 * 模板参数Service接口
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
public interface IDqmValidationMouldParameterService
{
    /**
     * 查询模板参数
     *
     * @param validationMouldParameterId 模板参数ID
     * @return 模板参数
     */
    public DqmValidationMouldParameter selectDqmValidationMouldParameterById(Integer validationMouldParameterId);

    /**
     * 查询模板参数列表
     *
     * @param dqmValidationMouldParameter 模板参数
     * @return 模板参数集合
     */
    public List<DqmValidationMouldParameter> selectDqmValidationMouldParameterList(DqmValidationMouldParameter dqmValidationMouldParameter);
    /**
     * 通过模板ID查询模板参数列表
     *
     * @param validationMouldId 模板参数
     * @return 模板参数集合
     */
    public List<DqmValidationMouldParameter> selectGetDataById(String validationMouldId);

    /**
     * 新增模板参数
     *
     * @param dqmValidationMouldParameter 模板参数
     * @return 结果
     */
    public int insertDqmValidationMouldParameter(DqmValidationMouldParameter dqmValidationMouldParameter);

    /**
     * 修改模板参数
     *
     * @param dqmValidationMouldParameter 模板参数
     * @return 结果
     */
    public int updateDqmValidationMouldParameter(DqmValidationMouldParameter dqmValidationMouldParameter);

    /**
     * 批量删除模板参数
     *
     * @param validationMouldParameterIds 需要删除的模板参数ID
     * @return 结果
     */
    public int deleteDqmValidationMouldParameterByIds(Integer[] validationMouldParameterIds);

    /**
     * 删除模板参数信息
     *d
     * @param validationMouldParameterId 模板参数ID
     * @return 结果
     */
    public int deleteDqmValidationMouldParameterById(Integer validationMouldParameterId);
    List<DqmValidationMouldParameter> getConditionById(Integer validationMouldId);
}
