import request from '@/utils/request'

// 查询元系统注册列表
export function listMdmRegistry(query) {
  return request({
    url: '/mdm/mdmRegistry/list',
    method: 'get',
    params: query
  })
}

// 查询元系统注册详细
export function getMdmRegistry(regId) {
  return request({
    url: '/mdm/mdmRegistry/' + regId,
    method: 'get'
  })
}

// 新增元系统注册
export function addMdmRegistry(data) {
  return request({
    url: '/mdm/mdmRegistry',
    method: 'post',
    data: data
  })
}

// 修改元系统注册
export function updateMdmRegistry(data) {
  return request({
    url: '/mdm/mdmRegistry',
    method: 'put',
    data: data
  })
}
// 元系统注册状态修改
export function changeRegistryStatus(regId, isEnable) {
  const data = {
    regId,
    isEnable
  }
  return request({
    url: '/mdm/mdmRegistry/changeStatus',
    method: 'put',
    data: data
  })
}
// 删除元系统注册
export function delMdmRegistry(regId) {
  return request({
    url: '/mdm/mdmRegistry/' + regId,
    method: 'delete'
  })
}

// 导出元系统注册
export function exportMdmRegistry(query) {
  return request({
    url: '/mdm/mdmRegistry/export',
    method: 'get',
    params: query
  })
}

// 下载元系统注册导入模板
export function importTemplate() {
  return request({
    url: '/mdm/mdmRegistry/importTemplate',
    method: 'get'
  })
}

// 元系统注册采集
export function collect(data) {
  return request({
    url: '/mdm/mdmRegistry/collect',
    method: 'post',
    data: data
  })
}

export function mdmlist(query) {
  return request({
    url: '/mdm/mdmRegistry/mdmlist',
    method: 'get',
    params: query
  })
}

export function unMdmlist(query) {
  return request({
    url: '/mdm/mdmRegistry/unMdmlist',
    method: 'get',
    params: query
  })
}

export function getData(regId) {
  return request({
    url: '/mdm/mdmRegistry/getData/' + regId,
    method: 'get'
  })
}

export function getCreateSql(regId) {
  return request({
    url: '/mdm/mdmRegistry/getCreateSql/' + regId,
    method: 'get'
  })
}

export function getMdmDataEntityVo(regId) {
  return request({
    url: '/mdm/mdmRegistry/getMdmDataEntityVo/' + regId,
    method: 'get'
  })
}

export function fastReg(data) {
  return request({
    url: '/mdm/mdmRegistry/fastReg',
    method: 'post',
    data: data
  })
}
