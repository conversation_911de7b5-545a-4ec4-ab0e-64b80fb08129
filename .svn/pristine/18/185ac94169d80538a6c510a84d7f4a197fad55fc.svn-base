package com.dqms.task.controller;

import java.util.List;

import com.dqms.task.domain.EtlTask;
import com.dqms.task.service.IEtlTaskService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.task.domain.EtlTaskGroup;
import com.dqms.task.service.IEtlTaskGroupService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 任务分组Controller
 *
 * <AUTHOR>
 * @date 2021-03-08
 */
@RestController
@RequestMapping("/task/taskGroup")
public class EtlTaskGroupController extends BaseController
{
    @Autowired
    private IEtlTaskGroupService etlTaskGroupService;

    @Autowired
    private IEtlTaskService etlTaskService;

    /**
     * 查询任务分组列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(EtlTaskGroup etlTaskGroup)
    {
        startPage();
        List<EtlTaskGroup> list = etlTaskGroupService.selectEtlTaskGroupList(etlTaskGroup);
        return getDataTable(list);
    }
    /**
     * 查询任务分组列表
     */
    @GetMapping("/listAll")
    public TableDataInfo listAll(EtlTaskGroup etlTaskGroup)
    {
        List<EtlTaskGroup> list = etlTaskGroupService.selectEtlTaskGroupList(etlTaskGroup);
        return getDataTable(list);
    }
    /**
     * 导出任务分组列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskGroup:export')")
    @Log(title = "任务分组", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EtlTaskGroup etlTaskGroup)
    {
        List<EtlTaskGroup> list = etlTaskGroupService.selectEtlTaskGroupList(etlTaskGroup);
        ExcelUtil<EtlTaskGroup> util = new ExcelUtil<EtlTaskGroup>(EtlTaskGroup.class);
        return util.exportExcel(list, "taskGroup");
    }

    /**
     * 获取任务分组详细信息
     */
    @PreAuthorize("@ss.hasPermi('task:taskGroup:query')")
    @GetMapping(value = "/{taskGroupId}")
    public AjaxResult getInfo(@PathVariable("taskGroupId") Long taskGroupId)
    {
        return AjaxResult.success(etlTaskGroupService.selectEtlTaskGroupById(taskGroupId));
    }

    /**
     * 新增任务分组
     */
    @PreAuthorize("@ss.hasPermi('task:taskGroup:add')")
    @Log(title = "任务分组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EtlTaskGroup etlTaskGroup)
    {
        return toAjax(etlTaskGroupService.insertEtlTaskGroup(etlTaskGroup));
    }

    /**
     * 修改任务分组
     */
    @PreAuthorize("@ss.hasPermi('task:taskGroup:edit')")
    @Log(title = "任务分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EtlTaskGroup etlTaskGroup)
    {
        return toAjax(etlTaskGroupService.updateEtlTaskGroup(etlTaskGroup));
    }

    /**
     * 删除任务分组
     */
    @PreAuthorize("@ss.hasPermi('task:taskGroup:remove')")
    @Log(title = "任务分组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskGroupIds}")
    public AjaxResult remove(@PathVariable Long[] taskGroupIds)
    {
        for (Long taskGroupId : taskGroupIds){
            EtlTask etlTask = new EtlTask();
            etlTask.setTaskGroupId(taskGroupId);
            List<EtlTask> list = etlTaskService.selectEtlTaskList(etlTask);
            if(list!=null&&list.size()>0){
                return AjaxResult.error("分组中已包含任务，请先删除任务");
            }
        }
        return toAjax(etlTaskGroupService.deleteEtlTaskGroupByIds(taskGroupIds));
    }
}
