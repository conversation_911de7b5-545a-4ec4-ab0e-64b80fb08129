{"name": "dqms", "version": "1.0.0", "description": "数据资产平台", "author": "Gbicc", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@antv/g6": "^4.2.0", "@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "0.4.0", "axios": "^0.21.1", "clipboard": "2.0.6", "codemirror": "^5.62.0", "core-js": "3.8.1", "diff-match-patch": "^1.0.5", "echarts": "^4.9.0", "el-table-infinite-scroll": "^1.0.10", "element-resize-detector": "^1.2.3", "element-ui": "2.15.5", "element-ui-rw-dispatcher": "^0.1.1", "file-saver": "2.0.4", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "jquery": "^3.6.0", "js-beautify": "1.13.0", "js-cookie": "2.2.1", "js-export-excel": "^1.1.4", "jsencrypt": "3.0.0-rc.1", "mathjax": "^3.1.4", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sql-formatter": "^2.3.4", "vue": "2.6.12", "vue-codemirror": "^4.0.6", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-router": "3.4.9", "vue-ueditor-wrap": "^2.5.3", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.0", "sass-loader": "10.1.0", "script-ext-html-webpack-plugin": "2.1.5", "sortablejs": "^1.10.2", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}