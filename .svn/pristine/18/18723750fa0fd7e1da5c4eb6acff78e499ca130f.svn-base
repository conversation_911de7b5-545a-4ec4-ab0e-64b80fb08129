package com.dqms.dsc.domain;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 数据脱敏对象 dsc_desensitization
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscDesensitization extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 脱敏ID */
    private Long desensitizationId;

    /** 实体ID */
    @Excel(name = "实体ID")
    private Long entityId;

    /** 脱敏方式 */
    @Excel(name = "脱敏方式")
    private String desensitizationType;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    private Long tableId;
    private String tableName;
    private Long datasourceId;
    private String datasourceName;
    private Long systemId;
    private String systemName;
    
    private List<DscDesensitizationDetail> details;
    public void setDesensitizationId(Long desensitizationId)
    {
        this.desensitizationId = desensitizationId;
    }

    public Long getDesensitizationId()
    {
        return desensitizationId;
    }
    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setDesensitizationType(String desensitizationType)
    {
        this.desensitizationType = desensitizationType;
    }

    public String getDesensitizationType()
    {
        return desensitizationType;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getTableId() {
		return tableId;
	}

	public void setTableId(Long tableId) {
		this.tableId = tableId;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public Long getDatasourceId() {
		return datasourceId;
	}

	public void setDatasourceId(Long datasourceId) {
		this.datasourceId = datasourceId;
	}

	public String getDatasourceName() {
		return datasourceName;
	}

	public void setDatasourceName(String datasourceName) {
		this.datasourceName = datasourceName;
	}

	public Long getSystemId() {
		return systemId;
	}

	public void setSystemId(Long systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public List<DscDesensitizationDetail> getDetails() {
		return details;
	}

	public void setDetails(List<DscDesensitizationDetail> details) {
		this.details = details;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("desensitizationId", getDesensitizationId())
            .append("entityId", getEntityId())
            .append("desensitizationType", getDesensitizationType())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
