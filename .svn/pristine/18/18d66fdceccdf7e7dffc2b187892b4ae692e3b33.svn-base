package com.dqms.dsc.mapper;

import java.util.List;

import com.dqms.dsc.domain.vo.DscEntityPropClassVo;
import org.apache.ibatis.annotations.Param;

import com.dqms.dsc.domain.DscEntityPropClass;

/**
 * 分级分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface DscEntityPropClassMapper 
{
    /**
     * 查询分级分类
     * 
     * @param entityPropClassId 分级分类ID
     * @return 分级分类
     */
    public DscEntityPropClass selectDscEntityPropClassById(Long entityPropClassId);

    /**
     * 查询分级分类列表
     * 
     * @param dscEntityPropClass 分级分类
     * @return 分级分类集合
     */
    public List<DscEntityPropClass> selectDscEntityPropClassList(DscEntityPropClass dscEntityPropClass);

    public List<DscEntityPropClassVo> selectDscEntityPropClassListVo(DscEntityPropClassVo dscEntityPropClassVo);
    public List<DscEntityPropClass> getGrandList();
    public List<DscEntityPropClass> getParentList(DscEntityPropClass dscEntityPropClass);
    public List<DscEntityPropClass> getEntityPropClassList(DscEntityPropClass dscEntityPropClass);


    /**
     * 新增分级分类
     * 
     * @param dscEntityPropClass 分级分类
     * @return 结果
     */
    public int insertDscEntityPropClass(DscEntityPropClass dscEntityPropClass);

    /**
     * 修改分级分类
     * 
     * @param dscEntityPropClass 分级分类
     * @return 结果
     */
    public int updateDscEntityPropClass(DscEntityPropClass dscEntityPropClass);

    /**
     * 删除分级分类
     * 
     * @param entityPropClassId 分级分类ID
     * @return 结果
     */
    public int deleteDscEntityPropClassById(Long entityPropClassId);

    /**
     * 批量删除分级分类
     * 
     * @param entityPropClassIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDscEntityPropClassByIds(Long[] entityPropClassIds);
    
    /**
     * 根据ID查询所有子任务分类
     *
     * @param entityPropClassId
     * @return 任务分类列表
     */
    public List<DscEntityPropClass> selectChildrenClassById(Long entityPropClassId);
    public int updateClassChildren(@Param("dscEntityPropClass") List<DscEntityPropClass> dscEntityPropClass);
    public int updateClassNameFullChildren(@Param("dscEntityPropClass") List<DscEntityPropClass> dscEntityPropClass);
    public DscEntityPropClass selectDscEntityProotpClassByClassName(String className);
    public DscEntityPropClass selectDscEntityPropClassByClassNameFull(String classNameFull);
    public DscEntityPropClass selectDscEntityPropClassByClassNameFullvss(String classNameFullvs);
    public DscEntityPropClass selectDscEntityPropClassByClassNameFullvs(DscEntityPropClass dscEntityPropClass);
    public List<DscEntityPropClass> selectClassName(DscEntityPropClass dscEntityPropClass);
    public List<DscEntityPropClass> selectClassNameFull(DscEntityPropClass dscEntityPropClass);
}
