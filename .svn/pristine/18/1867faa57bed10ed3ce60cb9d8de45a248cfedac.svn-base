import request from '@/utils/request'

// 查询检查分类列表
export function listDqmValidationClass(query) {
  return request({
    url: '/dqm/dqmValidationClass/list',
    method: 'get',
    params: query
  })
}

// 查询检查分类详细
export function getDqmValidationClass(validationClassId) {
  return request({
    url: '/dqm/dqmValidationClass/' + validationClassId,
    method: 'get'
  })
}

// 新增检查分类
export function addDqmValidationClass(data) {
  return request({
    url: '/dqm/dqmValidationClass',
    method: 'post',
    data: data
  })
}

// 修改检查分类
export function updateDqmValidationClass(data) {
  return request({
    url: '/dqm/dqmValidationClass',
    method: 'put',
    data: data
  })
}

// 删除检查分类
export function delDqmValidationClass(validationClassId) {
  return request({
    url: '/dqm/dqmValidationClass/' + validationClassId,
    method: 'delete'
  })
}

// 导出检查分类
export function exportDqmValidationClass(query) {
  return request({
    url: '/dqm/dqmValidationClass/export',
    method: 'get',
    params: query
  })
}

//查询分类下拉树结构
export function treeselect() {
  return request({
    url: '/dqm/dqmValidationClass/treeselect',
    method: 'get'
  })
}
