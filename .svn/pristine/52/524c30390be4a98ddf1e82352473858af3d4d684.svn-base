package com.dqms.task.job;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.task.domain.EtlTask;
import com.dqms.task.mapper.EtlTaskMapper;
import com.dqms.task.mapper.EtlTaskRelationMapper;



public class EtlCheckThread implements Runnable {
	private static final Logger log = LoggerFactory.getLogger(EtlCheckThread.class);
	Long[] taskRelationIds=null;
	
	public EtlCheckThread(Long[] taskRelationIds) {
		this.taskRelationIds=taskRelationIds;
	}
	@Override
	public void run() {
		SpringUtils.getBean(EtlTaskRelationMapper.class).resetTaskCheckAll();	
		if(taskRelationIds!=null&&!taskRelationIds[0].equals(0L)) {
			for(Long t : taskRelationIds) {
				Map<Long,Long> map = new HashMap<>();
				Long[] taskIds=new Long[]{t};
				SpringUtils.getBean(EtlTaskMapper.class).selectEtlTaskListByPos(taskIds);
				boolean flag=getPos(taskIds, t ,map);
				EtlTask ti = new EtlTask();
				if(flag) {
					ti.setTaskId(t);
					ti.setLoopCheck("Y");
				}else {
					ti.setTaskId(t);
					ti.setLoopCheck("Z");
				}
				SpringUtils.getBean(EtlTaskMapper.class).updateEtlTask(ti);
			}
		}else {
			List<EtlTask> list =SpringUtils.getBean(EtlTaskMapper.class).selectEtlTaskList(new EtlTask());
			for(EtlTask t : list) {
				Map<Long,Long> map = new HashMap<>();
				Long[] taskIds=new Long[]{t.getTaskId()};
				SpringUtils.getBean(EtlTaskMapper.class).selectEtlTaskListByPos(taskIds);
				boolean flag=getPos(taskIds, t.getTaskId(),map);
				EtlTask ti = new EtlTask();
				if(flag) {
					ti.setTaskId(t.getTaskId());
					ti.setLoopCheck("Y");
				}else {
					ti.setTaskId(t.getTaskId());
					ti.setLoopCheck("Z");
				}
				SpringUtils.getBean(EtlTaskMapper.class).updateEtlTask(ti);
			}
		}
		
	}
	
	public boolean getPos(Long[] taskIds, Long taskId ,Map<Long,Long> map) {
		List<EtlTask> list = SpringUtils.getBean(EtlTaskMapper.class).selectEtlTaskListByPos(taskIds);
		if(list!=null&&list.size()>0) {
			Long[] ids =new Long[list.size()];
			int i=0;
			boolean flag=false;
			for(EtlTask t : list) {
				if(map.get(t.getTaskId())!=null) {
					continue;
				}
				map.put(t.getTaskId(),t.getTaskId());
				ids[i++]=t.getTaskId();
				if(t.getTaskId().equals(taskId)) {
					flag = true;break;
				}
			}
			if(flag) {
				return flag;
			}else {
				return getPos(ids, taskId,map);
			}
		}
		return false;
		
	}

}
