package com.dqms.api.mapper;

import java.util.List;
import com.dqms.api.domain.ApiDefineColumn;

import io.lettuce.core.dynamic.annotation.Param;

/**
 * 接口输出Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-08-03
 */
public interface ApiDefineColumnMapper 
{
    /**
     * 查询接口输出
     * 
     * @param defineColumnId 接口输出ID
     * @return 接口输出
     */
    public ApiDefineColumn selectApiDefineColumnById(Long defineColumnId);

    /**
     * 查询接口输出列表
     * 
     * @param apiDefineColumn 接口输出
     * @return 接口输出集合
     */
    public List<ApiDefineColumn> selectApiDefineColumnList(ApiDefineColumn apiDefineColumn);

    /**
     * 新增接口输出
     * 
     * @param apiDefineColumn 接口输出
     * @return 结果
     */
    public int insertApiDefineColumn(ApiDefineColumn apiDefineColumn);

    /**
     * 修改接口输出
     * 
     * @param apiDefineColumn 接口输出
     * @return 结果
     */
    public int updateApiDefineColumn(ApiDefineColumn apiDefineColumn);

    /**
     * 删除接口输出
     * 
     * @param defineColumnId 接口输出ID
     * @return 结果
     */
    public int deleteApiDefineColumnById(Long defineColumnId);
    public int deleteApiDefineColumnByApiDefineId(@Param("defineId")Long defineId);

    /**
     * 批量删除接口输出
     * 
     * @param defineColumnIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteApiDefineColumnByIds(Long[] defineColumnIds);
}
