<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.mdm.mapper.MdmDataEntityMapper">

    <resultMap type="MdmDataEntity" id="MdmDataEntityResult">
        <result property="entityId"    column="entity_id"    />
        <result property="registryId"    column="registry_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableComment"    column="table_comment"    />
        <result property="tableSchema"    column="table_schema"    />
        <result property="sqlScripts"    column="sql_scripts"    />
        <result property="versionNo"    column="version_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="systemName"    column="system_name"    />
        <result property="datasourceId"    column="datasource_id"    />
    </resultMap>

    <sql id="selectMdmDataEntityVo">
        select e.entity_id, e.registry_id, table_name, table_comment, table_schema, sql_scripts, version_no, e.create_by, e.update_by, e.create_id, e.update_id, e.create_time, e.update_time,r.datasource_id from mdm_data_entity e
        left join mdm_registry r on e.registry_id = r.reg_id
    </sql>

    <select id="selectMdmDataEntityList" parameterType="MdmDataEntity" resultMap="MdmDataEntityResult">
        <include refid="selectMdmDataEntityVo"/>
        <where>
            <if test="registryId != null "> and registry_id = #{registryId}</if>
            <if test="tableName != null  and tableName != ''"> and lower(table_name) like lower(concat('%', #{tableName}, '%'))</if>
            <if test="tableComment != null  and tableComment != ''"> and table_comment = #{tableComment}</if>
            <if test="tableSchema != null  and tableSchema != ''"> and lower(table_schema) = lower(#{tableSchema})</if>
            <if test="sqlScripts != null  and sqlScripts != ''"> and sql_scripts = #{sqlScripts}</if>
            <if test="versionNo != null  and versionNo != ''"> and version_no = #{versionNo}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            and r.del_flag != '2'
        </where>
    </select>

    <select id="selectMdmDataEntityListByName" parameterType="MdmDataEntity" resultMap="MdmDataEntityResult">
        <include refid="selectMdmDataEntityVo"/>
        <where>
            <if test="registryId != null "> and e.registry_id = #{registryId}</if>
            <if test="tableName != null  and tableName != ''"> and lower(e.table_name) = lower(#{tableName}) </if>
            <if test="tableComment != null  and tableComment != ''"> and e.table_comment = #{tableComment}</if>
            <if test="tableSchema != null  and tableSchema != ''"> and lower(e.table_schema) = lower(#{tableSchema})</if>
            <if test="sqlScripts != null  and sqlScripts != ''"> and e.sql_scripts = #{sqlScripts}</if>
            <if test="versionNo != null  and versionNo != ''"> and e.version_no = #{versionNo}</if>
            <if test="createId != null "> and e.create_id = #{createId}</if>
            <if test="updateId != null "> and e.update_id = #{updateId}</if>
            <if test="datasourceId != null "> and r.datasource_id = #{datasourceId}</if>
            and r.del_flag != '2'
        </where>
    </select>

    <select id="selectMdmDataEntityById" parameterType="Long" resultMap="MdmDataEntityResult">
        <include refid="selectMdmDataEntityVo"/>
        where entity_id = #{entityId}
    </select>
    <select id="selectMdmDataEntityByTableName" parameterType="MdmDataEntity" resultMap="MdmDataEntityResult">
        select e.entity_id, e.registry_id, table_name, table_comment, table_schema, sql_scripts, version_no, e.create_by, e.update_by, e.create_id, e.update_id, e.create_time, e.update_time
        from mdm_data_entity e
        left join mdm_registry r on e.registry_id = r.reg_id
        left join sys_system ss on r.system_id=ss.system_id
        where e.table_name = #{tableName} 	AND e.table_schema = #{tableSchema} and r.del_flag != '2'
        <if test="systemName != null "> and ss.name=#{systemName} </if>
        <if test="datasourceId != null "> and r.datasource_id = #{datasourceId}</if>
    </select>


    <select id="selectMdmDataEntityByRegId" parameterType="Long" resultMap="MdmDataEntityResult">
        <include refid="selectMdmDataEntityVo"/>
        where registry_id = #{regId} and version_no = (select max(version_no) from mdm_data_entity where registry_id = #{regId})
    </select>

    <insert id="insertMdmDataEntity" parameterType="MdmDataEntity"  useGeneratedKeys="true" keyProperty="entityId">
        insert into mdm_data_entity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registryId != null">registry_id,</if>
            <if test="entityId != null">entity_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="tableComment != null">table_comment,</if>
            <if test="tableSchema != null">table_schema,</if>
            <if test="sqlScripts != null">sql_scripts,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registryId != null">#{registryId},</if>
            <if test="entityId != null">#{entityId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="tableComment != null">#{tableComment},</if>
            <if test="tableSchema != null">#{tableSchema},</if>
            <if test="sqlScripts != null">#{sqlScripts},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdmDataEntity" parameterType="MdmDataEntity">
        update mdm_data_entity
        <trim prefix="SET" suffixOverrides=",">
            <if test="registryId != null">registry_id = #{registryId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="tableComment != null">table_comment = #{tableComment},</if>
            <if test="tableSchema != null">table_schema = #{tableSchema},</if>
            <if test="sqlScripts != null">sql_scripts = #{sqlScripts},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where entity_id = #{entityId}
    </update>

    <delete id="deleteMdmDataEntityById" parameterType="Long">
        delete from mdm_data_entity where entity_id = #{entityId}
    </delete>

    <delete id="deleteMdmDataEntityByIds" parameterType="String">
        delete from mdm_data_entity where entity_id in
        <foreach item="entityId" collection="array" open="(" separator="," close=")">
            #{entityId}
        </foreach>
    </delete>

    <select id="checkDataEntityIsCollected" resultType="int">
        select count(1)
        from mdm_data_entity e join mdm_registry r on e.registry_id = r.reg_id
        where
        e.table_schema = #{tableSchema,jdbcType=VARCHAR}
        AND e.table_name = #{tableName,jdbcType=VARCHAR}
        AND r.datasource_id = #{datasourceId}
        	and r.del_flag != '2'
    </select>


    <select id="selectAll" resultMap="MdmDataEntityResult">
        <include refid="selectMdmDataEntityVo"/>
    </select>
    <resultMap type="MdmDataEntityVo" id="MdmDataEntityVoResult">
        <result property="entityId"    column="entity_id"    />
        <result property="registryId"    column="registry_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableComment"    column="table_comment"    />
        <result property="tableSchema"    column="table_schema"    />
        <result property="sqlScripts"    column="sql_scripts"    />
        <result property="versionNo"    column="version_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sysDatasourceName"    column="sys_datasource_name"    />
    </resultMap>
    <select id="selectAllTable" resultMap="MdmDataEntityVoResult">
        select e.entity_id, e.registry_id, table_name, table_comment, table_schema, sql_scripts, version_no, e.create_by, e.update_by, e.create_id, e.update_id, e.create_time, e.update_time ,d.name as sys_datasource_name
        from mdm_data_entity e
        left join mdm_registry r on e.registry_id = r.reg_id
        left join sys_datasource d on r.datasource_id=d.datasource_id
        where r.meta_type in ('1','2') and r.del_flag != '2'
        <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
        <if test="tableSchema != null  and tableSchema != ''"> and table_schema = #{tableSchema}</if>
        <if test="entityId != null  and entityId != ''"> and e.entity_id = #{entityId}</if>
        and r.del_flag != '2'
    </select>

    <select id="selectAllUnTable" resultMap="MdmDataEntityVoResult">
        select e.entity_id, e.registry_id, table_name, table_comment, table_schema, sql_scripts, version_no, e.create_by, e.update_by, e.create_id, e.update_id, e.create_time, e.update_time ,d.name as sys_datasource_name
        from mdm_data_entity e
        left join mdm_registry r on e.registry_id = r.reg_id
        left join sys_datasource d on r.datasource_id=d.datasource_id
        where r.meta_type != '1' and r.del_flag != '2'
        <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
        <if test="tableSchema != null  and tableSchema != ''"> and table_schema = #{tableSchema}</if>
        and r.del_flag != '2'
    </select>

    <select id="selectEntityAndSystem" parameterType="String" resultMap="MdmDataEntityResult">
        select * from (select entity_id ,  registry_id, CONCAT(ss.name,'.',r.reg_name) table_name, table_comment, table_schema, sql_scripts, version_no
            from mdm_data_entity e
            left join mdm_registry r on e.registry_id = r.reg_id
            left join sys_system ss on r.system_id = ss.system_id) t
            <where>
                <if test="name != null and name !=''">
                    t.table_name like concat('%', #{name}, '%')
                </if>
            </where>
    </select>

    <select id="getMdmDataEntityByTableName" resultMap="MdmDataEntityResult">
        select e.entity_id, e.registry_id, table_name, table_comment, table_schema, sql_scripts, version_no, e.create_by, e.update_by, e.create_id, e.update_id, e.create_time, e.update_time
        from mdm_data_entity e
        left join mdm_registry r on e.registry_id = r.reg_id
        left join sys_system ss on r.system_id=ss.system_id
        where e.table_name = #{tableName} 	AND e.table_schema = #{tableSchema}  and ss.name=#{systemName}
        	and r.del_flag != '2'
    </select>

</mapper>
