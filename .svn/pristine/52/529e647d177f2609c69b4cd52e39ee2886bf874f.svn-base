<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="数据名称" prop="manualDataName">
        <el-input
          v-model="queryParams.manualDataName"
          placeholder="请输入数据名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="源数据源" prop="datasourceId">
        <el-select
          v-model="queryParams.datasourceId"
          placeholder="请选择源数据源"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="目标表" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入目标表"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否审批" prop="isApply">
        <el-select v-model="queryParams.isApply" placeholder="请选择是否审批" clearable size="small">
          <el-option
            v-for="dict in isApplyOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="读写分离" prop="isSeparation">
        <el-select v-model="queryParams.isSeparation" placeholder="请选择读写分离" clearable size="small">
          <el-option
            v-for="dict in isSeparationOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="权限控制" prop="isAuth">
        <el-select v-model="queryParams.isAuth" placeholder="请选择权限控制" clearable size="small">
          <el-option
            v-for="dict in isAuthOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计周期" prop="cycle">
        <el-select v-model="queryParams.cycle" placeholder="请选择统计周期" clearable size="small">
          <el-option
            v-for="dict in cycleOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dic:dicManualDataDefine:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dic:dicManualDataDefine:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dic:dicManualDataDefine:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dic:dicManualDataDefine:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dicManualDataDefineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="数据名称" align="center" prop="manualDataName"   width="220">
	      <template slot-scope="scope">
			  <router-link size="mini" :to="'/dic/dicManualDataDeploy/' + scope.row.manualDataId" class="link-type" >{{ scope.row.manualDataName}} </router-link>
		  </template>
      </el-table-column>
      <el-table-column label="数据源" align="center" prop="datasourceName" />
      <el-table-column label="目标表" align="center" prop="tableName" />
      <el-table-column label="是否审批" align="center" prop="isApply" :formatter="isApplyFormat" />
      <el-table-column label="读写分离" align="center" prop="isSeparation" :formatter="isSeparationFormat" />
      <el-table-column label="权限控制" align="center" prop="isAuth" :formatter="isAuthFormat" />
      <el-table-column label="统计周期" align="center" prop="cycle" :formatter="cycleFormat" />
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat"  >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="后置任务" align="center" prop="posTaskName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dic:dicManualDataDefine:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dic:dicManualDataDefine:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUser(scope.row)"
            v-hasPermi="['dic:dicManualDataDefine:edit']"
          >赋权</el-button>          
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改手工数据配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row type="flex" justify="start" align="top">
        <el-form-item label="数据名称" prop="manualDataName">
          <el-input v-model="form.manualDataName" placeholder="请输入数据名称" clearable/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row type="flex" justify="start" align="top">        
        <el-form-item label="数据源" prop="datasourceId">
          <el-select
            v-model="form.datasourceId"
            @change="(value)=>changed(value)"
            placeholder="请选择源数据源"
            filterable
          >
            <el-option
              v-for="item in dataSourceOptions"
              :key="item.datasourceId"
              :label="item.name"
              :value="item.datasourceId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标表" prop="tableName">
          <el-select
            v-model="form.tableName"
            placeholder="请选择目标表"
            filterable
          >
            <el-option
              v-for="item in tableOptions"
              :key="item.label"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row type="flex" justify="start" align="top">        
        <el-form-item label="是否审批" prop="isApply">
          <el-select v-model="form.isApply" placeholder="请选择是否审批" clearable>
            <el-option
              v-for="dict in isApplyOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="读写分离" prop="isSeparation">
          <el-select v-model="form.isSeparation" placeholder="请选择读写分离" clearable>
            <el-option
              v-for="dict in isSeparationOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row type="flex" justify="start" align="top">        
        <el-form-item label="表单控制" prop="isAuth">
          <el-select v-model="form.isAuth" placeholder="请选择表单控制" clearable>
            <el-option
              v-for="dict in isAuthOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="统计周期" prop="cycle">
          <el-select v-model="form.cycle" placeholder="请选择统计周期" clearable>
            <el-option
              v-for="dict in cycleOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row type="flex" justify="start" align="top">        
        <el-form-item label="起始行" prop="minLine">
          <el-input v-model="form.minLine" placeholder="请输表头所属行，0为不限制" clearable/>
        </el-form-item>
        <el-form-item label="结束行" prop="maxLine">
          <el-input v-model="form.maxLine" placeholder="请输入结束行，0为不限制" clearable/>
        </el-form-item>
      </el-row>
      <el-form-item label="后置任务" prop="posTaskId">
   	      <el-select
		    v-model="form.posTaskId"
		    filterable
		    remote
		    reserve-keyword
		    placeholder="请输入后置任务"
		    :remote-method="remoteMethod"
		    :loading="loading"  style="width:100%">
		    <el-option
		      v-for="item in taskOptions"
		      :key="item.taskId"
		      :label="item.taskName"
		      :value="item.taskId">
		    </el-option>
		  </el-select>
      </el-form-item>      
      <el-form-item label="前置SQL" prop="preSql">
        <el-input v-model="form.preSql" type="textarea" placeholder="请输入内容" />
      </el-form-item>     
      <el-form-item label="说明" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
      </el-form-item>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label-width="0px" :error="uploaderr">
              <div class="upload">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :action="uploadUrl"
                  accept=".xls,.xlsx"
                  :auto-upload="true"
                  :file-list="fileList"
                  :on-success="handleSuccess"
                  :on-remove="handleRemove"
                  :headers="myHeaders"
                  :limit="1"
                  style="width:100%"
                >
                  <el-button size="small" icon="el-icon-upload2" type="danger"
                    >上传自定义模板</el-button
                  >
                </el-upload>
                <div class="uptxt">
                  （支持excel文件限制500M以内）
                </div>
              </div>
            </el-form-item>
            <el-form-item label="文档名称" prop="attachment" v-show="false">
              <el-input
                v-model="form.attachment"
                maxLength="20"
                placeholder="请输入文档名称"
              />
            </el-form-item>
          </el-col>
        </el-row>      
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <el-dialog :title="title" :visible.sync="openUser" width="700px" append-to-body>
     <el-form :model="queryParamsUser" ref="queryParamsUser" :inline="true" label-width="68px">
      <el-form-item label="用户" prop="nickName">
        <el-input
          v-model="queryParamsUser.nickName"
          placeholder="请输入用户"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryUser">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQueryUser">重置</el-button>
      </el-form-item>
    </el-form>   
      <el-table v-loading="loading" :data="dicManualDataDefineUserList">
      <el-table-column label="用户" align="center" prop="nickName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
	            <el-switch
	              v-model="scope.row.flag"
	              active-value="Y"
	              inactive-value="N"
	              active-color="#13ce66"
	              @change="authsubmitForm(scope.row)"
	            ></el-switch>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="totalUser>0"
      :total="totalUser"
      :page.sync="queryParamsUser.pageNum"
      :limit.sync="queryParamsUser.pageSize"
      @pagination="getListUser"
    />
    </el-dialog>
  </div>
</template>

<script>
import { listDicManualDataDefine, getDicManualDataDefine, delDicManualDataDefine, addDicManualDataDefine, updateDicManualDataDefine, exportDicManualDataDefine ,changeStatus} from "@/api/dic/dicManualDataDefine";
import { listDicManualDataDefineUser, delDicManualDataDefineUser, addDicManualDataDefineUser } from "@/api/dic/dicManualDataDefineUser";
import { listDatasourceAll,findTablesSelect } from "@/api/basic/datasource";
import { listTask} from "@/api/task/task";
import { getToken } from "@/utils/auth";
export default {
  name: "DicManualDataDefine",
  components: {
  },
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() }
      },
      uploading: false,
      uploaderr: "",
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/attachment/upload",
      myHeaders: {
        Authorization: "Bearer " + getToken()
      },
      isfile: true,
      fileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 手工数据配置表格数据
      dicManualDataDefineList: [],
      // 数据补录权限表格数据
      dicManualDataDefineUserList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否审批字典
      isApplyOptions: [],
      // 读写分离字典
      isSeparationOptions: [],
      // 权限控制字典
      isAuthOptions: [],
      // 统计周期字典
      cycleOptions: [],
      // 状态字典
      statusOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      //数据源选项
      dataSourceOptions: [],
      //表选项
      tableOptions: [],
      // 关联任务
      taskOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        manualDataName: null,
        datasourceId: null,
        tableName: null,
        preSql: null,
        posTaskId: null,
        isApply: null,
        isSeparation: null,
        isAuth: null,
        cycle: null,
        maxLine: null,
        minLine: null,
        status: null,
      },
      manualDataId:null,
      openUser:false,
      totalUser:0,
      queryParamsUser: {
        pageNum: 1,
        pageSize: 10,
        manualDataId: null,
        nickName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        manualDataName: [
          { required: true, message: "数据名称不能为空", trigger: "blur" }
        ],
        datasourceId: [
          { required: true, message: "数据源不能为空", trigger: "blur" }
        ],
        tableName: [
          { required: true, message: "目标表不能为空", trigger: "blur" }
        ],
        isApply: [
          { required: true, message: "是否审批不能为空", trigger: "change" }
        ],
        isSeparation: [
          { required: true, message: "读写分离不能为空", trigger: "change" }
        ],
        isAuth: [
          { required: true, message: "权限控制不能为空", trigger: "change" }
        ],
        cycle: [
          { required: true, message: "统计周期不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response => {
      this.isApplyOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isSeparationOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isAuthOptions = response.data;
    });
    this.getDicts("nes_needs_rate").then(response => {
      this.cycleOptions = response.data;
    });
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
    this.getDataSource();
  },
  methods: {
    /** 查询手工数据配置列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listDicManualDataDefine(this.queryParams).then(response => {
        this.dicManualDataDefineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    changed(value) {
      this.form.tableName=null
      if (value != null&&value != "") {
        findTablesSelect(value).then(response => {
          this.tableOptions = response.data;
        });
      }else{
          this.tableOptions = []
      }
    },
    // 是否审批字典翻译
    isApplyFormat(row, column) {
      return this.selectDictLabel(this.isApplyOptions, row.isApply);
    },
    // 读写分离字典翻译
    isSeparationFormat(row, column) {
      return this.selectDictLabel(this.isSeparationOptions, row.isSeparation);
    },
    // 权限控制字典翻译
    isAuthFormat(row, column) {
      return this.selectDictLabel(this.isAuthOptions, row.isAuth);
    },
    // 统计周期字典翻译
    cycleFormat(row, column) {
      return this.selectDictLabel(this.cycleOptions, row.cycle);
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        manualDataId: null,
        manualDataName: null,
        datasourceId: null,
        tableName: null,
        preSql: null,
        posTaskId: null,
        isApply: 'N',
        isSeparation: 'N',
        isAuth: 'N',
        cycle: 'REAL',
        maxLine: 0,
        minLine: 0,
        status: '0',
        remark: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.manualDataId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加手工数据配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const manualDataId = row.manualDataId || this.ids
      getDicManualDataDefine(manualDataId).then(response => {
   	  	this.taskOptions =[{
             taskName: response.data.posTaskName,
             taskId: response.data.posTaskId
           }]
   	 	if(response.data.attachment!=null && response.data.attachment!=""){
         this.fileList = [
           {
             name: response.data.attachment,
             url: response.data.attachment
           }
         ];
       }
        this.form = response.data;
        this.open = true;
        this.title = "修改手工数据配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.manualDataId != null) {
            updateDicManualDataDefine(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDicManualDataDefine(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const manualDataIds = row.manualDataId || this.ids;
      this.$confirm('是否确认删除手工数据配置编号为"' + manualDataIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDicManualDataDefine(manualDataIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有手工数据配置数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDicManualDataDefine(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    // 模糊搜索
    remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
            let taskParams = {taskName:query}
            listTask(taskParams).then(response => {
	          this.taskOptions = response.rows;
	        });
          }, 200);
        } else {
          this.taskOptions = [];
        }
    },
    // 状态修改
    handleStatusChange(row) {
    	return changeStatus(row.manualDataId,row.status);
    },
    handleSuccess(res, file, fileList) {
      // 文件上传成功处理
      this.form.attachment = res.msg;
      //成功后的业务逻辑处理
    },
    handleRemove(res, file, fileList){
      //文件移除处理
      this.form.attachment = "";
    },
    /** 查询数据补录权限列表 */
    getListUser() {
      this.loading = true;
      this.queryParamsUser.manualDataId=this.manualDataId;
      listDicManualDataDefineUser(this.queryParamsUser).then(response => {
        this.dicManualDataDefineUserList = response.rows;
        this.totalUser = response.total;
        this.loading = false;
      });
    },
    /** 修改按钮操作 */
    handleUser(row) {
        this.manualDataId = row.manualDataId || this.ids
        this.openUser = true;
        this.title = "权限管理";
        this.handleQueryUser();
    },
    /** 搜索按钮操作 */
    handleQueryUser() {
      this.queryParamsUser.pageNum = 1;
      this.getListUser();
    },
    /** 重置按钮操作 */
    resetQueryUser() {
      this.resetForm("queryFormUser");
      this.handleQueryUser();
    },
    /** 提交按钮 */
    authsubmitForm(row) {
   	 let systemForm={};
   	 systemForm.manualDataId = this.manualDataId;
   	 systemForm.userId = row.userId;
		if(row.flag=='N'){
			delDicManualDataDefineUser(systemForm).then(response => {
               this.msgSuccess("取消成功");
             });
		}else{
			addDicManualDataDefineUser(systemForm).then(response => {
               this.msgSuccess("赋权成功");
             });
		}
    }
  }
};
</script>
