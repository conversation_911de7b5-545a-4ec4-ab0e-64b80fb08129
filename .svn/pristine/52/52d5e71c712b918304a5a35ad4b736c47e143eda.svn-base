import request from '@/utils/request'

// 查询数据库分组列表
export function listDatasourseType(query) {
  return request({
    url: '/basic/datasourseType/list',
    method: 'get',
    params: query
  })
}

// 查询数据库分组详细
export function getDatasourseType(datasourceTypeId) {
  return request({
    url: '/basic/datasourseType/' + datasourceTypeId,
    method: 'get'
  })
}

// 新增数据库分组
export function addDatasourseType(data) {
  return request({
    url: '/basic/datasourseType',
    method: 'post',
    data: data
  })
}

// 修改数据库分组
export function updateDatasourseType(data) {
  return request({
    url: '/basic/datasourseType',
    method: 'put',
    data: data
  })
}

// 删除数据库分组
export function delDatasourseType(datasourceTypeId) {
  return request({
    url: '/basic/datasourseType/' + datasourceTypeId,
    method: 'delete'
  })
}

// 导出数据库分组
export function exportDatasourseType(query) {
  return request({
    url: '/basic/datasourseType/export',
    method: 'get',
    params: query
  })
}