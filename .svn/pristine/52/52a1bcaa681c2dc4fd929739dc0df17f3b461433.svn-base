package com.dqms.dsc.mapper;

import java.util.List;
import com.dqms.dsc.domain.DscSensitiveDataClassification;

/**
 * 敏感数据等级划分标准Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-11-30
 */
public interface DscSensitiveDataClassificationMapper 
{
    /**
     * 查询敏感数据等级划分标准
     * 
     * @param standardId 敏感数据等级划分标准ID
     * @return 敏感数据等级划分标准
     */
    public DscSensitiveDataClassification selectDscSensitiveDataClassificationById(Long standardId);

    /**
     * 查询敏感数据等级划分标准列表
     * 
     * @param dscSensitiveDataClassification 敏感数据等级划分标准
     * @return 敏感数据等级划分标准集合
     */
    public List<DscSensitiveDataClassification> selectDscSensitiveDataClassificationList(DscSensitiveDataClassification dscSensitiveDataClassification);

    /**
     * 新增敏感数据等级划分标准
     * 
     * @param dscSensitiveDataClassification 敏感数据等级划分标准
     * @return 结果
     */
    public int insertDscSensitiveDataClassification(DscSensitiveDataClassification dscSensitiveDataClassification);

    /**
     * 修改敏感数据等级划分标准
     * 
     * @param dscSensitiveDataClassification 敏感数据等级划分标准
     * @return 结果
     */
    public int updateDscSensitiveDataClassification(DscSensitiveDataClassification dscSensitiveDataClassification);

    /**
     * 删除敏感数据等级划分标准
     * 
     * @param standardId 敏感数据等级划分标准ID
     * @return 结果
     */
    public int deleteDscSensitiveDataClassificationById(Long standardId);

    /**
     * 批量删除敏感数据等级划分标准
     * 
     * @param standardIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDscSensitiveDataClassificationByIds(Long[] standardIds);
}
