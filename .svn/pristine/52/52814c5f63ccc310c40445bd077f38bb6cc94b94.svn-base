package com.dqms.mdm.mapper;

import java.util.List;

import com.dqms.common.core.domain.entity.SysUser;
import com.dqms.mdm.domain.MdmLayer;
import com.dqms.mdm.domain.MdmLayer;
import com.dqms.mdm.domain.MdmUserLayer;

/**
 * 分层管理Mapper接口
 *
 * <AUTHOR>
 * @date 2021-03-18
 */
public interface MdmLayerMapper
{
    /**
     * 查询分层管理
     *
     * @param layerId 分层管理ID
     * @return 分层管理
     */
    public MdmLayer selectMdmLayerById(Long layerId);

    /**
     * 查询分层管理列表
     *
     * @param mdmLayer 分层管理
     * @return 分层管理集合
     */
    public List<MdmLayer> selectMdmLayerList(MdmLayer mdmLayer);

    /**
     * 新增分层管理
     *
     * @param mdmLayer 分层管理
     * @return 结果
     */
    public int insertMdmLayer(MdmLayer mdmLayer);

    /**
     * 修改分层管理
     *
     * @param mdmLayer 分层管理
     * @return 结果
     */
    public int updateMdmLayer(MdmLayer mdmLayer);

    /**
     * 删除分层管理
     *
     * @param layerId 分层管理ID
     * @return 结果
     */
    public int deleteMdmLayerById(Long layerId);

    /**
     * 批量删除分层管理
     *
     * @param layerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMdmLayerByIds(Long[] layerIds);

    /**
     * 新添加的方法
     */

    public int batchUserLayer(List<MdmUserLayer> userLayerList);

    public int deleteUserLayer(Long[] ids);

    public int deleteUserLayerByLayerId(Long layerid);

    public List<Integer> selectuserIdsByLayerId(Long layerid);

    public MdmLayer selectLayerByName(String layerName);

    public Long[] selectUserIdByNames(String[] userName);

    public Long selectUserIdByName(String userName);

    public int checkLayerNameUnique(MdmLayer mdmLayer);

    public int checkLayerCodeUnique(MdmLayer mdmLayer);

    public int checkLayerCodeUniqueByName(MdmLayer mdmLayer);

    public List<MdmLayer> selectMdmLayerAll();
}
