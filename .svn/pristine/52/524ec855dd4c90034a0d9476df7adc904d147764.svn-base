package com.dqms.dqm.controller;


import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dqm.domain.DqmValidationMouldParameter;
import com.dqms.dqm.service.IDqmValidationMouldParameterService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 模板参数Controller
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
@RestController
@RequestMapping("/dqm/parameter")
public class DqmValidationMouldParameterController extends BaseController
{
    @Autowired
    private IDqmValidationMouldParameterService dqmValidationMouldParameterService;

    /**
     * 查询模板参数列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:list')")
    @GetMapping("/list")
    public TableDataInfo list(DqmValidationMouldParameter dqmValidationMouldParameter)
    {
        startPage();
        List<DqmValidationMouldParameter> list = dqmValidationMouldParameterService.selectDqmValidationMouldParameterList(dqmValidationMouldParameter);
        return getDataTable(list);
    }
    /**
     * 通过模板id查询模板参数列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:data')")
    @GetMapping("/data")
    public TableDataInfo getDataById(String validationMouldId)
    {
        startPage();
        List<DqmValidationMouldParameter> list = dqmValidationMouldParameterService.selectGetDataById(validationMouldId);
        return getDataTable(list);
    }

    /**
     * 导出模板参数列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:export')")
    @Log(title = "模板参数", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmValidationMouldParameter dqmValidationMouldParameter)
    {
        List<DqmValidationMouldParameter> list = dqmValidationMouldParameterService.selectDqmValidationMouldParameterList(dqmValidationMouldParameter);
        ExcelUtil<DqmValidationMouldParameter> util = new ExcelUtil<DqmValidationMouldParameter>(DqmValidationMouldParameter.class);
        return util.exportExcel(list, "parameter");
    }

    /**
     * 获取模板参数详细信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:query')")
    @GetMapping(value = "/{validationMouldParameterId}")
    public AjaxResult getInfo(@PathVariable("validationMouldParameterId") Integer validationMouldParameterId)
    {
        return AjaxResult.success(dqmValidationMouldParameterService.selectDqmValidationMouldParameterById(validationMouldParameterId));
    }

    /**
     * 新增模板参数
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:add')")
    @Log(title = "模板参数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DqmValidationMouldParameter dqmValidationMouldParameter)
    {
        return toAjax(dqmValidationMouldParameterService.insertDqmValidationMouldParameter(dqmValidationMouldParameter));
    }

    /**
     * 修改模板参数
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:edit')")
    @Log(title = "模板参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmValidationMouldParameter dqmValidationMouldParameter)
    {
        return toAjax(dqmValidationMouldParameterService.updateDqmValidationMouldParameter(dqmValidationMouldParameter));
    }

    /**
     * 删除模板参数
     */
    @PreAuthorize("@ss.hasPermi('dqm:parameter:remove')")
    @Log(title = "模板参数", businessType = BusinessType.DELETE)
    @DeleteMapping("/{validationMouldParameterIds}")
    public AjaxResult remove(@PathVariable Integer[] validationMouldParameterIds)
    {
        return toAjax(dqmValidationMouldParameterService.deleteDqmValidationMouldParameterByIds(validationMouldParameterIds));
    }

}
