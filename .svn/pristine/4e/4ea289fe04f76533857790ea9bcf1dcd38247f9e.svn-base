<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmBusinessTermsMapper">
    
    <resultMap type="DsmBusinessTerms" id="DsmBusinessTermsResult">
        <result property="termId"    column="term_id"    />
        <result property="termName"    column="term_name"    />
        <result property="termExplain"    column="term_explain"    />
        <result property="synonym"    column="synonym"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDsmBusinessTermsVo">
        select term_id, term_name, term_explain, synonym, create_by, update_by, create_id, update_id, create_time, update_time from dsm_business_terms
    </sql>

    <select id="selectDsmBusinessTermsList" parameterType="DsmBusinessTerms" resultMap="DsmBusinessTermsResult">
        <include refid="selectDsmBusinessTermsVo"/>
        <where>  
            <if test="termName != null  and termName != ''"> and term_name like concat('%', #{termName}, '%')</if>
            <if test="termExplain != null  and termExplain != ''"> and term_explain = #{termExplain}</if>
            <if test="synonym != null  and synonym != ''"> and synonym like concat('%', #{synonym}, '%')</if>
            <if test="createId != null  and createId != ''"> and create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''"> and update_id = #{updateId}</if>
        </where>
    </select>
    <select id="selectDsmBusinessTermsOnly" parameterType="DsmBusinessTerms" resultMap="DsmBusinessTermsResult">
        <include refid="selectDsmBusinessTermsVo"/>
        <where>
            <if test="termName != null  and termName != ''"> and term_name = #{termName}</if>
        </where>
    </select>
    <select id="selectDsmBusinessTermsById" parameterType="Long" resultMap="DsmBusinessTermsResult">
        <include refid="selectDsmBusinessTermsVo"/>
        where term_id = #{termId}
    </select>
        
    <insert id="insertDsmBusinessTerms" parameterType="DsmBusinessTerms" useGeneratedKeys="true" keyProperty="termId">
        insert into dsm_business_terms
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="termName != null and termName != ''">term_name,</if>
            <if test="termExplain != null">term_explain,</if>
            <if test="synonym != null">synonym,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="termName != null and termName != ''">#{termName},</if>
            <if test="termExplain != null">#{termExplain},</if>
            <if test="synonym != null">#{synonym},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDsmBusinessTerms" parameterType="DsmBusinessTerms">
        update dsm_business_terms
        <trim prefix="SET" suffixOverrides=",">
            <if test="termName != null and termName != ''">term_name = #{termName},</if>
            <if test="termExplain != null">term_explain = #{termExplain},</if>
            <if test="synonym != null">synonym = #{synonym},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where term_id = #{termId}
    </update>

    <delete id="deleteDsmBusinessTermsById" parameterType="Long">
        delete from dsm_business_terms where term_id = #{termId}
    </delete>

    <delete id="deleteDsmBusinessTermsByIds" parameterType="String">
        delete from dsm_business_terms where term_id in 
        <foreach item="termId" collection="array" open="(" separator="," close=")">
            #{termId}
        </foreach>
    </delete>
</mapper>