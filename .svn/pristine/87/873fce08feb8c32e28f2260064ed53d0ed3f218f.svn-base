package com.dqms.dsm.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.core.domain.entity.SysDept;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.*;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.enums.DamConstants;
import com.dqms.dam.service.IDamAssetsService;
import com.dqms.dsm.domain.*;
import com.dqms.dsm.mapper.*;
import com.dqms.system.mapper.SysDeptMapper;
import com.dqms.system.service.ISysConfigService;
import com.dqms.system.service.ISysUserService;
import org.activiti.api.process.model.ProcessInstance;
import org.activiti.api.process.model.builders.ProcessPayloadBuilder;
import org.activiti.api.process.runtime.ProcessRuntime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.dsm.domain.vo.DsmStandardVo;
import com.dqms.dsm.enums.DsmConstants;
import com.dqms.dsm.service.IDsmStandardService;
import com.dqms.framework.web.service.TokenService;

/**
 * 基础标准Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Service
public class DsmStandardServiceImpl implements IDsmStandardService
{
    @Autowired
    private DsmStandardMapper dsmStandardMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private DsmStandardSrcDeptMapper dsmStandardSrcDeptMapper;

    @Autowired
    private DsmStandardSrcSystemMapper dsmStandardSrcSystemMapper;

    @Autowired
    private DsmStandardTarDeptMapper dsmStandardTarDeptMapper;

    @Autowired
    private DsmStandardTarSystemMapper dsmStandardTarSystemMapper;

    @Autowired
    private DsmStandardMdmRelMapper dsmStandardMdmRelMapper;

	@Autowired
	private DsmStandardClassMapper dsmStandardClassMapper;

	@Autowired
	private SysSystemMapper sysSystemMapper;

	@Autowired
	private SysDeptMapper deptMapper;

	@Autowired
	private ISysUserService sysUserService;

	@Autowired
	private ProcessRuntime processRuntime;

    @Autowired
    private IDamAssetsService damAssetsService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询基础标准
     *
     * @param standardId 基础标准ID
     * @return 基础标准
     */
    @Override
    public DsmStandard selectDsmStandardById(Long standardId)
    {
    	return dsmStandardMapper.selectDsmStandardById(standardId);
    }

    @Override
    public DsmStandardVo selectDsmStandardVoById(Long standardId)
    {
    	DsmStandardVo vo = dsmStandardMapper.selectDsmStandardVoById(standardId);
    	if(vo!=null) {
    		StringBuffer s = new StringBuffer();
    		s.append("标准名称：").append(vo.getStandardName()).append("  ")
    		 .append("标准编码：").append(vo.getStandardCode()).append("  ")
    		 .append("标准分类：").append(vo.getStandardClassNameFull()).append("  ")
    		 .append("业务定义：").append(vo.getDefinition()).append("  ");
    		vo.setMemo(s.toString());
    	}
        return vo;
    }

    /**
     * 查询基础标准列表
     *
     * @param dsmStandard 基础标准
     * @return 基础标准
     */
    @Override
    public List<DsmStandard> selectDsmStandardList(DsmStandard dsmStandard)
    {
        return dsmStandardMapper.selectDsmStandardList(dsmStandard);
    }
    @Override
    public List<DsmStandard> selectDsmStandardListByNo(DsmStandard dsmStandard)
    {
        return dsmStandardMapper.selectDsmStandardListByNo(dsmStandard);
    }
	@Override
	public List<DsmStandardVo> selectDsmStandardVoList(DsmStandardVo dsmStandardVo)
	{
		List<DsmStandardVo> list = dsmStandardMapper.selectDsmStandardVoList(dsmStandardVo);

		SysDept sysDept = new SysDept();
		List<SysDept> sysDepts = deptMapper.selectDeptList(sysDept);
		Map<String, String> deptMap = sysDepts.stream().collect(Collectors.toMap(p -> p.getDeptId().toString(), p -> p.getDeptName(), (k1, k2) -> k1));

		for (DsmStandardVo vo:list) {
			String standardId=vo.getStandardId().toString();
			vo.setSrcSystemNames(dsmStandardMapper.getExportSystemSrcName(standardId));
			vo.setTarSystemNames(dsmStandardMapper.getExportSystemTarName(standardId));

			List<SysDept> exportDeptSrcList = dsmStandardMapper.getExportDeptSrcList(standardId);
			String srcDeptNames = "";
			for(SysDept dept : exportDeptSrcList){
				if(dept==null){ break; }
				String srcDeptName = "";
				String ancestors = dept.getAncestors();
				if(StringUtils.isNotNull(ancestors)&&(!"0".equals(ancestors))){
					String[] split = ancestors.split(",");
					for (String s : split){
						if("0".equals(s)){
							continue;
						}else{
							srcDeptName += deptMap.get(s) + "-";
						}
					}
				}
				srcDeptNames += srcDeptName + dept.getDeptName() + ";\r\n";
			}
			vo.setSrcDeptNames(srcDeptNames);


			List<SysDept> exportDeptTarList = dsmStandardMapper.getExportDeptTarList(standardId);
			String tarDeptNames = "";
			for(SysDept dept : exportDeptTarList){
				if(dept==null){ break; }
				String tarDeptName = "";
				String ancestors = dept.getAncestors();
				if(StringUtils.isNotNull(ancestors)&&(!"0".equals(ancestors))){
					String[] split = ancestors.split(",");
					for (String s : split){
						if("0".equals(s)){
							continue;
						}else{
							tarDeptName += deptMap.get(s) + "-";
						}
					}
				}
				tarDeptNames += tarDeptName + dept.getDeptName() + ";\r\n";
			}
			vo.setTarDeptNames(tarDeptNames);
		}
		return list;
	}
    /**
     * 新增基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDsmStandard(DsmStandard dsmStandard,String oldId)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmStandard.setCreateTime(DateUtils.getNowDate());
    	dsmStandard.setCreateId(loginUser.getUser().getUserId());
    	dsmStandard.setCreateBy(loginUser.getUser().getNickName());
    	dsmStandard.setUpdateTime(DateUtils.getNowDate());
    	dsmStandard.setUpdateId(loginUser.getUser().getUserId());
    	dsmStandard.setUpdateBy(loginUser.getUser().getNickName());
    	dsmStandard.setStatus(DsmConstants.DSM_STATUS_RUNNING);
		List<DsmStandard> tName=dsmStandardMapper.listDsmStandardByName(dsmStandard);
		if(tName!=null&&tName.size()!=0){
			throw new RuntimeException("数据标准名称已经存在！");
		}
		List<DsmStandard> tCode=dsmStandardMapper.listDsmStandardByCode(dsmStandard);
		if(tCode!=null&&tCode.size()!=0){
			throw new RuntimeException("数据标准编码已经存在！");
		}

    	int i = dsmStandardMapper.insertDsmStandard(dsmStandard);

    	if(dsmStandard.getSrcDeptIds()!=null&&dsmStandard.getSrcDeptIds().length>0) {
    		for(long srcDeptId : dsmStandard.getSrcDeptIds()) {
    			DsmStandardSrcDept dsmStandardSrcDept = new DsmStandardSrcDept();
    			dsmStandardSrcDept.setDeptId(srcDeptId);
    			dsmStandardSrcDept.setStandardId(dsmStandard.getStandardId());
    			dsmStandardSrcDeptMapper.insertDsmStandardSrcDept(dsmStandardSrcDept);
    		}
    	}
    	if(dsmStandard.getTarDeptIds()!=null&&dsmStandard.getTarDeptIds().length>0) {
    		for(long tarDeptId : dsmStandard.getTarDeptIds()) {
    			DsmStandardTarDept dsmStandardTarDept = new DsmStandardTarDept();
    			dsmStandardTarDept.setDeptId(tarDeptId);
    			dsmStandardTarDept.setStandardId(dsmStandard.getStandardId());
    			dsmStandardTarDeptMapper.insertDsmStandardTarDept(dsmStandardTarDept);
    		}
    	}
    	if(dsmStandard.getSrcSystemIds()!=null&&dsmStandard.getSrcSystemIds().length>0) {
    		for(long srcSystemId : dsmStandard.getSrcSystemIds()) {
    			DsmStandardSrcSystem dsmStandardSrcSystem = new DsmStandardSrcSystem();
    			dsmStandardSrcSystem.setSystemId(srcSystemId);
    			dsmStandardSrcSystem.setStandardId(dsmStandard.getStandardId());
    			dsmStandardSrcSystemMapper.insertDsmStandardSrcSystem(dsmStandardSrcSystem);
    		}
    	}
    	if(dsmStandard.getTarSystemIds()!=null&&dsmStandard.getTarSystemIds().length>0) {
    		for(long tarSystemId : dsmStandard.getTarSystemIds()) {
    			DsmStandardTarSystem dsmStandardTarSystem = new DsmStandardTarSystem();
    			dsmStandardTarSystem.setSystemId(tarSystemId);
    			dsmStandardTarSystem.setStandardId(dsmStandard.getStandardId());
    			dsmStandardTarSystemMapper.insertDsmStandardTarSystem(dsmStandardTarSystem);
    		}
    	}
    	if(dsmStandard.getPropIds()!=null&&dsmStandard.getPropIds().length>0) {
    		for(long propId : dsmStandard.getPropIds()) {
    			DsmStandardMdmRel dsmStandardMdmRel = new DsmStandardMdmRel();
    			dsmStandardMdmRel.setPropId(propId);
    			dsmStandardMdmRel.setStandardId(dsmStandard.getStandardId());
    			dsmStandardMdmRelMapper.insertDsmStandardMdmRel(dsmStandardMdmRel);
    		}
    	}
    	DamAssets damAssets = new DamAssets();
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	if(StringUtils.isNotEmpty(oldId)) {
        		damAssets = damAssetsService.selectDamAssetsByRel(oldId+"", DamConstants.DSM_TYPE_STANDARD);
            	if(damAssets!=null) {
            		damAssets.setAssetsName(dsmStandard.getStandardName());
                    damAssets.setAssetsType(DamConstants.DSM_TYPE_STANDARD);
                    damAssets.setAssetsCode(DamConstants.DSM_TYPE_STANDARD+dsmStandard.getStandardCode());
                    damAssets.setRelId(dsmStandard.getStandardId()+"");
            		StringBuffer s = new StringBuffer();
            		s.append("标准名称：").append(dsmStandard.getStandardName()).append("  ")
            		 .append("标准编码：").append(dsmStandard.getStandardCode()).append("  ")
            		 .append("业务定义：").append(dsmStandard.getDefinition()).append("  ");
            		damAssets.setRemark(s.toString());
            		damAssetsService.updateDamAssets(damAssets);
            	}
        	}else {
            	damAssets.setAssetsName(dsmStandard.getStandardName());
                damAssets.setAssetsType(DamConstants.DSM_TYPE_STANDARD);
                damAssets.setAssetsCode(DamConstants.DSM_TYPE_STANDARD+dsmStandard.getStandardCode());
                damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
                damAssets.setRelId(dsmStandard.getStandardId()+"");
        		StringBuffer s = new StringBuffer();
        		s.append("标准名称：").append(dsmStandard.getStandardName()).append("  ")
        		 .append("标准编码：").append(dsmStandard.getStandardCode()).append("  ")
        		 .append("业务定义：").append(dsmStandard.getDefinition()).append("  ");
        		damAssets.setRemark(s.toString());
                damAssetsService.insertDamAssets(damAssets);
        	}

        }
        String activity = sysConfigService.selectConfigByKey("dsm.dsmStandard.activity");
        if(StringUtils.isNotEmpty(activity)&&activity.equals("TRUE")) {
    		String join =StringUtils.join(sysUserService.selectUserNameByPostCodeAndDeptId("se",SecurityUtils.getLoginUser().getUser().getDeptId()), ",");
			ProcessInstance processInstance = processRuntime.start(ProcessPayloadBuilder.start()
				.withProcessDefinitionKey("dsmStandard") .withName(dsmStandard.getStandardName())
				.withBusinessKey(dsmStandard.getStandardId().toString())
				.withVariable("deptLeader",join) .build());
			DsmStandard ds=new DsmStandard();
			ds.setStandardId(dsmStandard.getStandardId());
			ds.setInstanceId(processInstance.getId());
			dsmStandardMapper.updateDsmStandard(ds);
    	}

        return i;
    }

    /**
     * 修改基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDsmStandard(DsmStandard dsmStandard)
    {
    	DsmStandard dsmStandardOld=dsmStandardMapper.selectDsmStandardByNo(dsmStandard.getStandardNo());
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmStandardOld.setUpdateTime(DateUtils.getNowDate());
    	dsmStandardOld.setUpdateId(loginUser.getUser().getUserId());
    	dsmStandardOld.setUpdateBy(loginUser.getUser().getNickName());
    	dsmStandardOld.setStatus(DsmConstants.DSM_STATUS_INVALID);
		List<DsmStandard> tName=dsmStandardMapper.listDsmStandardByName(dsmStandard);
		if(tName.size()>1){
			throw new RuntimeException("数据标准名称已经存在！");
		}else if(tName.size()==1){
			if(!tName.get(0).getStandardId().equals(dsmStandard.getStandardId())){
				throw new RuntimeException("数据标准名称已经存在！");
			}
		}
		List<DsmStandard> tCode=dsmStandardMapper.listDsmStandardByCode(dsmStandard);
		if(tCode.size()>1){
			throw new RuntimeException("数据标准编码已经存在！");
		}else if(tCode.size()==1){
			if(!tCode.get(0).getStandardId().equals(dsmStandard.getStandardId())){
				throw new RuntimeException("数据标准编码已经存在！");
			}
		}
		if(dsmStandard.getStatus().equals(DsmConstants.DSM_STATUS_INVALID)) {
			DamAssets damAssets = new DamAssets();
			String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
			if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
				damAssets = damAssetsService.selectDamAssetsByRel(dsmStandard.getStandardId() + "", DamConstants.DSM_TYPE_STANDARD);
				if (damAssets != null) {
					damAssets.setAssetsName(dsmStandard.getStandardName());
					damAssets.setAssetsType(DamConstants.DSM_TYPE_STANDARD);
					damAssets.setAssetsCode(DamConstants.DSM_TYPE_STANDARD + dsmStandard.getStandardCode());
					damAssets.setRelId(dsmStandard.getStandardId() + "");
					damAssets.setStatus(dsmStandard.getStatus());
					StringBuffer s = new StringBuffer();
					s.append("标准名称：").append(dsmStandard.getStandardName()).append("  ")
							.append("标准编码：").append(dsmStandard.getStandardCode()).append("  ")
							.append("业务定义：").append(dsmStandard.getDefinition()).append("  ");
					damAssets.setRemark(s.toString());
					damAssetsService.updateDamAssets(damAssets);
				}
			}
			//将历史版本置为废弃
			return dsmStandardMapper.updateDsmStandard(dsmStandardOld);
		}
		else {
			//将历史版本置为废弃
			dsmStandardMapper.updateDsmStandard(dsmStandardOld);
			//版本号更新
			dsmStandard.setVersion(dsmStandardOld.getVersion() + 1);
			dsmStandard.setStandardNo(dsmStandardOld.getStandardNo());
			dsmStandard.setStatus(dsmStandardOld.getStatus());

			if (dsmStandard.getSrcSystemIds() == null || dsmStandard.getSrcSystemIds().length <= 0) {
				List<DsmStandardSrcSystem> dsssList = dsmStandardSrcSystemMapper.selectDsmStandardSrcSystemById(dsmStandardOld.getStandardId());
				Long[] srcSystemIds = new Long[dsssList.size()];
				for (int i = 0; i < dsssList.size(); i++) {
					srcSystemIds[i] = dsssList.get(i).getSystemId();
				}
				dsmStandard.setSrcSystemIds(srcSystemIds);
			}
			if (dsmStandard.getTarSystemIds() == null || dsmStandard.getTarSystemIds().length <= 0) {
				List<DsmStandardTarSystem> dstsList = dsmStandardTarSystemMapper.selectDsmStandardTarSystemById(dsmStandardOld.getStandardId());
				Long[] tarSystemIds = new Long[dstsList.size()];
				for (int i = 0; i < dstsList.size(); i++) {
					tarSystemIds[i] = dstsList.get(i).getSystemId();
				}
				dsmStandard.setTarSystemIds(tarSystemIds);
			}
			if (dsmStandard.getSrcDeptIds() == null || dsmStandard.getSrcDeptIds().length <= 0) {
				List<DsmStandardSrcDept> dssdList = dsmStandardSrcDeptMapper.selectDsmStandardSrcDeptById(dsmStandardOld.getStandardId());
				Long[] srcDeptIds = new Long[dssdList.size()];
				for (int i = 0; i < dssdList.size(); i++) {
					srcDeptIds[i] = dssdList.get(i).getDeptId();
				}
				dsmStandard.setSrcDeptIds(srcDeptIds);
			}
			if (dsmStandard.getTarDeptIds() == null || dsmStandard.getTarDeptIds().length <= 0) {
				List<DsmStandardTarDept> dstdList = dsmStandardTarDeptMapper.selectDsmStandardTarDeptById(dsmStandardOld.getStandardId());
				Long[] tarDeptIds = new Long[dstdList.size()];
				for (int i = 0; i < dstdList.size(); i++) {
					tarDeptIds[i] = dstdList.get(i).getDeptId();
				}
				dsmStandard.setTarDeptIds(tarDeptIds);
			}
			return this.insertDsmStandard(dsmStandard, dsmStandardOld.getStandardId() + "");
		}
    }


	/**
	 * 审批修改基础标准
	 *
	 * @param dsmStandard 基础标准
	 * @return 结果
	 */
	@Override
	public int updateStatusDsmStandard(DsmStandard dsmStandard)
	{
		return dsmStandardMapper.updateDsmStandard(dsmStandard);
	}
	@Override
	public int updatedsmStandardStatus(DsmStandard dsmStandard)
	{
		return dsmStandardMapper.updatedsmStandardStatus(dsmStandard);
	}



    /**
     * 批量删除基础标准
     *
     * @param standardIds 需要删除的基础标准ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDsmStandardByIds(Long[] standardIds)
    {
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	for(Long standardId : standardIds ) {
        		DamAssets d = damAssetsService.selectDamAssetsByRel(standardId+"", DamConstants.DSM_TYPE_STANDARD);
            	if(d!=null) {
            		damAssetsService.deleteDamAssetsById(d.getDamAssetsId());
            	}
        	}
        }
        int x=0;
        for(int i=0;i<standardIds.length;i++) {
			DsmStandard dsmStandard =dsmStandardMapper.selectDsmStandardById(standardIds[i]);
			x+=dsmStandardMapper.deleteDsmStandardByNo(dsmStandard.getStandardNo());
		}
        return x;
    }

    /**
     * 删除基础标准信息
     *
     * @param standardId 基础标准ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDsmStandardById(Long standardId)
    {
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	DamAssets d = damAssetsService.selectDamAssetsByRel(standardId+"", DamConstants.DSM_TYPE_STANDARD);
        	if(d!=null) {
        		damAssetsService.deleteDamAssetsById(d.getDamAssetsId());
        	}
        }
        return dsmStandardMapper.deleteDsmStandardById(standardId);
    }
	/**
	 * 导入任务数据
	 *
	 * @param etlTaskRelationList 任务数据列表
	 * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
	 * @return 结果
	 */
	@Override
	@Transactional
	public String importDsmStandard(List<DsmStandardVo> dsmStandardList, Boolean isUpdateSupport)
	{
		//获取当前登陆用户
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		if (StringUtils.isNull(dsmStandardList) || dsmStandardList.size() == 0)
		{
			throw new CustomException("导入任务数据不能为空！");
		}
		int successNum = 0;
		int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		for (DsmStandardVo vo : dsmStandardList)
		{
			try {
				DsmStandard t = new DsmStandard();
				List<Long> srcSystemIds=new ArrayList<>();
				List<Long> tarSystemIds=new ArrayList<>();
				Set<Long> srcDeptIds=new HashSet<>();
				Set<Long> tarDeptIds=new HashSet<>();
				BeanUtils.copyBeanProp(t, vo);
				//校验code或name是否存在
				DsmStandard dsmStandard = dsmStandardMapper.selectDsmStandardByIdOrName(vo.getStandardCode(),vo.getStandardName());
				if (StringUtils.isNull(dsmStandard) || (StringUtils.isNotNull(dsmStandard) && isUpdateSupport)) {
					DsmStandardClass dsmStandardClass = dsmStandardClassMapper.selectDsmStandardClassByName(vo.getStandardClassName());
					if (StringUtils.isNull(dsmStandardClass)) {
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、基础标准 " + vo.getStandardClassName() + " 分类名称未定义");
						continue;
					}
					t.setStandardClassId(dsmStandardClass.getStandardClassId());
					if(StringUtils.isEmpty(vo.getStatus())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、基础标准 " + vo.getStatus() + " 状态为空");
						continue;
					}
					/*
					if(StringUtils.isEmpty(vo.getDataType())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、基础标准 " + vo.getDataType() + " 数据类型为空");
						continue;
					}
					if(StringUtils.isEmpty(vo.getIsPriKey())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、基础标准 " + vo.getIsPriKey() + " 是否主键为空");
						continue;
					}
					if(StringUtils.isEmpty(vo.getNullable())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、基础标准 " + vo.getNullable() + " 是否为空为空");
						continue;
					}
					*/
					String[] srcSystemNames=vo.getSrcSystemNames().split(",");
					Boolean srcSystemBool=true;
					for (String srcSystemName:srcSystemNames) {
						if(StringUtils.isNotEmpty(srcSystemName)){
							SysSystem sysSystem = sysSystemMapper.selectSysSystemByName(srcSystemName);
							if(StringUtils.isNull(sysSystem)){
								failureNum++;
								failureMsg.append("<br/>" + failureNum + "、基础标准 " + srcSystemName + " 来源系统名称未定义");
								srcSystemBool=false;
								continue;
							}
							srcSystemIds.add(sysSystem.getSystemId());
						}
					}
					if(!srcSystemBool){
						continue;
					}
					String[] tarSystemNames=vo.getTarSystemNames().split(",");
					Boolean tarSystemBool=true;
					for (String tarSystemName:tarSystemNames) {
						if(StringUtils.isNotEmpty(tarSystemName)){
							SysSystem sysSystem = sysSystemMapper.selectSysSystemByName(tarSystemName);
							if(StringUtils.isNull(sysSystem)){
								failureNum++;
								failureMsg.append("<br/>" + failureNum + "、基础标准 " + tarSystemName + " 应用系统名称未定义");
								tarSystemBool=false;
								continue;
							}
							tarSystemIds.add(sysSystem.getSystemId());
						}
					}
					if(!tarSystemBool){
						continue;
					}
					String[] srcDeptNames=vo.getSrcDeptNames().replaceAll("\r\n","").split(";");
					Boolean srcDeptNameBool=true;
					for (String srcDeptName:srcDeptNames) {
						if(srcDeptName.trim().length()==0){break;}
						Long parentId1= 0L;
						String[] deptName = srcDeptName.split("-");
						for(int i=0 ; i<deptName.length; i++){
							String name = deptName[i];
							SysDept info = deptMapper.checkDeptNameUnique(name, parentId1);
							if(StringUtils.isNull(info)){
								failureNum++;
								failureMsg.append("<br/>" + failureNum + "、基础标准 " + name + "来源部门名称未定义");
								srcDeptNameBool=false;
								continue;
							}else if(i == deptName.length-1){
								parentId1=info.getDeptId();
								srcDeptIds.add(parentId1);
							}else{
								parentId1=info.getDeptId();
							}
						}
					}
					if(!srcDeptNameBool){
						continue;
					}
					String[] tarDeptNames=vo.getTarDeptNames().replaceAll("\r\n","").split(";");
					Boolean tarDeptNameBool=true;
					for (String tarDeptName:tarDeptNames) {
						if(tarDeptName.trim().length()==0){break;}
						Long parentId2= 0L;
						String[] deptName = tarDeptName.split("-");
						for(int i=0 ; i<deptName.length; i++){
							String name = deptName[i];
							SysDept info = deptMapper.checkDeptNameUnique(name, parentId2);
							if(StringUtils.isNull(info)){
								failureNum++;
								failureMsg.append("<br/>" + failureNum + "、基础标准 " + name + "应用部门名称未定义");
								tarDeptNameBool=false;
								continue;
							}else if(i == deptName.length-1){
								parentId2=info.getDeptId();
								tarDeptIds.add(parentId2);
							}else{
								parentId2=info.getDeptId();
							}
						}
					}
					if(!tarDeptNameBool){
						continue;
					}
				}
				if (StringUtils.isNull(dsmStandard)) {
					t.setCreateTime(DateUtils.getNowDate());
					t.setCreateId(loginUser.getUser().getUserId());
					t.setCreateBy(loginUser.getUser().getNickName());
					t.setUpdateTime(DateUtils.getNowDate());
					t.setUpdateId(loginUser.getUser().getUserId());
					t.setUpdateBy(loginUser.getUser().getNickName());
					t.setStandardNo(UUID.randomUUID().toString());
			        t.setVersion(1L);
			        t.setStandardNo(UUID.randomUUID().toString());
					this.insertDsmStandard(t,null);
					for (Long srcSystemId:srcSystemIds) {
						DsmStandardSrcSystem dsmStandardSrcSystem = new DsmStandardSrcSystem();
						dsmStandardSrcSystem.setSystemId(srcSystemId);
						dsmStandardSrcSystem.setStandardId(t.getStandardId());
						dsmStandardSrcSystemMapper.insertDsmStandardSrcSystem(dsmStandardSrcSystem);
					}
					for (Long tarSystemId:tarSystemIds) {
						DsmStandardTarSystem dsmStandardTarSystem = new DsmStandardTarSystem();
						dsmStandardTarSystem.setSystemId(tarSystemId);
						dsmStandardTarSystem.setStandardId(t.getStandardId());
						dsmStandardTarSystemMapper.insertDsmStandardTarSystem(dsmStandardTarSystem);
					}
					for (Long srcDeptId:srcDeptIds) {
						DsmStandardSrcDept dsmStandardSrcDept = new DsmStandardSrcDept();
						dsmStandardSrcDept.setDeptId(srcDeptId);
						dsmStandardSrcDept.setStandardId(t.getStandardId());
						dsmStandardSrcDeptMapper.insertDsmStandardSrcDept(dsmStandardSrcDept);
					}
					for (Long tarDeptId:tarDeptIds) {
						DsmStandardTarDept dsmStandardTarDept = new DsmStandardTarDept();
						dsmStandardTarDept.setDeptId(tarDeptId);
						dsmStandardTarDept.setStandardId(t.getStandardId());
						dsmStandardTarDeptMapper.insertDsmStandardTarDept(dsmStandardTarDept);
					}
					successNum++;
					successMsg.append("<br/>" + successNum + "、基础标准 " + vo.getStandardName() + " 导入成功");
				} else if (isUpdateSupport) {
					Long standardId = dsmStandard.getStandardId();
					t.setStandardId(standardId);
					t.setUpdateTime(DateUtils.getNowDate());
					t.setUpdateId(loginUser.getUser().getUserId());
					t.setUpdateBy(loginUser.getUser().getNickName());
					t.setStandardNo(dsmStandard.getStandardNo());
					this.updateDsmStandard(t);

					//这里的修改，实际是废除原有的数据再新增一条。来源系统/部门等信息会继承原因数据的，需要删除重写
					dsmStandardSrcSystemMapper.deleteDsmStandardSrcSystemById(t.getStandardId());
					for (Long srcSystemId:srcSystemIds) {
						DsmStandardSrcSystem dsmStandardSrcSystem = new DsmStandardSrcSystem();
						dsmStandardSrcSystem.setSystemId(srcSystemId);
						dsmStandardSrcSystem.setStandardId(t.getStandardId());
						dsmStandardSrcSystemMapper.insertDsmStandardSrcSystem(dsmStandardSrcSystem);
					}

					dsmStandardTarSystemMapper.deleteDsmStandardTarSystemById(t.getStandardId());
					for (Long tarSystemId:tarSystemIds) {
						DsmStandardTarSystem dsmStandardTarSystem = new DsmStandardTarSystem();
						dsmStandardTarSystem.setSystemId(tarSystemId);
						dsmStandardTarSystem.setStandardId(t.getStandardId());
						dsmStandardTarSystemMapper.insertDsmStandardTarSystem(dsmStandardTarSystem);
					}

					dsmStandardSrcDeptMapper.deleteDsmStandardSrcDeptById(t.getStandardId());
					for (Long srcDeptId:srcDeptIds) {
						DsmStandardSrcDept dsmStandardSrcDept = new DsmStandardSrcDept();
						dsmStandardSrcDept.setDeptId(srcDeptId);
						dsmStandardSrcDept.setStandardId(t.getStandardId());
						dsmStandardSrcDeptMapper.insertDsmStandardSrcDept(dsmStandardSrcDept);
					}

					dsmStandardTarDeptMapper.deleteDsmStandardTarDeptById(t.getStandardId());
					for (Long tarDeptId:tarDeptIds) {
						DsmStandardTarDept dsmStandardTarDept = new DsmStandardTarDept();
						dsmStandardTarDept.setDeptId(tarDeptId);
						dsmStandardTarDept.setStandardId(t.getStandardId());
						dsmStandardTarDeptMapper.insertDsmStandardTarDept(dsmStandardTarDept);
					}
					successNum++;
					successMsg.append("<br/>" + successNum + "、基础标准 " + vo.getStandardName() + " 更新成功");
				} else {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、基础标准 "+vo.getStandardCode()+"/" + vo.getStandardName() + " 已存在");
				}
			}
			catch(Exception e)
				{
					failureNum++;
					String msg = "<br/>" + failureNum + "、基础标准 " + vo.getStandardName() + " 导入失败：";
					failureMsg.append(msg + e.getMessage());
				}

		}
		if (failureNum > 0)
		{
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
			throw new CustomException(failureMsg.toString());
		}
		else
		{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
}
