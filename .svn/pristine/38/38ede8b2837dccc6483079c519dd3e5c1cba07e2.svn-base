<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmQuotaMapper">
    
    <resultMap type="DsmQuota" id="DsmQuotaResult">
        <result property="quotaId"    column="quota_id"    />
        <result property="quotaCode"    column="quota_code"    />
        <result property="quotaName"    column="quota_name"    />
        <result property="quotaType"    column="quota_type"    />
        <result property="classId"    column="class_id"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="attachment"    column="attachment"    />
        <result property="definition"    column="definition"    />
        <result property="execSql"    column="exec_sql"    />
        <result property="unit"    column="unit"    />
        <result property="cycle"    column="cycle"    />
        <result property="formula"    column="formula"    />
        <result property="department"    column="department"    />
        <result property="systemId"    column="system_id"    />
        <result property="columnName"    column="column_name"    />
        <result property="columnType"    column="column_type"    />
        <result property="columnSize"    column="column_size"    />
        <result property="columnDecimal"    column="column_decimal"    />
        <result property="nullable"    column="nullable"    />
        <result property="defaultValue"    column="default_value"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="quotaNo"    column="quota_no"    />
        <result property="discernFlag"    column="discern_flag"    />
        <result property="checkFlag"    column="check_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDsmQuotaVo">
        select quota_id, quota_code, quota_name, quota_type, class_id, status, version, attachment, definition, exec_sql, unit, cycle, formula, department, system_id, column_name, column_type, column_size, column_decimal, nullable, default_value, datasource_id, quota_no, discern_flag, check_flag, create_by, update_by, create_id, update_id, create_time, update_time from dsm_quota
    </sql>

    <select id="selectDsmQuotaList" parameterType="DsmQuota" resultMap="DsmQuotaResult">
        <include refid="selectDsmQuotaVo"/>
        <where>  
            <if test="quotaCode != null  and quotaCode != ''"> and quota_code = #{quotaCode}</if>
            <if test="quotaName != null  and quotaName != ''"> and quota_name like concat('%', #{quotaName}, '%')</if>
            <if test="quotaType != null  and quotaType != ''"> and quota_type = #{quotaType}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="attachment != null  and attachment != ''"> and attachment = #{attachment}</if>
            <if test="definition != null  and definition != ''"> and definition = #{definition}</if>
            <if test="execSql != null  and execSql != ''"> and exec_sql = #{execSql}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="cycle != null  and cycle != ''"> and cycle = #{cycle}</if>
            <if test="formula != null  and formula != ''"> and formula = #{formula}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="columnName != null  and columnName != ''"> and column_name = #{columnName}</if>
            <if test="columnType != null  and columnType != ''"> and column_type = #{columnType}</if>
            <if test="columnSize != null "> and column_size = #{columnSize}</if>
            <if test="columnDecimal != null "> and column_decimal = #{columnDecimal}</if>
            <if test="nullable != null  and nullable != ''"> and nullable = #{nullable}</if>
            <if test="defaultValue != null  and defaultValue != ''"> and default_value = #{defaultValue}</if>
            <if test="datasourceId != null "> and datasource_id = #{datasourceId}</if>
        </where>
    </select>
    
    <select id="selectDsmQuotaById" parameterType="Long" resultMap="DsmQuotaResult">
        <include refid="selectDsmQuotaVo"/>
        where quota_id = #{quotaId}
    </select>
    
    <select id="selectDsmQuotaByCode" parameterType="DsmQuota" resultMap="DsmQuotaResult">
        <include refid="selectDsmQuotaVo"/>
        where quota_code = #{quotaCode}
        <if test="quotaNo != null  and quotaNo != ''"> and quota_no != #{quotaNo}</if>
    </select>
     
    <select id="selectDsmQuotaByName" parameterType="DsmQuota" resultMap="DsmQuotaResult">
        <include refid="selectDsmQuotaVo"/>
        where quota_name = #{quotaName}
        <if test="quotaNo != null  and quotaNo != ''"> and quota_no != #{quotaNo}</if>
    </select>
           
    <insert id="insertDsmQuota" parameterType="DsmQuota" useGeneratedKeys="true" keyProperty="quotaId">
        insert into dsm_quota
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quotaCode != null and quotaCode != ''">quota_code,</if>
            <if test="quotaName != null and quotaName != ''">quota_name,</if>
            <if test="quotaType != null and quotaType != ''">quota_type,</if>
            <if test="classId != null">class_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="version != null">version,</if>
            <if test="attachment != null">attachment,</if>
            <if test="definition != null">definition,</if>
            <if test="execSql != null">exec_sql,</if>
            <if test="unit != null">unit,</if>
            <if test="cycle != null">cycle,</if>
            <if test="formula != null">formula,</if>
            <if test="department != null">department,</if>
            <if test="systemId != null">system_id,</if>
            <if test="columnName != null">column_name,</if>
            <if test="columnType != null">column_type,</if>
            <if test="columnSize != null">column_size,</if>
            <if test="columnDecimal != null">column_decimal,</if>
            <if test="nullable != null">nullable,</if>
            <if test="defaultValue != null">default_value,</if>
            <if test="datasourceId != null">datasource_id,</if>
            <if test="quotaNo != null and quotaNo != ''">quota_no,</if>
            <if test="discernFlag != null">discern_flag,</if>
            <if test="checkFlag != null">check_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quotaCode != null and quotaCode != ''">#{quotaCode},</if>
            <if test="quotaName != null and quotaName != ''">#{quotaName},</if>
            <if test="quotaType != null and quotaType != ''">#{quotaType},</if>
            <if test="classId != null">#{classId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="definition != null">#{definition},</if>
            <if test="execSql != null">#{execSql},</if>
            <if test="unit != null">#{unit},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="formula != null">#{formula},</if>
            <if test="department != null">#{department},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="columnName != null">#{columnName},</if>
            <if test="columnType != null">#{columnType},</if>
            <if test="columnSize != null">#{columnSize},</if>
            <if test="columnDecimal != null">#{columnDecimal},</if>
            <if test="nullable != null">#{nullable},</if>
            <if test="defaultValue != null">#{defaultValue},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="quotaNo != null and quotaNo != ''">#{quotaNo},</if>
            <if test="discernFlag != null">#{discernFlag},</if>
            <if test="checkFlag != null">#{checkFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDsmQuota" parameterType="DsmQuota">
        update dsm_quota
        <trim prefix="SET" suffixOverrides=",">
            <if test="quotaCode != null and quotaCode != ''">quota_code = #{quotaCode},</if>
            <if test="quotaName != null and quotaName != ''">quota_name = #{quotaName},</if>
            <if test="quotaType != null and quotaType != ''">quota_type = #{quotaType},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="definition != null">definition = #{definition},</if>
            <if test="execSql != null">exec_sql = #{execSql},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="formula != null">formula = #{formula},</if>
            <if test="department != null">department = #{department},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="columnName != null">column_name = #{columnName},</if>
            <if test="columnType != null">column_type = #{columnType},</if>
            <if test="columnSize != null">column_size = #{columnSize},</if>
            <if test="columnDecimal != null">column_decimal = #{columnDecimal},</if>
            <if test="nullable != null">nullable = #{nullable},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
            <if test="quotaNo != null and quotaNo != ''">quota_no = #{quotaNo},</if>
            <if test="discernFlag != null">discern_flag = #{discernFlag},</if>
            <if test="checkFlag != null">check_flag = #{checkFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where quota_id = #{quotaId}
    </update>

    <delete id="deleteDsmQuotaById" parameterType="Long">
        delete from dsm_quota where quota_id = #{quotaId}
    </delete>

    <delete id="deleteDsmQuotaByIds" parameterType="String">
        delete from dsm_quota where quota_id in 
        <foreach item="quotaId" collection="array" open="(" separator="," close=")">
            #{quotaId}
        </foreach>
    </delete>
</mapper>