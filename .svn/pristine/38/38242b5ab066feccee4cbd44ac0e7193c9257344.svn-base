package com.dqms.task.service;

import java.util.List;

import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskRelation;
import com.dqms.task.domain.vo.EtlTaskVo;

/**
 * 任务关系Service接口
 * 
 * <AUTHOR>
 * @date 2021-03-12
 */
public interface IEtlTaskRelationService 
{
    /**
     * 查询任务关系
     * 
     * @param taskRelationId 任务关系ID
     * @return 任务关系
     */
    public EtlTaskRelation selectEtlTaskRelationById(Long taskRelationId);


    /**
     * 查询任务关系列表
     * 
     * @param etlTaskRelation 任务关系
     * @return 任务关系集合
     */
    public List<EtlTaskRelation> selectEtlTaskRelationList(EtlTaskRelation etlTaskRelation);
    public List<EtlTaskRelation> selectEtlTaskInstanceRelationList(EtlTaskRelation etlTaskRelation);
    public List<EtlTaskRelation> exportEtlTaskRelationList(EtlTask etlTask);


    /**
     * 新增任务关系
     * 
     * @param etlTaskRelation 任务关系
     * @return 结果
     */
    public int insertEtlTaskRelation(EtlTaskRelation etlTaskRelation);

    /**
     * 修改任务关系
     * 
     * @param etlTaskRelation 任务关系
     * @return 结果
     */
    public int updateEtlTaskRelation(EtlTaskRelation etlTaskRelation);

    /**
     * 批量删除任务关系
     * 
     * @param taskRelationIds 需要删除的任务关系ID
     * @return 结果
     */
    public int deleteEtlTaskRelationByIds(Long[] taskRelationIds);

    /**
     * 删除任务关系信息
     * 
     * @param taskRelationId 任务关系ID
     * @return 结果
     */
    public int deleteEtlTaskRelationById(Long taskRelationId);
    
    /**
     * 删除任务关系
     * 
     * @param etlTaskRelation 需要删除的任务关系
     * @return 结果
     */
    public int delTaskRelationByNode(EtlTaskRelation etlTaskRelation);
    
    /**
     * 导入任务数据
     * 
     * @param etlTaskRelationList 任务数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importEtlTaskRelation(List<EtlTaskRelation> etlTaskRelationList, Boolean isUpdateSupport);
}
