package com.dqms.dsm.domain;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 主数据对象 dsm_master_data
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
public class DsmMasterData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主数据ID */
    private Long masterDataId;

    /** 名称 */
    @Excel(name = "名称")
    private String masterDataName;

    /** 编码 */
    @Excel(name = "编码")
    private String masterDataCode;

    /** 类型 */
    @Excel(name = "类型")
    private String masterDataType;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    /** 分类 */
    @Excel(name = "分类")
    private String masterDataClassId; 
    private DsmMasterDataClass masterDataClass;
    
    /** 模型实例 */
    @Excel(name = "模型实例")
    private String modelEntityId; 
    private DsmModelEntity dsmModelEntity;
    
    private List<DsmMasterDataInstall> dsmMasterDataInstalls;

    public void setMasterDataId(Long masterDataId)
    {
        this.masterDataId = masterDataId;
    }

    public Long getMasterDataId()
    {
        return masterDataId;
    }
    public void setMasterDataName(String masterDataName)
    {
        this.masterDataName = masterDataName;
    }

    public String getMasterDataName()
    {
        return masterDataName;
    }
    public void setMasterDataCode(String masterDataCode)
    {
        this.masterDataCode = masterDataCode;
    }

    public String getMasterDataCode()
    {
        return masterDataCode;
    }
    public void setMasterDataType(String masterDataType)
    {
        this.masterDataType = masterDataType;
    }

    public String getMasterDataType()
    {
        return masterDataType;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public String getMasterDataClassId() {
		return masterDataClassId;
	}

	public void setMasterDataClassId(String masterDataClassId) {
		this.masterDataClassId = masterDataClassId;
	}

	public DsmMasterDataClass getMasterDataClass() {
		return masterDataClass;
	}

	public void setMasterDataClass(DsmMasterDataClass masterDataClass) {
		this.masterDataClass = masterDataClass;
	}

	public String getModelEntityId() {
		return modelEntityId;
	}

	public void setModelEntityId(String modelEntityId) {
		this.modelEntityId = modelEntityId;
	}

	public DsmModelEntity getDsmModelEntity() {
		return dsmModelEntity;
	}

	public void setDsmModelEntity(DsmModelEntity dsmModelEntity) {
		this.dsmModelEntity = dsmModelEntity;
	}

	public List<DsmMasterDataInstall> getDsmMasterDataInstalls() {
		return dsmMasterDataInstalls;
	}

	public void setDsmMasterDataInstalls(List<DsmMasterDataInstall> dsmMasterDataInstalls) {
		this.dsmMasterDataInstalls = dsmMasterDataInstalls;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("masterDataId", getMasterDataId())
            .append("masterDataName", getMasterDataName())
            .append("masterDataCode", getMasterDataCode())
            .append("masterDataType", getMasterDataType())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
