<template>
  <div class="app-container" style="overflow:auto">
    <el-row :gutter="20">
      <el-col :span="5" :xs="24">
        <div class="head-container">
          <el-input
            v-model="dbName"
            placeholder="请输入数据源/表名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container head-container2">
          <el-tree
            class="flow-tree"
            :data="dataSourceOptions"
            :props="defaultProps"
            node-key="id"
            accordion
            :expand-on-click-node="true"
            :load="loadNode"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            ref="dbtree"
            lazy
          >
            <span slot-scope="{ node, data }">
              <span v-if="data.icon == 'iconOne'">
                <i class="el-icon-folder-opened"></i>{{ node.data.code }}_{{ node.label }}
              </span>
              <span v-if="data.icon == 'iconTwo'">
                <i class="el-icon-document"></i>{{ node.label }}
              </span>
              <span v-if="data.icon == 'iconThree'">
                <i class="el-icon-document"></i>{{ node.label }}
              </span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="19" :xs="24" class="sqlText">
        <div class="sqlBtn">
          <el-button
            class="runBtn"
            type="primary"
            icon="el-icon-caret-right"
            size="mini"
            :loading="runLoading"
            @click="handleRestart(true)"
            >运行</el-button
          >
          <el-button
            class="beatyBtn"
            type="success"
            icon="el-icon-magic-stick"
            size="mini"
            @click="sqlFormat"
            >美化</el-button
          >
          <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              :disabled="tableData.length==0"
              @click="handleExport"
              >导出</el-button
            >
          <span style="margin-left: 10px;font-size: 14px">每页显示</span>
          <el-select
            placeholder="请选择"
            size="small"
            v-model="pageSize"
            style="margin-left: 5px;width: 100px;"
            @change="pageSizeChange"
          >
            <el-option :key="20" :label="20" :value="20"></el-option>
            <el-option :key="50" :label="50" :value="50"></el-option>
            <el-option :key="100" :label="100" :value="100"></el-option>
            <el-option :key="200" :label="200" :value="200"></el-option>
          </el-select>
          <span style="margin-left: 10px;font-size: 14px">最大查询数</span>
			{{dataMax}}
        </div>
        <div style="font-size: 13px">
          <div
            class="spanStyle"
            :title="
              '用户:' +
                this.datasource.username +
                ' 连接信息:' +
                this.datasource.url
            "
          >
            <span style="font-weight: bold">用户: </span>
            {{ this.datasource.username }}
            <span style="margin-left:20px;font-weight: bold"> 连接信息:</span>
            {{ this.datasource.url }}
          </div>
        </div>
        <!-- <codemirror ref="myCm" v-model="sqlText" :options="cmOptions" @ready="onCmReady" @focus="onCmFocus" @input="onCmCodeChange"></codemirror> -->
        <div id="sqlQueryCol">
          <textarea
            ref="myCm"
            style="height: calc(100vh - 432px);"
            id="sqlQueryText"
            name="sqlQueryText"
          ></textarea>
        </div>
        <!-- <el-tabs type="border-card" v-if="typeof (sqlInfo.hasResult) != 'undefined'"> -->
        <!-- <el-tab-pane label="信息" v-if="!sqlInfo.hasResult && !sqlInfo.result"
            >ERROR>> {{ sqlInfo.msg }}</el-tab-pane
          >
          <el-tab-pane label="信息" v-if="!sqlInfo.hasResult && sqlInfo.result"
            >INFO>> {{ sqlInfo.msg }}</el-tab-pane
          >
          <el-tab-pane label="结果" v-if="sqlInfo.hasResult">
            <el-table :data="sqlInfo.resList" height="200" style="width: 100%">
              <el-table-column
                v-for="(item, ind) in sqlInfo.header"
                :key="ind"
                :prop="item"
                :label="item"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs> -->
        <el-tabs type="border-card" ref="tabs" v-model="activeName">
          <el-tab-pane label="结果" v-if="tableData.length != 0" name="first">
            <el-table border
                      :data="tableData2" style="width: 100%"
                      :row-style="{height:0}"
                      :cell-style="{padding:0}"
            >
              <el-table-column
                v-for="(item, key, index) in tableData[0]"
                :key="index"
                :prop="key"
                :label="key"
                align="center"
                header-align="center"
                :show-overflow-tooltip="true"
                :min-width="
                  key.length * 12 < (table[key]+20)
                    ? (table[key]+20)
                    : key.length * 12
                "
              >
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="信息" v-if="infoData.length != 0" name="second">
            <el-table border :data="infoData" style="width: 100%">
              <el-table-column
                v-for="(item, key, index) in infoData[0]"
                :key="index"
                :prop="key"
                :label="key"
                align="center"
                header-align="center"
                :min-width="
                  key.length * 12 < (info[key]+20)
                    ? (info[key]+20 > 500
                      ? 500
                      : info[key]+20)
                    : key.length * 12
                "
              >
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <!--          <el-tab-pane label="信息">INFO>> {{ sqlInfo.msg }}</el-tab-pane>-->
        </el-tabs>
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="paginationInfo.currentPage"
          :page-size="pageSize"
          :hide-on-single-page="true"
          layout="total, prev, pager, next, jumper"
          :total="paginationInfo.total"
          style="text-align:right">
        </el-pagination>

        <div class="paginationBox" v-if="sqlInfo.hasResult">
          <span>
            展示
            <!-- <span v-if="this.total"> {{this.pageBean.page == 1 ? 1 : (this.pageBean.page - 1) * 10 + 1}} -</span> -->
            {{ sqlInfo.resList.length }} 条数据 ， 总共
            {{ sqlInfo.total }} 条记录。
          </span>
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-size="queryParams.pageSize"
            :total="sqlInfo.total"
            layout="prev, pager, next"
            style="float: right"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import JsExportExcel from 'js-export-excel'; // 纯前端导出
import {
  findTablesByDS,
  excuteSql,
  listDatasourceTreeAll,getDataMax
} from "@/api/basic/datasource";

import CodeMirror from "codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/merge/merge.js";
import "codemirror/addon/merge/merge.css";
import "codemirror/addon/hint/show-hint.css";
import "codemirror/addon/hint/sql-hint.js";
import { codemirror } from "vue-codemirror";
// 引入主题,配置后生效
import "codemirror/theme/monokai.css";
import "codemirror/theme/idea.css";
//引入语言,配置后生效
import "codemirror/mode/sql/sql.js";
import "codemirror/addon/hint/show-hint.js";
import "codemirror/addon/display/autorefresh.js";
import DiffMatchPatch from "diff-match-patch";
import sqlFormatter from "sql-formatter";
import { findLexicon } from "../../../api/basic/datasource";
window.diff_match_patch = DiffMatchPatch;
window.DIFF_DELETE = -1;
window.DIFF_INSERT = 1;
window.DIFF_EQUAL = 0;
export default {
  name: "MdmDataEntityHis",
  components: {},
  data() {
    return {
      table: {}, // 用于自定义列表宽度
      info: [], // 用于自定义列表宽度s
      idbLexicon: {},
      isRealTimeTip: false, // 是否是实时的提示
      activeName: "first",
      pageSize: 20,
      datasource: {},
      dbName: undefined,
      paginationInfo:{
        currentPage:1,
        total:20
      },
      sqlInfo: [],
      codeEditor: null,
      defaultProps: {
        icon: "iconOne",
        label: "label",
        isLeaf: "leaf",
        code: "code"
      },
      tableData: [],
      infoData: [],
      runLoading:false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 20,
      //数据源选项
      dataSourceOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      dataMax:1000
    };
  },
  watch: {
    // 根据名称筛选部门树
    dbName(val) {
      this.$refs.dbtree.filter(val);
    }
  },

  created() {
  },
  mounted() {
    this.init();
    getDataMax().then(response => {
    	this.dataMax=response.data;
      });
    
  },
  methods: {
    handleExport(){ //导出
      // data数据
      let dataTable = this.tableData
      var option = {}
      option.fileName = '数据查询'
      option.datas = [
        {
          sheetData: dataTable,
          sheetName: 'sheet',
          sheetFilter: Object.keys(this.tableData[0]),
          sheetHeader: Object.keys(this.tableData[0])
        }
      ]
      // 执行导出
      var toExcel = new JsExportExcel(option)
      toExcel.saveExcel()
    },
    // ...mapActions({
    //   setIdbLexicon: 'setIdbLexicon'
    // }),
    init() {
      // 实例初始化
      const targetF = document.getElementById("sqlQueryCol");
      targetF.innerHTML = '<textarea id="sqlQueryText" name="sqlQueryText" style="height: calc(100vh - 432px);" />';
      const target = document.getElementById("sqlQueryText");
      this.codeEditor = CodeMirror.fromTextArea(target, {
        lineNumbers: true, //显示行号
        styleActiveLine: true,
        matchBrackets: true,
        mode: "text/x-sql",
        connect: "align",
        theme: "monokai",
        autoCloseBrackets: true,
        autoRefresh: true,
        readOnly: false,
        hintOptions: {
          completeSingle: false,
          tables: this.idbLexicon
        },
        extraKeys: {
          "Ctrl-Space": editor => {
            editor.showHint();
          }
        }
      });
      this.codeEditor.setValue("");

      this.codeEditor.on("keypress", editor => {
        const __Cursor = editor.getDoc().getCursor();
        const __Token = editor.getTokenAt(__Cursor);
        if (
          __Token.type &&
          __Token.type !== "string" &&
          __Token.type !== "punctuation" &&
          __Token.string.indexOf(".") === -1
        ) {
          // 把输入的关键字统一变成大写字母
          editor.replaceRange(
            __Token.string.toUpperCase(),
            {
              line: __Cursor.line,
              ch: __Token.start
            },
            {
              line: __Cursor.line,
              ch: __Token.end
            },
            __Token.string
          );
        }
        editor.showHint();
      });
    },
    // 获取当前位置之前的sql语句（截至到上一个分号）
    getLast(__Cursor) {
      let sql = "";
      const s = this.codeEditor.getRange(
        { line: 0, ch: 0 },
        { line: __Cursor.line, ch: __Cursor.ch }
      );
      const i = s.lastIndexOf(";");
      if (i === -1) {
        sql = s;
      } else {
        sql = s.slice(i + 1);
      }
      return sql;
    },
    // 获取当前位置之后的sql语句（截至到下一个分号）
    getNext(__Cursor) {
      let sql = "";
      const lastLine = this.codeEditor.lastLine();
      const n = this.codeEditor.getRange(
        { line: __Cursor.line, ch: __Cursor.ch },
        { line: lastLine, ch: this.codeEditor.getLine(lastLine).length }
      );
      const i = n.indexOf(";");
      if (i === -1) {
        sql += n;
      } else {
        sql += n.slice(0, i + 1);
      }
      return sql;
    },
    // 设置value
    setValue(val) {
      this.codeEditor.setValue(val);
    },
    // 获取value
    getValue() {
      return this.codeEditor.getValue();
    },
    // 获取选中内容
    getSelection() {
      return this.codeEditor.getSelection();
    },

    getDataSource() {
      listDatasourceTreeAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    handleRestart() {
      this.runLoading = true
      let sqlScript =
        this.getSelection() != "" ? this.getSelection() : this.getValue();
      if (sqlScript == null || sqlScript == "") {
        this.$message.warning("请输入脚本后再点击运行");
        this.runLoading = false
        return;
      }
      if (this.datasource.id == null) {
        this.$message.warning("请选择数据源后再点击运行");
        this.runLoading = false
        return;
      }
      this.tableData = [];
      this.infoData = [];
      excuteSql(this.datasource.id, sqlScript, this.dataMax)
        .then(res => {
          this.tableData = res.data;
          this.tableData.map((el, index) => {
            Object.entries(el).map((el2, index2) => {
              var width = this.getTextWidth(el2[1].toString());
              if (!this.table[el2[0]] || width > this.table[el2[0]]) {
                this.table[el2[0]] = width;
              }
            });
          });
          this.infoData = res.info;
          this.infoData.map((el, index) => {
            Object.entries(el).map((el2, index2) => {
              var width = this.getTextWidth(el2[1].toString());
              if (!this.info[el2[0]] || width > this.info[el2[0]]) {
                this.info[el2[0]] = width;
              }
            });
          });
          if (this.tableData.length != 0 && this.infoData.length != 0) {
            this.activeName = "first";
          }
          if (this.tableData.length == 0 && this.infoData.length != 0) {
            this.activeName = "second";
          }
          if (this.tableData.length != 0 && this.infoData.length == 0) {
            this.activeName = "first";
          }
          this.paginationInfo.total = this.tableData.length
          this.tableData2 = this.tableData.slice(0,this.pageSize)
          this.$nextTick(()=>{
             document.body.scrollTop=document.documentElement.scrollTop = 800
          })
        })
        .catch(() => {
          let data = [];
        })
        .finally(()=>{
          this.runLoading = false
        })
    },
    getTextWidth(str) {
      var width = 0;
      var html = document.createElement("span");
      html.innerText = str;
      html.className = "getTextWidth";
      document.querySelector("body").appendChild(html);
      width = document.querySelector(".getTextWidth").offsetWidth;
      document.querySelector(".getTextWidth").remove();
      return width;
    },
    sqlFormat() {
      /*获取文本编辑器内容*/
      let sqlContent = "";
      sqlContent = this.codeEditor.getValue();
      /*将sql内容进行格式后放入编辑器中*/
      this.codeEditor.setValue(sqlFormatter.format(sqlContent));
    },
    handleSizeChange() {},
    handleCurrentChange(val) {
      var start = (val-1)*this.pageSize
      var end = val*this.pageSize
      this.tableData2 = this.tableData.slice(start,end)
      this.$forceUpdate()
    },
    pageSizeChange(){
      this.tableData2 = this.tableData.slice(0,this.pageSize)
      this.$forceUpdate()
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1 || data.code.indexOf(value) !== -1 ;
    },

    GroupBy(array, fn) {
      const groups = {};
      array.forEach(function(item) {
        const group = JSON.stringify(fn(item));
        //这里利用对象的key值唯一性的，创建数组
        groups[group] = groups[group] || [];
        groups[group].push(item);
      });
      //最后再利用map循环处理分组出来
      return Object.keys(groups).map(function(group) {
        return groups[group];
      });
    },

    loadNode(node, resolve) {
      // console.log(666)
      if (node.level === 0) {
        this.getDataSource();
      } else if (node.level === 2) {
        // console.log(node.data.children);
        if(node.data.children){
          return resolve(node.data.children);
        }else{
          this.$message.error("操作失败");
        }
      } else {
        findTablesByDS(node.data.id)
          .then(res => {
            if (res.data) {
              // let _data = ;
              // this.idbLexicon = {};
              // const results = this.GroupBy(_data, function(item) {
              //   return [item.schema];
              // });
              // if (!!results && JSON.stringify(results) !== "{}") {
              //   for (let i = 0; i < results.length; i++) {
              //     var datas = results[i];
              //     const key = datas[0].schema;
              //     this.isRealTimeTip = true;
              //     const values = [];
              //     for (let j = 0; j < datas.length; j++) {
              //       var data = datas[j].label.split(".")[1];
              //       values.push(data);
              //     }
              //     this.$set(this.idbLexicon, key, values);
              //   }
              // }
              // console.log(this.idbLexicon);
              this.codeEditor.options.hintOptions.tables = res.data.tables;
              return resolve(res.data.list);
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch(() => {
            let data = [];
            return resolve(data);
          });
      }
      /*if (node.level == 1) {
        findLexicon(node.data.id).then(response => {
          // console.log(response.data);
          this.codeEditor.options.hintOptions.tables = response.data;
        });
        this.datasource = node.data;
      }*/
    },
    // 节点单击事件
    handleNodeClick(data, node) {
      if (node.level == 2) {
        //console.log("点击2级节点");
        this.datasource = node.parent.data;
      }
      if (node.level == 3) {
        //console.log("点击3级节点");
        this.datasource = node.parent.parent.data;
      }
      //TODO 判断节点是否展开
      if (node.level == 1) {
        this.datasource = node.data;
      }
    },
    // 节点展开事件
    handleNodeExpand(data, node){
      if (node.level == 1) {
        findLexicon(data.id).then(response => {
          // console.log(response.data);
          this.codeEditor.options.hintOptions.tables = response.data;
        });
        this.datasource = node.data;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .CodeMirror pre.CodeMirror-line,
::v-deep .CodeMirror pre.CodeMirror-line-like {
  line-height: 22px;
}
::v-deep .CodeMirror {
  height: 550px !important;
}
::v-deep .CodeMirror-merge,
::v-deep .CodeMirror-merge .CodeMirror {
  height: 550px !important;
}
::v-deep .CodeMirror-merge-r-chunk {
  background: #38380d !important;
}

::v-deep .infinite-list-wrapper {
  height: calc(100vh - 84px);
}
::v-deep .el-tag--medium {
  margin-left: 5px;
}
::v-deep .loadingStatus {
  text-align: center;
  color: #303133;
  font-size: 14px;
}
.sqlTextarea {
  resize: none;
  width: 100%;
  height: calc(100vh - 432px);
  min-height: 400px;
  background: #f5f7fa;
}
.head-container:last-child {
  overflow-y: scroll;
  height: calc(100vh - 180px);
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.head-container2::-webkit-scrollbar {
  height: 0 !important;
  width: 0px !important;
}
.sqlBtn {
  margin-bottom: 1px;
}
.spanStyle {
  white-space: nowrap;
  width: 90%;
  word-break: keep-all;
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
}
::v-deep .el-table th > .cell {
  padding: 0;
  text-align: center;
}
::v-deep .getTextWidth {
  font-size: 14px;
}
</style>
<style>
.el-tooltip__popper{
  font-size: 14px;
  max-width:50%;
  /*backgroud: #68859a !important;*/  /*背景色　　!important优先级*/
}/*设置显示隐藏部分内容，按50%显示*/
.el-tree-node__content {
  height: 32px;
}
</style>
<style scoped>
.flow-tree{
  overflow: auto;
  /*margin: 10px*/
}
>>>.el-tree-node > .el-tree-node__children{
  overflow: visible !important;
}
</style>
