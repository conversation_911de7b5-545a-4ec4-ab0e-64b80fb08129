package com.dqms.dsc.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 报表权限对象 dsc_master_user
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscMasterUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户权限 */
    private Long masterUserId;

    /** 用户 */
    @Excel(name = "用户")
    private Long userId;

    /** 报表 */
    @Excel(name = "报表")
    private Long mdmId;

    public void setMasterUserId(Long masterUserId)
    {
        this.masterUserId = masterUserId;
    }

    public Long getMasterUserId()
    {
        return masterUserId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setMdmId(Long mdmId)
    {
        this.mdmId = mdmId;
    }

    public Long getMdmId()
    {
        return mdmId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("masterUserId", getMasterUserId())
            .append("userId", getUserId())
            .append("mdmId", getMdmId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
