package com.dqms.utils.sql;

import java.util.HashSet;

public class GbColumnVo {
	
	 private static final long serialVersionUID = 1L;
		
	 private      String              name;
	 private      HashSet<String>         alias;
	 private      HashSet<String>     relation;
	 private GbSelectVo gbSelectVo;
	 
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public HashSet<String> getAlias() {
		return alias;
	}
	
	public void setAlias(HashSet<String> alias) {
		this.alias = alias;
	}
	
	public HashSet<String> getRelation() {
		return relation;
	}
	
	public void setRelation(HashSet<String> relation) {
		this.relation = relation;
	}

	public GbSelectVo getGbSelectVo() {
		return gbSelectVo;
	}

	public void setGbSelectVo(GbSelectVo gbSelectVo) {
		this.gbSelectVo = gbSelectVo;
	}
 
 
}
