import request from '@/utils/request'

// 查询对标主表列表
export function listDsmCheckMain(query) {
  return request({
    url: '/dsm/dsmCheckMain/list',
    method: 'get',
    params: query
  })
}

// 查询对标主表详细
export function getDsmCheckMain(checkMainId) {
  return request({
    url: '/dsm/dsmCheckMain/' + checkMainId,
    method: 'get'
  })
}

// 新增对标主表
export function addDsmCheckMain(data) {
  return request({
    url: '/dsm/dsmCheckMain',
    method: 'post',
    data: data
  })
}

// 修改对标主表
export function updateDsmCheckMain(data) {
  return request({
    url: '/dsm/dsmCheckMain',
    method: 'put',
    data: data
  })
}

// 删除对标主表
export function delDsmCheckMain(checkMainId) {
  return request({
    url: '/dsm/dsmCheckMain/' + checkMainId,
    method: 'delete'
  })
}

// 导出对标主表
export function exportDsmCheckMain(query) {
  return request({
    url: '/dsm/dsmCheckMain/export',
    method: 'get',
    params: query
  })
}