<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmModelSqlCheckMapper">
    
    <resultMap type="DsmModelSqlCheck" id="DsmModelSqlCheckResult">
        <result property="modelSqlCheckId"    column="model_sql_check_id"    />
        <result property="sqlName"    column="sql_name"    />
        <result property="sqlScript"    column="sql_script"    />
        <result property="result"    column="result"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectDsmModelSqlCheckVo">
        select model_sql_check_id, sql_name, sql_script, result, create_time from dsm_model_sql_check
    </sql>

    <select id="selectDsmModelSqlCheckList" parameterType="DsmModelSqlCheck" resultMap="DsmModelSqlCheckResult">
        <include refid="selectDsmModelSqlCheckVo"/>
        <where>  
            <if test="sqlName != null  and sqlName != ''"> and sql_name like concat('%', #{sqlName}, '%')</if>
        </where>
        order by model_sql_check_id desc
    </select>
    
    <select id="selectDsmModelSqlCheckById" parameterType="Long" resultMap="DsmModelSqlCheckResult">
        <include refid="selectDsmModelSqlCheckVo"/>
        where model_sql_check_id = #{modelSqlCheckId}
    </select>
        
    <insert id="insertDsmModelSqlCheck" parameterType="DsmModelSqlCheck" useGeneratedKeys="true" keyProperty="modelSqlCheckId">
        insert into dsm_model_sql_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sqlName != null">sql_name,</if>
            <if test="sqlScript != null">sql_script,</if>
            <if test="result != null">result,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sqlName != null">#{sqlName},</if>
            <if test="sqlScript != null">#{sqlScript},</if>
            <if test="result != null">#{result},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateDsmModelSqlCheck" parameterType="DsmModelSqlCheck">
        update dsm_model_sql_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="sqlName != null">sql_name = #{sqlName},</if>
            <if test="sqlScript != null">sql_script = #{sqlScript},</if>
            <if test="result != null">result = #{result},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where model_sql_check_id = #{modelSqlCheckId}
    </update>

    <delete id="deleteDsmModelSqlCheckById" parameterType="Long">
        delete from dsm_model_sql_check where model_sql_check_id = #{modelSqlCheckId}
    </delete>

    <delete id="deleteDsmModelSqlCheckByIds" parameterType="String">
        delete from dsm_model_sql_check where model_sql_check_id in 
        <foreach item="modelSqlCheckId" collection="array" open="(" separator="," close=")">
            #{modelSqlCheckId}
        </foreach>
    </delete>
</mapper>