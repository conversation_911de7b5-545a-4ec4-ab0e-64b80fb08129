package com.dqms.task.domain.vo;

import com.dqms.common.core.domain.BaseEntity;

public class EtlTaskAssetsEdges extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private String source;

    private String target;

    private String label;
    
    private String type;

	private Long fontSize;

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Long getFontSize() {
		return fontSize;
	}

	public void setFontSize(Long fontSize) {
		this.fontSize = fontSize;
	}
}
