package com.dqms.basic.mapper;

import java.util.List;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.core.domain.entity.SysSystem;

/**
 * 数据源管理Mapper接口
 *
 * <AUTHOR>
 * @date 2021-03-09
 */
public interface SysDatasourceMapper
{
    /**
     * 查询数据源管理
     *
     * @param datasourceId 数据源管理ID
     * @return 数据源管理
     */
    public SysDatasource selectSysDatasourceById(Long datasourceId);

    /**
     * 查询数据源管理
     *
     * @param datasourceName 数据源管理Name
     * @return 数据源管理
     */
    public SysDatasource selectSysDatasourceByName(String datasourceName);

    /**
     * 查询数据源管理
     *
     * @param sysDatasource 数据源管理Name
     * @return 数据源管理
     */
    public SysDatasource getSysDatasourceByName(SysDatasource sysDatasource);

    /**
     * 查询数据源管理列表
     *
     * @param sysDatasource 数据源管理
     * @return 数据源管理集合
     */
    public List<SysDatasource> selectSysDatasourceList(SysDatasource sysDatasource);

    /**
     * 新增数据源管理
     *
     * @param sysDatasource 数据源管理
     * @return 结果
     */
    public int insertSysDatasource(SysDatasource sysDatasource);

    /**
     * 修改数据源管理
     *
     * @param sysDatasource 数据源管理
     * @return 结果
     */
    public int updateSysDatasource(SysDatasource sysDatasource);

    /**
     * 删除数据源管理
     *
     * @param datasourceId 数据源管理ID
     * @return 结果
     */
    public int deleteSysDatasourceById(Long datasourceId);

    /**
     * 批量删除数据源管理
     *
     * @param datasourceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysDatasourceByIds(Long[] datasourceIds);

    public List<SysDatasource> selectSysDatasourceAll();

    public List<SysDatasource> selectSysDatasourceTreeAll();

    public SysDatasource sysDatasourceByRegId(Long regId);

    public SysDatasource selectSysDatasourceByNameOrCode(SysDatasource sysDatasource);
    public List listSysDatasourceByName(SysDatasource sysDatasource);
    public List selectSysDatasourceByCode(SysDatasource sysDatasource);
    
    /**
     * 查询检查规则
     *
     * @param sysDatasource 数据源管理
     * @return 数据源管理集合
     */
    public List<SysDatasource> selectCheckRuleList();
    
    public List<SysDatasource> findDataTypeAll();

}
