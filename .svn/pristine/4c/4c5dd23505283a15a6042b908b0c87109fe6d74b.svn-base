package com.dqms.mdm.util.StrategyType;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.enums.TableInfo;
import com.dqms.common.utils.StringUtils;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.util.MetaDataStrategy;
import com.dqms.utils.JdbcTemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ClickHouseStrategy extends MetaDataStrategy {
    @Override
    public Map<String, Object> getTableInfo(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        log.info("ClickHouse获取表结构方法");

        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Map<String, Object> m = new HashedMap();
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(mdmRegistry.getRegDir(), mdmRegistry.getRegDir(), mdmRegistry.getRegName(), null);
            int i = 0;
            while (rs.next()) {
                log.info("---------[" + i++ + "]---------");
                String tableName = "";
                tableName = rs.getString("TABLE_NAME");
                String tableCat = rs.getString("TABLE_CAT");
                String tableSchem = rs.getString("TABLE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String tableType = rs.getString("TABLE_TYPE");
                String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                m.put(TableInfo.TABLE_NAME.getCode(), tableName);
                m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return m;
    }

    @Override
    public List<Map<String, Object>> getColumns(MdmRegistry mdmRegistry,SysDatasource sysDatasource) {

        log.info("这是MYSQL获取表字段方法");

        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        List<Map<String, Object>> columns = new ArrayList<Map<String, Object>>();
        Connection conn = null;
        ResultSet rs = null;
        try {
//        	String regDir=mdmRegistry.getRegDir()==null?null:mdmRegistry.getRegDir().toLowerCase();
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getPrimaryKeys(mdmRegistry.getRegDir(),  mdmRegistry.getRegDir(), mdmRegistry.getRegName());
            List<String> primaryKeys = new ArrayList<String>();
            while (rs.next()) {
                primaryKeys.add(rs.getString("COLUMN_NAME"));
            }
            rs = dbmd.getColumns(mdmRegistry.getRegDir(), mdmRegistry.getRegDir(),  mdmRegistry.getRegName(), "%");
            getColumnsList(columns, rs, primaryKeys);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }

        return columns;
    }

    @Override
    public Boolean checkTableExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Map<String, Object> m = new HashedMap();
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(mdmRegistry.getRegDir(), mdmRegistry.getRegDir(), mdmRegistry.getRegName(), null);
            while (rs.next()) {
                //判断TABLE、VIEW使用正确
                String tableType = rs.getString("TABLE_TYPE");
                if(("TABLE".equalsIgnoreCase(tableType)&&Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.TABLE)
                || ("VIEW".equalsIgnoreCase(tableType)&&Integer.parseInt(mdmRegistry.getMetaType())==MdmConstants.VIEW)
                ){
                    return true;
                }
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return false;
    }

    @Override
    public Boolean checkProcedureExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getProcedures(mdmRegistry.getRegDir(), sysDatasource.getUsername() , mdmRegistry.getRegName());
            while (rs.next()) {
                return true;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> getTablesAndViews(SysDatasource sysDatasource,String catalog,String [] types) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getTables(catalog, sysDatasource.getUsername() , null, types);
            while (rs.next()) {
                Map<String, Object> m = new HashedMap();
                log.info(rs.toString());
                String tableName = "";
                tableName = rs.getString("TABLE_NAME");
                String tableCat = rs.getString("TABLE_CAT");
                String tableSchem = rs.getString("TABLE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String tableType = rs.getString("TABLE_TYPE");
                String schema = StringUtils.isNotBlank(tableCat) ? tableCat : tableSchem;
                m.put("label", schema+'.'+tableName);
                m.put("tableName", tableName);
                m.put("icon", "iconThree");
                m.put("leaf", true);
                m.put(TableInfo.TABLE_SCHEMA.getCode(), schema);
                m.put(TableInfo.TABLE_REMARK.getCode(), remarks);
                m.put(TableInfo.TABLE_TYPE.getCode(), tableType);
                list.add(m);
            }
            return list;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getProcedures(SysDatasource sysDatasource, String catalog) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String,Object>> list= new ArrayList<>();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getProcedures(catalog, sysDatasource.getUsername(), null);
            while (rs.next()) {
                Map<String, Object> m = new HashedMap();
                log.info(rs.toString());
                String procedureName = rs.getString("PROCEDURE_NAME");
                String procedureCat = rs.getString("PROCEDURE_CAT");
                String procedureSchema = rs.getString("PROCEDURE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String procedureType = rs.getString("PROCEDURE_TYPE");
                m.put(TableInfo.PROCEDURE_NAME.getCode(), procedureName);
                m.put(TableInfo.PROCEDURE_CAT.getCode(), procedureCat);
                m.put(TableInfo.PROCEDURE_SCHEMA.getCode(), procedureSchema);
                m.put(TableInfo.PROCEDURE_REMARKS.getCode(), remarks);
                m.put(TableInfo.PROCEDURE_TYPE.getCode(), procedureType);
                list.add(m);
            }
            return list;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }

    @Override
    public Map<String, Object> getProcedureInfo(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        JdbcTemplate jdbcTemplate = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        Connection conn = null;
        ResultSet rs = null;
        Map<String, Object> m = new HashedMap();
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            DatabaseMetaData dbmd = conn.getMetaData();
            rs = dbmd.getProcedures(mdmRegistry.getRegDir(), mdmRegistry.getRegDir() , mdmRegistry.getRegName());
            while (rs.next()) {
                String procedureName = rs.getString("PROCEDURE_NAME");
                String procedureCat = rs.getString("PROCEDURE_CAT");
                String procedureSchema = rs.getString("PROCEDURE_SCHEM");
                String remarks = rs.getString("REMARKS");
                String procedureType = rs.getString("PROCEDURE_TYPE");
                m.put(TableInfo.PROCEDURE_NAME.getCode(), procedureName);
                m.put(TableInfo.PROCEDURE_CAT.getCode(), procedureCat);
                m.put(TableInfo.PROCEDURE_SCHEMA.getCode(), procedureSchema);
                m.put(TableInfo.PROCEDURE_REMARKS.getCode(), remarks);
                m.put(TableInfo.PROCEDURE_TYPE.getCode(), procedureType);
            }
            return m;
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            close(conn, rs);
        }
        return null;
    }


    @Override
    public Map excuteByLimit(SysDatasource sysDatasource, String sqlText,int pageSize) {
        return getExcuteMaps(sysDatasource, sqlText, DbType.mysql,pageSize);
    }


    @Override
    public List<Map<String, Object>> quaryByPage(SysDatasource sysDatasource, String sqlText,int page,int size) {
        return getSqlPage(sysDatasource, sqlText, DbType.mysql,page,size);
    }

    @Override
    public int getSqlCount(SysDatasource sysDatasource, String sqlText) {
        return getSqlCount(sysDatasource, sqlText, DbType.mysql);
    }

    @Override
    public String getTableCreate(SysDatasource sysDatasource,MdmRegistry mdmRegistry) {
    	List<Map<String, Object>>  dataList = new ArrayList<Map<String,Object>>();
    	String sqlText=StringUtils.format("show create table {}.{}",mdmRegistry.getRegDir(),mdmRegistry.getRegName());
    	JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
    	dataList = jd.queryForList(sqlText);
    	if(dataList.size()>0) {
    		return (String) dataList.get(0).get("Create Table");
    	}else {
    		return "无";
    	}
    }

    @Override
    public String getProcedureCreate(SysDatasource sysDatasource,MdmRegistry mdmRegistry) {
        List<Map<String, Object>>  dataList = new ArrayList<Map<String,Object>>();
        String sqlText=StringUtils.format("show create procedure {}.{}",mdmRegistry.getRegDir(),mdmRegistry.getRegName());
        JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        dataList = jd.queryForList(sqlText);
        if(dataList.size()>0) {
            return (String) dataList.get(0).get("Create Procedure");
        }else {
            return "无";
        }
    }

    @Override
    public String getViewCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        List<Map<String, Object>>  dataList = new ArrayList<Map<String,Object>>();
        String sqlText=StringUtils.format("SELECT create_table_query FROM system.tables where engine='View' and name='{}'",mdmRegistry.getRegName());
        JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
        dataList = jd.queryForList(sqlText);
        if(dataList.size()>0) {
            return (String) dataList.get(0).get("create_table_query");
        }else {
            return "无";
        }
    }

    @Override
    public Map<String,List> getDatabaseInfos(SysDatasource sysDatasource, MdmRegistry mdmRegistry) {
        Map<String,List> map = new HashMap<>();
        Boolean isPro = false;
        Boolean isTable = false;
        Boolean isView = false;
        for (Integer metaType : mdmRegistry.getMetaTypes()) {
            if(metaType==MdmConstants.TABLE){
                isTable = true;
            }
            if(metaType==MdmConstants.PROCEDURE){
                isPro = true;
            }
            if(metaType==MdmConstants.VIEW){
                isView = true;
            }
        }
        if(isTable){
            List<Map<String,Object>> list = getTablesAndViews(sysDatasource,mdmRegistry.getRegDir(),new String[]{"TABLE"});
            log.info("表list:{}",list.size());
            map.put("table",list);
        }
        if(isView){
            List<Map<String,Object>> list = getTablesAndViews(sysDatasource,mdmRegistry.getRegDir(),new String[]{"VIEW"});
            log.info("视图list:{}",list.size());
            map.put("view",list);
        }
        if(isPro){
            List<Map<String,Object>> list = getProcedures(sysDatasource,mdmRegistry.getRegDir());
            log.info("存储过程list:{}",list.size());
            map.put("procedures",list);
        }
        return map;
    }

    @Override
    public List<SQLStatement> parseStatements(SysDatasource sysDatasource, String sqlText) {
        return SQLUtils.parseStatements(sqlText, DbType.mysql);
    }

    @Override
    public SchemaStatVisitor createSchemaStatVisitor(SysDatasource sysDatasource) {
        return SQLUtils.createSchemaStatVisitor( DbType.mysql);
    }

	@Override
	public Boolean createUser(SysDatasource sysDatasource, String username, String passwd) {
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		//先判断用户是否存在，如果已经存在则不再添加
		int count = jd.queryForObject("SELECT COUNT(*) FROM mysql.user t WHERE USER='"+username+"'",Integer.class);
		if(count==0) {
			String sql ="CREATE USER '"+username+"'@'%' IDENTIFIED BY '"+passwd+"'";
			jd.execute(sql);
			return true;
		}else {
			String sql ="set password for '"+username+"'@'%' = password('"+passwd+"')";
			jd.execute(sql);
			return false;
		}
	}

	@Override
	public Boolean grantBytable(SysDatasource sysDatasource, String username, String tables, String passwd,boolean flag) {
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		String sql ="";
		if(flag) {
			sql ="revoke all on *.* from '"+username+"'@'%'";
			jd.execute(sql);
		}
		
		String[] ts= tables.split("\\;");
		for(String table : ts) {
			if(table!=null&&!table.equals("")) {
				sql ="GRANT SELECT ON "+table+" TO '"+username+"'@'%'";
				jd.execute(sql);
			}
		}
		return true;
	}


	@Override
	public Boolean revokeBytable(SysDatasource sysDatasource, String username, String tables, String passwd,boolean flag) {
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		String[] ts= tables.split("\\;");
		for(String table : ts) {
			if(table!=null&&!table.equals("")) {
				String sql ="revoke SELECT ON "+table+" FROM '"+username+"'@'%'";
				jd.execute(sql);
			}
		}
		return true;
	}
	
	@Override
	public Boolean dropUser(SysDatasource sysDatasource, String username) {
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		String sql ="DROP USER "+username;
		jd.execute(sql);
		return true;
	}

    @Override
    public String createSqlGeneration(DicDataExchange dicDataExchange,SysDatasource sysDatasource) {
        return null;
    }

	@Override
	public void createTableTemp(String tableName, SysDatasource sysDatasource) {
		String sql="create table IF NOT EXISTS "+tableName+"_temp like "+tableName;
		JdbcTemplate jd = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN batch_id VARCHAR(36) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN apply_status VARCHAR(10) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN create_type VARCHAR(10) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN creat_by VARCHAR(100) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN create_time VARCHAR(20) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN msg_ VARCHAR(2000) NULL ";
		jd.execute(sql);
		sql="ALTER TABLE "+tableName+"_temp ADD COLUMN keys_ VARCHAR(200) NULL ";
		jd.execute(sql);
	}

}
