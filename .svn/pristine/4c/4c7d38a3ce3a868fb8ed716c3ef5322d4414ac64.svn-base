package com.dqms.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户和角色关联 sys_user_role
 *
*/
public class SysUserSystem
{
    /** 用户ID */
    private Long userId;

    /** 应用ID */
    private Long systemId;

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getSystemId()
    {
        return systemId;
    }

    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("systemId", getSystemId())
            .toString();
    }
}
