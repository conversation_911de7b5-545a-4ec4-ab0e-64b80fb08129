import request from '@/utils/request'

// 查询脚本检核列表
export function listDsmModelSqlCheck(query) {
  return request({
    url: '/dsm/dsmModelSqlCheck/list',
    method: 'get',
    params: query
  })
}

// 查询脚本检核详细
export function getDsmModelSqlCheck(modelSqlCheckId) {
  return request({
    url: '/dsm/dsmModelSqlCheck/' + modelSqlCheckId,
    method: 'get'
  })
}

// 新增脚本检核
export function addDsmModelSqlCheck(data) {
  return request({
    url: '/dsm/dsmModelSqlCheck',
    method: 'post',
    data: data
  })
}

// 修改脚本检核
export function updateDsmModelSqlCheck(data) {
  return request({
    url: '/dsm/dsmModelSqlCheck',
    method: 'put',
    data: data
  })
}

// 删除脚本检核
export function delDsmModelSqlCheck(modelSqlCheckId) {
  return request({
    url: '/dsm/dsmModelSqlCheck/' + modelSqlCheckId,
    method: 'delete'
  })
}

// 导出脚本检核
export function exportDsmModelSqlCheck(query) {
  return request({
    url: '/dsm/dsmModelSqlCheck/export',
    method: 'get',
    params: query
  })
}
//下载任务导入模板
export function importTemplate() {
  return request({
    url: '/dsm/dsmModelSqlCheck/importTemplate',
    method: 'get'
  })
}