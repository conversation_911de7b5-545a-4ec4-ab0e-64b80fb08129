package com.dqms.dqm.service.impl;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.dqms.common.constant.EsConstant;
import com.dqms.common.core.page.PageDomain;
import com.dqms.common.core.page.TableSupport;
import com.dqms.common.utils.StringUtils;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dqm.service.IDqmKnowledgeBaseServiceES;
import com.dqms.system.service.ISysConfigService;
import lombok.SneakyThrows;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchResultMapper;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.dqm.domain.DqmKnowledgeBase;
import com.dqms.dqm.mapper.DqmKnowledgeBaseMapper;
import com.dqms.dqm.service.IDqmKnowledgeBaseService;

/**
 * 质量检查规则知识库Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-10
 */
@Service
public class DqmKnowledgeBaseServiceImpl implements IDqmKnowledgeBaseService
{
    @Autowired
    private DqmKnowledgeBaseMapper dqmKnowledgeBaseMapper;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired(required = false)
    private IDqmKnowledgeBaseServiceES dqmKnowledgeBaseServiceES;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    /**
     * 查询质量检查规则知识库
     *
     * @param knowledgeBaseId 质量检查规则知识库ID
     * @return 质量检查规则知识库
     */
    @Override
    public DqmKnowledgeBase selectDqmKnowledgeBaseById(Long knowledgeBaseId)
    {
        return dqmKnowledgeBaseMapper.selectDqmKnowledgeBaseById(knowledgeBaseId);
    }

    /**
     * 查询质量检查规则知识库列表
     *
     * @param dqmKnowledgeBase 质量检查规则知识库
     * @return 质量检查规则知识库
     */
    @DataScope(systemAlias = "dvrc")
    @Override
    public List<DqmKnowledgeBase> selectDqmKnowledgeBaseList(DqmKnowledgeBase dqmKnowledgeBase)
    {
        return dqmKnowledgeBaseMapper.selectDqmKnowledgeBaseList(dqmKnowledgeBase);
    }
    @Override
    public List<DqmKnowledgeBase> selectDqmKnowledgeBaseListToES(DqmKnowledgeBase dqmKnowledgeBase){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageDomain.getPageNum() - 1, pageDomain.getPageSize());
        //检索条件
        BoolQueryBuilder bqb = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldBqb = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(dqmKnowledgeBase.getKnowledgeBaseContext())) {
            shouldBqb.should(QueryBuilders.matchQuery("knowledgeBaseName", dqmKnowledgeBase.getKnowledgeBaseContext()))
                    .should(QueryBuilders.matchQuery("knowledgeBaseContext", dqmKnowledgeBase.getKnowledgeBaseContext()));
        }
        bqb.must(shouldBqb);
        List<String> highlightFields = new ArrayList<String>();
        highlightFields.add("knowledgeBaseName");
        highlightFields.add("knowledgeBaseContext");
        HighlightBuilder.Field[] fields = new HighlightBuilder.Field[highlightFields.size()];
        for (int x = 0; x < highlightFields.size(); x++) {
            fields[x] = new HighlightBuilder.Field(highlightFields.get(x)).preTags(EsConstant.HIGH_LIGHT_START_TAG)
                    .postTags(EsConstant.HIGH_LIGHT_END_TAG);
        }
        SearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(bqb)
                .withPageable(pageable)
                .withHighlightFields(fields)
                .build();
        Page<DqmKnowledgeBase> dqmKnowledgeBasePage = elasticsearchTemplate.queryForPage(query, DqmKnowledgeBase.class, new SearchResultMapper() {
            @SneakyThrows
            @Override
            public <T> AggregatedPage<T> mapResults(SearchResponse searchResponse, Class<T> aClass, Pageable pageable) {
                List<DqmKnowledgeBase> damAssetsList = new ArrayList<>();
                SearchHits hits = searchResponse.getHits();
                for (SearchHit hit : hits) {
                    if (hits.getHits().length <= 0) {
                        return null;
                    }
                    DqmKnowledgeBase knowledgeBase = new DqmKnowledgeBase();
                    knowledgeBase.setKnowledgeBaseId(Long.parseLong(hit.getId()));
                    knowledgeBase.setKnowledgeBaseName((String) hit.getSourceAsMap().get("knowledgeBaseName"));
                    knowledgeBase.setKnowledgeBaseContext((String) hit.getSourceAsMap().get("knowledgeBaseContext"));
                    knowledgeBase.setHandlingLoding(String.valueOf(hit.getSourceAsMap().get("handlingLoding")));
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("dqmValidationProblemId"))) {
                        knowledgeBase.setDqmValidationProblemId(Long.parseLong(hit.getSourceAsMap().get("dqmValidationProblemId").toString()));
                    }
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("createId"))) {
                        knowledgeBase.setCreateId(Long.parseLong(hit.getSourceAsMap().get("createId").toString()));
                    }
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("updateId"))) {
                        knowledgeBase.setUpdateId(Long.parseLong(hit.getSourceAsMap().get("updateId").toString()));
                    }
                    setHighLight(hit, "knowledgeBaseName", knowledgeBase);
                    setHighLight(hit, "knowledgeBaseContext", knowledgeBase);
                    damAssetsList.add(knowledgeBase);
                }
                return new AggregatedPageImpl<T>((List<T>) damAssetsList, pageable,
                        searchResponse.getHits().getTotalHits());
            }

            @Override
            public <T> T mapSearchHit(SearchHit searchHit, Class<T> aClass) {
                return null;
            }
        });
//        Page<DqmKnowledgeBase> dqmKnowledgeBasePage = elasticsearchTemplate.queryForPage(query,DqmKnowledgeBase.class);
        return dqmKnowledgeBasePage.getContent();
    }

    public void setHighLight(SearchHit searchHit, String field, Object object) {
        Map<String, HighlightField> highlightFieldMap = searchHit.getHighlightFields();
        HighlightField highlightField = highlightFieldMap.get(field);
        if (highlightField != null) {
            String highLightMessage = highlightField.fragments()[0].toString();
            String capitalize = StringUtils.capitalize(field);
            String methodName = "set" + capitalize;
            Class<?> clazz = object.getClass();
            try {
                Method setMethod = clazz.getMethod(methodName, String.class);
                setMethod.invoke(object, highLightMessage);

            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * 新增质量检查规则知识库
     *
     * @param dqmKnowledgeBase 质量检查规则知识库
     * @return 结果
     */
    @Override
    public int insertDqmKnowledgeBase(DqmKnowledgeBase dqmKnowledgeBase)
    {
    	dqmKnowledgeBase.setCreateTime(DateUtils.getNowDate());
    	dqmKnowledgeBase.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dqmKnowledgeBase.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	dqmKnowledgeBase.setUpdateTime(DateUtils.getNowDate());
    	dqmKnowledgeBase.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dqmKnowledgeBase.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        int i =dqmKnowledgeBaseMapper.insertDqmKnowledgeBase(dqmKnowledgeBase);
        //录入ES数据库
        String flag = sysConfigService.selectConfigByKey("dqm.dqmKnowledgeBase.es");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
            dqmKnowledgeBaseServiceES.save(dqmKnowledgeBase);
        }
        return i;
    }

    /**
     * 修改质量检查规则知识库
     *
     * @param dqmKnowledgeBase 质量检查规则知识库
     * @return 结果
     */
    @Override
    public int updateDqmKnowledgeBase(DqmKnowledgeBase dqmKnowledgeBase)
    {
    	dqmKnowledgeBase.setUpdateTime(DateUtils.getNowDate());
    	dqmKnowledgeBase.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dqmKnowledgeBase.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        int i =dqmKnowledgeBaseMapper.updateDqmKnowledgeBase(dqmKnowledgeBase);
        //录入ES数据库
        String flag = sysConfigService.selectConfigByKey("dqm.dqmKnowledgeBase.es");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
            dqmKnowledgeBaseServiceES.save(dqmKnowledgeBase);
        }
        return i;
    }

    /**
     * 批量删除质量检查规则知识库
     *
     * @param knowledgeBaseIds 需要删除的质量检查规则知识库ID
     * @return 结果
     */
    @Override
    public int deleteDqmKnowledgeBaseByIds(Long[] knowledgeBaseIds)
    {
        return dqmKnowledgeBaseMapper.deleteDqmKnowledgeBaseByIds(knowledgeBaseIds);
    }

    /**
     * 删除质量检查规则知识库信息
     *
     * @param knowledgeBaseId 质量检查规则知识库ID
     * @return 结果
     */
    @Override
    public int deleteDqmKnowledgeBaseById(Long knowledgeBaseId)
    {
        return dqmKnowledgeBaseMapper.deleteDqmKnowledgeBaseById(knowledgeBaseId);
    }

    @Override
    public Map getDqmKnowledgeBaseById(Long knowledgeBaseId) {
        return dqmKnowledgeBaseMapper.getDqmKnowledgeBaseById(knowledgeBaseId);
    }
}
