package com.dqms.mdm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.mdm.domain.MdmCollectHis;
import com.dqms.mdm.service.IMdmCollectHisService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 元数据采集历史Controller
 *
 * <AUTHOR>
 * @date 2021-05-20
 */
@RestController
@RequestMapping("/mdm/mdmCollectHis")
public class MdmCollectHisController extends BaseController
{
    @Autowired
    private IMdmCollectHisService mdmCollectHisService;

    /**
     * 查询元数据采集历史列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectHis:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmCollectHis mdmCollectHis)
    {
        startPage();
        List<MdmCollectHis> list = mdmCollectHisService.selectMdmCollectHisList(mdmCollectHis);
        return getDataTable(list);
    }

    /**
     * 导出元数据采集历史列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectHis:export')")
    @Log(title = "元数据采集历史", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmCollectHis mdmCollectHis)
    {
        List<MdmCollectHis> list = mdmCollectHisService.selectMdmCollectHisList(mdmCollectHis);
        ExcelUtil<MdmCollectHis> util = new ExcelUtil<MdmCollectHis>(MdmCollectHis.class);
        return util.exportExcel(list, "mdmCollectHis");
    }

    /**
     * 获取元数据采集历史详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectHis:query')")
    @GetMapping(value = "/{regName}")
    public AjaxResult getInfo(@PathVariable("regName") String regName)
    {
        return AjaxResult.success(mdmCollectHisService.selectMdmCollectHisById(regName));
    }

    /**
     * 新增元数据采集历史
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectHis:add')")
    @Log(title = "元数据采集历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmCollectHis mdmCollectHis)
    {
        return toAjax(mdmCollectHisService.insertMdmCollectHis(mdmCollectHis));
    }

    /**
     * 修改元数据采集历史
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectHis:edit')")
    @Log(title = "元数据采集历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmCollectHis mdmCollectHis)
    {
        return toAjax(mdmCollectHisService.updateMdmCollectHis(mdmCollectHis));
    }

    /**
     * 删除元数据采集历史
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectHis:remove')")
    @Log(title = "元数据采集历史", businessType = BusinessType.DELETE)
	@DeleteMapping("/{regNames}")
    public AjaxResult remove(@PathVariable String[] regNames)
    {
        return toAjax(mdmCollectHisService.deleteMdmCollectHisByIds(regNames));
    }
}
