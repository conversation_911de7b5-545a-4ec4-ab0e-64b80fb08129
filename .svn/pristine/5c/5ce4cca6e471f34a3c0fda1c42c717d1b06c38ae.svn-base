package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmStandardTarDept;
import com.dqms.dsm.service.IDsmStandardTarDeptService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 标准应用部门Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmStandardTarDept")
public class DsmStandardTarDeptController extends BaseController
{
    @Autowired
    private IDsmStandardTarDeptService dsmStandardTarDeptService;

    /**
     * 查询标准应用部门列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarDept:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmStandardTarDept dsmStandardTarDept)
    {
        startPage();
        List<DsmStandardTarDept> list = dsmStandardTarDeptService.selectDsmStandardTarDeptList(dsmStandardTarDept);
        return getDataTable(list);
    }

    /**
     * 导出标准应用部门列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarDept:export')")
    @Log(title = "标准应用部门", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmStandardTarDept dsmStandardTarDept)
    {
        List<DsmStandardTarDept> list = dsmStandardTarDeptService.selectDsmStandardTarDeptList(dsmStandardTarDept);
        ExcelUtil<DsmStandardTarDept> util = new ExcelUtil<DsmStandardTarDept>(DsmStandardTarDept.class);
        return util.exportExcel(list, "dsmStandardTarDept");
    }

    /**
     * 获取标准应用部门详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarDept:query')")
    @GetMapping(value = "/{standardId}")
    public AjaxResult getInfo(@PathVariable("standardId") Long standardId)
    {
        return AjaxResult.success(dsmStandardTarDeptService.selectDsmStandardTarDeptById(standardId));
    }

    /**
     * 新增标准应用部门
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarDept:add')")
    @Log(title = "标准应用部门", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmStandardTarDept dsmStandardTarDept)
    {
        return toAjax(dsmStandardTarDeptService.insertDsmStandardTarDept(dsmStandardTarDept));
    }

    /**
     * 修改标准应用部门
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarDept:edit')")
    @Log(title = "标准应用部门", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmStandardTarDept dsmStandardTarDept)
    {
        return toAjax(dsmStandardTarDeptService.updateDsmStandardTarDept(dsmStandardTarDept));
    }

    /**
     * 删除标准应用部门
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardTarDept:remove')")
    @Log(title = "标准应用部门", businessType = BusinessType.DELETE)
	@DeleteMapping("/{standardIds}")
    public AjaxResult remove(@PathVariable Long[] standardIds)
    {
        return toAjax(dsmStandardTarDeptService.deleteDsmStandardTarDeptByIds(standardIds));
    }
}
