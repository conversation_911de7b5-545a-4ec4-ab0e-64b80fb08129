package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmModelEntityProp;
import com.dqms.dsm.service.IDsmModelEntityPropService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 模型字段Controller
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@RestController
@RequestMapping("/dsm/dsmModelEntityProp")
public class DsmModelEntityPropController extends BaseController
{
    @Autowired
    private IDsmModelEntityPropService dsmModelEntityPropService;

    /**
     * 查询模型字段列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DsmModelEntityProp dsmModelEntityProp)
    {
        startPage();
        List<DsmModelEntityProp> list = dsmModelEntityPropService.selectDsmModelEntityPropList(dsmModelEntityProp);
        return getDataTable(list);
    }

    /**
     * 导出模型字段列表
     */
    @Log(title = "模型字段", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmModelEntityProp dsmModelEntityProp)
    {
        List<DsmModelEntityProp> list = dsmModelEntityPropService.selectDsmModelEntityPropList(dsmModelEntityProp);
        ExcelUtil<DsmModelEntityProp> util = new ExcelUtil<DsmModelEntityProp>(DsmModelEntityProp.class);
        return util.exportExcel(list, "dsmModelEntityProp");
    }

    /**
     * 获取模型字段详细信息
     */
    @GetMapping(value = "/{modelEntityPropId}")
    public AjaxResult getInfo(@PathVariable("modelEntityPropId") Long modelEntityPropId)
    {
        return AjaxResult.success(dsmModelEntityPropService.selectDsmModelEntityPropById(modelEntityPropId));
    }

    /**
     * 新增模型字段
     */
    @Log(title = "模型字段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmModelEntityProp dsmModelEntityProp)
    {
        return toAjax(dsmModelEntityPropService.insertDsmModelEntityProp(dsmModelEntityProp));
    }

    /**
     * 修改模型字段
     */
    @Log(title = "模型字段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmModelEntityProp dsmModelEntityProp)
    {
        return toAjax(dsmModelEntityPropService.updateDsmModelEntityProp(dsmModelEntityProp));
    }

    /**
     * 删除模型字段
     */
    @Log(title = "模型字段", businessType = BusinessType.DELETE)
	@DeleteMapping("/{modelEntityPropIds}")
    public AjaxResult remove(@PathVariable Long[] modelEntityPropIds)
    {
        return toAjax(dsmModelEntityPropService.deleteDsmModelEntityPropByIds(modelEntityPropIds));
    }
}
