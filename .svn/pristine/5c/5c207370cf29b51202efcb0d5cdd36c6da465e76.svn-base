package com.dqms.task.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Treeselect树结构实体类
 *
*/
public class EtlTaskClassTreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EtlTaskClassTreeSelect> children;

    public EtlTaskClassTreeSelect()
    {

    }

    public EtlTaskClassTreeSelect(EtlTaskClass etlTaskClass)
    {
        this.id = etlTaskClass.getTaskClassId();
        this.label = etlTaskClass.getTaskClassName();
        this.children = etlTaskClass.getChildren().stream().map(EtlTaskClassTreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<EtlTaskClassTreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<EtlTaskClassTreeSelect> children)
    {
        this.children = children;
    }
}
