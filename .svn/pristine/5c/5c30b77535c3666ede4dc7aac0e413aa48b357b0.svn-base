package com.dqms.dsc.service;

import java.util.List;
import com.dqms.dsc.domain.DscMasterSystem;

/**
 * 接口权限Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscMasterSystemService 
{
    /**
     * 查询接口权限
     * 
     * @param masterSystemId 接口权限ID
     * @return 接口权限
     */
    public DscMasterSystem selectDscMasterSystemById(Long masterSystemId);

    /**
     * 查询接口权限列表
     * 
     * @param dscMasterSystem 接口权限
     * @return 接口权限集合
     */
    public List<DscMasterSystem> selectDscMasterSystemList(DscMasterSystem dscMasterSystem);

    /**
     * 新增接口权限
     * 
     * @param dscMasterSystem 接口权限
     * @return 结果
     */
    public int insertDscMasterSystem(DscMasterSystem dscMasterSystem);

    /**
     * 修改接口权限
     * 
     * @param dscMasterSystem 接口权限
     * @return 结果
     */
    public int updateDscMasterSystem(DscMasterSystem dscMasterSystem);

    /**
     * 批量删除接口权限
     * 
     * @param masterSystemIds 需要删除的接口权限ID
     * @return 结果
     */
    public int deleteDscMasterSystemByIds(Long[] masterSystemIds);

    /**
     * 删除接口权限信息
     * 
     * @param masterSystemId 接口权限ID
     * @return 结果
     */
    public int deleteDscMasterSystemById(Long masterSystemId);
}
