package com.dqms.dqm.service.impl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dqm.mapper.DqmValidationTaskMapper;
import com.dqms.dqm.domain.DqmValidationTask;
import com.dqms.dqm.service.IDqmValidationTaskService;

/**
 * 检查规则任务管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
@Service
public class DqmValidationTaskServiceImpl implements IDqmValidationTaskService
{
    @Autowired
    private DqmValidationTaskMapper dqmValidationTaskMapper;

    /**
     * 查询检查规则任务管理
     *
     * @param dqmValidationTaskId 检查规则任务管理ID
     * @return 检查规则任务管理
     */
    @Override
    public DqmValidationTask selectDqmValidationTaskById(Integer dqmValidationTaskId)
    {
        return dqmValidationTaskMapper.selectDqmValidationTaskById(dqmValidationTaskId);
    }

    /**
     * 查询检查规则任务管理列表
     *
     * @param dqmValidationTask 检查规则任务管理
     * @return 检查规则任务管理
     */
    @DataScope(systemAlias = "dvrc")
    @Override
    public List<DqmValidationTask> selectDqmValidationTaskList(DqmValidationTask dqmValidationTask)
    {
        return dqmValidationTaskMapper.selectDqmValidationTaskList(dqmValidationTask);
    }

    /**
     * 新增检查规则任务管理
     *
     * @param dqmValidationTask 检查规则任务管理
     * @return 结果
     */
    @Override
    public int insertDqmValidationTask(DqmValidationTask dqmValidationTask)
    {   dqmValidationTask.setErrorMsg("");
        dqmValidationTask.setCreateTime(DateUtils.getNowDate());
        return dqmValidationTaskMapper.insertDqmValidationTask(dqmValidationTask);
    }

    /**
     * 修改检查规则任务管理
     *
     * @param dqmValidationTask 检查规则任务管理
     * @return 结果
     */
    @Override
    public int updateDqmValidationTask(DqmValidationTask dqmValidationTask)
    {
        dqmValidationTask.setUpdateTime(DateUtils.getNowDate());
        return dqmValidationTaskMapper.updateDqmValidationTask(dqmValidationTask);
    }

    /**
     * 批量删除检查规则任务管理
     *
     * @param dqmValidationTaskIds 需要删除的检查规则任务管理ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationTaskByIds(Integer[] dqmValidationTaskIds)
    {
        return dqmValidationTaskMapper.deleteDqmValidationTaskByIds(dqmValidationTaskIds);
    }

    /**
     * 删除检查规则任务管理信息
     *
     * @param dqmValidationTaskId 检查规则任务管理ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationTaskById(Integer dqmValidationTaskId)
    {
        return dqmValidationTaskMapper.deleteDqmValidationTaskById(dqmValidationTaskId);
    }

    @DataScope(systemAlias = "dvrc")
    @Override
    public List<Map> getExeCuteState30(DqmValidationTask dqmValidationTask) {
        return dqmValidationTaskMapper.getExeCuteState30(dqmValidationTask);
    }
    @Override
    @DataScope(systemAlias = "dvrc")
    public List<Map> getErrorRestule30(DqmValidationTask dqmValidationTask) {
        return dqmValidationTaskMapper.getErrorRestule30(dqmValidationTask);
    }

    @Override
    @DataScope(systemAlias = "dvrc")
    public List<Map> getTaskRun(DqmValidationTask dqmValidationTask) {
        return dqmValidationTaskMapper.getTaskRun(dqmValidationTask);
    }

    @Override
    @DataScope(systemAlias = "dvrc")
    public List<Map> getTaskRunTime(DqmValidationTask dqmValidationTask) {
        return dqmValidationTaskMapper.getTaskRunTime(dqmValidationTask);
    }


}
