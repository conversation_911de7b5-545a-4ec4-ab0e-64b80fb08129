<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dam.mapper.DamAssetsClassMapper">
    
    <resultMap type="DamAssetsClass" id="DamAssetsClassResult">
        <result property="assetsClassId"    column="assets_class_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDamAssetsClassVo">
        select assets_class_id, parent_id, ancestors, class_name, class_name_full, order_num, create_by, update_by, create_id, update_id, create_time, update_time from dam_assets_class
    </sql>

    <select id="selectDamAssetsClassList" parameterType="DamAssetsClass" resultMap="DamAssetsClassResult">
        <include refid="selectDamAssetsClassVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="classNameFull != null  and classNameFull != ''"> and class_name_full like concat('%', #{classNameFull}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
        order by order_num
    </select>
    
    <select id="selectDamAssetsClassById" parameterType="Long" resultMap="DamAssetsClassResult">
        <include refid="selectDamAssetsClassVo"/>
        where assets_class_id = #{assetsClassId}
    </select>

    <select id="selectDamAssetsClassByClassNameFull" parameterType="String" resultMap="DamAssetsClassResult">
        <include refid="selectDamAssetsClassVo"/>
        where class_name = #{className}
    </select>
        
    <insert id="insertDamAssetsClass" parameterType="DamAssetsClass" useGeneratedKeys="true" keyProperty="assetsClassId">
        insert into dam_assets_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="className != null">class_name,</if>
            <if test="classNameFull != null">class_name_full,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="className != null">#{className},</if>
            <if test="classNameFull != null">#{classNameFull},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDamAssetsClass" parameterType="DamAssetsClass">
        update dam_assets_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="classNameFull != null">class_name_full = #{classNameFull},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where assets_class_id = #{assetsClassId}
    </update>

    <delete id="deleteDamAssetsClassById" parameterType="Long">
        delete from dam_assets_class where assets_class_id = #{assetsClassId}
    </delete>

    <delete id="deleteDamAssetsClassByIds" parameterType="String">
        delete from dam_assets_class where assets_class_id in 
        <foreach item="assetsClassId" collection="array" open="(" separator="," close=")">
            #{assetsClassId}
        </foreach>
    </delete>
    
    <select id="selectChildrenClassById" parameterType="Long" resultMap="DamAssetsClassResult">
		select * from dam_assets_class where find_in_set(#{assetsClassId}, ancestors)
	</select>
	
	<update id="updateClassChildren" parameterType="java.util.List">
	    update dam_assets_class set ancestors =
	    <foreach collection="damAssetsClass" item="item" index="index"
	        separator=" " open="case assets_class_id" close="end">
	        when #{item.assetsClassId} then #{item.ancestors}
	    </foreach>
	    where assets_class_id in
	    <foreach collection="damAssetsClass" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.assetsClassId}
	    </foreach>
	</update>
	
	<update id="updateClassNameFullChildren" parameterType="java.util.List">
	    update dam_assets_class set class_name_full =
	    <foreach collection="damAssetsClass" item="item" index="index"
	        separator=" " open="case assets_class_id" close="end">
	        when #{item.assetsClassId} then #{item.classNameFull}
	    </foreach>
	    where assets_class_id in
	    <foreach collection="damAssetsClass" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.assetsClassId}
	    </foreach>
	</update>    
</mapper>