package com.dqms.dsm.service;

import java.util.List;
import com.dqms.dsm.domain.DsmStandardTarSystem;

/**
 * 标准应用系统Service接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface IDsmStandardTarSystemService
{
    /**
     * 查询标准应用系统
     *
     * @param standardId 标准应用系统ID
     * @return 标准应用系统
     */
    public List<DsmStandardTarSystem> selectDsmStandardTarSystemById(Long standardId);

    /**
     * 查询标准应用系统列表
     *
     * @param dsmStandardTarSystem 标准应用系统
     * @return 标准应用系统集合
     */
    public List<DsmStandardTarSystem> selectDsmStandardTarSystemList(DsmStandardTarSystem dsmStandardTarSystem);

    /**
     * 新增标准应用系统
     *
     * @param dsmStandardTarSystem 标准应用系统
     * @return 结果
     */
    public int insertDsmStandardTarSystem(DsmStandardTarSystem dsmStandardTarSystem);

    /**
     * 修改标准应用系统
     *
     * @param dsmStandardTarSystem 标准应用系统
     * @return 结果
     */
    public int updateDsmStandardTarSystem(DsmStandardTarSystem dsmStandardTarSystem);

    /**
     * 批量删除标准应用系统
     *
     * @param standardIds 需要删除的标准应用系统ID
     * @return 结果
     */
    public int deleteDsmStandardTarSystemByIds(Long[] standardIds);

    /**
     * 删除标准应用系统信息
     *
     * @param standardId 标准应用系统ID
     * @return 结果
     */
    public int deleteDsmStandardTarSystemById(Long standardId);
}
