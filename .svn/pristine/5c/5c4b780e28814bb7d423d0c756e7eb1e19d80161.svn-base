package com.dqms.dsm.service;

import java.util.List;
import com.dqms.dsm.domain.DsmMasterDataInstall;

/**
 * 主数据配置Service接口
 * 
 * <AUTHOR>
 * @date 2021-11-23
 */
public interface IDsmMasterDataInstallService 
{
    /**
     * 查询主数据配置
     * 
     * @param masterDataInstallId 主数据配置ID
     * @return 主数据配置
     */
    public DsmMasterDataInstall selectDsmMasterDataInstallById(Long masterDataInstallId);

    /**
     * 查询主数据配置列表
     * 
     * @param dsmMasterDataInstall 主数据配置
     * @return 主数据配置集合
     */
    public List<DsmMasterDataInstall> selectDsmMasterDataInstallList(DsmMasterDataInstall dsmMasterDataInstall);

    /**
     * 新增主数据配置
     * 
     * @param dsmMasterDataInstall 主数据配置
     * @return 结果
     */
    public int insertDsmMasterDataInstall(DsmMasterDataInstall dsmMasterDataInstall);

    /**
     * 修改主数据配置
     * 
     * @param dsmMasterDataInstall 主数据配置
     * @return 结果
     */
    public int updateDsmMasterDataInstall(DsmMasterDataInstall dsmMasterDataInstall);

    /**
     * 批量删除主数据配置
     * 
     * @param masterDataInstallIds 需要删除的主数据配置ID
     * @return 结果
     */
    public int deleteDsmMasterDataInstallByIds(Long[] masterDataInstallIds);

    /**
     * 删除主数据配置信息
     * 
     * @param masterDataInstallId 主数据配置ID
     * @return 结果
     */
    public int deleteDsmMasterDataInstallById(Long masterDataInstallId);
}
