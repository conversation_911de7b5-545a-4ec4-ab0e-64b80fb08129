package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.constant.DicConstants;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.dsm.domain.DsmModelEntity;
import com.dqms.dsm.domain.DsmModelEntityProp;
import com.dqms.dsm.domain.DsmModelEntityPropTemp;
import com.dqms.dsm.domain.DsmModelEntityShip;
import com.dqms.dsm.domain.DsmModelEntityShipTemp;
import com.dqms.dsm.domain.DsmModelEntityTemp;
import com.dqms.dsm.mapper.DsmModelEntityMapper;
import com.dqms.dsm.mapper.DsmModelEntityTempMapper;
import com.dqms.dsm.service.IDsmModelEntityPropService;
import com.dqms.dsm.service.IDsmModelEntityPropTempService;
import com.dqms.dsm.service.IDsmModelEntityService;
import com.dqms.dsm.service.IDsmModelEntityShipService;
import com.dqms.dsm.service.IDsmModelEntityShipTempService;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.domain.MdmDataEntityShip;
import com.dqms.mdm.domain.vo.MdmDataEntityVo;
import com.dqms.mdm.mapper.MdmDataEntityMapper;
import com.dqms.mdm.mapper.MdmDataEntityPropMapper;
import com.dqms.mdm.mapper.MdmDataEntityShipMapper;
import com.dqms.mdm.mapper.MdmRegistryMapper;
import com.dqms.mdm.util.MetaDataContext;
import com.dqms.needs.enums.NesConstants;

/**
 * 模型实例Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@Service
public class DsmModelEntityServiceImpl implements IDsmModelEntityService
{
    @Autowired
    private DsmModelEntityMapper dsmModelEntityMapper;
    
    @Autowired
    private IDsmModelEntityPropService dsmModelEntityPropService;
    
    @Autowired
    private IDsmModelEntityShipService dsmModelEntityShipService;
    
    @Autowired
    private DsmModelEntityTempMapper dsmModelEntityTempMapper;
    
    @Autowired
    private IDsmModelEntityPropTempService dsmModelEntityPropTempService;
    
    @Autowired
    private IDsmModelEntityShipTempService dsmModelEntityShipTempService;
    
    @Autowired
    private MetaDataContext metaDataContext;
    
    @Autowired
    private MdmRegistryMapper mdmRegistryMapper;

    @Autowired
    private MdmDataEntityMapper mdmDataEntityMapper ;

    @Autowired
    private MdmDataEntityPropMapper mdmDataEntityPropMapper ;
    
    @Autowired
    private MdmDataEntityShipMapper mdmDataEntityShipMapper;
    /**
     * 查询模型实例
     *
     * @param modelEntityId 模型实例ID
     * @return 模型实例
     */
    @Override
    public DsmModelEntity selectDsmModelEntityById(Long modelEntityId)
    {
        return dsmModelEntityMapper.selectDsmModelEntityById(modelEntityId);
    }

    /**
     * 查询模型实例列表
     *
     * @param dsmModelEntity 模型实例
     * @return 模型实例
     */
    @Override
    public List<DsmModelEntity> selectDsmModelEntityList(DsmModelEntity dsmModelEntity)
    {
        return dsmModelEntityMapper.selectDsmModelEntityList(dsmModelEntity);
    }

    /**
     * 新增模型实例
     *
     * @param dsmModelEntity 模型实例
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDsmModelEntity(DsmModelEntity dsmModelEntity)
    {
    	dsmModelEntity.setCreateTime(DateUtils.getNowDate());
    	dsmModelEntity.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntity.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntity.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntity.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntity.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntity.setCreateScript(sqlGeneration(dsmModelEntity));
    	int i = dsmModelEntityMapper.insertDsmModelEntity(dsmModelEntity);
    	Map<String,Long> propMap =new HashMap<>();
    	if(dsmModelEntity.getDsmModelEntityProps()!=null&&dsmModelEntity.getDsmModelEntityProps().size()>0) {
    		for(DsmModelEntityProp prop : dsmModelEntity.getDsmModelEntityProps()) {
    			prop.setModelEntityId(dsmModelEntity.getModelEntityId());
    			prop.setCreateTime(DateUtils.getNowDate());
    			prop.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			prop.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			prop.setUpdateTime(DateUtils.getNowDate());
    			prop.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			prop.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityPropService.insertDsmModelEntityProp(prop);
    			propMap.put(prop.getPropName(), prop.getModelEntityPropId());
    		}
    	}
    	
    	if(dsmModelEntity.getDsmModelEntityShips()!=null&&dsmModelEntity.getDsmModelEntityShips().size()>0) {
    		for(DsmModelEntityShip ship : dsmModelEntity.getDsmModelEntityShips()) {
    			if(propMap.get(ship.getSrcModelEntityPropName())==null) {
    				throw new RuntimeException(ship.getSrcModelEntityPropName()+"不存在");
    			}else {
    				ship.setSrcModelEntityPropId(propMap.get(ship.getSrcModelEntityPropName()));
    			}
    			DsmModelEntityProp prop = dsmModelEntityPropService.selectDsmModelEntityPropById(ship.getTarModelEntityPropId());
    			ship.setSrcModelEntityId(dsmModelEntity.getModelEntityId());
    			ship.setTarModelEntityId(prop.getModelEntityId());
    			ship.setCreateTime(DateUtils.getNowDate());
    			ship.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			ship.setUpdateTime(DateUtils.getNowDate());
    			ship.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityShipService.insertDsmModelEntityShip(ship);
    		}
    	}
        return i;
    }

    public String sqlGeneration(DsmModelEntity dsmModelEntity) {
    	DicDataExchange dicDataExchange = new DicDataExchange();
    	dicDataExchange.setTarDatasourceId(Long.parseLong(dsmModelEntity.getDatasourceId()));
    	dicDataExchange.setBucketCol(dsmModelEntity.getBucketCol());
    	dicDataExchange.setBucketNum(dsmModelEntity.getBucketNum());
    	dicDataExchange.setPartitionType(dsmModelEntity.getPartitionType());
    	dicDataExchange.setPartitionColType(dsmModelEntity.getPartitionColType());
    	dicDataExchange.setTarSchema(dsmModelEntity.getTableSchema());
    	dicDataExchange.setTarTableName(dsmModelEntity.getTableName());
    	dicDataExchange.setTableComment(dsmModelEntity.getTableComment());
    	if(dsmModelEntity.getDsmModelEntityProps()!=null&&dsmModelEntity.getDsmModelEntityProps().size()>0) {
    		List<MdmDataEntityProp> sqlVoProps = new ArrayList<MdmDataEntityProp>();
    		int i=0;
    		for(DsmModelEntityProp prop : dsmModelEntity.getDsmModelEntityProps()) {
    			MdmDataEntityProp p = new MdmDataEntityProp();
    			p.setColumnSize(prop.getColumnSize()==null?null:Integer.parseInt(prop.getColumnSize().toString()));
    			p.setDataType(prop.getDataType());
    			p.setDecimalDigits(prop.getDecimalDigits()==null?null:Integer.parseInt(prop.getDecimalDigits().toString()));
    			p.setDefaultValue(prop.getDefaultValue());
    			p.setIsPriKey(prop.getIsPriKey());
    			p.setNullable(prop.getNullable());
    			p.setPropComment(prop.getPropComment());
    			p.setPropDesc(prop.getPropDesc());
    			p.setPropName(prop.getPropName());
    			p.setRemark(prop.getRemark());
    			if(prop.getOrderId()!=null) {
    				p.setOrderId(Integer.parseInt(prop.getOrderId()+""));
    			}else {
    				p.setOrderId(i++);
    			}
    			sqlVoProps.add(p);
    		}
    		dicDataExchange.setProps(sqlVoProps);
    	}
        return metaDataContext.createSqlGeneration(dicDataExchange);
    }
    /**
     * 修改模型实例
     *
     * @param dsmModelEntity 模型实例
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDsmModelEntity(DsmModelEntity dsmModelEntity)
    {
    	dsmModelEntity.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntity.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntity.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	int i = dsmModelEntityMapper.updateDsmModelEntity(dsmModelEntity);
    	
		DsmModelEntityProp dsmModelEntityProp = new DsmModelEntityProp();
		dsmModelEntityProp.setModelEntityId(dsmModelEntity.getModelEntityId());
		List<DsmModelEntityProp> list = dsmModelEntityPropService.selectDsmModelEntityPropList(dsmModelEntityProp);
		Map<String,Long> map =new HashMap<>();
		Map<String,Long> propMap =new HashMap<>();
		for(DsmModelEntityProp prop : list) {
			map.put(prop.getPropName(), prop.getModelEntityPropId());
		}
    	if(dsmModelEntity.getDsmModelEntityProps()!=null&&dsmModelEntity.getDsmModelEntityProps().size()>0) {
    		for(DsmModelEntityProp prop : dsmModelEntity.getDsmModelEntityProps()) {
    			if(map.get(prop.getPropName())==null) {
    				prop.setModelEntityId(dsmModelEntity.getModelEntityId());
        			prop.setCreateTime(DateUtils.getNowDate());
        			prop.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        			prop.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        			prop.setUpdateTime(DateUtils.getNowDate());
        			prop.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        			prop.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        			dsmModelEntityPropService.insertDsmModelEntityProp(prop);
        			propMap.put(prop.getPropName(), prop.getModelEntityPropId());
    			}else {
    				prop.setModelEntityId(dsmModelEntity.getModelEntityId());
    				prop.setModelEntityPropId(map.get(prop.getPropName()));
        			prop.setUpdateTime(DateUtils.getNowDate());
        			prop.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        			prop.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        			dsmModelEntityPropService.updateDsmModelEntityProp(prop);
        			propMap.put(prop.getPropName(), map.get(prop.getPropName()));
        			map.remove(prop.getPropName());
    			}
    			
    		}
    	}
    	for (Map.Entry<String,Long> entry : map.entrySet()) { 
			dsmModelEntityPropService.deleteDsmModelEntityPropById(entry.getValue());
		}
    	
    	dsmModelEntityShipService.deleteDsmModelEntityShipByDsmModelEntityId(dsmModelEntity.getModelEntityId());
    	if(dsmModelEntity.getDsmModelEntityShips()!=null&&dsmModelEntity.getDsmModelEntityShips().size()>0) {
    		for(DsmModelEntityShip ship : dsmModelEntity.getDsmModelEntityShips()) {
    			if(propMap.get(ship.getSrcModelEntityPropName())==null) {
    				throw new RuntimeException(ship.getSrcModelEntityPropName()+"不存在");
    			}else {
    				ship.setSrcModelEntityPropId(propMap.get(ship.getSrcModelEntityPropName()));
    			}
    			DsmModelEntityProp prop = dsmModelEntityPropService.selectDsmModelEntityPropById(ship.getTarModelEntityPropId());
    			ship.setSrcModelEntityId(dsmModelEntity.getModelEntityId());
    			ship.setTarModelEntityId(prop.getModelEntityId());
    			ship.setCreateTime(DateUtils.getNowDate());
    			ship.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			ship.setUpdateTime(DateUtils.getNowDate());
    			ship.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityShipService.insertDsmModelEntityShip(ship);
    		}
    	}
        return i;
    }
    
    @Override
    @Transactional
    public int updateDsmModelEntityToApply(DsmModelEntity dsmModelEntity)
    {
    	DsmModelEntityTemp dsmModelEntityTemp=new DsmModelEntityTemp();
    	BeanUtils.copyBeanProp(dsmModelEntityTemp, dsmModelEntity);
    	dsmModelEntityTemp.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntityTemp.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntityTemp.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntityTemp.setCreateTime(DateUtils.getNowDate());
    	dsmModelEntityTemp.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntityTemp.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntityTemp.setStatus(NesConstants.NES_NEEDS_STATUS_UNSUBMIT);
    	dsmModelEntityTemp.setSqlChange("执行SQL脚本");
    	int i = dsmModelEntityTempMapper.insertDsmModelEntityTemp(dsmModelEntityTemp);
    	Map<String,Long> propMap =new HashMap<>();
    	if(dsmModelEntity.getDsmModelEntityProps()!=null&&dsmModelEntity.getDsmModelEntityProps().size()>0) {
    		for(DsmModelEntityProp prop : dsmModelEntity.getDsmModelEntityProps()) {
    			DsmModelEntityPropTemp propTemp=new DsmModelEntityPropTemp();
    			BeanUtils.copyBeanProp(propTemp, prop);
    			propTemp.setModelEntityId(dsmModelEntityTemp.getModelEntityId());
    			propTemp.setCreateTime(DateUtils.getNowDate());
    			propTemp.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			propTemp.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			propTemp.setUpdateTime(DateUtils.getNowDate());
    			propTemp.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			propTemp.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityPropTempService.insertDsmModelEntityPropTemp(propTemp);
    			propMap.put(propTemp.getPropName(), propTemp.getModelEntityPropId());
    		}
    	}
    	
    	List<DsmModelEntityShip> shipNs=dsmModelEntity.getDsmModelEntityShips();
    	if(shipNs!=null&&shipNs.size()>0) {
    		for(DsmModelEntityShip ship : shipNs) {
    			DsmModelEntityShipTemp shipTemp = new DsmModelEntityShipTemp();
    			if(propMap.get(ship.getSrcModelEntityPropName())==null) {
    				throw new RuntimeException(ship.getSrcModelEntityPropName()+"不存在");
    			}else {
    				shipTemp.setSrcModelEntityPropId(propMap.get(ship.getSrcModelEntityPropName()));
    			}
    			DsmModelEntityProp prop = dsmModelEntityPropService.selectDsmModelEntityPropById(ship.getTarModelEntityPropId());
    			shipTemp.setSrcModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
    			shipTemp.setTarModelEntityId(prop.getModelEntityId());
    			shipTemp.setCreateTime(DateUtils.getNowDate());
    			shipTemp.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			shipTemp.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			shipTemp.setUpdateTime(DateUtils.getNowDate());
    			shipTemp.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			shipTemp.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityShipTempService.insertDsmModelEntityShipTemp(shipTemp);
    			ship.setSrcModelEntityPropId(shipTemp.getSrcModelEntityPropId());
    		}
    	}
    	
    	
    	
		DsmModelEntityProp dsmModelEntityProp = new DsmModelEntityProp();
		dsmModelEntityProp.setModelEntityId(dsmModelEntity.getModelEntityId());
		List<DsmModelEntityProp> list = dsmModelEntityPropService.selectDsmModelEntityPropList(dsmModelEntityProp);
		Map<String,DsmModelEntityProp> map =new HashMap<>();
		boolean flag=false;
		DsmModelEntity dsmModelEntityO= this.selectDsmModelEntityById(dsmModelEntity.getModelEntityId());
		String os=dsmModelEntity.getSqlScript(); String ns=dsmModelEntityO.getSqlScript();
		if((os==null&&ns!=null)||(os!=null&&ns==null)||(os!=null&&ns!=null&&!os.equals(ns))) {
			flag=true;//SQL脚本不一致
		}
		for(DsmModelEntityProp prop : list) {
			map.put(prop.getPropName(), prop);
		}
    	if(dsmModelEntity.getDsmModelEntityProps()!=null&&dsmModelEntity.getDsmModelEntityProps().size()>0) {
    		for(DsmModelEntityProp prop : dsmModelEntity.getDsmModelEntityProps()) {
    			if(map.get(prop.getPropName())==null) {
    				flag=true;
    			}else {
    				DsmModelEntityProp p = map.get(prop.getPropName());
    				String o=p.getPropName(); String n=prop.getPropName();
    				if((o==null&&n!=null)||(o!=null&&n==null)||(o!=null&&n!=null&&!o.equals(n))) {
    					flag=true;//名称不一致
    				}
    				o=p.getPropComment(); n=prop.getPropComment();
    				if((o==null&&n!=null)||(o!=null&&n==null)||(o!=null&&n!=null&&!o.equals(n))) {
    					flag=true;//注释不一致
    				}
    				o=p.getIsPriKey(); n=prop.getIsPriKey();
    				if((o==null&&n!=null)||(o!=null&&n==null)||(o!=null&&n!=null&&!o.equals(n))) {
    					flag=true;//是否主键不一致
    				}
    				o=p.getNullable(); n=prop.getNullable();
    				if((o==null&&n!=null)||(o!=null&&n==null)||(o!=null&&n!=null&&!o.equals(n))) {
    					flag=true;//是否为空不一致
    				}
    				Long ol=p.getColumnSize(); Long nl=prop.getColumnSize();
    				if((ol==null&&nl!=null)||(ol!=null&&nl==null)||(ol!=null&&nl!=null&&!ol.equals(nl))) {
    					flag=true;//数据长度不一致
    				}
    				ol=p.getDecimalDigits(); nl=prop.getDecimalDigits();
    				if((ol==null&&nl!=null)||(ol!=null&&nl==null)||(ol!=null&&nl!=null&&!ol.equals(nl))) {
    					flag=true;//小数位数不一致
    				}
    				o=p.getDataType(); n=prop.getDataType();
    				if((o==null&&n!=null)||(o!=null&&n==null)||(o!=null&&n!=null&&!o.equals(n))) {
    					flag=true;//数据类型不一致
    				}
    				o=p.getDefaultValue(); n=prop.getDefaultValue();
    				if((o==null&&n!=null)||(o!=null&&n==null)||(o!=null&&n!=null&&!o.equals(n))) {
    					flag=true;//默认值不一致
    				}
    				map.remove(prop.getPropName());
    			}
    			
    		}
    	}
    	if(map!=null&&map.size()>0) {
    		flag=true;
    	}
    	
    	List<DsmModelEntityShip> shipOs=dsmModelEntityO.getDsmModelEntityShips();
    	if((shipOs==null&&shipNs!=null)||(shipOs!=null&&shipNs==null)||(shipOs!=null&&shipNs!=null&&shipOs.size()!=shipNs.size())) {
    		flag=true;
    	}
    	Map<Long,Long> shipMap =new HashMap<>();
    	for(DsmModelEntityShip ship : shipOs) {
    		shipMap.put(ship.getSrcModelEntityPropId(), ship.getTarModelEntityPropId());
    	}
    	for(DsmModelEntityShip ship : shipNs) {
    		if(shipMap.get(ship.getSrcModelEntityPropId())==null) {
				flag=true;
			}else {
				if(!shipMap.get(ship.getSrcModelEntityPropId()).equals(ship.getTarModelEntityPropId())) {
					flag=true;	
				}
				shipMap.remove(ship.getSrcModelEntityPropId());
			}
    	}
    	if(shipMap!=null&&shipMap.size()>0) {
    		flag=true;
    	}
    	if(!flag) {
    		throw new RuntimeException("表信息没有变化，不记录修改！");
    	}
        return i;
    }
    /**
     * 批量删除模型实例
     *
     * @param modelEntityIds 需要删除的模型实例ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityByIds(Long[] modelEntityIds)
    {
        return dsmModelEntityMapper.deleteDsmModelEntityByIds(modelEntityIds);
    }

    /**
     * 删除模型实例信息
     *
     * @param modelEntityId 模型实例ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityById(Long modelEntityId)
    {
        return dsmModelEntityMapper.deleteDsmModelEntityById(modelEntityId);
    }

	@Override
	public int impDsmModelEntity(Long regId,Long modelEntityClassId) {
		
		MdmDataEntity mdmDataEntity = mdmDataEntityMapper.selectMdmDataEntityByRegId(regId);
		DsmModelEntity dsmModelEntity= new DsmModelEntity();
		dsmModelEntity.setModelEntityClassId(modelEntityClassId);
		dsmModelEntity.setTableName(mdmDataEntity.getTableName());
		dsmModelEntity.setTableComment(mdmDataEntity.getTableComment());
		dsmModelEntity.setTableSchema(mdmDataEntity.getTableSchema());
		dsmModelEntity.setVersionNo("1");
		dsmModelEntity.setDatasourceId(mdmDataEntity.getDatasourceId()+"");
		dsmModelEntity.setPartitionType(DicConstants.PARTITION_TYPE_N);
		List<MdmDataEntityProp> propList = mdmDataEntityPropMapper.selectMdmDataEntityPropByRegId(regId);
		List<DsmModelEntityProp> dsmModelEntityProps = new ArrayList<>();
		List<DsmModelEntityShip> dsmModelEntityShips = new ArrayList<>();
		dsmModelEntity.setDsmModelEntityProps(dsmModelEntityProps);
		dsmModelEntity.setDsmModelEntityShips(dsmModelEntityShips);
		for(MdmDataEntityProp mdmDataEntityProp : propList) {
			DsmModelEntityProp dsmModelEntityProp = new DsmModelEntityProp();
			dsmModelEntityProp.setPropName(mdmDataEntityProp.getPropName());
			dsmModelEntityProp.setPropComment(mdmDataEntityProp.getPropComment());
			dsmModelEntityProp.setIsPriKey(mdmDataEntityProp.getIsPriKey());
			dsmModelEntityProp.setNullable(mdmDataEntityProp.getNullable());
			if(mdmDataEntityProp.getColumnSize()!=null) {
				dsmModelEntityProp.setColumnSize(Long.parseLong(mdmDataEntityProp.getColumnSize()+""));
			}
			if(mdmDataEntityProp.getDecimalDigits()!=null) {
				dsmModelEntityProp.setDecimalDigits(Long.parseLong(mdmDataEntityProp.getDecimalDigits()+""));	
			}
			dsmModelEntityProp.setDataType(mdmDataEntityProp.getDataType());
			dsmModelEntityProp.setDefaultValue(mdmDataEntityProp.getDefaultValue());
			if(mdmDataEntityProp.getOrderId()!=null) {
				dsmModelEntityProp.setOrderId(Long.parseLong(mdmDataEntityProp.getOrderId()+""));
			}
			dsmModelEntityProp.setPropId(mdmDataEntityProp.getPropId());
			dsmModelEntityProp.setEntityId(mdmDataEntityProp.getEntityId());
			dsmModelEntityProp.setPropDesc(mdmDataEntityProp.getPropDesc());
			dsmModelEntityProps.add(dsmModelEntityProp);
			
		}
		
		//获取字段关系
		List<MdmDataEntityVo>  srcList = mdmDataEntityShipMapper.findShipMdmDataEntityVoByTarEntityIds(new Long[] {mdmDataEntity.getEntityId()},null);
		if(srcList!=null&&srcList.size()>0) {
			for(MdmDataEntityVo vo : srcList) {
				if(vo.getRelations()!=null&&vo.getRelations().size()>0) {
					for(MdmDataEntityShip ship : vo.getRelations()) {
						MdmDataEntity e = mdmDataEntityMapper.selectMdmDataEntityById(ship.getSrcEntityId());
						if(e==null||ship.getSrcPropName()==null) {continue;}
						DsmModelEntityProp dsmModelEntityProp = dsmModelEntityPropService.selectDsmModelEntityPropByName(e.getTableSchema(),e.getTableName(),ship.getSrcPropName());
						if(dsmModelEntityProp==null) {continue;}
						DsmModelEntityShip sh = new DsmModelEntityShip();
						sh.setSrcModelEntityPropName(ship.getTarPropName());
						sh.setTarModelEntityPropId(dsmModelEntityProp.getModelEntityPropId());
						dsmModelEntityShips.add(sh);
					}
				}
			}
		}

		List<MdmDataEntityVo>  tarList = mdmDataEntityShipMapper.findShipMdmDataEntityVoBySrcEntityIds(new Long[] {mdmDataEntity.getEntityId()},null);
		if(tarList!=null&&tarList.size()>0) {
			for(MdmDataEntityVo vo : tarList) {
				if(vo.getRelations()!=null&&vo.getRelations().size()>0) {
					for(MdmDataEntityShip ship : vo.getRelations()) {
						MdmDataEntity e = mdmDataEntityMapper.selectMdmDataEntityById(ship.getTarEntityId());
						if(e==null||ship.getSrcPropName()==null) {continue;}
						DsmModelEntityProp dsmModelEntityProp = dsmModelEntityPropService.selectDsmModelEntityPropByName(e.getTableSchema(),e.getTableName(),ship.getTarPropName());
						if(dsmModelEntityProp==null) {continue;}
						DsmModelEntityShip sh = new DsmModelEntityShip();
						sh.setSrcModelEntityPropName(ship.getSrcPropName());
						sh.setTarModelEntityPropId(dsmModelEntityProp.getModelEntityPropId());
						dsmModelEntityShips.add(sh);
					}
				}
			}
		}
		
		return insertDsmModelEntity(dsmModelEntity);
	}
    
}
