package com.dqms.dsm.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dsm.domain.DsmModelEntityProp;


/**
 * 模型字段Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-11-23
 */
public interface DsmModelEntityPropMapper 
{
    /**
     * 查询模型字段
     * 
     * @param modelEntityPropId 模型字段ID
     * @return 模型字段
     */
    public DsmModelEntityProp selectDsmModelEntityPropById(Long modelEntityPropId);
    public DsmModelEntityProp selectDsmModelEntityPropByName(@Param("tableSchema")String tableSchema,@Param("tableName")String tableName,@Param("propName")String propName);

    /**
     * 查询模型字段列表
     * 
     * @param dsmModelEntityProp 模型字段
     * @return 模型字段集合
     */
    public List<DsmModelEntityProp> selectDsmModelEntityPropList(DsmModelEntityProp dsmModelEntityProp);

    /**
     * 新增模型字段
     * 
     * @param dsmModelEntityProp 模型字段
     * @return 结果
     */
    public int insertDsmModelEntityProp(DsmModelEntityProp dsmModelEntityProp);

    /**
     * 修改模型字段
     * 
     * @param dsmModelEntityProp 模型字段
     * @return 结果
     */
    public int updateDsmModelEntityProp(DsmModelEntityProp dsmModelEntityProp);

    /**
     * 删除模型字段
     * 
     * @param modelEntityPropId 模型字段ID
     * @return 结果
     */
    public int deleteDsmModelEntityPropById(Long modelEntityPropId);

    /**
     * 批量删除模型字段
     * 
     * @param modelEntityPropIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmModelEntityPropByIds(Long[] modelEntityPropIds);
}
