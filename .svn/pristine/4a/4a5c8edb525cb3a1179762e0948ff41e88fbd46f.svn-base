package com.dqms.mdm.mapper;

import java.util.List;
import com.dqms.mdm.domain.MdmCollectFastLog;

/**
 * 元数据快速注册日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-08-09
 */
public interface MdmCollectFastLogMapper 
{
    /**
     * 查询元数据快速注册日志
     * 
     * @param datasourceId 元数据快速注册日志ID
     * @return 元数据快速注册日志
     */
    public MdmCollectFastLog selectMdmCollectFastLogById(Long datasourceId);

    /**
     * 查询元数据快速注册日志列表
     * 
     * @param mdmCollectFastLog 元数据快速注册日志
     * @return 元数据快速注册日志集合
     */
    public List<MdmCollectFastLog> selectMdmCollectFastLogList(MdmCollectFastLog mdmCollectFastLog);

    /**
     * 新增元数据快速注册日志
     * 
     * @param mdmCollectFastLog 元数据快速注册日志
     * @return 结果
     */
    public int insertMdmCollectFastLog(MdmCollectFastLog mdmCollectFastLog);

    /**
     * 修改元数据快速注册日志
     * 
     * @param mdmCollectFastLog 元数据快速注册日志
     * @return 结果
     */
    public int updateMdmCollectFastLog(MdmCollectFastLog mdmCollectFastLog);

    /**
     * 删除元数据快速注册日志
     * 
     * @param datasourceId 元数据快速注册日志ID
     * @return 结果
     */
    public int deleteMdmCollectFastLogById(Long datasourceId);

    /**
     * 批量删除元数据快速注册日志
     * 
     * @param datasourceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMdmCollectFastLogByIds(Long[] datasourceIds);
}
