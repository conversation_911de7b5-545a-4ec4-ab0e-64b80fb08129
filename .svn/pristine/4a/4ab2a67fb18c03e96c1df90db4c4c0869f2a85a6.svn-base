package com.dqms.task.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.common.core.domain.entity.SysDept;
import com.dqms.task.domain.EtlTaskClass;

/**
 * 任务分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-11
 */
public interface EtlTaskClassMapper 
{
    /**
     * 查询任务分类
     * 
     * @param taskClassId 任务分类ID
     * @return 任务分类
     */
    public EtlTaskClass selectEtlTaskClassById(Long taskClassId);
    /**
     * 查询任务分类
     * 
     * @param taskClassName 任务分类Name
     * @return 任务分类
     */
    public EtlTaskClass selectEtlTaskClassByName(String taskClassName);

    /**
     * 查询任务分类列表
     * 
     * @param etlTaskClass 任务分类
     * @return 任务分类集合
     */
    public List<EtlTaskClass> selectEtlTaskClassList(EtlTaskClass etlTaskClass);

    /**
     * 新增任务分类
     * 
     * @param etlTaskClass 任务分类
     * @return 结果
     */
    public int insertEtlTaskClass(EtlTaskClass etlTaskClass);

    /**
     * 修改任务分类
     * 
     * @param etlTaskClass 任务分类
     * @return 结果
     */
    public int updateEtlTaskClass(EtlTaskClass etlTaskClass);

    /**
     * 删除任务分类
     * 
     * @param taskClassId 任务分类ID
     * @return 结果
     */
    public int deleteEtlTaskClassById(Long taskClassId);

    /**
     * 批量删除任务分类
     * 
     * @param taskClassIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskClassByIds(Long[] taskClassIds);
    /**
     * 根据ID查询所有子任务分类
     *
     * @param deptId 任务分类ID
     * @return 任务分类列表
     */
    public List<EtlTaskClass> selectChildrenEtlTaskClassById(Long taskClassId);
    
    /**
     * 修改子元素关系
     *
     * @param taskClasss 子元素
     * @return 结果
     */
    public int updateEtlTaskClassChildren(@Param("taskClasss") List<EtlTaskClass> taskClasss);
    public int updateEtlTaskClassNameFullChildren(@Param("taskClasss") List<EtlTaskClass> taskClasss);
}
