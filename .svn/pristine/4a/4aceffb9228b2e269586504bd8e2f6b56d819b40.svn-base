<template>
  <el-table
    v-el-table-infinite-scroll="handleCurrentChange"
    v-loading="loading"
    height="400px"
    :data="dsmMdmRelUnList"
  >
    <!--<el-table-column type="selection" width="55" align="center" />-->
    <el-table-column label="系统" align="center" prop="systemName" />
    <el-table-column label="数据源" align="center" prop="datasourceName" />
    <el-table-column label="表" align="center" prop="tableName" />
    <el-table-column label="字段" align="center" prop="propName" />
    <el-table-column
      label="操作"
      align="center"
      class-name="small-padding fixed-width"
    >
      <template slot="header" slot-scope="scope">
        <el-input
          v-model="dsmMdmRelUnListSearch"
          size="mini"
          placeholder="输入表名或字段名"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="getDsmMdmRelUnList2"
            size="mini"
          ></el-button>
        </el-input>
      </template>
      <template slot-scope="scope">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-share"
          @click="addDsmMdmRel(scope.row, scope.$index)"
          v-hasPermi="['dsm:dsmStandard:remove']"
          >引用</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import elTableInfiniteScroll from 'el-table-infinite-scroll';
import { unlistDsmMdmRel } from "@/api/dsm/dsmMdmRel";
export default {
    props:['loading'],
    data(){
        return {
            dsmMdmRelUnList: [],
            pageNum:1,
            dsmMdmRelUnListSearch:''
        }
    },
    directives: {
        'el-table-infinite-scroll': elTableInfiniteScroll
    },
    created(){
        this.getDsmMdmRelUnList();
    },
    methods:{
        handleCurrentChange(){
            this.pageNum++
            if(this.pageNum*20<=this.total){
                this.getDsmMdmRelUnList();
            }
        },
        getDsmMdmRelUnList2(){
            this.pageNum = 1
            this.dsmMdmRelUnList = []
            this.getDsmMdmRelUnList();
        },
        getDsmMdmRelUnList() {
            this.dsmMdmRelOpen = true;
            const relQueryParams = {
                dimensionId: this.dimensionId,
                propName: this.dsmMdmRelUnListSearch,
                pageNum: this.pageNum,
                pageSize: 20
            };
            unlistDsmMdmRel(relQueryParams).then(response => {
                this.dsmMdmRelUnList.push(...response.rows);
                this.total = response.total
            });
        },
        addDsmMdmRel(row, index) {
            if(this.$emit("addDsmMdmRel", row, index)){
                this.dsmMdmRelUnList.splice(index, 1);
            }
        },
    }
};
</script>

<style>
</style>
