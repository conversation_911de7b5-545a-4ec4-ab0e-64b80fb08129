<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="名称" prop="regName">
        <el-input
          v-model="queryParams.regName"
          placeholder="请输入表代码/脚本名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="元类型" prop="metaType">
        <el-select
          v-model="queryParams.metaType"
          placeholder="请选择元类型"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="dict in metaTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
       <el-form-item label="采集状态" prop="acqStatus">
              <el-select
                v-model="queryParams.acqStatus"
                placeholder="请选择元类型"
                clearable
                size="small"
                filterable
              >
                <el-option
                  v-for="dict in acqStatusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
      <el-form-item label="数据源" prop="datasourceId">
        <el-select
          v-model="queryParams.datasourceId"
          placeholder="请选择数据源"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属系统" prop="systemId">
        <el-select
          v-model="queryParams.systemId"
          placeholder="请选择所属系统"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属主题" prop="themeId">
        <el-select
          v-model="queryParams.themeId"
          placeholder="请选择所属主题"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in themeOptions"
            :key="item.themeId"
            :label="item.themeName"
            :value="item.themeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属分层" prop="layerId">
        <el-select
          v-model="queryParams.layerId"
          placeholder="请选择所属分层"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="item in layerOptions"
            :key="item.layerId"
            :label="item.layerName"
            :value="item.layerId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否扩展" prop="expandFlag">
        <el-select
          v-model="queryParams.expandFlag"
          placeholder="请选择是否扩展"
          clearable
          size="small"
          filterable
        >
          <el-option
            v-for="dict in expandFlagOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mdm:mdmRegistry:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mdm:mdmRegistry:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mdm:mdmRegistry:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mdm:mdmRegistry:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['mdm:mdmRegistry:export']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-edit-outline"
          size="mini"
          @click="fastRegistry"
          v-hasPermi="['mdm:mdmRegistry:export']"
          >快速注册</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :refreshShow="true"
        :searchShow="true"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="mdmRegistryList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!--      <el-table-column label="元数据注册ID" align="center" prop="regId" />-->
      <el-table-column label="名称" align="center" prop="regName"  width="260"/>
      <el-table-column
        label="元类型"
        align="center"
        prop="metaType"
        :formatter="metaTypeFormat"
      />
      <el-table-column label="采集状态" align="center" prop="acqStatus">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.acqStatus === '1'"
            :type="'primary'"
            disable-transitions
            >未采集</el-tag
          >
          <el-tag
            v-if="scope.row.acqStatus === '2'"
            :type="'success'"
            disable-transitions
            >已采集</el-tag
          >
          <el-tag
            v-if="scope.row.acqStatus === '3'"
            :type="'danger'"
            disable-transitions
            >已废弃</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="中文注释" align="center" prop="regAnt"  width="225"/>
      <el-table-column
        label="数据源类型"
        align="center"
        prop="dsType"
        :formatter="dsTypeFormat"
        width="120"
      />
      <el-table-column label="数据源" align="center" prop="datasourceName"  width="155"/>
      <el-table-column label="所属系统" align="center" prop="systemName"  width="155"/>
      <el-table-column label="所属主题" align="center" prop="themeName" />
      <el-table-column label="所属分层" align="center" prop="layerName" />
      <el-table-column label="启用状态" align="center" prop="isEnable">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isEnable"
            active-value="Y"
            inactive-value="N"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="修改时间"
        align="center"
        prop="updateTime"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="270"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mdm:mdmRegistry:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mdm:mdmRegistry:remove']"
            >删除</el-button
          >
          <el-button
            v-if="scope.row.isEnable == 'Y'"
            size="mini"
            type="text"
            icon="el-icon-menu"
            @click="handleCollect(scope.row)"
            v-hasPermi="['mdm:mdmRegistry:collect']"
            >采集</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['mdm:mdmRegistry:query']"
            v-if="scope.row.metaType==1||scope.row.metaType==2"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-share"
            v-if="scope.row.entityId!=null"
            ><router-link size="mini" :to="'/mdm/mdmDataEntityShip/' + scope.row.entityId" class="link-type" >血缘</router-link></el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改元系统注册对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="表代码/脚本名称" prop="regName">
          <el-input
            v-model="form.regName"
            placeholder="请输入表代码/脚本名称"
            clearable
            :style="{ width: '85%' }"
          />
        </el-form-item>
        <el-form-item label="SCHEMA/脚本路径" prop="regDir">
          <el-input
            v-model="form.regDir"
            placeholder="请输入用户/库/脚本路径"
            clearable
            :style="{ width: '85%' }"
          />
        </el-form-item>
        <el-form-item label="中文注释" prop="regAnt">
          <el-input
            v-model="form.regAnt"
            placeholder="请输入中文注释"
            clearable
            :style="{ width: '85%' }"
          />
        </el-form-item>
        <el-form-item label="元类型">
          <el-radio-group v-model="form.metaType">
            <el-radio
              v-for="dict in metaTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-row type="flex" justify="start" align="top">
          <el-form-item label="启用状态" prop="isEnable">
            <el-select
              v-model="form.isEnable"
              placeholder="请选择启用状态"
              clearable
            >
              <el-option
                v-for="dict in isAutoAnalysisOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据源" prop="datasourceId">
            <el-select
              v-model="form.datasourceId"
              placeholder="请选择数据源"
              filterable
            >
              <el-option
                v-for="item in dataSourceOptions"
                :key="item.datasourceId"
                :label="item.name"
                :value="item.datasourceId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-form-item label="所属系统" prop="systemId">
            <el-select
              v-model="form.systemId"
              placeholder="请选择所属系统"
              filterable
            >
              <el-option
                v-for="item in systemOptions"
                :key="item.systemId"
                :label="item.name"
                :value="item.systemId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属分层" prop="layerId">
            <el-select
              v-model="form.layerId"
              placeholder="请选择所属分层"
              filterable
            >
              <el-option
                v-for="item in layerOptions"
                :key="item.layerId"
                :label="item.layerName"
                :value="item.layerId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-form-item label="所属主题" prop="themeId">
            <el-select
              v-model="form.themeId"
              placeholder="请选择所属主题"
              filterable
              clearable
            >
              <el-option
                v-for="item in themeOptions"
                :key="item.themeId"
                :label="item.themeName"
                :value="item.themeId"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="是否自动解析"
            prop="isAutoAnalysis"
            v-if="form.metaType != '1'"
          >
            <el-select
              v-model="form.isAutoAnalysis"
              placeholder="请选择是否自动解析"
              clearable
            >
              <el-option
                v-for="dict in isAutoAnalysisOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="是否扩展"
            prop="expandFlag"
            v-if="form.metaType == '1'"
          >
            <el-select
              v-model="form.expandFlag"
              placeholder="请选择是否扩展"
              clearable
            >
              <el-option
                v-for="dict in expandFlagOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="titleFast"
      :visible.sync="openFast"
      width="800px"
      append-to-body
    >
      <el-form ref="fastForm" :model="form" :rules="rules" label-width="150px">
        <el-row type="flex" justify="start" align="top">
          <el-form-item label="数据源" prop="datasourceId">
            <el-select
              v-model="form.datasourceId"
              placeholder="请选择数据源"
              filterable
            >
              <el-option
                v-for="item in dataSourceOptions"
                :key="item.datasourceId"
                :label="item.name"
                :value="item.datasourceId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="启用状态" prop="isEnable">
            <el-select
              v-model="form.isEnable"
              placeholder="请选择启用状态"
              clearable
            >
              <el-option
                v-for="dict in isAutoAnalysisOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>

        </el-row>

        <el-form-item label="元类型">
          <el-checkbox-group v-model="form.metaTypes">
            <el-checkbox
              v-for="dict in metaTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              v-if="dict.dictLabel != '脚本' && dict.dictLabel != '报表'"
              >{{ dict.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-row type="flex" justify="start" align="top">
          <el-form-item label="用户/SECHMA" prop="regDir">
            <el-input
              v-model="form.regDir"
              placeholder="请输入用户/SECHMA"
              clearable
            />
          </el-form-item>
          <el-form-item label="采集范围" prop="acqRange">
            <el-input
              v-model="form.acqRange"
              placeholder="请输入正则表达式"
              clearable
            />
          </el-form-item>
        </el-row>

<!--        <el-row type="flex" justify="start" align="top">-->

<!--          <el-form-item label="所属分层" prop="layerId">-->
<!--            <el-select-->
<!--              v-model="form.layerId"-->
<!--              placeholder="请选择所属分层"-->
<!--              filterable-->
<!--            >-->
<!--              <el-option-->
<!--                v-for="item in layerOptions"-->
<!--                :key="item.layerId"-->
<!--                :label="item.layerName"-->
<!--                :value="item.layerId"-->
<!--              ></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-row>-->
        <el-row type="flex" justify="start" align="top">
<!--          <el-form-item label="所属主题" prop="themeId">-->
<!--            <el-select-->
<!--              v-model="form.themeId"-->
<!--              placeholder="请选择所属主题"-->
<!--              filterable-->
<!--            >-->
<!--              <el-option-->
<!--                v-for="item in themeOptions"-->
<!--                :key="item.themeId"-->
<!--                :label="item.themeName"-->
<!--                :value="item.themeId"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="所属系统" prop="systemId">
            <el-select
              v-model="form.systemId"
              placeholder="请选择所属系统"
              filterable
            >
              <el-option
                v-for="item in systemOptions"
                :key="item.systemId"
                :label="item.name"
                :value="item.systemId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否自动解析" prop="isAutoAnalysis">
            <el-select
              v-model="form.isAutoAnalysis"
              placeholder="请选择是否自动解析"
            >
              <el-option
                v-for="dict in isAutoAnalysisOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-form-item label="是否扩展" prop="expandFlag">
          <el-select v-model="form.expandFlag" placeholder="请选择是否扩展">
            <el-option
              v-for="dict in expandFlagOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormFast">确 定</el-button>
        <el-button @click="cancelFast">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 主题导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox
            v-model="upload.updateSupport"
          />是否更新已经存在的元数据,以<span style="font-weight: bold"
            >名称</span
          >为主键更新
          <el-link type="info" style="font-size:12px" @click="importTemplate"
            >下载模板</el-link
          >
        </div>
        <div class="el-upload__tip" style="color:#ff0000" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="openHandle"
      class="handleDialogClass"
      width="1000px"
      append-to-body
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="实体信息" name="first">
          <el-row type="flex" justify="start" align="top">
            <el-col :span="24">
              <!--              <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;margin-left: 20px;font-weight: bold"  >变更后实体信息（<span style="color:#1FB0FF">蓝色</span>为修改,<span style="color:#4FC414">绿色</span>为新增,<span style="color:#FF4050">红色</span>为删除）</el-tag>-->
              <el-table
                :data="entityList"
                border
                :show-header="false"
                style="width: 96%;margin-left: 20px"
              >
                <!--                  :header-cell-class-name="entityHeaderClassName">-->
                <!--                  <el-table-column label="变更后实体信息（红色为修改）" >-->
                <el-table-column label="属性">
                  <template slot-scope="scope">
                    <span style="font-weight:bold;">{{
                      scope.row.attrName
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="值"> </el-table-column>
                <el-table-column
                  prop="changeType"
                  label="是否为改变字段"
                  v-if="false"
                >
                </el-table-column>
                <!--                  </el-table-column>-->
              </el-table>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="属性信息" name="second">
          <el-row type="flex" justify="start" align="top">
            <el-col :span="24">
              <!--              <el-tag type="info"  style="font-size: 15px;margin-bottom: 2px;margin-left: 20px;font-weight: bold"  >变更后字段信息（<span style="color:#1FB0FF">蓝色</span>为修改,<span style="color:#4FC414">绿色</span>为新增,<span style="color:#FF4050">红色</span>为删除）</el-tag>-->
              <!--              <right-toolbar  :columns="columns" :refreshShow="false" :searchShow="false"></right-toolbar>-->
              <el-table
                max-height="500px"
                :data="entityPropList"
                border
                style="width: 96%;margin-left: 20px"
              >
                <!--                <el-table-column label="变更后字段信息（红色为修改,蓝色为新增,-为删除）" >-->
                <el-table-column
                  fixed
                  prop="propName"
                  label="属性名称"
                  min-width="120px"
                  :show-overflow-tooltip="true"
                  v-if="columns[0].visible"
                >
                </el-table-column>
                <el-table-column
                  prop="orderId"
                  label="排序"
                  :show-overflow-tooltip="true"
                  v-if="columns[1].visible"
                >
                </el-table-column>
                <el-table-column
                  prop="propComment"
                  label="属性注释"
                  min-width="120px"
                  :show-overflow-tooltip="true"
                  v-if="columns[2].visible"
                >
                </el-table-column>
                <el-table-column
                  v-if="columns[3].visible"
                  prop="isPriKey"
                  label="是否主键"
                >
                </el-table-column>
                <el-table-column
                  v-if="columns[4].visible"
                  prop="nullable"
                  label="可否为空"
                >
                </el-table-column>
                <el-table-column
                  v-if="columns[5].visible"
                  prop="columnSize"
                  label="列大小"
                >
                </el-table-column>
                <el-table-column
                  v-if="columns[6].visible"
                  prop="decimalDigits"
                  label="小数位数"
                >
                </el-table-column>
                <el-table-column
                  v-if="columns[7].visible"
                  prop="dataType"
                  label="数据类型"
                  min-width="100px"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column
                  v-if="columns[8].visible"
                  prop="defaultValue"
                  label="默认值"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>
                <!--                </el-table-column>-->
              </el-table>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelHandle">关 闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="脚本详细信息"
      :visible.sync="ftpOpen"
      width="1000px"
      append-to-body
    >
      <el-row>
        <el-col :span="24" id="sqlCol">
          <!--            <div style="white-space: pre-wrap"  v-html="ftpForm.sqlScripts" ></div>-->
          <textarea id="sqlText" name="sqlText" />
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="ftpOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMdmRegistry,
  getMdmRegistry,
  delMdmRegistry,
  addMdmRegistry,
  updateMdmRegistry,
  exportMdmRegistry,
  changeRegistryStatus,
  importTemplate,
  collect,
  fastReg
} from "@/api/mdm/mdmRegistry";
import request from "@/utils/request";
import { listDatasourceAll, listSystem } from "@/api/basic/datasource";
import { listMdmThemeAll } from "@/api/mdm/mdmTheme";
import { listMdmLayerAll } from "@/api/mdm/mdmLayer";
import { getToken } from "@/utils/auth";
import { getEntityNewAndOld } from "@/api/mdm/mdmDataMonitor";

import CodeMirror from "codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/merge/merge.js";
import "codemirror/addon/merge/merge.css";
import "codemirror/addon/hint/show-hint.css";
import "codemirror/addon/hint/sql-hint.js";
import { codemirror } from "vue-codemirror";
// 引入主题,配置后生效
import "codemirror/theme/monokai.css";
import "codemirror/theme/idea.css";
//引入语言,配置后生效
import "codemirror/mode/sql/sql.js";
import "codemirror/addon/hint/show-hint.js";
import "codemirror/addon/display/autorefresh.js";

export default {
  name: "MdmRegistry",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      activeName: "first",
      entityList: [],
      entityPropList: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 元系统注册表格数据
      mdmRegistryList: [],
      // 弹出层标题
      title: "",
      titleFast: "快速注册",
      ftpOpen: false,
      ftpForm: {},
      // 是否显示弹出层
      open: false,
      openFast: false,
      // 是否显示详情弹出层
      openHandle: false,
      // 元类型字典
      metaTypeOptions: [],
      // 数据源类型字典
      dsTypeOptions: [],
      // 采集状态字典
      acqStatusOptions: [],
      // 是否自动解析字典
      isAutoAnalysisOptions: [],
      //应用系统选项
      systemOptions: [],
      //数据源选项
      dataSourceOptions: [],
      //主题选项
      themeOptions: [],
      //分层选项
      layerOptions: [],
      //是否主题
      expandFlagOptions: [],
      // 主题导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/mdm/mdmRegistry/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        regName: null,
        metaType: null,
        acqStatus:null,
        datasourceId: null,
        systemId: null,
        themeId: null,
        layerId: null,
        expandFlag: null
      },
      // 列信息
      columns: [
        { key: 0, label: `属性名称`, visible: true },
        { key: 1, label: `排序`, visible: true },
        { key: 2, label: `属性注释`, visible: true },
        { key: 3, label: `是否主键`, visible: true },
        { key: 4, label: `可否为空`, visible: true },
        { key: 5, label: `列大小`, visible: true },
        { key: 6, label: `小数位数`, visible: true },
        { key: 7, label: `数据类型`, visible: true },
        { key: 8, label: `默认值`, visible: true }
      ],
      // 表单参数
      form: {
        metaTypes: []
      },
      // 表单校验
      rules: {
        regName: [
          {
            required: true,
            message: "表代码/脚本名称不能为空",
            trigger: "blur"
          }
        ],
        regDir: [
          {
            required: true,
            message: "用户/库/脚本路径不能为空",
            trigger: "blur"
          }
        ],
        isEnable: [
          { required: true, message: "启用状态不能为空", trigger: "blur" }
        ],
        datasourceId: [
          { required: true, message: "数据源不能为空", trigger: "blur" }
        ],
        systemId: [
          { required: true, message: "所属系统不能为空", trigger: "blur" }
        ],
        layerId: [
          { required: true, message: "所属分层不能为空", trigger: "blur" }
        ],
        acqRange: [
          { required: true, message: "采集范围不能为空", trigger: "blur" }
        ]
        // ,
        // isAutoAnalysis: [
        //   { required: true, message: "是否自动解析不能为空", trigger: "blur" }
        // ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("mdm_meta_type").then(response => {
      this.metaTypeOptions = response.data;
    });
    this.getDicts("ds_type").then(response => {
      this.dsTypeOptions = response.data;
    });

    this.getDicts("mdm_acq_status").then(response => {
      this.acqStatusOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isAutoAnalysisOptions = response.data;
      this.expandFlagOptions = response.data;
    });
    this.getSystem();
    this.getDataSource();
    this.getMdmTheme();
    this.getMdmLayer();
  },
  methods: {
    /** 查询元系统注册列表 */
    getList() {
      this.loading = true;
      listMdmRegistry(this.queryParams).then(response => {
        this.mdmRegistryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 元类型字典翻译
    metaTypeFormat(row, column) {
      return this.selectDictLabel(this.metaTypeOptions, row.metaType);
    },
    // 数据源类型字典翻译
    dsTypeFormat(row, column) {
      return this.selectDictLabel(this.dsTypeOptions, row.dsType);
    },
    // 采集状态字典翻译
    acqStatusFormat(row, column) {
      return this.selectDictLabel(this.acqStatusOptions, row.acqStatus);
    },
    // 是否自动解析字典翻译
    isAutoAnalysisFormat(row, column) {
      return this.selectDictLabel(
        this.isAutoAnalysisOptions,
        row.isAutoAnalysis
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelHandle() {
      this.activeName = "first";
      this.entityList = [];
      this.entityPropList = [];
      this.openHandle = false;
    },
    // 表单重置
    reset() {
      this.form = {
        acqRange: "^T_.*",
        regId: null,
        regName: null,
        regDir: null,
        metaTypes: [],
        metaType: "1",
        acqStatus: null,
        datasourceId: null,
        systemId: null,
        themeId: null,
        regAnt: null,
        layerId: null,
        isAutoAnalysis: "N",
        isEnable: "Y",
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null,
        expandFlag: "N"
      };
      this.resetForm("form");
      this.resetForm("fastForm");
    },
    // 删除状态修改
    handleStatusChange(row) {
      let text = row.isEnable === "Y" ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.regName + '"元数据吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return changeRegistryStatus(row.regId, row.isEnable);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function() {
          row.isEnable = row.isEnable === "N" ? "Y" : "N";
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.regId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加元系统注册";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const regId = row.regId || this.ids;
      getMdmRegistry(regId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改元系统注册";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.regId != null) {
            updateMdmRegistry(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMdmRegistry(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 采集数据 */
    handleCollect(row) {
      let selthis = this;
      this.loading = true;
      collect(row)
        .then(response => {
          this.msgSuccess(response.msg);
          this.getList();
          this.loading = false;
        })
        .catch(function(error) {
          selthis.loading = false;
        });
    },

    /** 查看详情 */
    handleView(row) {
      let name = "";
      name = row.metaType == "2" ? "视图" : "表";
      getEntityNewAndOld(row).then(response => {
        const entityData =
          response.data.oldEntity == null ? [] : response.data.oldEntity;
        let entityProp =
          response.data.oldEntityProp == null
            ? []
            : response.data.oldEntityProp;
        this.entityList = [
          {
            attrName: name + "名",
            value: entityData.tableName,
            changeType: "0"
          },
          {
            attrName: name + "注释",
            value: entityData.tableComment,
            changeType: "0"
          },
          {
            attrName: "SCHEMA",
            value: entityData.tableSchema,
            changeType: "0"
          }
        ];
        this.entityPropList = entityProp;
        this.title = "元数据采集详细信息";
        this.openHandle = true;
      });
    },
    /** 查看详情 */
    handleFtpView(row) {
      this.row = row;
      getEntityNewAndOld(row).then(response => {
        const entityData =
          response.data.oldEntity == null ? [] : response.data.oldEntity;
        this.ftpOpen = true;
        let editor = null;
        this.$nextTick(function() {
          const targetF = document.getElementById("sqlCol");
          targetF.innerHTML = '<textarea id="sqlText" name="sqlText" />';
          const target = document.getElementById("sqlText");
          editor = CodeMirror.fromTextArea(target, {
            lineNumbers: true, //显示行号
            styleActiveLine: true,
            matchBrackets: true,
            mode: "text/x-sql",
            connect: "align",
            theme: "monokai",
            readOnly: true, //只读 不可修改
            autoRefresh: true,
            hintOptions: {
              completeSingle: true // 当匹配只有一项的时候是否自动补全
            },
            extraKeys: { Ctrl: "autocomplete" }
          });
          if (entityData.length == 0) {
            editor.setValue("");
          } else {
            editor.setValue(entityData.sqlScripts);
          }
        });
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const regIds = row.regId || this.ids;
      this.$confirm(
        '是否确认删除元系统注册编号为"' + regIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delMdmRegistry(regIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有元系统注册数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportMdmRegistry(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "元数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 获取应用系统 */
    getSystem() {
      listSystem().then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    getMdmTheme() {
      listMdmThemeAll().then(response => {
        this.themeOptions = response.data;
      });
    },
    getMdmLayer() {
      listMdmLayerAll().then(response => {
        this.layerOptions = response.data;
      });
    },
    // 应用系统翻译
    systemFormat(row, column) {
      return this.selectLabel(
        this.systemOptions,
        row.systemId,
        "systemId",
        "name"
      );
    },
    // 数据源翻译
    dataSourceFormat(row, column) {
      return this.selectLabel(
        this.dataSourceOptions,
        row.datasourceId,
        "datasourceId",
        "name"
      );
    },
    // 主题翻译
    themeFormat(row, column) {
      return this.selectLabel(
        this.themeOptions,
        row.themeId,
        "themeId",
        "themeName"
      );
    },
    // 分层翻译
    mdmLayerFormat(row, column) {
      return this.selectLabel(
        this.layerOptions,
        row.layerId,
        "layerId",
        "layerName"
      );
    },
    fastRegistry() {
      this.reset();
      this.openFast = true;
    },
    cancelFast() {
      this.openFast = false;
      this.reset();
    },
    submitFormFast() {
      this.$refs["fastForm"].validate(valid => {
        if (valid) {
          fastReg(this.form).then(response => {
            this.msgSuccess(response.msg);
            this.openFast = false;
            this.getList();
          });
        }
      });
    }
  }
};
</script>
<style scoped>
>>> .CodeMirror pre.CodeMirror-line,
>>> .CodeMirror pre.CodeMirror-line-like {
  line-height: 22px;
}
>>> .CodeMirror {
  height: 550px !important;
}
</style>
