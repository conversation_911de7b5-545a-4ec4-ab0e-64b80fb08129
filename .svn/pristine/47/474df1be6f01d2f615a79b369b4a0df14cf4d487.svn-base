import request from '@/utils/request'

// 查询标准来源部门列表
export function listDsmStandardSrcDept(query) {
  return request({
    url: '/dsm/dsmStandardSrcDept/list',
    method: 'get',
    params: query
  })
}

// 查询标准来源部门详细
export function getDsmStandardSrcDept(standardId) {
  return request({
    url: '/dsm/dsmStandardSrcDept/' + standardId,
    method: 'get'
  })
}

// 新增标准来源部门
export function addDsmStandardSrcDept(data) {
  return request({
    url: '/dsm/dsmStandardSrcDept',
    method: 'post',
    data: data
  })
}

// 修改标准来源部门
export function updateDsmStandardSrcDept(data) {
  return request({
    url: '/dsm/dsmStandardSrcDept',
    method: 'put',
    data: data
  })
}

// 删除标准来源部门
export function delDsmStandardSrcDept(standardId) {
  return request({
    url: '/dsm/dsmStandardSrcDept/' + standardId,
    method: 'getInfo'
  })
}

// 导出标准来源部门
export function exportDsmStandardSrcDept(query) {
  return request({
    url: '/dsm/dsmStandardSrcDept/export',
    method: 'get',
    params: query
  })
}
