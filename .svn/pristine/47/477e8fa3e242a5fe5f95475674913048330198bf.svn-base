package com.dqms.task.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 任务监控对象 etl_task_instance
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
public class EtlTaskInstance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 实例主键 */
    private Long taskInstanceId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 调度ID */
    @Excel(name = "调度ID")
    private Long schedulerId;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchId;

    /** 优先级 */
    @Excel(name = "优先级")
    private Integer priorityNo;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 执行参数 */
    @Excel(name = "执行参数")
    private String loadDate;

    /** 触发类型 */
    @Excel(name = "触发类型")
    private String triggerType;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 信息 */
    @Excel(name = "信息")
    private String msg;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** 执行方式 */
    private String[] runType;

    private Long[] taskIds;

    private Long taskClassId;

    private EtlTask etlTask;

    /** 任务名称 */
    private String taskName;

    private String ip;

    /** 任务分组 */
    private Long taskGroupId;

    private String hours;

    private String seconds;

    private String minutes;

    private String execTime;

    public String getHours() {
        return hours;
    }

    public void setHours(String hours) {
        this.hours = hours;
    }

    public String getSeconds() {
        return seconds;
    }

    public void setSeconds(String seconds) {
        this.seconds = seconds;
    }

    public String getMinutes() {
        return minutes;
    }

    public void setMinutes(String minutes) {
        this.minutes = minutes;
    }

    public void setTaskInstanceId(Long taskInstanceId)
    {
        this.taskInstanceId = taskInstanceId;
    }

    public Long getTaskInstanceId()
    {
        return taskInstanceId;
    }
    public void setTaskId(Long taskId)
    {
        this.taskId = taskId;
    }

    public Long getTaskId()
    {
        return taskId;
    }
    public void setSchedulerId(Long schedulerId)
    {
        this.schedulerId = schedulerId;
    }

    public Long getSchedulerId()
    {
        return schedulerId;
    }
    public void setBatchId(String batchId)
    {
        this.batchId = batchId;
    }

    public String getBatchId()
    {
        return batchId;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setLoadDate(String loadDate)
    {
        this.loadDate = loadDate;
    }

    public String getLoadDate()
    {
        return loadDate;
    }
    public void setTriggerType(String triggerType)
    {
        this.triggerType = triggerType;
    }

    public String getTriggerType()
    {
        return triggerType;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public String getMsg()
    {
        return msg;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public String[] getRunType() {
		return runType;
	}

	public void setRunType(String[] runType) {
		this.runType = runType;
	}

	public Long[] getTaskIds() {
		return taskIds;
	}

	public void setTaskIds(Long[] taskIds) {
		this.taskIds = taskIds;
	}

	public EtlTask getEtlTask() {
		return etlTask;
	}

	public void setEtlTask(EtlTask etlTask) {
		this.etlTask = etlTask;
	}


	public Integer getPriorityNo() {
		return priorityNo;
	}

	public void setPriorityNo(Integer priorityNo) {
		this.priorityNo = priorityNo;
	}

	public Long getTaskClassId() {
		return taskClassId;
	}

	public void setTaskClassId(Long taskClassId) {
		this.taskClassId = taskClassId;
	}

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Long getTaskGroupId() {
		return taskGroupId;
	}

	public void setTaskGroupId(Long taskGroupId) {
		this.taskGroupId = taskGroupId;
	}

	public String getExecTime() {
		return execTime;
	}

	public void setExecTime(String execTime) {
		this.execTime = execTime;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskInstanceId", getTaskInstanceId())
            .append("taskId", getTaskId())
            .append("schedulerId", getSchedulerId())
            .append("batchId", getBatchId())
            .append("status", getStatus())
            .append("loadDate", getLoadDate())
            .append("triggerType", getTriggerType())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("msg", getMsg())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
