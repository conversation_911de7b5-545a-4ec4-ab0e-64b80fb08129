package com.dqms.dqm.service.impl;


import java.util.List;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.dqm.mapper.DqmValidationSubscriptionMapper;
import com.dqms.dqm.domain.DqmValidationSubscription;
import com.dqms.dqm.service.IDqmValidationSubscriptionService;
import com.dqms.task.enums.EtlConstants;

/**
 * 规则任务订阅Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@Service
public class DqmValidationSubscriptionServiceImpl implements IDqmValidationSubscriptionService
{
    @Autowired
    private DqmValidationSubscriptionMapper dqmValidationSubscriptionMapper;
    /**
     * 查询规则任务订阅
     *
     * @param validationSubscriptionId 规则任务订阅ID
     * @return 规则任务订阅
     */
    @Override
    public DqmValidationSubscription selectDqmValidationSubscriptionById(Long validationSubscriptionId)
    {
        return dqmValidationSubscriptionMapper.selectDqmValidationSubscriptionById(validationSubscriptionId);
    }

    /**
     * 查询规则任务订阅列表
     *
     * @param dqmValidationSubscription 规则任务订阅
     * @return 规则任务订阅
     */
    @Override
    public List<DqmValidationSubscription> selectDqmValidationSubscriptionList(DqmValidationSubscription dqmValidationSubscription)
    {
        return dqmValidationSubscriptionMapper.selectDqmValidationSubscriptionList(dqmValidationSubscription);
    }

    /**
     * 新增规则任务订阅
     *
     * @param validationRuleCateIds 规则任务订阅
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDqmValidationSubscription(Long[] validationRuleCateIds)
    {
        DqmValidationSubscription dqmValidationSubscription=new DqmValidationSubscription();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        dqmValidationSubscription.setCreateId(loginUser.getUser().getUserId());
        dqmValidationSubscription.setCreateBy(loginUser.getUser().getNickName());
        if(validationRuleCateIds != null && validationRuleCateIds.toString().length() > 0){
            for (Long validationRuleCateId : validationRuleCateIds) {
                dqmValidationSubscription.setCreateTime(DateUtils.getNowDate());
                dqmValidationSubscription.setValidationRuleCateId(validationRuleCateId);
                if(dqmValidationSubscriptionMapper.selectDqmValidationSubscriptionList(dqmValidationSubscription).size()==0){
                    dqmValidationSubscriptionMapper.insertDqmValidationSubscription(dqmValidationSubscription);
                }
            }

        }
        return 1;
    }

    /**
     * 修改规则任务订阅
     *
     * @param dqmValidationSubscription 规则任务订阅
     * @return 结果
     */
    @Override
    public int updateDqmValidationSubscription(DqmValidationSubscription dqmValidationSubscription)
    {
        return dqmValidationSubscriptionMapper.updateDqmValidationSubscription(dqmValidationSubscription);
    }

    /**
     * 批量删除规则任务订阅
     *
     * @param validationSubscriptionIds 需要删除的规则任务订阅ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationSubscriptionByIds(Long[] validationSubscriptionIds)
    {
        return dqmValidationSubscriptionMapper.deleteDqmValidationSubscriptionByIds(validationSubscriptionIds);
    }

    /**
     * 删除规则任务订阅信息
     *
     * @param validationSubscriptionId 规则任务订阅ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationSubscriptionById(Long validationSubscriptionId)
    {
        return dqmValidationSubscriptionMapper.deleteDqmValidationSubscriptionById(validationSubscriptionId);
    }

    @Override
    @Transactional
    public int deleteByValidationRuleCateIdAndCreateId(Long validationSubscriptionId) {
        DqmValidationSubscription dqmValidationSubscription=new DqmValidationSubscription();
        dqmValidationSubscription.setValidationRuleCateId(validationSubscriptionId);
        dqmValidationSubscription.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        return dqmValidationSubscriptionMapper.deleteByValidationRuleCateIdAndCreateId(dqmValidationSubscription);
    }
    
    @Override
    @Transactional
    public int subscriptionChange(DqmValidationSubscription dqmValidationSubscription)
    {
    	LoginUser loginUser = SecurityUtils.getLoginUser();
    	DqmValidationSubscription subscription =new DqmValidationSubscription();
    	subscription.setValidationRuleCateId(dqmValidationSubscription.getValidationRuleCateId());
    	subscription.setCreateId(loginUser.getUser().getUserId());;
    	List<DqmValidationSubscription> list = dqmValidationSubscriptionMapper.selectDqmValidationSubscriptionList(dqmValidationSubscription);
    	
    	if(list!=null&&list.size()>0) {
    		subscription=list.get(0);
    		if(dqmValidationSubscription.getType().equals("emailFlag")) {
    			if(subscription.getEmailFlag().equals(EtlConstants.YES)) {
    				subscription.setEmailFlag(EtlConstants.NO);
    			}else {
    				subscription.setEmailFlag(EtlConstants.YES);
    			}
    		}else  {
    			if(subscription.getSmsFlag().equals(EtlConstants.YES)) {
    				subscription.setSmsFlag(EtlConstants.NO);
    			}else {
    				subscription.setSmsFlag(EtlConstants.YES);
    			}
    			subscription.setCreateId(loginUser.getUser().getUserId());
    			dqmValidationSubscriptionMapper.updateDqmValidationSubscription(subscription);
    		};
    	}else {
    		if(dqmValidationSubscription.getType().equals("emailFlag")) {
    			dqmValidationSubscription.setEmailFlag(EtlConstants.YES);
    			dqmValidationSubscription.setSmsFlag(EtlConstants.NO);
    		}else  {
    			dqmValidationSubscription.setEmailFlag(EtlConstants.NO);
    			dqmValidationSubscription.setSmsFlag(EtlConstants.YES);
    		}
            dqmValidationSubscription.setCreateId(loginUser.getUser().getUserId());
            dqmValidationSubscription.setCreateBy(loginUser.getUser().getNickName());
            dqmValidationSubscription.setCreateTime(DateUtils.getNowDate());
            dqmValidationSubscriptionMapper.insertDqmValidationSubscription(dqmValidationSubscription);
    	}
        return 1;
    }
}

