package com.dqms.task.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.domain.EtlTaskSchedule;
import com.dqms.task.domain.vo.EtlTaskInstanceVo;
import com.dqms.task.domain.vo.TaskRunFbVo;
import com.dqms.task.domain.vo.TaskRunTimeVo;

/**
 * 任务监控Service接口
 * 
 * <AUTHOR>
 * @date 2021-03-15
 */
public interface IEtlTaskInstanceService 
{
    /**
     * 查询任务监控
     * 
     * @param taskInstanceId 任务监控ID
     * @return 任务监控
     */
    public EtlTaskInstance selectEtlTaskInstanceById(Long taskInstanceId);

    /**
     * 查询任务监控列表
     * 
     * @param etlTaskInstance 任务监控
     * @return 任务监控集合
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceList(EtlTaskInstance etlTaskInstance);
    public List<EtlTaskInstanceVo> selectEtlTaskInstanceVoList(EtlTaskInstance etlTaskInstance);
    public List<EtlTaskInstance> selectEtlTaskInstanceListByPage(EtlTaskInstance etlTaskInstance);
    
    /**
     * 根据批次查询任务监控列表
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceListByBatch(EtlTaskInstance etlTaskInstance);

    /**
     * 新增任务监控
     * 
     * @param etlTaskInstance 任务监控
     * @return 结果
     */
    public int insertEtlTaskInstance(EtlTaskInstance etlTaskInstance);

    /**
     * 修改任务监控
     * 
     * @param etlTaskInstance 任务监控
     * @return 结果
     */
    public int updateEtlTaskInstance(EtlTaskInstance etlTaskInstance);

    /**
     * 批量删除任务监控
     * 
     * @param taskInstanceIds 需要删除的任务监控ID
     * @return 结果
     */
    public int deleteEtlTaskInstanceByIds(Long[] taskInstanceIds);

    /**
     * 删除任务监控信息
     * 
     * @param taskInstanceId 任务监控ID
     * @return 结果
     */
    public int deleteEtlTaskInstanceById(Long taskInstanceId);
    
    /**
     * 手动执行任务监控
     * 
     * @param etlTaskInstance 任务监控
     * @return 结果
     */
    public int insertEtlTaskInstanceBySd(EtlTaskInstance etlTaskInstance);
    
    /**
     * 调度执行任务监控
     * 
     * @param etlTaskInstance 任务监控
     * @return 结果
     */
    public int insertEtlTaskInstanceByDd(EtlTaskInstance etlTaskInstance,EtlTaskSchedule etlTaskSchedule);
    
    /**
     * 查询任务Ids分组查所有监控列表
     * 
     * @param etlTaskInstance 任务监控
     * @return 任务监控集合
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceListByIds(EtlTaskInstance etlTaskInstance);
    /**
     * 查询所有可以执行的任务列表
     * 
     * @param etlTaskInstance 任务监控
     * @return 任务监控集合
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceListByAbleRun(EtlTaskInstance etlTaskInstance);
    
    /**
     * 开始任务
     * 
     * @param taskInstanceId 任务监控ID
     * @return 结果
     */
    public int startEtlTaskInstance(Long taskInstanceId,String batchId,Date date);
    /**
     *根据任务监控ID终止所有后续任务及组任务
     * 
     * @param taskInstanceId 任务监控ID
     * @return 结果
     */
    public void terminatePostTask(Long taskInstanceId,String batchId);
    
    /**
     * 查看当前组内任务是否都已经完成
     * 
     * @param EtlTaskInstance etlTaskInstanceID
     * @return 结果
     */
    public int getUnTaskEtlTaskByBatchIdAndPriorityNo(EtlTaskInstance etlTaskInstance);
    
    /**
     * 终止后续任务
     * 
     * @param taskInstanceIds 
     * @return 结果
     */
    public void terminateLine(Long[] taskInstanceIds);
    
    /**
     * 终止批次任务
     * 
     * @param taskInstanceIds
     * @return 结果
     */
    public void terminateAll(Long[] taskInstanceIds);
    
    /**
     * 清空任务实例
     * 
     * @param taskInstanceIds
     * @return 结果
     */
    public int deleteAllEtlTaskInstance();
    
    /**
     * 断点重调失败任务
     * 
     * @param taskInstanceIds 
     * @return 结果
     */
    public void resetTask(Long[] taskInstanceIds);
    
    /**
     * 查看调度上次是否都已经完成
     * 
     * @param taskScheduleId taskScheduleId
     * @return 结果
     */
    public int getUnTaskEtlTaskBySchedule(Long taskScheduleId);
    
    public List<TaskRunFbVo> getRunFb();
    public List<TaskRunFbVo> getRunZl();
    public List<TaskRunFbVo> getRunWcl();
    public List<TaskRunFbVo> getRunThread();
    public List<TaskRunFbVo> getRunGroup();
    public List<TaskRunFbVo> getRunAgent();
    public List<EtlTask> getRunHs();
    public List<TaskRunTimeVo> getRunTime();
    public List<EtlTask> getRunYc();

}
