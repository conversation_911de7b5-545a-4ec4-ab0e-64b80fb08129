<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.system.mapper.SysUserSystemMapper">

	<resultMap type="SysUserSystem" id="SysUserSystemResult">
		<result property="userId"     column="user_id"      />
		<result property="systemId"     column="system_id"      />
	</resultMap>

	<delete id="deleteUserSystemByUserId" parameterType="Long">
		delete from sys_user_system where user_id=#{userId}
	</delete>

	<select id="countUserSystemBySystemId" resultType="Integer">
	    select count(1) from sys_user_system where system_id=#{systemId}
	</select>

	<delete id="deleteUserSystem" parameterType="Long">
 		delete from sys_user_system where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>

	<insert id="batchUserSystem">
		insert into sys_user_system(user_id, system_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.userId},#{item.systemId})
		</foreach>
	</insert>

	<delete id="deleteUserSystemInfo" parameterType="SysUserRole">
		delete from sys_user_system where user_id=#{userId} and system_id=#{systemId}
	</delete>

	<delete id="deleteUserSystemInfos">
	    delete from sys_user_system where system_id=#{systemId} and user_id in
 	    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
 	        #{userId}
            </foreach>
	</delete>
</mapper>
