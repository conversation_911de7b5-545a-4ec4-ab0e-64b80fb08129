import request from '@/utils/request'

// 查询字段应用系统列表
export function listDscEntityPropSystem(query) {
  return request({
    url: '/dsc/dscEntityPropSystem/list',
    method: 'get',
    params: query
  })
}

// 查询字段应用系统详细
export function getDscEntityPropSystem(entityPropId) {
  return request({
    url: '/dsc/dscEntityPropSystem/' + entityPropId,
    method: 'get'
  })
}

// 新增字段应用系统
export function addDscEntityPropSystem(data) {
  return request({
    url: '/dsc/dscEntityPropSystem',
    method: 'post',
    data: data
  })
}

// 修改字段应用系统
export function updateDscEntityPropSystem(data) {
  return request({
    url: '/dsc/dscEntityPropSystem',
    method: 'put',
    data: data
  })
}

// 删除字段应用系统
export function delDscEntityPropSystem(entityPropId) {
  return request({
    url: '/dsc/dscEntityPropSystem/' + entityPropId,
    method: 'delete'
  })
}

// 导出字段应用系统
export function exportDscEntityPropSystem(query) {
  return request({
    url: '/dsc/dscEntityPropSystem/export',
    method: 'get',
    params: query
  })
}