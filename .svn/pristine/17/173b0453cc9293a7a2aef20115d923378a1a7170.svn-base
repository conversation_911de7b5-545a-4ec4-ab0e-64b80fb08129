package com.dqms.task.mapper;

import java.util.List;
import com.dqms.task.domain.EtlTaskGroup;
import com.dqms.task.domain.EtlTaskInstance;

/**
 * 任务分组Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-08
 */
public interface EtlTaskGroupMapper 
{
    /**
     * 查询任务分组
     * 
     * @param taskGroupId 任务分组ID
     * @return 任务分组
     */
    public EtlTaskGroup selectEtlTaskGroupById(Long taskGroupId);
    /**
     * 查询任务分组
     * 
     * @param taskGroupName 任务分组Name
     * @return 任务分组
     */
    public EtlTaskGroup selectEtlTaskGroupByName(String taskGroupName);
    /**
     * 查询任务分组列表
     * 
     * @param etlTaskGroup 任务分组
     * @return 任务分组集合
     */
    public List<EtlTaskGroup> selectEtlTaskGroupList(EtlTaskGroup etlTaskGroup);

    /**
     * 新增任务分组
     * 
     * @param etlTaskGroup 任务分组
     * @return 结果
     */
    public int insertEtlTaskGroup(EtlTaskGroup etlTaskGroup);

    /**
     * 修改任务分组
     * 
     * @param etlTaskGroup 任务分组
     * @return 结果
     */
    public int updateEtlTaskGroup(EtlTaskGroup etlTaskGroup);

    /**
     * 删除任务分组
     * 
     * @param taskGroupId 任务分组ID
     * @return 结果
     */
    public int deleteEtlTaskGroupById(Long taskGroupId);

    /**
     * 批量删除任务分组
     * 
     * @param taskGroupIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskGroupByIds(Long[] taskGroupIds);
    
    /**
     * 查询任务分组的所有后置任务组列表
     * 
     * @param long[] taskGroupIds
     * @return 任务分组集合
     */
    public List<EtlTaskGroup> selectPosEtlGroupTask(Long[] taskGroupIds);
    
    
}
