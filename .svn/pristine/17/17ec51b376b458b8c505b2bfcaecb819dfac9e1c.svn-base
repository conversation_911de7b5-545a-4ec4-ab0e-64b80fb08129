package com.dqms.dam.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dam.domain.DamAssetsEvaluate;
import com.dqms.dam.service.IDamAssetsEvaluateService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 资产评价Controller
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
@RestController
@RequestMapping("/dam/damAssetsEvaluate")
public class DamAssetsEvaluateController extends BaseController
{
    @Autowired
    private IDamAssetsEvaluateService damAssetsEvaluateService;

    /**
     * 查询资产评价列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DamAssetsEvaluate damAssetsEvaluate)
    {
        startPage();
        List<DamAssetsEvaluate> list = damAssetsEvaluateService.selectDamAssetsEvaluateList(damAssetsEvaluate);
        return getDataTable(list);
    }

    /**
     * 导出资产评价列表
     */
    @Log(title = "资产评价", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DamAssetsEvaluate damAssetsEvaluate)
    {
        List<DamAssetsEvaluate> list = damAssetsEvaluateService.selectDamAssetsEvaluateList(damAssetsEvaluate);
        ExcelUtil<DamAssetsEvaluate> util = new ExcelUtil<DamAssetsEvaluate>(DamAssetsEvaluate.class);
        return util.exportExcel(list, "damAssetsEvaluate");
    }

    /**
     * 获取资产评价详细信息
     */
    @GetMapping(value = "/{assetsEvaluateId}")
    public AjaxResult getInfo(@PathVariable("assetsEvaluateId") Long assetsEvaluateId)
    {
        return AjaxResult.success(damAssetsEvaluateService.selectDamAssetsEvaluateById(assetsEvaluateId));
    }

    /**
     * 新增资产评价
     */
    @Log(title = "资产评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DamAssetsEvaluate damAssetsEvaluate)
    {
        return toAjax(damAssetsEvaluateService.insertDamAssetsEvaluate(damAssetsEvaluate));
    }

    /**
     * 修改资产评价
     */
    @Log(title = "资产评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DamAssetsEvaluate damAssetsEvaluate)
    {
        return toAjax(damAssetsEvaluateService.updateDamAssetsEvaluate(damAssetsEvaluate));
    }

    /**
     * 删除资产评价
     */
    @Log(title = "资产评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetsEvaluateIds}")
    public AjaxResult remove(@PathVariable Long[] assetsEvaluateIds)
    {
        return toAjax(damAssetsEvaluateService.deleteDamAssetsEvaluateByIds(assetsEvaluateIds));
    }
}
