<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.api.mapper.ApiDefineColumnMapper">
    
    <resultMap type="ApiDefineColumn" id="ApiDefineColumnResult">
        <result property="defineColumnId"    column="define_column_id"    />
        <result property="defineId"    column="define_id"    />
        <result property="defineColumnName"    column="define_column_name"    />
        <result property="remark"    column="remark"    />
        <result property="isMust"    column="is_must"    />
        <result property="defineColumnType"    column="define_column_type"    />
        <result property="isDesensitization"    column="is_desensitization"    />
        <result property="parentId"    column="parent_id"    />
    </resultMap>

    <sql id="selectApiDefineColumnVo">
        select define_column_id, define_id, define_column_name, remark, is_must, define_column_type, is_desensitization, parent_id from api_define_column
    </sql>

    <select id="selectApiDefineColumnList" parameterType="ApiDefineColumn" resultMap="ApiDefineColumnResult">
        <include refid="selectApiDefineColumnVo"/>
        <where>  
            <if test="defineId != null "> and define_id = #{defineId}</if>
            <if test="defineColumnName != null  and defineColumnName != ''"> and define_column_name like concat('%', #{defineColumnName}, '%')</if>
            <if test="isMust != null  and isMust != ''"> and is_must = #{isMust}</if>
            <if test="defineColumnType != null  and defineColumnType != ''"> and define_column_type = #{defineColumnType}</if>
            <if test="isDesensitization != null  and isDesensitization != ''"> and is_desensitization = #{isDesensitization}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
    </select>
    
    <select id="selectApiDefineColumnById" parameterType="Long" resultMap="ApiDefineColumnResult">
        <include refid="selectApiDefineColumnVo"/>
        where define_column_id = #{defineColumnId}
    </select>
        
    <insert id="insertApiDefineColumn" parameterType="ApiDefineColumn" useGeneratedKeys="true" keyProperty="defineColumnId">
        insert into api_define_column
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="defineId != null">define_id,</if>
            <if test="defineColumnName != null and defineColumnName != ''">define_column_name,</if>
            <if test="remark != null">remark,</if>
            <if test="isMust != null and isMust != ''">is_must,</if>
            <if test="defineColumnType != null and defineColumnType != ''">define_column_type,</if>
            <if test="isDesensitization != null">is_desensitization,</if>
            <if test="parentId != null">parent_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="defineId != null">#{defineId},</if>
            <if test="defineColumnName != null and defineColumnName != ''">#{defineColumnName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isMust != null and isMust != ''">#{isMust},</if>
            <if test="defineColumnType != null and defineColumnType != ''">#{defineColumnType},</if>
            <if test="isDesensitization != null">#{isDesensitization},</if>
            <if test="parentId != null">#{parentId},</if>
         </trim>
    </insert>

    <update id="updateApiDefineColumn" parameterType="ApiDefineColumn">
        update api_define_column
        <trim prefix="SET" suffixOverrides=",">
            <if test="defineId != null">define_id = #{defineId},</if>
            <if test="defineColumnName != null and defineColumnName != ''">define_column_name = #{defineColumnName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isMust != null and isMust != ''">is_must = #{isMust},</if>
            <if test="defineColumnType != null and defineColumnType != ''">define_column_type = #{defineColumnType},</if>
            <if test="isDesensitization != null">is_desensitization = #{isDesensitization},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
        </trim>
        where define_column_id = #{defineColumnId}
    </update>

    <delete id="deleteApiDefineColumnById" parameterType="Long">
        delete from api_define_column where define_column_id = #{defineColumnId}
    </delete>
    <delete id="deleteApiDefineColumnByApiDefineId" parameterType="Long">
        delete from api_define_column where define_id = #{defineId}
    </delete>
    <delete id="deleteApiDefineColumnByIds" parameterType="String">
        delete from api_define_column where define_column_id in 
        <foreach item="defineColumnId" collection="array" open="(" separator="," close=")">
            #{defineColumnId}
        </foreach>
    </delete>
</mapper>