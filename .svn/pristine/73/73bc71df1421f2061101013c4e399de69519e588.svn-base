package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 指标与币种对象 dsm_quota_currency_rel
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
public class DsmQuotaCurrencyRel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标 */
    @Excel(name = "指标")
    private Long quotaId;

    /** 币种 */
    @Excel(name = "币种")
    private String currencyId;

    public void setQuotaId(Long quotaId)
    {
        this.quotaId = quotaId;
    }

    public Long getQuotaId()
    {
        return quotaId;
    }
    public void setCurrencyId(String currencyId)
    {
        this.currencyId = currencyId;
    }

    public String getCurrencyId()
    {
        return currencyId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("quotaId", getQuotaId())
            .append("currencyId", getCurrencyId())
            .toString();
    }
}
