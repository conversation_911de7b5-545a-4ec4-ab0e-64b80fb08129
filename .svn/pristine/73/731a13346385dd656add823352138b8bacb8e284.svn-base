package com.dqms.dsm.service;

import java.util.List;

import com.dqms.dsm.domain.DsmQuotaClass;
import com.dqms.dsm.domain.DsmQuotaClassTreeSelect;

/**
 * 指标分类Service接口
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
public interface IDsmQuotaClassService 
{
    /**
     * 查询指标分类
     * 
     * @param quotaClassId 指标分类ID
     * @return 指标分类
     */
    public DsmQuotaClass selectDsmQuotaClassById(Long quotaClassId);

    /**
     * 查询指标分类列表
     * 
     * @param dsmQuotaClass 指标分类
     * @return 指标分类集合
     */
    public List<DsmQuotaClass> selectDsmQuotaClassList(DsmQuotaClass dsmQuotaClass);

    /**
     * 新增指标分类
     * 
     * @param dsmQuotaClass 指标分类
     * @return 结果
     */
    public int insertDsmQuotaClass(DsmQuotaClass dsmQuotaClass);

    /**
     * 修改指标分类
     * 
     * @param dsmQuotaClass 指标分类
     * @return 结果
     */
    public int updateDsmQuotaClass(DsmQuotaClass dsmQuotaClass);

    /**
     * 批量删除指标分类
     * 
     * @param quotaClassIds 需要删除的指标分类ID
     * @return 结果
     */
    public int deleteDsmQuotaClassByIds(Long[] quotaClassIds);

    /**
     * 删除指标分类信息
     * 
     * @param quotaClassId 指标分类ID
     * @return 结果
     */
    public int deleteDsmQuotaClassById(Long quotaClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 树结构列表
     */
    public List<DsmQuotaClass> buildDsmQuotaClassTree(List<DsmQuotaClass> dsmQuotaClass);
     
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    public List<DsmQuotaClassTreeSelect> buildDsmQuotaClassTreeSelect(List<DsmQuotaClass> dsmQuotaClass);
}
