package com.dqms.mdm.mapper;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.domain.vo.MdmView;

/**
 * 源系统注册Mapper接口
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
public interface MdmRegistryMapper
{
    /**
     * 查询源系统注册
     *
     * @param regId 源系统注册ID
     * @return 源系统注册
     */
    public MdmRegistry selectMdmRegistryById(Long regId);

    /**
     * 查询源系统注册列表
     *
     * @param mdmRegistry 源系统注册
     * @return 源系统注册集合
     */
    public List<MdmRegistry> selectMdmRegistryList(MdmRegistry mdmRegistry);
    public List<MdmRegistry> selectMdmRegistryListByPage(@Param("datasourceId")Long datasourceId,@Param("page")int page ,@Param("size")int size);

    /**
     * 查询源系统注册列表
     *
     * @param mdmRegistry 源系统注册
     * @return 源系统注册集合
     */
    public MdmRegistry getMdmRegistry(MdmRegistry mdmRegistry);

    /**
     * 新增源系统注册
     *
     * @param mdmRegistry 源系统注册
     * @return 结果
     */
    public int insertMdmRegistry(MdmRegistry mdmRegistry);

    /**
     * 修改源系统注册
     *
     * @param mdmRegistry 源系统注册
     * @return 结果
     */
    public int updateMdmRegistry(MdmRegistry mdmRegistry);

    /**
     * 删除源系统注册
     *
     * @param regId 源系统注册ID
     * @return 结果
     */
    public int deleteMdmRegistryById(Long regId);

    /**
     * 批量删除源系统注册
     *
     * @param regIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMdmRegistryByIds(Long[] regIds);

    public int checkRegistryNameUnique(MdmRegistry mdmRegistry);

    /**
     * 任务中心查询绑定的任务
     *
     * @param taskId任务IDs
     * @return 结果
     */
    public List<MdmRegistry> selectMdmRegistryListByTaskId(@Param("taskId")Long taskId);
    public List<MdmRegistry> selectMdmRegistryListByRegIdsForTask(@Param("regIds")Long[] regIds);
    /**
     * 任务中心查询未绑定的任务
     *
     * @param regIds 源系统注册ID
     * @return 结果
     */
    public List<MdmRegistry> selectUnMdmRegistryListByTaskId(MdmRegistry mdmRegistry);
    /**
     * 任务中心添加绑定任务
     *
     * @param taskId 任务IDs regIds 源系统注册ID
     * @return 结果
     */
    public int addMdmRegistryAndTaskRel(@Param("taskId")Long taskId,@Param("regIds")Long[] regIds);
    /**
     * 任务中心删除绑定任务
     *
     * @param taskId 任务IDs
     * @return 结果
     */
    public int delMdmRegistryAndTaskRel(@Param("taskId")Long taskId);

    int insertBatch(List<MdmRegistry> mdmRegistryList);
    
    public List<MdmView> getView();
}
