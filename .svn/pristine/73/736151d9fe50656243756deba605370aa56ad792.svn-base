package com.dqms.dsm.service;

import java.util.List;

import com.dqms.dsm.domain.DsmStandard;
import com.dqms.dsm.domain.vo.DsmStandardVo;

/**
 * 基础标准Service接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface IDsmStandardService
{
    /**
     * 查询基础标准
     *
     * @param standardId 基础标准ID
     * @return 基础标准
     */
    public DsmStandard selectDsmStandardById(Long standardId);
    public DsmStandardVo selectDsmStandardVoById(Long standardId);

    /**
     * 查询基础标准列表
     *
     * @param dsmStandard 基础标准
     * @return 基础标准集合
     */
    public List<DsmStandard> selectDsmStandardList(DsmStandard dsmStandard);
    public List<DsmStandard> selectDsmStandardListByNo(DsmStandard dsmStandard);
    public List<DsmStandardVo> selectDsmStandardVoList(DsmStandardVo dsmStandardVo);

    /**
     * 新增基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    public int insertDsmStandard(DsmStandard dsmStandard,String oldId);

    /**
     * 修改基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    public int updateDsmStandard(DsmStandard dsmStandard);
    public int updatedsmStandardStatus(DsmStandard dsmStandard);


    /**
     * 审批修改基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    public int updateStatusDsmStandard(DsmStandard dsmStandard);

    /**
     * 批量删除基础标准
     *
     * @param standardIds 需要删除的基础标准ID
     * @return 结果
     */
    public int deleteDsmStandardByIds(Long[] standardIds);

    /**
     * 删除基础标准信息
     *
     * @param standardId 基础标准ID
     * @return 结果
     */
    public int deleteDsmStandardById(Long standardId);

    /**
     * 导入基础标准
     * @param dsmStandardList
     * @param isUpdateSupport
     * @return
     */
    public String importDsmStandard(List<DsmStandardVo> dsmStandardList, Boolean isUpdateSupport);
}
