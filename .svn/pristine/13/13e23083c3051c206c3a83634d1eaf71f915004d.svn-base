package com.dqms.dsm.domain.vo;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.dsm.domain.DsmDimension;
import com.dqms.dsm.domain.DsmIndex;

/**
 * 指标管理对象 dsm_index
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public class DsmIndexRelVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标ID */
    private String id;
    private String label;
    private List<DsmIndexRelVo> children;
    private String type;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}

	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
	public List<DsmIndexRelVo> getChildren() {
		return children;
	}
	public void setChildren(List<DsmIndexRelVo> children) {
		this.children = children;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
    
}
