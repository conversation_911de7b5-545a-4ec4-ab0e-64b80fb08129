package com.dqms.dic.domain;

public class DicFieldMapping {

    private Long mappId;
    private String mappName;
    private String srcField;
    private String tarField;
    private int defaultLength;
    private int defaultDecimal;

    public Long getMappId() {
        return mappId;
    }

    public void setMappId(Long mappId) {
        this.mappId = mappId;
    }

    public String getMappName() {
        return mappName;
    }

    public void setMappName(String mappName) {
        this.mappName = mappName;
    }

    public String getSrcField() {
        return srcField;
    }

    public void setSrcField(String srcField) {
        this.srcField = srcField;
    }

    public String getTarField() {
        return tarField;
    }

    public void setTarField(String tarField) {
        this.tarField = tarField;
    }

    public int getDefaultLength() {
        return defaultLength;
    }

    public void setDefaultLength(int defaultLength) {
        this.defaultLength = defaultLength;
    }

    public int getDefaultDecimal() {
        return defaultDecimal;
    }

    public void setDefaultDecimal(int defaultDecimal) {
        this.defaultDecimal = defaultDecimal;
    }
}
