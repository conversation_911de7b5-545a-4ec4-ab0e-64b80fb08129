package com.dqms.mdm.service;

import java.util.List;
import com.dqms.mdm.domain.MdmDataEntityShipAnalysisRel;

/**
 * 解析关联Service接口
 * 
 * <AUTHOR>
 * @date 2021-09-07
 */
public interface IMdmDataEntityShipAnalysisRelService 
{
    /**
     * 查询解析关联
     * 
     * @param shipId 解析关联ID
     * @return 解析关联
     */
    public MdmDataEntityShipAnalysisRel selectMdmDataEntityShipAnalysisRelById(Long shipId);

    /**
     * 查询解析关联列表
     * 
     * @param mdmDataEntityShipAnalysisRel 解析关联
     * @return 解析关联集合
     */
    public List<MdmDataEntityShipAnalysisRel> selectMdmDataEntityShipAnalysisRelList(MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel);

    /**
     * 新增解析关联
     * 
     * @param mdmDataEntityShipAnalysisRel 解析关联
     * @return 结果
     */
    public int insertMdmDataEntityShipAnalysisRel(MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel);

    /**
     * 修改解析关联
     * 
     * @param mdmDataEntityShipAnalysisRel 解析关联
     * @return 结果
     */
    public int updateMdmDataEntityShipAnalysisRel(MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel);

    /**
     * 批量删除解析关联
     * 
     * @param shipIds 需要删除的解析关联ID
     * @return 结果
     */
    public int deleteMdmDataEntityShipAnalysisRelByIds(Long[] shipIds);

    /**
     * 删除解析关联信息
     * 
     * @param shipId 解析关联ID
     * @return 结果
     */
    public int deleteMdmDataEntityShipAnalysisRelById(Long shipId);
}
