<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true"
             v-show="showSearch" label-width="70px">
      <el-form-item label="导入类型" prop="importType">
        <el-select v-model="form.importType" clearable
                    placeholder="请选择" @change="(e)=>importTypeChange(e,1)">
          <el-option
            v-for="item in importTypeOptions"
            :key="item.dictCode"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="导入模板" prop="templateId">
        <el-select v-model="queryParams.templateId" clearable placeholder="请选择">
          <el-option
            v-for="item in templateIdOptions"
            :key="item.dictValue||item.id"
            :label="item.dictLabel||item.tName"
            :value="item.dictValue||item.id">
          </el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="模板名" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="导入日期" prop="importTime">
        <el-date-picker clearable size="small"
          v-model="queryParams.importTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择导入日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['api:apiTemplateLog:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['api:apiTemplateLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['api:apiTemplateLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="apiTemplateLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="导入操作员" align="center" prop="importOperator" />
      <el-table-column label="模板名" align="center" prop="templateName" />
      <el-table-column label="excel文件名" align="center" prop="exname" />
      <el-table-column label="导入结果" align="center" prop="importResult" />
      <el-table-column label="导入日期" align="center" prop="importTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.importTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['api:apiTemplateLog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据补录日志对话框 -->
    <el-dialog :title="title" :visible.sync="open"
      width="500px">
      <el-dialog
        width="40%"
        :title="'表名'+msg"
        :visible.sync="innerVisible"
        append-to-body>

         <el-table :data="apiTaskManage_tempData">
          <el-table-column
            v-for="(item,index) in tableColumnItem"
            :key="index"
            :label="item.commentstring"
            align="center"
            :prop="item.column_name" />
          </el-table>
           <pagination
              hide-on-single-page
              :total="total2"
              :page.sync="queryParams2.pageNum"
              :limit.sync="queryParams2.pageSize"
              @pagination="getList2"
            />
          <el-button type="primary" @click="executeTable">校验导入</el-button>
      </el-dialog>
      <el-form ref="form" :model="form2" :rules="rules" label-width="80px">
       <el-form-item label="选择分类" prop="importType">
        <el-select v-model="form2.importType" clearable
                    placeholder="请选择" @change="(e)=>importTypeChange(e,2)">
          <el-option
            v-for="item in importTypeOptions"
            :key="item.dictCode"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择模板" prop="templateId">
        <el-select v-model="form2.templateId" clearable placeholder="请选择">
          <el-option
            v-for="item in templateIdOptions2"
            :key="item.dictValue||item.id"
            :label="item.dictLabel||item.tName"
            :value="item.dictValue||item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-upload
          class="upload-demo"
          ref="upload"
          :headers="headers"
          :before-upload="beforeAvatarUpload"
          :on-success="successUpload"
          :action="api+'/api/apiTemplateMapping/uploadFiles'"
          :limit="1"
          :file-list="fileList"
          :auto-upload="false">
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        </el-upload>
      </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading2" @click="submitUpload">预 览</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiTemplateLog, uploadFileTemp,
        getApiTemplateLog, delApiTemplateLog,
        addApiTemplateLog, updateApiTemplateLog,
        exportApiTemplateLog, templateIdToTable,
        getTableMapping, taskManage_tempData, executeTable
} from "@/api/api/apiTemplateLog";

import { listApiTemplateMapping } from "@/api/api/apiTemplateMapping";
import { getToken } from "@/utils/auth";
export default {
  name: "ApiTemplateLog",
  components: {
  },
  data() {
    return {
      loading2:false,
      templateId:'',
      msg:'',
      tableColumnItem:[],
      apiTaskManage_tempData:[],
      innerVisible:false,
      headers: { Authorization: "Bearer " + getToken() },
      api:process.env.VUE_APP_BASE_API,
      fileList: [],
      templateIdOptions2:[],
      importTypeOptions:[],
      templateIdOptions:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据补录日志表格数据
      apiTemplateLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateId:null,
        templateName: null,
        exname: null,
        importTime: null
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 10
      },
      total2:0,
      // 表单参数
      form:{},
      form2:{
        templateId:''
      },
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();

    this.getDicts("api_export_type").then(response => {
      this.importTypeOptions = response.data;
    });
  },
  methods: {
    getList2() {
      taskManage_tempData(this.msg,this.queryParams2)
      .then(res=>{
        this.apiTaskManage_tempData = res.rows
      })
    },
    executeTable() {
      executeTable(this.templateId).then(res=>{
        this.msgSuccess(res.msg)
        this.open = false
        this.getList()
      })
    },
    successUpload(response, file, fileList){
      this.innerVisible = true
      this.loading2 = true
      if(this.form2.importType==0){
        let templateName = this.templateIdOptions2.filter(el=>el.id==this.form2.templateId)
        var form = {
          templateId:this.form2.templateId,
          templateId2:'',
          templateName:templateName[0].tName,
          templateName2:'',
          fileName:response.fileName
        }
      }else{
        let templateName = this.templateIdOptions2.filter(el=>el.dictValue==this.form2.templateId)
        var form = {
          templateId:'',
          templateId2:this.form2.templateId,
          templateName:'',
          templateName2:templateName[0].dictLabel,
          fileName:response.fileName
        }
      }
      this.templateId = form.templateId || form.templateId2
      uploadFileTemp(form).then(res=>{
        return templateIdToTable(this.templateId)
      }).then(res=>{
        this.msg = res.msg
        return getTableMapping(this.msg)
      }).then((res)=>{
        this.tableColumnItem = res.data
        return taskManage_tempData(this.msg,this.queryParams2)
      }).then(res=>{
        this.apiTaskManage_tempData = res.rows
        this.loading2 = false
      }).catch(()=>{
        this.loading2 = false
      })
    },
    beforeAvatarUpload(file) {
      var type = ['bmp', 'gif', 'jpg', 'jpeg', 'png',
      'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'html', 'htm', 'txt', 'rar', 'zip', 'gz', 'bz2',
      'pdf', 'bpmn', 'bar']
      var fileType = file.name.split('.')[1]
      const isJPG = type.some(el=>el==fileType);
      if (!isJPG) {
        this.$message.error('上传正确格式!');
      }
      return isJPG;
    },
    submitUpload() {
        this.$refs.upload.submit();
    },
    importTypeChange(e,index) {
      if(e==0){
        listApiTemplateMapping().then(res=>{
          if(index==1){
            this.templateIdOptions = res.rows;
            this.queryParams.templateId = null
          }else{
            this.templateIdOptions2 = res.rows;
            this.form2.templateId = ''
          }
        })
      }else if(e==1){
        this.getDicts("api_complex_table").then(response => {
          if(index==1){
            this.templateIdOptions = response.data;
            this.queryParams.templateId = null
          }else{
            this.templateIdOptions2 = response.data;
            this.form2.templateId = ''
          }
        });
      }else{
        if(index==1){
            this.queryParams.templateId = ''
            this.templateIdOptions = []
          }else{
            this.form2.templateId = ''
            this.templateIdOptions2 = []
          }

      }
    },
    /** 查询数据补录日志列表 */
    getList() {
      this.loading = true;
      listApiTemplateLog(this.queryParams).then(response => {
        this.apiTemplateLogList = response.rows;
        this.total = response.total;
        this.innerVisible = false
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        importOperator: null,
        templateName: null,
        templateId: null,
        exname: null,
        importResult: null,
        importTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form2 = {
        templateId:null
      }
      this.open = true;
      this.title = "添加数据补录日志";
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          addApiTemplateLog(this.form).then(response => {
            this.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logId = row.logId || this.ids;
      this.$confirm('是否确认删除数据补录日志编号为"' + logId + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delApiTemplateLog(logId);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有数据补录日志数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportApiTemplateLog(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
