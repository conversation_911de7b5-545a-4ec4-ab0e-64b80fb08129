package com.dqms.utils;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.constant.MdmConstants;
import com.dqms.common.enums.DbType;
import com.dqms.common.utils.DataSourceUtils;
import com.dqms.common.utils.DictUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.system.service.ISysDictDataService;
import com.zaxxer.hikari.HikariConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@Component
public class JdbcTemplateUtils {

    @Autowired
    private ISysDictDataService iSysDictDataService;

    public static JdbcTemplateUtils jdbcTemplateUtils;

    @PostConstruct // 初始化
    public void init(){
        jdbcTemplateUtils = this;
        jdbcTemplateUtils.iSysDictDataService = this.iSysDictDataService;
    }

    public static JdbcTemplate getJdbcTemplate(SysDatasource sysDatasource)  {
        if(sysDatasource.getAuthType().equals(MdmConstants.KERBEROS_AUTH)){
            DataSourceUtils.loginKerberos(sysDatasource.getKerberosUser(),sysDatasource.getKerberosPath());
        }
        HikariConfig hikariConfig = getHikariConfig(sysDatasource);
        return  new JdbcTemplate(DataSourceUtils.getDataSource(hikariConfig));
    }

    public static void addDataSourceMap(SysDatasource sysDatasource)  {
        HikariConfig hikariConfig = getHikariConfig(sysDatasource);
        DataSourceUtils.add(hikariConfig);
    }

    public static void updateDataSourceMap(SysDatasource sysDatasource)  {
        HikariConfig hikariConfig = getHikariConfig(sysDatasource);
        DataSourceUtils.update(hikariConfig);
    }

    private static HikariConfig getHikariConfig(SysDatasource sysDatasource) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(sysDatasource.getUrl());
        hikariConfig.setUsername(sysDatasource.getUsername());
        hikariConfig.setPassword(sysDatasource.getPassword());
        hikariConfig.setMinimumIdle(sysDatasource.getMinimumIdle());
        hikariConfig.setMaximumPoolSize(sysDatasource.getMaximumPoolSize());
        hikariConfig.setIdleTimeout(sysDatasource.getIdleTimeout());
        hikariConfig.setMaxLifetime(sysDatasource.getMaxLifetime());
        hikariConfig.setConnectionTimeout(sysDatasource.getConnectionTimeout());
        hikariConfig.setDriverClassName(sysDatasource.getSysDatasourceType().getDatasourceTypeDrive());
        hikariConfig.addDataSourceProperty("datasourceId",sysDatasource.getDatasourceId());
        hikariConfig.addDataSourceProperty("remarks", "true");
        hikariConfig.addDataSourceProperty("useInformationSchema", "true");
        return hikariConfig;
    }

    public static void removeDataSourceMap(Long datasourceId) {
        DataSourceUtils.remove(datasourceId.toString());
    }

    public static void removeDataSourceMapAll(Long[] datasourceIds) {
        DataSourceUtils.removeAll(datasourceIds);
    }

}
