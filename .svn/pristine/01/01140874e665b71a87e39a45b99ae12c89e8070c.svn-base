import request from '@/utils/request'

// 查询主数据列表
export function listDsmMasterData(query) {
  return request({
    url: '/dsm/dsmMasterData/list',
    method: 'get',
    params: query
  })
}

// 查询主数据详细
export function getDsmMasterData(masterDataId) {
  return request({
    url: '/dsm/dsmMasterData/' + masterDataId,
    method: 'get'
  })
}

// 新增主数据
export function addDsmMasterData(data) {
  return request({
    url: '/dsm/dsmMasterData',
    method: 'post',
    data: data
  })
}
export function installDsmMasterData(data) {
  return request({
    url: '/dsm/dsmMasterData/install',
    method: 'post',
    data: data
  })
}
// 修改主数据
export function updateDsmMasterData(data) {
  return request({
    url: '/dsm/dsmMasterData',
    method: 'put',
    data: data
  })
}

// 删除主数据
export function delDsmMasterData(masterDataId) {
  return request({
    url: '/dsm/dsmMasterData/' + masterDataId,
    method: 'delete'
  })
}

// 导出主数据
export function exportDsmMasterData(query) {
  return request({
    url: '/dsm/dsmMasterData/export',
    method: 'get',
    params: query
  })
}

export function validateRules(rule, value,callback){
  if(value==''||value==undefined||value==null){
    callback();
  }else {
    if ((!eval(rule.reg).test(value)) && value != '') {
      callback(new Error(rule.message));
    } else {
      callback();
    }
  }
}

export function maxValidateRules(rule, value,callback){
	if(value==''||value==undefined||value==null){
	  callback();
	}else {
	  if (value.length>rule.max) {
	    callback(new Error(rule.message));
	  } else {
	    callback();
	  }
	}
}


export function getData(data) {
  return request({
    url: '/dsm/dsmMasterData/getData',
    method: 'put',
    data: data
  })
}

export function saveDsmMasterData(data) {
  return request({
    url: '/dsm/dsmMasterData/saveData',
    method: 'post',
    data: data
  })
}


//下载任务导入模板
export function importTemplate() {
return request({
 url: '/dsm/dsmMasterData/importTemplate',
 method: 'get'
})
}