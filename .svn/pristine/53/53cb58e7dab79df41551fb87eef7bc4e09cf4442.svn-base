<template>
  <div class="app-container">
    <el-row>
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>任务分布</span>
		  </div>
	      <div id="runFb" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>执行总览</span>
		  </div>
	      <div id="runZl" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>完成率</span>
		  </div>
	      <div id="runWcl" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
      <el-col :span="6">
	  	<el-card :body-style="{ padding: '0px' }" class="card" shadow="always" >
	      <div slot="header" class="clearfix">
			    <span>级别分布</span>
		  </div>
	      <div id="runThread" style="width:100%; height:200px"></div>
	    </el-card>
	   </el-col>
	  
	  <el-col :span="24">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>需求趋势情况</span>
		  </div>
	      <div id="runGroup" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>

	  <el-col :span="8">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>开发平均耗时排名(前五)</span>
		  </div>
	      <div id="runHs" style="width:100%; height:300px"></div>
	    </el-card>
	  </el-col>
	  		    
	  <el-col :span="8">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>指标热度排名(前五)</span>
		  </div>
	      <div id="runTime" style="width:100%; height:300px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="8">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>关联系统热度排名</span>
		  </div>
		  <div id="systemRd" style="width:100%; height:300px"></div>
	    </el-card>
	  </el-col>
	  
    </el-row>
  </div>
</template>

<script>
import { getRunFb,getRunZl,getRunWcl,getRunGroup,getRunYc,getRunHs,getRunTime,getRunThread,getSystemRd} from "@/api/needs/needsView";
import G6 from '@antv/g6'
import backgroundImage from '@/assets/images/timg.jpg';

export default {
  name: "NeedsView",
  components: {
  },
  data() {
    return {
      
    };
  },
  mounted() {
	  this.showRunFb();
	  this.showRunZl();
	  this.showRunWcl();
	  this.showRunGroup();
	  this.showRunHs();
	  this.showRunTime();
	  this.showRunThread();
	  this.showSystemRd();
  },
  methods: {
	  showRunFb() {
    	  getRunFb();
      },
      showRunZl() {
    	  getRunZl();
      },
      showRunWcl() {
    	  getRunWcl();
      },
      showRunGroup() {
    	  getRunGroup();
      },
      showRunYc() {
    	  getRunYc();
      },
      showRunHs() {
    	  getRunHs();
      },
      showRunTime() {
    	  getRunTime();
      },
      showRunThread() {
    	  getRunThread();
      },
      showSystemRd() {
    	  getSystemRd();
      }
  },
  destroyed () {
	  
  }
};
</script>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }
  
  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }
  
  .clearfix:after {
      clear: both
  }
  
  .card{
  	margin:10px;
  }
  
  </style>