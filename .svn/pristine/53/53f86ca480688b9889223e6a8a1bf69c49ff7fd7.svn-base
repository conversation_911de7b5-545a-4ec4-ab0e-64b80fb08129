<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dqm.mapper.DqmValidationDetailMapper">
    <!--查询是否存在该表名-->
    <select id="selectTableNameExit" parameterType="String" resultType="int">
        select count(*) from information_schema.TABLES where table_name = #{tableName}
    </select>
    <parameterMap type="map" id="tableVo">
        <parameter property="tableName" jdbcType="VARCHAR" mode="IN"/>
    </parameterMap>
    <!--创建表-->
    <select id="createTableDetail" parameterMap="tableVo" statementType="CALLABLE">
        CALL create_DetailTableName(#{tableName,mode=IN});
    </select>
    <!--添加明细数据-->
    <insert id="insertDetailTable" parameterType="DqmValidationDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="problemId != null">PROBLEM_ID,</if>
            <if test="createTime != null">create_time,</if>
            <if test="validationRuleCateId != null">VALIDATION_RULE_CATE_ID,</if>
            <if test="detail != null">detail,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="problemId != null">#{problemId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="validationRuleCateId != null">#{validationRuleCateId},</if>
            <if test="detail != null">#{detail},</if>
        </trim>
    </insert>
    <!--根据问题ID查询明细列表-->
    <select id="selectDetailList" parameterType="DqmValidationDetail" resultType="java.util.HashMap">
        select t.detail from ${tableName} t where t.PROBLEM_ID = #{problemId}
    </select>
</mapper>