package com.dqms.basic.mapper;

import java.util.List;

import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.entity.SysSystemPerson;

/**
 * 应用系统Mapper接口
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
public interface SysSystemMapper
{
    /**
     * 查询应用系统
     *
     * @param systemId 应用系统ID
     * @return 应用系统
     */
    public SysSystem selectSysSystemById(Long systemId);
    public SysSystem selectSysSystemByNameOrCode(SysSystem sysSystem);
    /**
     * 查询应用系统
     *
     * @param systemName 应用系统name
     * @return 应用系统
     */
    public SysSystem getSysSystemByName(String systemName);

    /**
     * 查询应用系统
     *
     * @param systemName 应用系统name
     * @return 应用系统
     */
    public SysSystem selectSysSystemByName(String systemName);

    /**
     * 查询应用系统列表
     *
     * @param sysSystem 应用系统
     * @return 应用系统集合
     */
    public List<SysSystem> selectSysSystemList(SysSystem sysSystem);

    /**
     * 查询所有应用系统
     *
     * @return 应用系统集合
     */
    public List<SysSystem> selectSysSystemAll(SysSystem sysSystem);


    /**
     * 新增应用系统
     *
     * @param sysSystem 应用系统
     * @return 结果
     */
    public int insertSysSystem(SysSystem sysSystem);

    /**
     * 修改应用系统
     *
     * @param sysSystem 应用系统
     * @return 结果
     */
    public int updateSysSystem(SysSystem sysSystem);

    /**
     * 删除应用系统
     *
     * @param systemId 应用系统ID
     * @return 结果
     */
    public int deleteSysSystemById(Long systemId);

    /**
     * 批量删除应用系统
     *
     * @param systemIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysSystemByIds(Long[] systemIds);

    /**
     * 批量删除系统负责人
     *
     * @param customerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysSystemPersonBySystemIds(Long[] systemIds);

    /**
     * 批量新增系统负责人
     *
     * @param sysSystemUserList 系统负责人列表
     * @return 结果
     */
    public int batchSysSystemPerson(List<SysSystemPerson> sysSystemUserList);


    /**
     * 通过应用系统ID删除系统负责人信息
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteSysSystemPersonBySystemId(Long systemId);

    /**
     * 根据用户ID获取应用系统选择框列表
     *
     * @param userId 用户ID
     * @return 选中应用系统ID列表
     */
    public List<Integer> selectSystemListByUserId(Long userId);
    /**
     * 根据系统ID获取负责人系统选择框列表
     *
     * @param systemId 用户ID
     * @return 选中负责人ID列表
     */
    public List<Integer> selectPersonIdsBySystemId(Long systemId);

    public  List<SysSystem> ListSysSystemByName(SysSystem sysSystem);
    public  List<SysSystem> selectSysSystemByCode(SysSystem sysSystem);


}
