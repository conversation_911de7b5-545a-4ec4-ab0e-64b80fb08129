package com.dqms.mdm.util.StrategyType;

import base.BaseJunit;
import com.dqms.DqmsApplication;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.service.IDamAssetsServiceES;
import com.dqms.mdm.domain.MdmRegistry;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;


public class MysqlStrategyTest extends BaseJunit {

    @Autowired
    MysqlStrategy mysqlStrategy;

    @Autowired
    SysDatasourceMapper sysDatasourceMapper;

    @Test
    public void getTableInfo() {
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("sys_system_copy2");
        mdmRegistry.setRegDir("dqms");
        mdmRegistry.setDatasourceId(23L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        Map<String, Object> table = mysqlStrategy.getTableInfo(mdmRegistry, sysDatasource);
        for(Map.Entry<String, Object> map : table.entrySet()){
            System.out.println("key: "+map.getKey()+"; value: "+map.getValue());
        }
        io.jsonwebtoken.lang.Assert.notEmpty(table,"不能为空");
    }

    @Test
    public void getColumns() throws Exception {
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("sys_system_copy2");
        mdmRegistry.setRegDir("dqms");
        mdmRegistry.setDatasourceId(23L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        List<Map<String, Object>> list =  mysqlStrategy.getColumns(mdmRegistry,sysDatasource);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.print(m.getKey() + " ");
                System.out.println(m.getValue());
                System.out.println("\n");
            }
        }
        Assert.notEmpty(list,"不能为空");
    }

    @Test
    public void checkTableExist() {
    }

    @Test
    public void checkProcedureExist() {
    }

    @Test
    public void getTablesAndViews() {
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("sys_system_copy2");
        mdmRegistry.setRegDir("dqms");
        mdmRegistry.setDatasourceId(23L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        List<Map<String, Object>> list =  mysqlStrategy.getTablesAndViews(sysDatasource, "dqms", null);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.print(m.getKey() + " ");
                System.out.println(m.getValue());
            }
            System.out.println("\n");
        }
    }

    @Test
    public void getProcedures() {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(23L);

        List<Map<String, Object>> list =  mysqlStrategy.getProcedures(sysDatasource, "dqms");
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey() + " : " + m.getValue());
            }
            System.out.println("------");
        }
    }

    @Test
    public void getProcedureInfo() {
    }

    @Test
    public void excuteByLimit() {
    }

    @Test
    public void quaryByPage() {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(23L);
        String sqlText = "SELECT * from sys_datasource order by datasource_id ";
        List<Map<String, Object>> maps = mysqlStrategy.quaryByPage(sysDatasource, sqlText, 3, 5);
        for (Map<String, Object> stringObjectMap : maps) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("\n");
        }
    }

    @Test
    public void getSqlCount() {
    }

    @Test
    public void getTableCreate() {
    }

    @Test
    public void getProcedureCreate() {
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("pag_add");
        mdmRegistry.setRegDir("dqms");
        mdmRegistry.setDatasourceId(23L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        String procedureCreate = mysqlStrategy.getProcedureCreate(sysDatasource, mdmRegistry);

        System.out.println("procedureCreate: "+procedureCreate);

    }

    @Test
    public void getViewCreate() {
    }

    @Test
    public void getDatabaseInfos() {
    }

    @Test
    public void parseStatements() {
    }

    @Test
    public void createSchemaStatVisitor() {
    }

    @Test
    public void createUser(){//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(23L);
        Boolean testUser = mysqlStrategy.createUser(sysDatasource, "testUser", "1qaz@WSX");
        System.out.println("createUser："+testUser);
    }

    @Test
    public void grantBytable(){//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(23L);
        Boolean testUser = mysqlStrategy.grantBytable(sysDatasource, "testUser", "sys_datasource;sys_datasource_type", null);
        System.out.println("grantBytable："+testUser);
    }

    @Test
    public void dropUser(){//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(23L);
        Boolean testUser = mysqlStrategy.dropUser(sysDatasource, "testUser");
        System.out.println("grantBytable："+testUser);
    }
}
