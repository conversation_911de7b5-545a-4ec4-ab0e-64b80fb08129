package com.dqms.dic.mapper;

import java.util.List;
import com.dqms.dic.domain.DicManualDataDefineUser;

/**
 * 数据补录权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-26
 */
public interface DicManualDataDefineUserMapper 
{
    /**
     * 查询数据补录权限
     * 
     * @param manualDataId 数据补录权限ID
     * @return 数据补录权限
     */
    public DicManualDataDefineUser selectDicManualDataDefineUserById(Long manualDataId);

    /**
     * 查询数据补录权限列表
     * 
     * @param dicManualDataDefineUser 数据补录权限
     * @return 数据补录权限集合
     */
    public List<DicManualDataDefineUser> selectDicManualDataDefineUserList(DicManualDataDefineUser dicManualDataDefineUser);

    /**
     * 新增数据补录权限
     * 
     * @param dicManualDataDefineUser 数据补录权限
     * @return 结果
     */
    public int insertDicManualDataDefineUser(DicManualDataDefineUser dicManualDataDefineUser);
    
    public int deleteDicManualDataDefineUser(DicManualDataDefineUser dicManualDataDefineUser);

    /**
     * 修改数据补录权限
     * 
     * @param dicManualDataDefineUser 数据补录权限
     * @return 结果
     */
    public int updateDicManualDataDefineUser(DicManualDataDefineUser dicManualDataDefineUser);

    /**
     * 删除数据补录权限
     * 
     * @param manualDataId 数据补录权限ID
     * @return 结果
     */
    public int deleteDicManualDataDefineUserById(Long manualDataId);

    /**
     * 批量删除数据补录权限
     * 
     * @param manualDataIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDicManualDataDefineUserByIds(Long[] manualDataIds);
}
