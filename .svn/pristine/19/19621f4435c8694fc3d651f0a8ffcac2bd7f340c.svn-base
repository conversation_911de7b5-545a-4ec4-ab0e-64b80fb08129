package com.dqms.dam.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dam.domain.DamAssetsSubscribe;
import com.dqms.dam.service.IDamAssetsSubscribeService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 资产订阅Controller
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
@RestController
@RequestMapping("/dam/damAssetsSubscribe")
public class DamAssetsSubscribeController extends BaseController
{
    @Autowired
    private IDamAssetsSubscribeService damAssetsSubscribeService;

    /**
     * 查询资产订阅列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DamAssetsSubscribe damAssetsSubscribe)
    {
        startPage();
        List<DamAssetsSubscribe> list = damAssetsSubscribeService.selectDamAssetsSubscribeList(damAssetsSubscribe);
        return getDataTable(list);
    }

    /**
     * 导出资产订阅列表
     */
    @Log(title = "资产订阅", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DamAssetsSubscribe damAssetsSubscribe)
    {
        List<DamAssetsSubscribe> list = damAssetsSubscribeService.selectDamAssetsSubscribeList(damAssetsSubscribe);
        ExcelUtil<DamAssetsSubscribe> util = new ExcelUtil<DamAssetsSubscribe>(DamAssetsSubscribe.class);
        return util.exportExcel(list, "damAssetsSubscribe");
    }

    /**
     * 获取资产订阅详细信息
     */
    @GetMapping(value = "/{assetsSubscribeId}")
    public AjaxResult getInfo(@PathVariable("assetsSubscribeId") Long assetsSubscribeId)
    {
        return AjaxResult.success(damAssetsSubscribeService.selectDamAssetsSubscribeById(assetsSubscribeId));
    }

    /**
     * 新增资产订阅
     */
    @Log(title = "资产订阅", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DamAssetsSubscribe damAssetsSubscribe)
    {
        return toAjax(damAssetsSubscribeService.insertDamAssetsSubscribe(damAssetsSubscribe));
    }

    /**
     * 修改资产订阅
     */
    @Log(title = "资产订阅", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DamAssetsSubscribe damAssetsSubscribe)
    {
        return toAjax(damAssetsSubscribeService.updateDamAssetsSubscribe(damAssetsSubscribe));
    }

    /**
     * 删除资产订阅
     */
    @Log(title = "资产订阅", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetsSubscribeIds}")
    public AjaxResult remove(@PathVariable Long[] assetsSubscribeIds)
    {
        return toAjax(damAssetsSubscribeService.deleteDamAssetsSubscribeByIds(assetsSubscribeIds));
    }
}
