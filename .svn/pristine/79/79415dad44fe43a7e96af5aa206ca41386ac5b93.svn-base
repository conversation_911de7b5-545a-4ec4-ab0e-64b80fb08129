package com.dqms.dam.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.dam.domain.DamAssetsClass;
import com.dqms.dam.domain.DamAssetsClassTreeSelect;
import com.dqms.dam.mapper.DamAssetsClassMapper;
import com.dqms.dam.service.IDamAssetsClassService;
import com.dqms.framework.web.service.TokenService;

import javax.management.RuntimeErrorException;

/**
 * 资产分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Service
public class DamAssetsClassServiceImpl implements IDamAssetsClassService
{
    @Autowired
    private DamAssetsClassMapper damAssetsClassMapper;

    @Autowired
    private TokenService tokenService;
    /**
     * 查询资产分类
     *
     * @param assetsClassId 资产分类ID
     * @return 资产分类
     */
    @Override
    public DamAssetsClass selectDamAssetsClassById(Long assetsClassId)
    {
        return damAssetsClassMapper.selectDamAssetsClassById(assetsClassId);
    }

    /**
     * 查询资产分类列表
     *
     * @param damAssetsClass 资产分类
     * @return 资产分类
     */
    @Override
    public List<DamAssetsClass> selectDamAssetsClassList(DamAssetsClass damAssetsClass)
    {
        return damAssetsClassMapper.selectDamAssetsClassList(damAssetsClass);
    }

    /**
     * 新增资产分类
     *
     * @param damAssetsClass 资产分类
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDamAssetsClass(DamAssetsClass damAssetsClass)
    {
    	DamAssetsClass info = damAssetsClassMapper.selectDamAssetsClassById(damAssetsClass.getParentId());
    	if(info!=null) {
    		damAssetsClass.setAncestors(info.getAncestors() + "," + damAssetsClass.getParentId());
    		damAssetsClass.setClassNameFull(info.getClassNameFull() + "/" + damAssetsClass.getClassName());
    	}else {
    		damAssetsClass.setAncestors("0");
    		damAssetsClass.setClassNameFull(damAssetsClass.getClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	damAssetsClass.setCreateTime(DateUtils.getNowDate());
    	damAssetsClass.setCreateId(loginUser.getUser().getUserId());
    	damAssetsClass.setCreateBy(loginUser.getUser().getNickName());
    	damAssetsClass.setUpdateTime(DateUtils.getNowDate());
    	damAssetsClass.setUpdateId(loginUser.getUser().getUserId());
    	damAssetsClass.setUpdateBy(loginUser.getUser().getNickName());
        return damAssetsClassMapper.insertDamAssetsClass(damAssetsClass);
    }

    /**
     * 修改资产分类
     *
     * @param damAssetsClass 资产分类
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDamAssetsClass(DamAssetsClass damAssetsClass)
    {
        if(damAssetsClass.getAssetsClassId().equals(damAssetsClass.getParentId())){
            throw new RuntimeErrorException(null, "父分类不能是本身！");
        }
    	DamAssetsClass newC = damAssetsClassMapper.selectDamAssetsClassById(damAssetsClass.getParentId());
    	DamAssetsClass oldC = damAssetsClassMapper.selectDamAssetsClassById(damAssetsClass.getAssetsClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String ancestorsFlag = oldC.getAncestors() + "," + damAssetsClass.getAssetsClassId();
             if(newC.getAncestors().contains(ancestorsFlag)){
                 throw new RuntimeErrorException(null, "父分类不能是本身下级！");
             }
             String newAncestors = newC.getAncestors() + "," + damAssetsClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = newC.getClassNameFull() + "/" + damAssetsClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             damAssetsClass.setAncestors(newAncestors);
             damAssetsClass.setClassNameFull(newClassNameFull);
             updateClassChildren(damAssetsClass.getAssetsClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(damAssetsClass.getAssetsClassId(), newClassNameFull, oldClassNameFull);
         }else if(newC==null){
             String newAncestors = "0";
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = damAssetsClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             damAssetsClass.setAncestors(newAncestors);
             damAssetsClass.setClassNameFull(newClassNameFull);
             updateClassChildren(damAssetsClass.getAssetsClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(damAssetsClass.getAssetsClassId(), newClassNameFull, oldClassNameFull);
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	damAssetsClass.setUpdateTime(DateUtils.getNowDate());
    	damAssetsClass.setUpdateId(loginUser.getUser().getUserId());
    	damAssetsClass.setUpdateBy(loginUser.getUser().getNickName());
        return damAssetsClassMapper.updateDamAssetsClass(damAssetsClass);
    }

    /**
     * 批量删除资产分类
     *
     * @param assetsClassIds 需要删除的资产分类ID
     * @return 结果
     */
    @Override
    public int deleteDamAssetsClassByIds(Long[] assetsClassIds)
    {
        //删除前，需删除子级
        for(Long assetsClassId : assetsClassIds){
            List<DamAssetsClass> damAssetsClasses = damAssetsClassMapper.selectChildrenClassById(assetsClassId);
            if(damAssetsClasses!=null && damAssetsClasses.size()>0){
                throw new RuntimeErrorException(null, "请先删除子分类");
            }
        }
        return damAssetsClassMapper.deleteDamAssetsClassByIds(assetsClassIds);
    }

    /**
     * 删除资产分类信息
     *
     * @param assetsClassId 资产分类ID
     * @return 结果
     */
    @Override
    public int deleteDamAssetsClassById(Long assetsClassId)
    {
        List<DamAssetsClass> damAssetsClasses = damAssetsClassMapper.selectChildrenClassById(assetsClassId);
        if(damAssetsClasses!=null && damAssetsClasses.size()>0){
            throw new RuntimeErrorException(null, "请先删除子分类");
        }
        return damAssetsClassMapper.deleteDamAssetsClassById(assetsClassId);
    }
    
    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateClassChildren(Long assetsClassId, String newAncestors, String oldAncestors)
    {
        List<DamAssetsClass> children = damAssetsClassMapper.selectChildrenClassById(assetsClassId);
        for (DamAssetsClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	damAssetsClassMapper.updateClassChildren(children);
        }
    }
    @Transactional
    public void updateClassNameFullChildren(Long assetsClassId, String newClassNameFull, String oldClassNameFull)
    {
        List<DamAssetsClass> children = damAssetsClassMapper.selectChildrenClassById(assetsClassId);
        for (DamAssetsClass child : children)
        {
            child.setClassNameFull(child.getClassNameFull().replace(oldClassNameFull, newClassNameFull));
        }
        if (children.size() > 0)
        {
        	damAssetsClassMapper.updateClassNameFullChildren(children);
        }
    }
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<DamAssetsClassTreeSelect> buildDamAssetsClassTreeSelect(List<DamAssetsClass> damAssetsClass)
    {
        List<DamAssetsClass> damAssetsClassTrees = buildDamAssetsClassTree(damAssetsClass);
        return damAssetsClassTrees.stream().map(DamAssetsClassTreeSelect::new).collect(Collectors.toList());
    }
    
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<DamAssetsClass> buildDamAssetsClassTree(List<DamAssetsClass> damAssetsClass)
    {
        List<DamAssetsClass> returnList = new ArrayList<DamAssetsClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (DamAssetsClass item : damAssetsClass)
        {
            tempList.add(item.getAssetsClassId());
        }
        for (Iterator<DamAssetsClass> iterator = damAssetsClass.iterator(); iterator.hasNext();)
        {
        	DamAssetsClass item = (DamAssetsClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(item.getParentId()))
            {
                recursionFn(damAssetsClass, item);
                returnList.add(item);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = damAssetsClass;
        }
        return returnList;
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<DamAssetsClass> list, DamAssetsClass t)
    {
        // 得到子节点列表
        List<DamAssetsClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DamAssetsClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<DamAssetsClass> getChildList(List<DamAssetsClass> list, DamAssetsClass t)
    {
        List<DamAssetsClass> tlist = new ArrayList<DamAssetsClass>();
        Iterator<DamAssetsClass> it = list.iterator();
        while (it.hasNext())
        {
        	DamAssetsClass n = (DamAssetsClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getAssetsClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DamAssetsClass> list, DamAssetsClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
