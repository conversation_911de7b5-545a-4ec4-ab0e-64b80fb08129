package com.dqms.dam.service;

import java.util.List;
import com.dqms.dam.domain.DamMind;

/**
 * 我的脑图Service接口
 * 
 * <AUTHOR>
 * @date 2021-07-01
 */
public interface IDamMindService 
{
    /**
     * 查询我的脑图
     * 
     * @param mindId 我的脑图ID
     * @return 我的脑图
     */
    public DamMind selectDamMindById(Long mindId);

    /**
     * 查询我的脑图列表
     * 
     * @param damMind 我的脑图
     * @return 我的脑图集合
     */
    public List<DamMind> selectDamMindList(DamMind damMind);

    /**
     * 新增我的脑图
     * 
     * @param damMind 我的脑图
     * @return 结果
     */
    public int insertDamMind(DamMind damMind);

    /**
     * 修改我的脑图
     * 
     * @param damMind 我的脑图
     * @return 结果
     */
    public int updateDamMind(DamMind damMind);

    /**
     * 批量删除我的脑图
     * 
     * @param mindIds 需要删除的我的脑图ID
     * @return 结果
     */
    public int deleteDamMindByIds(Long[] mindIds);

    /**
     * 删除我的脑图信息
     * 
     * @param mindId 我的脑图ID
     * @return 结果
     */
    public int deleteDamMindById(Long mindId);
}
