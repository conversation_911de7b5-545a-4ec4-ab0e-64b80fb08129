<template>
  <div class="app-container" style="height: calc(100vh - 84px);">
    <el-container style="height: 100%; border: 1px solid #eee">
      <el-aside
        style="background-color: rgb(48, 65, 86);padding-top:25px;margin-bottom: 0px;border-radius: 10px;"
      >
        <div id="runWcl" style="width:100%; height:200px"></div>
        <el-divider>{{ dsmDiscernMain.startTime }}</el-divider>
        <el-form :model="form" ref="form" :inline="true" label-width="70px">
          <div>
            <el-checkbox-group v-model="form.discernTypeLabels" size="small">
              <el-checkbox-button
                v-for="dict in discernTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-checkbox-button>
            </el-checkbox-group>
          </div>
          <div style="height: calc(100vh - 550px);overflow-y:auto;">
            <el-checkbox-group v-model="form.systemLabels" size="small">
              <el-checkbox
                v-for="item in systemOptions"
                :key="item.systemId"
                :label="item.name"
                :value="item.systemId"
                style="display:block;margin:5px 0px ;color:#FFF;"
                border
              ></el-checkbox>
            </el-checkbox-group>
          </div>
          <br />
          <div style="text-align:center;">
            <el-button
              icon="el-icon-caret-right"
              :disabled="
                form.systemLabels.length == 0 ||
                form.discernTypeLabels.length == 0
                  ? true
                  : false
              "
              type="primary"
              @click="submitForm"
              plain
              round
              >立即执行</el-button
            >
          </div>
        </el-form>
      </el-aside>
      <el-main>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="70px"
        >
          <el-form-item label="标准" prop="dsmId">
            <el-input
              v-model="queryParams.dsmName"
              placeholder="请输入标准"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="推荐类型" prop="discernType">
            <el-select
              v-model="queryParams.discernType"
              placeholder="请选择推荐类型"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in discernTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="dsmDiscernList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="一级推荐" align="center" prop="prodIdOneName">
            <template slot-scope="scope">
              {{ scope.row.prodIdOneName }}
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleUpdate(scope.row, scope.row.prodIdOne)"
                v-hasPermi="['dsm:dsmDiscern:remove']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
          <el-table-column label="二级推荐" align="center" prop="prodIdTwoName">
            <template slot-scope="scope">
              {{ scope.row.prodIdTwoName }}
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleUpdate(scope.row, scope.row.prodIdTwo)"
                v-hasPermi="['dsm:dsmDiscern:remove']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="三级推荐"
            align="center"
            prop="prodIdThreeName"
          >
            <template slot-scope="scope">
              {{ scope.row.prodIdThreeName }}
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleUpdate(scope.row, scope.row.prodIdThree)"
                v-hasPermi="['dsm:dsmDiscern:remove']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="四级推荐"
            align="center"
            prop="propIdFourName"
          >
            <template slot-scope="scope">
              {{ scope.row.propIdFourName }}
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleUpdate(scope.row, scope.row.propIdFour)"
                v-hasPermi="['dsm:dsmDiscern:remove']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="五级推荐"
            align="center"
            prop="propIdFiveName"
          >
            <template slot-scope="scope">
              {{ scope.row.propIdFiveName }}
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleUpdate(scope.row, scope.row.propIdFive)"
                v-hasPermi="['dsm:dsmDiscern:remove']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
          <el-table-column label="标准名称" align="center" prop="dsmName" />
          <el-table-column
            label="推荐类型"
            align="center"
            prop="discernType"
            :formatter="discernTypeFormat"
          />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleIgnore(scope.row)"
                v-hasPermi="['dsm:dsmDiscern:remove']"
                >忽略</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import {
  listDsmDiscern,
  getDsmDiscern,
  delDsmDiscern,
  addDsmDiscern,
  updateDsmDiscern,
  exportDsmDiscern,
  getRunWcl,
  ignoreDsmDiscern
} from "@/api/dsm/dsmDiscern";
import { listDsmDiscernMain } from "@/api/dsm/dsmDiscernMain";
import { listSystemAll } from "@/api/basic/system";

export default {
  name: "DsmDiscern",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 标准落标表格数据
      dsmDiscernList: [],
      dsmDiscernMain: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 推荐类型字典
      discernTypeOptions: [],
      // 系统字典
      systemOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        prodIdOne: null,
        prodIdTwo: null,
        prodIdThree: null,
        propIdFour: null,
        propIdFive: null,
        dsmId: null,
        discernType: null
      },
      // 表单参数
      form: {
        discernTypes: [],
        systems: [],
        discernTypeLabels: [],
        systemLabels: []
      },
      // 表单校验
      rules: {}
    };
  },
  created() {
    listDsmDiscernMain(this.queryParams).then(response => {
      if (response.rows != null && response.rows.length > 0) {
        this.dsmDiscernMain = response.rows[0];
        this.form.discernTypeLabels = this.dsmDiscernMain.discernTypeLabels.split(
          ","
        );
        this.form.systemLabels = this.dsmDiscernMain.systemLabels.split(",");
        this.showRunWcl(
          (
            (this.dsmDiscernMain.runNum / this.dsmDiscernMain.dsmNum) *
            100
          ).toFixed(2)
        );
      } else {
        this.showRunWcl(0);
      }
    });
    this.getList();
    this.getDicts("dsm_type").then(response => {
      this.discernTypeOptions = response.data;
    });
    listSystemAll().then(response => {
      this.systemOptions = response.data;
    });
  },
  mounted() {},
  methods: {
    /** 查询标准落标列表 */
    getList() {
      this.loading = true;
      listDsmDiscern(this.queryParams).then(response => {
        this.dsmDiscernList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 推荐类型字典翻译
    discernTypeFormat(row, column) {
      return this.selectDictLabel(this.discernTypeOptions, row.discernType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        discernTypes: [],
        systems: [],
        discernTypeLabels: [],
        systemLabels: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.discernId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row, propId) {
      let form = {
        discernId: row.discernId,
        prodId: propId,
        discernType: row.discernType
      };
      updateDsmDiscern(form).then(response => {
        this.msgSuccess("绑定成功");
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      let systems = [];
      for (let i = 0; i < this.systemOptions.length; i++) {
        if (this.form.systemLabels.indexOf(this.systemOptions[i].name) != -1) {
          systems.push(this.systemOptions[i].systemId);
        }
      }
      this.form.systems = systems;

      let types = [];
      for (let i = 0; i < this.discernTypeOptions.length; i++) {
        if (
          this.form.discernTypeLabels.indexOf(
            this.discernTypeOptions[i].dictLabel
          ) != -1
        ) {
          types.push(this.discernTypeOptions[i].dictValue);
        }
      }
      this.form.discernTypes = types;
      console.log(types);
      this.$refs["form"].validate(valid => {
        if (valid) {
          addDsmDiscern(this.form).then(response => {
            this.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleIgnore(row) {
      const discernIds = row.discernId || this.ids;
      this.$confirm(
        '是否确认忽略标准落标编号为"' +
          discernIds +
          '"的数据项，后续不再进行落标检查?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return ignoreDsmDiscern(discernIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("忽略成功");
        });
    },
    showRunWcl(value_) {
      getRunWcl(value_);
    }
  }
};
</script>
<style scoped>
>>> .el-divider__text {
  background-color: rgb(48, 65, 86);
  color: #fff;
  width: 180px;
}
>>> .el-checkbox {
  margin-left: 0px;
}
</style>
