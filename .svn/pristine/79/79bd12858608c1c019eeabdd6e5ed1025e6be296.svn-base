package com.dqms.dqm.service;

import com.dqms.dqm.domain.DqmValidationDetail;

import java.util.List;
import java.util.Map;

/**
 * 明细管理接口
 *
 * <AUTHOR>
 * @date 2021-07-10
 */
public interface IDqmValidationDetailService {
    /**
     * 查询是否存在该表名
     * @param tableName
     * @return
     */
    public int selectTableNameExit(String tableName);

    /**
     * 添加问题明细数据
     * @param dqmValidationDeatail
     */
    int insertDetailTable(DqmValidationDetail dqmValidationDeatail);

    List<Map<String, Object>> selectDetailList(DqmValidationDetail dqmValidationDetail) throws  Exception;
}
