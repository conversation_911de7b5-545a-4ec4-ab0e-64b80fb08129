package com.dqms.dqm.service;

import java.util.List;

import com.dqms.dqm.domain.DqmValidationClass;
import com.dqms.dqm.domain.DqmValidationClassTreeSelect;
import com.dqms.dsm.domain.DsmDimensionClass;

/**
 * 检查分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-05-28
 */
public interface IDqmValidationClassService 
{
    /**
     * 查询检查分类
     * 
     * @param validationClassId 检查分类ID
     * @return 检查分类
     */
    public DqmValidationClass selectDqmValidationClassById(Long validationClassId);

    /**
     * 查询检查分类列表
     * 
     * @param dqmValidationClass 检查分类
     * @return 检查分类集合
     */
    public List<DqmValidationClass> selectDqmValidationClassList(DqmValidationClass dqmValidationClass);

    /**
     * 新增检查分类
     * 
     * @param dqmValidationClass 检查分类
     * @return 结果
     */
    public int insertDqmValidationClass(DqmValidationClass dqmValidationClass);

    /**
     * 修改检查分类
     * 
     * @param dqmValidationClass 检查分类
     * @return 结果
     */
    public int updateDqmValidationClass(DqmValidationClass dqmValidationClass);

    /**
     * 批量删除检查分类
     * 
     * @param validationClassIds 需要删除的检查分类ID
     * @return 结果
     */
    public int deleteDqmValidationClassByIds(Long[] validationClassIds);

    /**
     * 删除检查分类信息
     * 
     * @param validationClassId 检查分类ID
     * @return 结果
     */
    public int deleteDqmValidationClassById(Long validationClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param dqmValidationClass 任务分类列表
     * @return 树结构列表
     */
    public List<DqmValidationClass> buildDqmValidationClassTree(List<DqmValidationClass> dqmValidationClass);
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param dqmValidationClass 任务分类列表
     * @return 下拉树结构列表
     */
    public List<DqmValidationClassTreeSelect> buildDqmValidationClassTreeSelect(List<DqmValidationClass> dqmValidationClass);
}
