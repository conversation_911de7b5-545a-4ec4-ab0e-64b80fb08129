package com.dqms.task.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 任务监控历史对象 etl_task_instance_his
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
public class EtlTaskInstanceHis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 实例历史ID */
    private Long taskInstanceHisId;

    /** 实例ID */
    @Excel(name = "实例ID")
    private Long taskInstanceId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 调度ID */
    @Excel(name = "调度ID")
    private Long schedulerId;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchId;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 执行参数 */
    @Excel(name = "执行参数")
    private String loadDate;

    /** 触发类型 */
    @Excel(name = "触发类型")
    private String triggerType;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 执行信息 */
    @Excel(name = "执行信息")
    private String msg;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    public void setTaskInstanceHisId(Long taskInstanceHisId)
    {
        this.taskInstanceHisId = taskInstanceHisId;
    }

    public Long getTaskInstanceHisId()
    {
        return taskInstanceHisId;
    }
    public void setTaskInstanceId(Long taskInstanceId)
    {
        this.taskInstanceId = taskInstanceId;
    }

    public Long getTaskInstanceId()
    {
        return taskInstanceId;
    }
    public void setTaskId(Long taskId)
    {
        this.taskId = taskId;
    }

    public Long getTaskId()
    {
        return taskId;
    }
    public void setSchedulerId(Long schedulerId)
    {
        this.schedulerId = schedulerId;
    }

    public Long getSchedulerId()
    {
        return schedulerId;
    }
    public void setBatchId(String batchId)
    {
        this.batchId = batchId;
    }

    public String getBatchId()
    {
        return batchId;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public String getLoadDate() {
		return loadDate;
	}

	public void setLoadDate(String loadDate) {
		this.loadDate = loadDate;
	}

	public void setTriggerType(String triggerType)
    {
        this.triggerType = triggerType;
    }

    public String getTriggerType()
    {
        return triggerType;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public String getMsg()
    {
        return msg;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskInstanceHisId", getTaskInstanceHisId())
            .append("taskInstanceId", getTaskInstanceId())
            .append("taskId", getTaskId())
            .append("schedulerId", getSchedulerId())
            .append("batchId", getBatchId())
            .append("status", getStatus())
            .append("loadDate", getLoadDate())
            .append("triggerType", getTriggerType())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("msg", getMsg())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
