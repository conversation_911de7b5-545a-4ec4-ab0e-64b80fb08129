package com.dqms.task.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.task.domain.EtlTaskScheduleHis;
import com.dqms.task.service.IEtlTaskScheduleHisService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 调度执行历史Controller
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@RestController
@RequestMapping("/task/taskScheduleHis")
public class EtlTaskScheduleHisController extends BaseController
{
    @Autowired
    private IEtlTaskScheduleHisService etlTaskScheduleHisService;

    /**
     * 查询调度执行历史列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskScheduleHis:list')")
    @GetMapping("/list")
    public TableDataInfo list(EtlTaskScheduleHis etlTaskScheduleHis)
    {
        startPage();
        List<EtlTaskScheduleHis> list = etlTaskScheduleHisService.selectEtlTaskScheduleHisList(etlTaskScheduleHis);
        return getDataTable(list);
    }

    /**
     * 导出调度执行历史列表
     */
    @PreAuthorize("@ss.hasPermi('task:taskScheduleHis:export')")
    @Log(title = "调度执行历史", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EtlTaskScheduleHis etlTaskScheduleHis)
    {
        List<EtlTaskScheduleHis> list = etlTaskScheduleHisService.selectEtlTaskScheduleHisList(etlTaskScheduleHis);
        ExcelUtil<EtlTaskScheduleHis> util = new ExcelUtil<EtlTaskScheduleHis>(EtlTaskScheduleHis.class);
        return util.exportExcel(list, "taskScheduleHis");
    }

    /**
     * 获取调度执行历史详细信息
     */
    @PreAuthorize("@ss.hasPermi('task:taskScheduleHis:query')")
    @GetMapping(value = "/{taskScheduleHisId}")
    public AjaxResult getInfo(@PathVariable("taskScheduleHisId") Long taskScheduleHisId)
    {
        return AjaxResult.success(etlTaskScheduleHisService.selectEtlTaskScheduleHisById(taskScheduleHisId));
    }

    /**
     * 新增调度执行历史
     */
    @PreAuthorize("@ss.hasPermi('task:taskScheduleHis:add')")
    @Log(title = "调度执行历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EtlTaskScheduleHis etlTaskScheduleHis)
    {
        return toAjax(etlTaskScheduleHisService.insertEtlTaskScheduleHis(etlTaskScheduleHis));
    }

    /**
     * 修改调度执行历史
     */
    @PreAuthorize("@ss.hasPermi('task:taskScheduleHis:edit')")
    @Log(title = "调度执行历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EtlTaskScheduleHis etlTaskScheduleHis)
    {
        return toAjax(etlTaskScheduleHisService.updateEtlTaskScheduleHis(etlTaskScheduleHis));
    }

    /**
     * 删除调度执行历史
     */
    @PreAuthorize("@ss.hasPermi('task:taskScheduleHis:remove')")
    @Log(title = "调度执行历史", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskScheduleHisIds}")
    public AjaxResult remove(@PathVariable Long[] taskScheduleHisIds)
    {
        return toAjax(etlTaskScheduleHisService.deleteEtlTaskScheduleHisByIds(taskScheduleHisIds));
    }
}
