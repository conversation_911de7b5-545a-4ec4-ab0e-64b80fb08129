import request from '@/utils/request'

// 查询字段规则列表
export function listDicManualDataInstallRule(query) {
  return request({
    url: '/dic/dicManualDataInstallRule/list',
    method: 'get',
    params: query
  })
}

// 查询字段规则详细
export function getDicManualDataInstallRule(manualDataInstallId) {
  return request({
    url: '/dic/dicManualDataInstallRule/' + manualDataInstallId,
    method: 'get'
  })
}

// 新增字段规则
export function addDicManualDataInstallRule(data) {
  return request({
    url: '/dic/dicManualDataInstallRule',
    method: 'post',
    data: data
  })
}

// 修改字段规则
export function updateDicManualDataInstallRule(data) {
  return request({
    url: '/dic/dicManualDataInstallRule',
    method: 'put',
    data: data
  })
}

// 删除字段规则
export function delDicManualDataInstallRule(manualDataInstallId) {
  return request({
    url: '/dic/dicManualDataInstallRule/' + manualDataInstallId,
    method: 'delete'
  })
}

// 导出字段规则
export function exportDicManualDataInstallRule(query) {
  return request({
    url: '/dic/dicManualDataInstallRule/export',
    method: 'get',
    params: query
  })
}