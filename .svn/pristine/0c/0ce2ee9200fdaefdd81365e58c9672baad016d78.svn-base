package com.dqms.dic.service;

import java.util.List;
import com.dqms.dic.domain.DicManualDataBatch;

/**
 * 补录批次Service接口
 * 
 * <AUTHOR>
 * @date 2022-07-22
 */
public interface IDicManualDataBatchService 
{
    /**
     * 查询补录批次
     * 
     * @param batchId 补录批次ID
     * @return 补录批次
     */
    public DicManualDataBatch selectDicManualDataBatchById(String batchId);

    /**
     * 查询补录批次列表
     * 
     * @param dicManualDataBatch 补录批次
     * @return 补录批次集合
     */
    public List<DicManualDataBatch> selectDicManualDataBatchList(DicManualDataBatch dicManualDataBatch);

    /**
     * 新增补录批次
     * 
     * @param dicManualDataBatch 补录批次
     * @return 结果
     */
    public int insertDicManualDataBatch(DicManualDataBatch dicManualDataBatch);

    /**
     * 修改补录批次
     * 
     * @param dicManualDataBatch 补录批次
     * @return 结果
     */
    public int updateDicManualDataBatch(DicManualDataBatch dicManualDataBatch);

    /**
     * 批量删除补录批次
     * 
     * @param batchIds 需要删除的补录批次ID
     * @return 结果
     */
    public int deleteDicManualDataBatchByIds(String[] batchIds);

    /**
     * 删除补录批次信息
     * 
     * @param batchId 补录批次ID
     * @return 结果
     */
    public int deleteDicManualDataBatchById(String batchId);
}
