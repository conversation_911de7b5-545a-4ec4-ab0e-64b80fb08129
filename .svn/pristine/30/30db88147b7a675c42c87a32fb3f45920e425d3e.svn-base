package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmStandardSrcDept;

/**
 * 标准来源部门Mapper接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmStandardSrcDeptMapper
{
    /**
     * 查询标准来源部门
     *
     * @param standardId 标准来源部门ID
     * @return 标准来源部门
     */
    public List<DsmStandardSrcDept> selectDsmStandardSrcDeptById(Long standardId);

    /**
     * 查询标准来源部门列表
     *
     * @param dsmStandardSrcDept 标准来源部门
     * @return 标准来源部门集合
     */
    public List<DsmStandardSrcDept> selectDsmStandardSrcDeptList(DsmStandardSrcDept dsmStandardSrcDept);

    /**
     * 新增标准来源部门
     *
     * @param dsmStandardSrcDept 标准来源部门
     * @return 结果
     */
    public int insertDsmStandardSrcDept(DsmStandardSrcDept dsmStandardSrcDept);

    /**
     * 修改标准来源部门
     *
     * @param dsmStandardSrcDept 标准来源部门
     * @return 结果
     */
    public int updateDsmStandardSrcDept(DsmStandardSrcDept dsmStandardSrcDept);

    /**
     * 删除标准来源部门
     *
     * @param standardId 标准来源部门ID
     * @return 结果
     */
    public int deleteDsmStandardSrcDeptById(Long standardId);

    /**
     * 批量删除标准来源部门
     *
     * @param standardIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmStandardSrcDeptByIds(Long[] standardIds);
}
