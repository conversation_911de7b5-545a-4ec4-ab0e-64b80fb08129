<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="所属系统" prop="systemId">
        <el-select
          v-model="queryParams.systemId"
          placeholder="请选择所属系统"
          clearable
          size="small"
        >
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据源" prop="datasourceId">
        <el-select
          v-model="queryParams.datasourceId"
          placeholder="请选择数据源"
          clearable
          size="small"
        >
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="表名" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入表名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="脱敏方式" >
        <el-select v-model="queryParams.desensitizationType" placeholder="请选择脱敏方式" clearable size="small">
          <el-option
            v-for="dict in desensitizationTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dsc:dscDesensitization:remove']"
          >注销</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dsc:dscDesensitization:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :refreshShow="true"
        :searchShow="true"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="dscDesensitizationList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属系统" align="center" prop="systemName" />
      <el-table-column label="数据源" align="center" prop="datasourceName" />
      <el-table-column label="表" align="center" prop="tableName" width="220" />
      <el-table-column
        label="脱敏方式"
        align="center"
        prop="desensitizationType"
        :formatter="desensitizationTypeFormat"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsc:dscDesensitization:edit']"
            >设置</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsc:dscDesensitization:remove']"
            >注销</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据脱敏对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="实体ID" prop="entityId" v-show="false">
          <el-input
            v-model="form.entityId"
            placeholder="请输入实体ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="脱敏方式" prop="desensitizationType">
          <el-select
            v-model="form.desensitizationType"
            placeholder="请选择脱敏方式 "
            style="width:100%"
            clearable
          >
            <el-option
              v-for="dict in desensitizationTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="entityPropList">
        <el-table-column label="字段" align="center" prop="propName" />
        <el-table-column label="注释" align="center" prop="propComment" />
        <el-table-column
          label="脱敏方式"
          align="center"
          prop="tableName"
          width="220"
        >
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.runType"
              placeholder="请选择脱敏方式 "
              clearable
            >
              <el-option
                v-for="dict in runTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDscDesensitization,
  getDscDesensitization,
  delDscDesensitization,
  addDscDesensitization,
  updateDscDesensitization,
  exportDscDesensitization
} from "@/api/dsc/dscDesensitization";
import { listDscDesensitizationDetail } from "@/api/dsc/dscDesensitizationDetail";
import { listDatasourceAll, listSystem } from "@/api/basic/datasource";
import { findAllEntityProp } from "@/api/mdm/mdmDataEntityShip";
export default {
  name: "DscDesensitization",
  components: {},
  data() {
    return {
      dataSourceOptions: [],
      //应用系统选项
      systemOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据脱敏表格数据
      dscDesensitizationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 脱敏方式字典
      desensitizationTypeOptions: [],
      // 脱敏类型字典
      runTypeOptions: [],
      entityPropList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entityId: null,
        desensitizationType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entityId: [
          { required: true, message: "实体ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("dsc_run_type").then(response => {
    	this.desensitizationTypeOptions = response.data;
    });
    this.getDicts("dsc_desensitization_type").then(response => {
    	this.runTypeOptions = response.data;
    });
    console.log(this.runTypeOptions);
    this.getSystem();
    this.getDataSource();
  },
  methods: {
    /** 获取应用系统 */
    getSystem() {
      let systemParams = {};
      listSystem(systemParams).then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    /** 查询数据脱敏列表 */
    getList() {
      this.loading = true;
      listDscDesensitization(this.queryParams).then(response => {
        this.dscDesensitizationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 脱敏方式字典翻译
    desensitizationTypeFormat(row, column) {
      return this.selectDictLabel( this.desensitizationTypeOptions, row.desensitizationType );
    },
    runTypeFormat(row, column) {
      return this.selectDictLabel( this.runTypeOptions, row.runType );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        desensitizationId: null,
        entityId: null,
        desensitizationType: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.desensitizationId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据脱敏";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const desensitizationId = row.desensitizationId || this.ids;
      this.form.entityId = row.tableId;
      if (desensitizationId != null && desensitizationId != "") {
        getDscDesensitization(desensitizationId).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改数据脱敏";
        });
        let queryParams = {
          pageNum: 1,
          pageSize: 1000,
          desensitizationId: desensitizationId
        };
        listDscDesensitizationDetail(queryParams).then(response => {
          this.entityPropList = response.rows;
        });
      } else {
        this.open = true;
        this.title = "添加数据脱敏";
        let mdmDataEntityProp = { pageNum: 1,pageSize: 500,entityId:row.tableId};
        findAllEntityProp(mdmDataEntityProp).then(response => {
          this.entityPropList = response.rows;
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.details = [];
          for (var i = 0; i < this.entityPropList.length; i++) {
            this.form.details.push({
              propId: this.entityPropList[i].propId,
              runType: this.entityPropList[i].runType
            });
          }
          if (this.form.desensitizationId != null) {
            updateDscDesensitization(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDscDesensitization(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const desensitizationIds = row.desensitizationId || this.ids;
      this.$confirm(
        '是否确认删除数据脱敏编号为"' + desensitizationIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delDscDesensitization(desensitizationIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有数据脱敏数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportDscDesensitization(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    }
  }
};
</script>
