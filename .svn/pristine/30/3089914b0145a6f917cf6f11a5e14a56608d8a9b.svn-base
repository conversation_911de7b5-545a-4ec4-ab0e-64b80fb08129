package com.dqms.common.core.domain.entity;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 应用系统对象 sys_system
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
public class SysSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 系统ID */
    private Long systemId;

    /** 编码 */
    @Excel(name = "编码")
    private String code;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 优先级 */
    @Excel(name = "优先级")
    private String level;

    /** 源系统 */
    @Excel(name = "源系统")
    private String isSrc;

    /** 应用系统 */
    @Excel(name = "应用系统")
    private String isTar;

    /** 状态 */
    @Excel(name = "状态")
    private String status;
    
    /** 令牌 */
    @Excel(name = "令牌")
    private String password;
    
    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createId;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private String updateId;

    /** 系统负责人信息 */
    private List<SysUser> sysPersons;
    
    private Long[] sysPersonIds;

    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setLevel(String level)
    {
        this.level = level;
    }

    public String getLevel()
    {
        return level;
    }
    public void setIsSrc(String isSrc)
    {
        this.isSrc = isSrc;
    }

    public String getIsSrc()
    {
        return isSrc;
    }
    public void setIsTar(String isTar)
    {
        this.isTar = isTar;
    }

    public String getIsTar()
    {
        return isTar;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateId(String createId)
    {
        this.createId = createId;
    }

    public String getCreateId()
    {
        return createId;
    }
    public void setUpdateId(String updateId)
    {
        this.updateId = updateId;
    }

    public String getUpdateId()
    {
        return updateId;
    }

    public List<SysUser> getSysPersons() {
		return sysPersons;
	}

	public void setSysPersons(List<SysUser> sysPersons) {
		this.sysPersons = sysPersons;
	}

	public Long[] getSysPersonIds() {
		return sysPersonIds;
	}

	public void setSysPersonIds(Long[] sysPersonIds) {
		this.sysPersonIds = sysPersonIds;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("systemId", getSystemId())
            .append("code", getCode())
            .append("name", getName())
            .append("level", getLevel())
            .append("isSrc", getIsSrc())
            .append("isTar", getIsTar())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
