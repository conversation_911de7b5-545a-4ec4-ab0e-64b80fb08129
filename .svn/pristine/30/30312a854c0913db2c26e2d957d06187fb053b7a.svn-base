<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmStandardSrcSystemMapper">
    
    <resultMap type="DsmStandardSrcSystem" id="DsmStandardSrcSystemResult">
        <result property="standardId"    column="standard_id"    />
        <result property="systemId"    column="system_id"    />
    </resultMap>

    <sql id="selectDsmStandardSrcSystemVo">
        select standard_id, system_id from dsm_standard_src_system
    </sql>

    <select id="selectDsmStandardSrcSystemList" parameterType="DsmStandardSrcSystem" resultMap="DsmStandardSrcSystemResult">
        <include refid="selectDsmStandardSrcSystemVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectDsmStandardSrcSystemById" parameterType="Long" resultMap="DsmStandardSrcSystemResult">
        <include refid="selectDsmStandardSrcSystemVo"/>
        where standard_id = #{standardId}
    </select>
        
    <insert id="insertDsmStandardSrcSystem" parameterType="DsmStandardSrcSystem">
        insert into dsm_standard_src_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="standardId != null">standard_id,</if>
            <if test="systemId != null">system_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="standardId != null">#{standardId},</if>
            <if test="systemId != null">#{systemId},</if>
         </trim>
    </insert>

    <update id="updateDsmStandardSrcSystem" parameterType="DsmStandardSrcSystem">
        update dsm_standard_src_system
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemId != null">system_id = #{systemId},</if>
        </trim>
        where standard_id = #{standardId}
    </update>

    <delete id="deleteDsmStandardSrcSystemById" parameterType="Long">
        delete from dsm_standard_src_system where standard_id = #{standardId}
    </delete>

    <delete id="deleteDsmStandardSrcSystemByIds" parameterType="String">
        delete from dsm_standard_src_system where standard_id in 
        <foreach item="standardId" collection="array" open="(" separator="," close=")">
            #{standardId}
        </foreach>
    </delete>
</mapper>