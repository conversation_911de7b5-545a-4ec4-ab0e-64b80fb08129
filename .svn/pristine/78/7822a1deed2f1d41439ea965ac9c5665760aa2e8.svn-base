<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.basic.mapper.SysSystemMapper">

    <resultMap type="SysSystem" id="SysSystemResult">
        <result property="systemId"    column="system_id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="level"    column="level"    />
        <result property="isSrc"    column="is_src"    />
        <result property="isTar"    column="is_tar"    />
        <result property="status"    column="status"    />
        <result property="password"    column="password"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <collection  property="sysPersons"   javaType="java.util.List"  resultMap="SysUserResult" />
    </resultMap>

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
		<result property="nickName"     column="nick_name"    />
    </resultMap>

    <sql id="selectSysSystemVo">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time,
            u.nick_name, b.user_id
        from sys_system a
        left join sys_system_person b on b.system_id = a.system_id
        left join sys_user u on u.user_id = b.user_id
    </sql>

    <select id="selectSysSystemList" parameterType="SysSystem" resultMap="SysSystemResult">
        <include refid="selectSysSystemVo"/>
        <where>
            <if test="code != null  and code != ''"> and a.code = #{code}</if>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="level != null  and level != ''"> and a.level = #{level}</if>
            <if test="isSrc != null  and isSrc != ''"> and a.is_src = #{isSrc}</if>
            <if test="isTar != null  and isTar != ''"> and a.is_tar = #{isTar}</if>
            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="createId != null  and createId != ''"> and a.create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''"> and a.update_id = #{updateId}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>

		order by a.create_time desc
    </select>

    <select id="selectSysSystemById" parameterType="Long" resultMap="SysSystemResult">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time,
            u.nick_name, b.user_id
        from sys_system a
        left join sys_system_person b on b.system_id = a.system_id
        left join sys_user u on u.user_id = b.user_id
        where a.system_id = #{systemId}
    </select>
    <select id="selectSysSystemByNameOrCode" parameterType="SysSystem" resultMap="SysSystemResult">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time
        from sys_system a
        <where>
            a.code = #{code} or a.name = #{name}
        </where>
    </select>

    <select id="selectSysSystemByName" parameterType="String" resultMap="SysSystemResult">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time,
            u.nick_name, b.user_id
        from sys_system a
        left join sys_system_person b on b.system_id = a.system_id
        left join sys_user u on u.user_id = b.user_id
        where a.name = #{systemName}
    </select>

    <select id="getSysSystemByName" parameterType="String" resultMap="SysSystemResult">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time
        from sys_system a
        where a.name = #{systemName}
    </select>

    <insert id="insertSysSystem" parameterType="SysSystem" useGeneratedKeys="true" keyProperty="systemId">
        insert into sys_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemId != null">system_id,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="level != null">level,</if>
            <if test="isSrc != null">is_src,</if>
            <if test="isTar != null">is_tar,</if>
            <if test="status != null">status,</if>
            <if test="password != null">password,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemId != null">#{systemId},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="level != null">#{level},</if>
            <if test="isSrc != null">#{isSrc},</if>
            <if test="isTar != null">#{isTar},</if>
            <if test="status != null">#{status},</if>
            <if test="password != null">#{password},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysSystem" parameterType="SysSystem">
        update sys_system
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="level != null">level = #{level},</if>
            <if test="isSrc != null">is_src = #{isSrc},</if>
            <if test="isTar != null">is_tar = #{isTar},</if>
            <if test="status != null">status = #{status},</if>
            <if test="password != null">password = #{password},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where system_id = #{systemId}
    </update>

    <delete id="deleteSysSystemById" parameterType="Long">
        delete from sys_system where system_id = #{systemId}
    </delete>

    <delete id="deleteSysSystemByIds" parameterType="String">
        delete from sys_system where system_id in
        <foreach item="systemId" collection="array" open="(" separator="," close=")">
            #{systemId}
        </foreach>
    </delete>

    <delete id="deleteSysSystemPersonBySystemIds" parameterType="String">
        delete from sys_system_person where system_id in
        <foreach item="systemId" collection="array" open="(" separator="," close=")">
            #{systemId}
        </foreach>
    </delete>

    <delete id="deleteSysSystemPersonBySystemId" parameterType="Long">
        delete from sys_system_person where system_id = #{systemId}
    </delete>

    <insert id="batchSysSystemPerson">
        insert into sys_system_person(system_id, user_id) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.systemId}, #{item.userId})
        </foreach>
    </insert>

    <select id="selectSystemListByUserId" parameterType="Long" resultType="Integer">
		select r.system_id
        from sys_system r
	        left join sys_user_system ur on ur.system_id = r.system_id
	        left join sys_user u on u.user_id = ur.user_id
	    where u.user_id = #{userId}
	</select>
	<select id="selectPersonIdsBySystemId" parameterType="Long" resultType="Integer">
		select r.user_id
        from sys_user r
	        left join sys_system_person up on up.user_id = r.user_id
	    where up.system_id = #{systemId}
	</select>

    <select id="selectSysSystemAll" resultMap="SysSystemResult">
        <include refid="selectSysSystemVo"/>
        <where>
            ${params.dataScope}
        </where>
    </select>
    <select id="ListSysSystemByName" parameterType="SysSystem" resultMap="SysSystemResult">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time,
            u.nick_name, b.user_id
        from sys_system a
        left join sys_system_person b on b.system_id = a.system_id
        left join sys_user u on u.user_id = b.user_id
        where a.name = #{name}
    </select>
    <select id="selectSysSystemByCode" parameterType="SysSystem" resultMap="SysSystemResult">
        select a.system_id, a.code, a.name, a.level, a.is_src, a.is_tar, a.status, a.password, a.ip_address, a.create_by, a.update_by, a.create_id, a.update_id, a.create_time, a.update_time,
            u.nick_name, b.user_id
        from sys_system a
        left join sys_system_person b on b.system_id = a.system_id
        left join sys_user u on u.user_id = b.user_id
        where a.code = #{code}
    </select>

</mapper>
