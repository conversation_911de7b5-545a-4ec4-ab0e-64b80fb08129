package com.dqms.dsm.mapper;

import java.util.List;

import com.dqms.common.core.domain.entity.SysDept;
import com.dqms.dsm.domain.DsmStandard;
import com.dqms.dsm.domain.vo.DsmStandardVo;
import org.apache.ibatis.annotations.Param;

/**
 * 基础标准Mapper接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmStandardMapper
{
    /**
     * 查询基础标准
     *
     * @param standardId 基础标准ID
     * @return 基础标准
     */
    public DsmStandard selectDsmStandardById(Long standardId);
    public DsmStandardVo selectDsmStandardVoById(Long standardId);

    public DsmStandard selectDsmStandardByNo(String standardNo);

    public DsmStandard selectDsmStandardByIdOrName(@Param("standardCode") String standardCode,@Param("standardName") String standardName);

    /**
     * 查询基础标准列表
     *
     * @param dsmStandard 基础标准
     * @return 基础标准集合
     */
    public List<DsmStandard> selectDsmStandardList(DsmStandard dsmStandard);
    public List<DsmStandard> selectDsmStandardListByNo(DsmStandard dsmStandard);
    public List<DsmStandardVo> selectDsmStandardVoList(DsmStandardVo dsmStandardVo);

    /**
     * 新增基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    public int insertDsmStandard(DsmStandard dsmStandard);

    /**
     * 修改基础标准
     *
     * @param dsmStandard 基础标准
     * @return 结果
     */
    public int updateDsmStandard(DsmStandard dsmStandard);
    public int updatedsmStandardStatus(DsmStandard dsmStandard);

    /**
     * 删除基础标准
     *
     * @param standardId 基础标准ID
     * @return 结果
     */
    public int deleteDsmStandardById(Long standardId);

    /**
     * 批量删除基础标准
     *
     * @param standardIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmStandardByIds(Long[] standardIds);
    public int deleteDsmStandardByNo(String standardNo);
    /**
     * 获取导出来源系统
     * @param standardId
     * @return
     */
    public String getExportSystemSrcName(String standardId);

    /**
     * 获取导出应用系统
     * @param standardId
     * @return
     */
    public String getExportSystemTarName(String standardId);

    /**
     * 获取导出来源部门
     * @param standardId
     * @return
     */
    public String getExportDeptSrcName(String standardId);

    /**
     * 获取导出应用部门
     * @param standardId
     * @return
     */
    public String getExportDeptTarName(String standardId);

    /**
     * 获取导出来源部门
     * @param standardId
     * @return
     */
    public List<SysDept> getExportDeptSrcList(String standardId);

    /**
     * 获取导出应用部门
     * @param standardId
     * @return
     */
    public List<SysDept> getExportDeptTarList(String standardId);

    public DsmStandard selectDsmStandardByNameOrCode(DsmStandard dsmStandard);
    public List<DsmStandard> listDsmStandardByName(DsmStandard dsmStandard);
    public List<DsmStandard> listDsmStandardByCode(DsmStandard dsmStandard);



}
