package com.dqms.needs.linstener;

import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.dsm.domain.DsmStandard;
import com.dqms.dsm.service.IDsmStandardService;
//import org.activiti.engine.delegate.DelegateExecution;
//import org.activiti.engine.delegate.ExecutionListener;
//import org.activiti.engine.delegate.Expression;

/**
 * <AUTHOR>
 * @date 2021/7/14
 */
public class DsmStandardStateListener
//        implements ExecutionListener
{
//    private Expression state;
//    @Override
//    public void notify(DelegateExecution execution) {
//        DsmStandard sd=new DsmStandard();
//        sd.setStandardId(Long.parseLong(execution.getProcessInstanceBusinessKey()));
//        sd.setStatus(state.getValue(execution).toString());
//        SpringUtils.getBean(IDsmStandardService.class).updateStatusDsmStandard(sd);
//    }
}
