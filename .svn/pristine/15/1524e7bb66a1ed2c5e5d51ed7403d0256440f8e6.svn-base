<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dqm:dqmValidationMould:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dqm:dqmValidationMould:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dqm:dqmValidationMould:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dqm:dqmValidationMould:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :refreshShow="true"
        :searchShow="true"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="mouldList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="templateName" />
      <el-table-column label="规则主题" align="center" prop="topic" :formatter="topicFormat"/>
      <el-table-column label="状态" align="center" prop="state" :formatter="statusFormat"/>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改质量模板对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="templateName">
          <el-input
            v-model="form.templateName"
            placeholder="请输入模板名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="SQL" prop="fillSql">
          <el-input
            v-model="form.fillSql"
            type="textarea"
            placeholder="请输入SQL，例：select ${字段A} from ${表} where ${字段B} is null"
          />
        </el-form-item>
        <el-form-item label="参数" required>
          <el-table v-loading="loading" border :data="cateList">
            <el-table-column label="序号" align="center">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="名称" align="center" prop="parameterName" />
            <el-table-column
              label="类型"
              align="center"
              prop="parameterType"
              :formatter="parameterTypeFormat"
            />
            <el-table-column align="center">
              <template slot="header">
                <el-button
                  slot="append"
                  icon="el-icon-share"
                  @click="add"
                  size="mini"
                  type="text"
                  >添加</el-button
                >
              </template>
              <template slot-scope="scope">
                <el-button type="text" @click="revise(scope)">修改</el-button>
                <el-button type="text" @click="deleted(scope)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-dialog
          width="30%"
          :title="parameterTitle"
          :visible.sync="innerVisible"
          append-to-body
        >
          <el-form
            :inline="true"
            :model="formInline"
            ref="ruleForm"
            :rules="rules2"
            class="demo-form-inline"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="名称" prop="parameterName">
                  <el-input
                    v-model="formInline.parameterName"
                    placeholder="请输入名称"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="类型" prop="parameterType">
                  <el-select
                    v-model="formInline.parameterType"
                    placeholder="请选择类型"
                  >
                    <el-option
                      v-for="dict in parameterTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button type="primary" @click="submitForm2">确定</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>
        <el-form-item label="说明">
          <el-input v-model="form.remark" placeholder="请输入说明" clearable />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则主题" prop="topic">
              <el-radio-group v-model="form.topic">
                <el-radio :label="'1'">数据质量</el-radio>
                <el-radio :label="'2'">数据标准</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" class="state">
              <el-select
                v-model="form.state"
                placeholder="请选择状态"
                clearable
              >
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMould,
  getMould,
  delMould,
  addMould,
  updateMould,
  exportMould,
  getParameter,
  addParameter,
  updateParameter,
  delParameter
} from "@/api/dqm/dqmModule";
import { treeselect } from "@/api/dqm/dqmValidationClass";

export default {
  name: "Mould",
  components: {},
  data() {
    return {
      defaultProps: "",
      formInline: {
        parameterName: "",
        parameterType: ""
      },
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" }
        ],
        topic: [{ required: true, message: "请选择规则主题", trigger: "blur" }],
        fillSql: [{ required: true, message: "请输入SQL", trigger: "blur" }]
      },
      rules2: {
        parameterName: [
          { required: true, message: "请输入名称", trigger: "blur" }
        ],
        parameterType: [
          { required: true, message: "请输入类型", trigger: "blur" }
        ]
      },
      // 控制增加弹窗
      innerVisible: false,
      cateList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质量模板表格数据
      mouldList: [],
      //类型字典
      parameterTypeOptions: [],
        topicOptions: [],
        // 状态字典
        statusOptions: [],
      // 弹出层标题
      title: "",
      parameterTitle: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null
      },
      // 表单参数
      form: {
        templateName: "",
        topic: '1',
        fillSql: "",
        remark: "",
        table_name: "表名",
        field_name: "字段名",
        condition_name: "条件",
        state: '0'
      },
      options: [
        {
          value: "选项1",
          label: "黄金糕"
        },
        {
          value: "选项2",
          label: "双皮奶"
        },
        {
          value: "选项3",
          label: "蚵仔煎"
        },
        {
          value: "选项4",
          label: "龙须面"
        },
        {
          value: "选项5",
          label: "北京烤鸭"
        }
      ],
      value: "",
      isRevise: false, // 判断是否是修改
      reviseIndex: ""
    };
  },
  created() {
    this.getList();
    this.getDicts("dqm_parameter_type").then(response => {
      this.parameterTypeOptions = response.data;
    });
    this.getDicts("dqm_rule_object").then(response => {
        this.topicOptions = response.data;
    });
    this.getDicts("sys_normal_disable").then(response => {
        this.statusOptions = response.data;
    });
  },
  methods: {
    deleted(scope) {
      if (this.ids.length == 0) {
        this.cateList.splice(scope.$index, 1);
      } else {
        delParameter(scope.row.validationMouldParameterId).then(res => {
          this.cateList.splice(scope.$index, 1);
        });
      }
    },
    add() {
      this.parameterTitle = "新增参数";
      this.innerVisible = true;
      this.formInline = {
        parameterName: "",
        parameterType: ""
      };
    },
    revise(scope) {
      this.parameterTitle = "修改参数";
      this.reviseIndex = scope.$index;
      this.innerVisible = true;
      this.formInline = { ...scope.row };
      this.isRevise = true;
    },
    submitForm2() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          if (!this.isRevise) {
            if (this.ids.length != 0) {
              return addParameter({
                ...this.formInline,
                validationMouldId: this.ids[0]
              }).then(res => {
                this.cateList.push(this.formInline);
                this.innerVisible = false;
              });
            }
            this.cateList.push(this.formInline);
            this.innerVisible = false;
          } else {
            if (this.ids.length != 0) {
              return updateParameter({
                ...this.formInline,
                validationMouldId: this.ids[0]
              }).then(res => {
                this.cateList[this.reviseIndex] = { ...this.formInline };
                this.cateList = Object.assign([], this.cateList);
                this.formInline = {
                  parameterName: "",
                  parameterType: ""
                };
                this.innerVisible = false;
              });
            }
            this.cateList[this.reviseIndex] = { ...this.formInline };
            this.cateList = Object.assign([], this.cateList);
            this.formInline = {
              parameterName: "",
              parameterType: ""
            };
            this.innerVisible = false;
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    /** 查询质量模板列表 */
    getList() {
      this.loading = true;
      listMould(this.queryParams).then(response => {
        this.mouldList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        validationMouldId: null,
        templateName: null,
        topic: '1',
        fillSql: null,
        tableName: null,
        fieldName: null,
        conditionName: null,
        state: '0',
        remark: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.cateList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.validationMouldId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质量模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const validationMouldId = row.validationMouldId || this.ids;
      getMould(validationMouldId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改质量模板";
      });
      getParameter({ validationMouldId: validationMouldId[0] }).then(
        response => {
          this.cateList = response.rows;
        }
      );
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (this.cateList.length == 0) {
          return this.msgError("请新增参数");
        }
        if (valid) {
          if (this.form.validationMouldId != null) {
            updateMould(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            var Pojo = {
              dqmValidationMould: { ...this.form },
              dqmValidationMouldParameter: [...this.cateList]
            };
            addMould(Pojo).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const validationMouldIds = row.validationMouldId || this.ids;
      this.$confirm(
        '是否确认删除质量模板编号为"' + validationMouldIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delMould(validationMouldIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有质量模板数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportMould(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    },
    // 采集方式字典翻译
    parameterTypeFormat(row, column) {
      return this.selectDictLabel(this.parameterTypeOptions, row.parameterType);
    },
    // 字典翻译
    topicFormat(row, column) {
        return this.selectDictLabel(this.topicOptions, row.topic);
    },
    // 状态字典翻译
    statusFormat(row, column) {
       return this.selectDictLabel(this.statusOptions, row.state);
    }
  }
};
</script>
<style scoped>
.state >>> .el-select {
  width: 100%;
}
>>> .el-select {
  width: 202px;
}
</style>
