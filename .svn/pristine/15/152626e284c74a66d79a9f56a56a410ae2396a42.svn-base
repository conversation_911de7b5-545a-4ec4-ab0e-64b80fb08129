package com.dqms.dic.domain.vo;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据交换配置对象 dic_data_exchange
 *
 * <AUTHOR>
 * @date 2021-11-25
 */
public class GenerationSqlVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 源数据源ID */

    private Long srcDatasourceId;

    private String srcDatasourceName;

    /** 源表名 */
    private String srcTableName;

    /** 源Schema */
    private String srcSchema;

    /** 目标数据源ID */

    private Long tarDatasourceId;

    @Excel(name = "目标数据源")
    private String tarDatasourceName;

    /** 目标表名 */
    @Excel(name = "目标表名")
    private String tarTableName;

    /** 目标Schema */
    private String tarSchema;

    /** 分区方式 */
    private String partitionType;

    /** 分区字段类型 */
    private String partitionColType;

    /** 表注释 */
    private String tableComment;

    /** 分桶字段 */
    private String bucketCol;

    /** 分桶数量 */
    private Long bucketNum;

    /** HDFS路径 */
    private String hdfsPath;

    @Valid
    private List<MdmDataEntityProp> props;

    public Long getSrcDatasourceId() {
        return srcDatasourceId;
    }

    public void setSrcDatasourceId(Long srcDatasourceId) {
        this.srcDatasourceId = srcDatasourceId;
    }

    public String getSrcDatasourceName() {
        return srcDatasourceName;
    }

    public void setSrcDatasourceName(String srcDatasourceName) {
        this.srcDatasourceName = srcDatasourceName;
    }

    public String getSrcTableName() {
        return srcTableName;
    }

    public void setSrcTableName(String srcTableName) {
        this.srcTableName = srcTableName;
    }

    public String getSrcSchema() {
        return srcSchema;
    }

    public void setSrcSchema(String srcSchema) {
        this.srcSchema = srcSchema;
    }

    public Long getTarDatasourceId() {
        return tarDatasourceId;
    }

    public void setTarDatasourceId(Long tarDatasourceId) {
        this.tarDatasourceId = tarDatasourceId;
    }

    public String getTarDatasourceName() {
        return tarDatasourceName;
    }

    public void setTarDatasourceName(String tarDatasourceName) {
        this.tarDatasourceName = tarDatasourceName;
    }

    public String getTarTableName() {
        return tarTableName;
    }

    public void setTarTableName(String tarTableName) {
        this.tarTableName = tarTableName;
    }

    public String getTarSchema() {
        return tarSchema;
    }

    public void setTarSchema(String tarSchema) {
        this.tarSchema = tarSchema;
    }

    public String getPartitionType() {
        return partitionType;
    }

    public void setPartitionType(String partitionType) {
        this.partitionType = partitionType;
    }

    public String getPartitionColType() {
        return partitionColType;
    }

    public void setPartitionColType(String partitionColType) {
        this.partitionColType = partitionColType;
    }

    public String getTableComment() {
        return tableComment;
    }

    public void setTableComment(String tableComment) {
        this.tableComment = tableComment;
    }

    public String getBucketCol() {
        return bucketCol;
    }

    public void setBucketCol(String bucketCol) {
        this.bucketCol = bucketCol;
    }

    public Long getBucketNum() {
        return bucketNum;
    }

    public void setBucketNum(Long bucketNum) {
        this.bucketNum = bucketNum;
    }

    public String getHdfsPath() {
        return hdfsPath;
    }

    public void setHdfsPath(String hdfsPath) {
        this.hdfsPath = hdfsPath;
    }

    public List<MdmDataEntityProp> getProps() {
        return props;
    }

    public void setProps(List<MdmDataEntityProp> props) {
        this.props = props;
    }
}
