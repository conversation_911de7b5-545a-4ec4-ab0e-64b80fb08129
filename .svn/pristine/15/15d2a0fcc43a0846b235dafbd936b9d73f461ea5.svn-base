package com.dqms.dam.service.impl;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.WildcardQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder.Field;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchResultMapper;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.constant.EsConstant;
import com.dqms.common.constant.HttpStatus;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.entity.SysUser;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.core.page.PageDomain;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.core.page.TableSupport;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.domain.DamAssetsClass;
import com.dqms.dam.domain.DamAssetsClassRel;
import com.dqms.dam.domain.DamAssetsSubscribe;
import com.dqms.dam.domain.vo.DamAssetsVo;
import com.dqms.dam.mapper.DamAssetsClassMapper;
import com.dqms.dam.mapper.DamAssetsClassRelMapper;
import com.dqms.dam.mapper.DamAssetsMapper;
import com.dqms.dam.mapper.DamAssetsSubscribeMapper;
import com.dqms.dam.service.IDamAssetsService;
import com.dqms.dam.service.IDamAssetsServiceES;
import com.dqms.system.mapper.SysUserMapper;
import com.dqms.system.service.ISysConfigService;
import com.dqms.utils.NoticeUtils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据资产Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-08
 */
@Service
@Slf4j
public class DamAssetsServiceImpl implements IDamAssetsService {
    @Autowired
    private DamAssetsMapper damAssetsMapper;

    @Autowired
    private DamAssetsClassRelMapper damAssetsClassRelMapper;

    @Autowired
    private SysSystemMapper sysSystemMapper;


    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;

    @Autowired
    private DamAssetsClassMapper damAssetsClassMapper;


    @Autowired(required = false)
    private IDamAssetsServiceES iDamAssetsServiceES;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private DamAssetsSubscribeMapper damAssetsSubscribeMapper;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询数据资产
     *
     * @param damAssetsId 数据资产ID
     * @return 数据资产
     */
    @Override
    public DamAssets selectDamAssetsById(Long damAssetsId) {
        return damAssetsMapper.selectDamAssetsById(damAssetsId);
    }

    @Override
    public DamAssetsVo selectDamAssetsVoById(Long damAssetsId) {

        return damAssetsMapper.selectDamAssetsVoById(damAssetsId);
    }

    @Override
    public DamAssets selectDamAssetsByRel(String relId ,String assetsType) {
        return damAssetsMapper.selectDamAssetsByRel(relId,assetsType);
    }
    /**
     * 查询数据资产列表
     *
     * @param damAssets 数据资产
     * @return 数据资产
     */
    @Override
    public List<DamAssets> selectDamAssetsList(DamAssets damAssets) {
        List<DamAssets> list = damAssetsMapper.selectDamAssetsList(damAssets);
        return list;
    }

    @Override
    public List<DamAssetsVo> selectDamAssetsVoList(DamAssetsVo damAssetsVo) {
        List<DamAssetsVo> list = damAssetsMapper.selectDamAssetsVoList(damAssetsVo);
        for(DamAssetsVo vo :list) {
        	List<DamAssetsClass> classs = vo.getAssetsClasss();
        	if(classs!=null&&classs.size()>0) {
        		String name ="";
        		for(DamAssetsClass c :classs) {
        			name=name+","+c.getClassName();
        		}
        		vo.setClassNames(name.replaceFirst(",", ""));
        	}

        }
        return list;
    }
    
    @Override
    public List<DamAssets> selectDamAssetsAnaLyseList(DamAssets damAssets) {
    	List<DamAssets> list = null;
    	if(damAssets.getAssetsType().equals("订阅排名")) {
    		list = damAssetsMapper.selectDamAssetsListOrderDy(damAssets);
    	}else if(damAssets.getAssetsType().equals("评分排名")) {
    		list = damAssetsMapper.selectDamAssetsListOrderPf(damAssets);
    	}else if(damAssets.getAssetsType().equals("引用排名")) {
    		list = damAssetsMapper.selectDamAssetsListOrderYy(damAssets);
    	}else if(damAssets.getAssetsType().equals("安全排名")) {
    		list = damAssetsMapper.selectDamAssetsListOrderAq(damAssets);
    	}else if(damAssets.getAssetsType().equals("落标率排名")) {
    		list = damAssetsMapper.selectDamAssetsListOrderLbl(damAssets);
    	}else if(damAssets.getAssetsType().equals("注释率排名")) {
    		list = damAssetsMapper.selectDamAssetsListOrderZsl(damAssets);
    	}
        
        return list;
    }

    @Override
    public TableDataInfo selectMyDamAssetsList(DamAssets damAssets) {


        PageDomain pageDomain = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageDomain.getPageNum() - 1, pageDomain.getPageSize());
        //检索条件
        BoolQueryBuilder bqb = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldBqb = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(damAssets.getRemark())) {

            WildcardQueryBuilder queryBuilder1 = QueryBuilders.wildcardQuery("remark", "*"+damAssets.getRemark().toLowerCase()+"*");//搜索名字中含有jack的文档
            WildcardQueryBuilder queryBuilder2 = QueryBuilders.wildcardQuery("assetsName", "*"+damAssets.getRemark().toLowerCase()+"*");
            MatchPhraseQueryBuilder queryBuilder3 = QueryBuilders.matchPhraseQuery("remark", "*"+damAssets.getRemark().toLowerCase()+"*");//搜索名字中含有jack的文档
            MatchPhraseQueryBuilder queryBuilder4 = QueryBuilders.matchPhraseQuery("assetsName", "*"+damAssets.getRemark().toLowerCase()+"*");
            shouldBqb.should(queryBuilder1)
                    .should(queryBuilder2)
                    .should(queryBuilder3)
                    .should(queryBuilder4);
        }
        bqb.must(shouldBqb);
        if (StringUtils.isNotNull(damAssets.getAssetsClassId())) {
            bqb.must(QueryBuilders.matchQuery("assetsClassIds", damAssets.getAssetsClassId()));
        }
        if (StringUtils.isNotNull(damAssets.getSystemId())) {
            bqb.must(QueryBuilders.matchQuery("systemId", damAssets.getSystemId()));
        }
        if (StringUtils.isNotNull(damAssets.getDatasourceId())) {
            bqb.must(QueryBuilders.matchQuery("datasourceId", damAssets.getDatasourceId()));
        }
        if (StringUtils.isNotEmpty(damAssets.getLevel())) {
            bqb.must(QueryBuilders.matchQuery("level", damAssets.getLevel()));
        }
        if (StringUtils.isNotEmpty(damAssets.getAssetsType())) {
            bqb.must(QueryBuilders.matchQuery("assetsType", damAssets.getAssetsType()));
        }

        List<String> highlightFields = new ArrayList<String>();
        highlightFields.add("remark");
        highlightFields.add("assetsName");
        Field[] fields = new Field[highlightFields.size()];
        for (int x = 0; x < highlightFields.size(); x++) {
            fields[x] = new HighlightBuilder.Field(highlightFields.get(x)).preTags(EsConstant.HIGH_LIGHT_START_TAG)
                    .postTags(EsConstant.HIGH_LIGHT_END_TAG);
        }
        SearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(bqb)
                .withPageable(pageable)
                .withHighlightFields(fields)
                .build();

        Page<DamAssets> damAssetsPage = elasticsearchTemplate.queryForPage(query, DamAssets.class, new SearchResultMapper() {
            @SneakyThrows
            @Override
            public <T> AggregatedPage<T> mapResults(SearchResponse searchResponse, Class<T> aClass, Pageable pageable) {
                List<DamAssets> damAssetsList = new ArrayList<>();
                SearchHits hits = searchResponse.getHits();
                for (SearchHit hit : hits) {
                    if (hits.getHits().length <= 0) {
                        return null;
                    }
                    DamAssets dam = new DamAssets();
                    dam.setDamAssetsId(Long.parseLong(hit.getId()));
                    dam.setAssetsName((String) hit.getSourceAsMap().get("assetsName"));
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("assetsClassId"))) {
                        dam.setAssetsClassId(Long.parseLong(hit.getSourceAsMap().get("assetsClassId").toString()));
                    }
                    dam.setAssetsCode((String) hit.getSourceAsMap().get("assetsCode"));
                    dam.setAssetsType((String) hit.getSourceAsMap().get("assetsType"));
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("systemId"))) {
                        dam.setSystemId(Long.parseLong(hit.getSourceAsMap().get("systemId").toString()));
                    }
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("datasourceId"))) {
                        dam.setDatasourceId(Long.parseLong(hit.getSourceAsMap().get("datasourceId").toString()));
                    }
                    dam.setPerson((String) hit.getSourceAsMap().get("person"));
                    dam.setLevel((String) hit.getSourceAsMap().get("level"));
                    dam.setRelId((String) hit.getSourceAsMap().get("relId"));
                    dam.setStatus((String) hit.getSourceAsMap().get("status"));
                    dam.setAttachment((String) hit.getSourceAsMap().get("attachment"));
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("createId"))) {
                        dam.setCreateId(Long.parseLong(hit.getSourceAsMap().get("createId").toString()));
                    }
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("updateId"))) {
                        dam.setUpdateId(Long.parseLong(hit.getSourceAsMap().get("updateId").toString()));
                    }
                    dam.setClassNames((String) hit.getSourceAsMap().get("className"));
                    dam.setRemark((String) hit.getSourceAsMap().get("remark"));
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("grade"))) {
                        dam.setGrade(Float.parseFloat(hit.getSourceAsMap().get("grade").toString()));
                    }
                    if (StringUtils.isNotNull(hit.getSourceAsMap().get("assetsSubscribeId"))) {
                        dam.setAssetsSubscribeId(Long.parseLong(hit.getSourceAsMap().get("assetsSubscribeId").toString()));
                    }
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Object startTime = hit.getSourceAsMap().get("startTime");
                    Object endTime = hit.getSourceAsMap().get("endTime");
                    if (startTime != null) {
                        dam.setStartTime(simpleDateFormat.parse(startTime.toString()));
                    }
                    if (endTime != null) {
                        dam.setEndTime(simpleDateFormat.parse(endTime.toString()));
                    }
                    DamAssets assets = new DamAssets();
                    assets.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
                    assets.setDamAssetsId(dam.getDamAssetsId());
                    List<DamAssets> list = damAssetsMapper.selectMyDamAssetsList(assets);
                    if(list!=null&&list.size()>0) {
                    	dam.setAssetsSubscribeId(list.get(0).getAssetsSubscribeId());
                    	dam.setGrade(list.get(0).getGrade());
                    }
                    setHighLight(hit, "remark", dam);
                    setHighLight(hit, "assetsName", dam);
                    damAssetsList.add(dam);
                }
                return new AggregatedPageImpl<T>((List<T>) damAssetsList, pageable,
                        searchResponse.getHits().getTotalHits());
            }

            @Override
            public <T> T mapSearchHit(SearchHit searchHit, Class<T> aClass) {
                return null;
            }
        });
//        Page<DamAssets> damAssetsPage = iDamAssetsServiceES.search(query);
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("查询成功");
        tableDataInfo.setRows(damAssetsPage.getContent());
        tableDataInfo.setTotal(damAssetsPage.getTotalElements());
        return tableDataInfo;

//        return damAssetsMapper.selectMyDamAssetsList(damAssets);
    }

    public void setHighLight(SearchHit searchHit, String field, Object object) {
        Map<String, HighlightField> highlightFieldMap = searchHit.getHighlightFields();
        HighlightField highlightField = highlightFieldMap.get(field);
        if (highlightField != null) {
            String highLightMessage = highlightField.fragments()[0].toString();
            String capitalize = StringUtils.capitalize(field);
            String methodName = "set" + capitalize;
            Class<?> clazz = object.getClass();
            try {
                Method setMethod = clazz.getMethod(methodName, String.class);
                setMethod.invoke(object, highLightMessage);

            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * 新增数据资产
     *
     * @param damAssets 数据资产
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDamAssets(DamAssets damAssets) {
        damAssets.setCreateTime(DateUtils.getNowDate());
        damAssets.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        damAssets.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        damAssets.setUpdateTime(DateUtils.getNowDate());
        damAssets.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        damAssets.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        int i = damAssetsMapper.insertDamAssets(damAssets);
        if (damAssets.getAssetsClassIds() != null && damAssets.getAssetsClassIds().length > 0) {
            damAssetsClassRelMapper.insertDamAssetsClassRelByDamAssets(damAssets.getDamAssetsId(), damAssets.getAssetsClassIds());
        }
        String flag = sysConfigService.selectConfigByKey("dam.synchro.elasticsearch");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	iDamAssetsServiceES.save(damAssets);
        }
        return i;
    }

    /**
     * 修改数据资产
     *
     * @param damAssets 数据资产
     * @return 结果
     */
    @Override
    @Transactional
    @Profile("!junit")
    public int updateDamAssets(DamAssets damAssets) {
        damAssets.setUpdateTime(DateUtils.getNowDate());
        damAssets.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        damAssets.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        int i = damAssetsMapper.updateDamAssets(damAssets);
        damAssetsClassRelMapper.deleteDamAssetsClassRelById(damAssets.getDamAssetsId());
        if (damAssets.getAssetsClassIds() != null && damAssets.getAssetsClassIds().length > 0) {
            damAssetsClassRelMapper.insertDamAssetsClassRelByDamAssets(damAssets.getDamAssetsId(), damAssets.getAssetsClassIds());
        }
        String flag = sysConfigService.selectConfigByKey("dam.synchro.elasticsearch");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	iDamAssetsServiceES.save(damAssets);
        }
        DamAssetsSubscribe damAssetsSubscribe = new DamAssetsSubscribe();
        damAssetsSubscribe.setDamAssetsId(damAssets.getDamAssetsId());
        List<DamAssetsSubscribe>  list = damAssetsSubscribeMapper.selectDamAssetsSubscribeList(damAssetsSubscribe);
        if(list!=null&&list.size()>0) {
            for(DamAssetsSubscribe s : list) {
                NoticeUtils.sendNotice(s.getCreateId()+"", s.getUserName(), "数据资产变更通知", damAssets.getAssetsName()+"("+damAssets.getAssetsCode()+")已变更！变更时间为"+DateUtils.getTime());
            }
        }
        return i;
    }

    /**
     * 批量删除数据资产
     *
     * @param damAssetsIds 需要删除的数据资产ID
     * @return 结果
     */
    @Override
    @Transactional
    @Profile("!junit")
    public int deleteDamAssetsByIds(Long[] damAssetsIds) {

        String flag = sysConfigService.selectConfigByKey("dam.synchro.elasticsearch");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
	        for (Long damAssetsId : damAssetsIds) {
	        	iDamAssetsServiceES.deleteById(damAssetsId);
	        	DamAssets damAssets=damAssetsMapper.selectDamAssetsById(damAssetsId);
	        	if(damAssets==null) {continue;}
	            DamAssetsSubscribe damAssetsSubscribe = new DamAssetsSubscribe();
	            damAssetsSubscribe.setDamAssetsId(damAssetsId);
	            List<DamAssetsSubscribe>  list = damAssetsSubscribeMapper.selectDamAssetsSubscribeList(damAssetsSubscribe);
	            if(list!=null&&list.size()>0) {
	            	for(DamAssetsSubscribe s : list) {
	            		NoticeUtils.sendNotice(s.getCreateId()+"", s.getUserName(), "数据资产注销通知", damAssets.getAssetsName()+"("+damAssets.getAssetsCode()+")已注销！注销时间为"+DateUtils.getTime());
	            	}
	            }
	        }
        }
        int result = damAssetsMapper.deleteDamAssetsByIds(damAssetsIds);
        return result;
    }

    /**
     * 删除数据资产信息
     *
     * @param damAssetsId 数据资产ID
     * @return 结果
     */
    @Override
    @Transactional
    @Profile("!junit")
    public int deleteDamAssetsById(Long damAssetsId) {
        String flag = sysConfigService.selectConfigByKey("dam.synchro.elasticsearch");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	iDamAssetsServiceES.deleteById(damAssetsId);
        }
    	DamAssets damAssets=damAssetsMapper.selectDamAssetsById(damAssetsId);
    	if(damAssets!=null) {
	        DamAssetsSubscribe damAssetsSubscribe = new DamAssetsSubscribe();
	        damAssetsSubscribe.setDamAssetsId(damAssetsId);
	        List<DamAssetsSubscribe>  list = damAssetsSubscribeMapper.selectDamAssetsSubscribeList(damAssetsSubscribe);
	        if(list!=null&&list.size()>0) {
	        	for(DamAssetsSubscribe s : list) {
	        		NoticeUtils.sendNotice(s.getCreateId()+"", s.getUserName(), "数据资产注销通知", damAssets.getAssetsName()+"("+damAssets.getAssetsCode()+")已注销！注销时间为"+DateUtils.getTime());
	        	}
	        }
    	}
        int result = damAssetsMapper.deleteDamAssetsById(damAssetsId);
        return result;
    }

    @Override
    @Transactional
    public String importDamAssets(List<DamAssetsVo> damAssetsVoList, Boolean isUpdateSupport) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNull(damAssetsVoList) || damAssetsVoList.size() == 0) {
            throw new CustomException("导入任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DamAssetsVo vo : damAssetsVoList) {
            try {
                /*----------非空检查begain-------------------*/
                if (StringUtils.isEmpty(vo.getAssetsName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、资产名称为空");
                    continue;
                }

                if (StringUtils.isEmpty(vo.getAssetsType())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、资产类型为空");
                    continue;
                }

                /*
                if (StringUtils.isEmpty(vo.getClassNames())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "资产标签为空");
                    continue;
                }

                if (StringUtils.isEmpty(vo.getAssetsCode())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、资产编码为空");
                    continue;
                }

                if (StringUtils.isEmpty(vo.getSystemName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、管理系统为空");
                    continue;
                }

                if (StringUtils.isEmpty(vo.getDatasourceName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据源为空");
                    continue;
                }

                if (StringUtils.isEmpty(vo.getPersonName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、负责人为空");
                    continue;
                }

                if(vo.getStartTime()==null){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、生效日期为空");
                    continue;
                }

                if (vo.getEndTime() == null) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、失效日期为空");
                    continue;
                }

                if (StringUtils.isEmpty(vo.getLevel())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、资产等级为空");
                    continue;
                }

                if (vo.getRelId() == null || StringUtils.isEmpty(vo.getRelId().toString())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、关联属性为空");
                    continue;

                }

                if (StringUtils.isEmpty(vo.getStatus())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、资产状态为空");
                    continue;
                }
                */

                /*if(vo.getAttachment()==null && vo.getAttachment().length()==0){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、附件路径为空");
                    continue;
                }*/

                /*  ----------非空检查end-------------------*/
                //验证系统是否存在
                if(StringUtils.isNotEmpty(vo.getSystemName())){
                    SysSystem sysSystem = sysSystemMapper.getSysSystemByName(vo.getSystemName());
                    if (StringUtils.isNull(sysSystem)) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、系统：" + vo.getSystemName() + " 未定义");
                        continue;
                    }
                    vo.setSystemId(sysSystem.getSystemId());
                }
                //验证数据源是否存在
                if(StringUtils.isNotEmpty(vo.getDatasourceName())){
                    SysDatasource sysDatasourceList = new SysDatasource();
                    sysDatasourceList.setName(vo.getDatasourceName());
                    SysDatasource sysDatasource = sysDatasourceMapper.getSysDatasourceByName(sysDatasourceList);
                    if (StringUtils.isNull(sysDatasource)) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据源：" + vo.getDatasourceName() + "未定义");
                        continue;
                    }
                    vo.setDatasourceId(sysDatasource.getDatasourceId());
                }
                //验证资产标签是否已经分类
                List<DamAssetsClass> assetsClasss = new ArrayList<>();
                if(StringUtils.isNotEmpty(vo.getClassNames())){
                    String[] className = vo.getClassNames().split(",");//所有资产标签数组
                    for (String classNameData : className) {
                        DamAssetsClass damAssetsClass = damAssetsClassMapper.selectDamAssetsClassByClassNameFull(classNameData);
                        if (StringUtils.isNull(damAssetsClass)) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、资产标签：" + classNameData + "未定义");
                            continue;
                        }
                        assetsClasss.add(damAssetsClass);
                    }
                }
                if(StringUtils.isEmpty(vo.getPersonName())){
                    vo.setPerson(loginUser.getUser().getUserId().toString());
                }else{
                    SysUser sysUser = userMapper.selectUserByNickName(vo.getPersonName());
                    if(sysUser==null){
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、负责人：" + vo.getPersonName() + "未定义");
                        continue;
                    }else{
                        vo.setPerson(sysUser.getUserId().toString());
                    }
                }
                if (failureNum > 0) {
                    continue;
                }
                vo.setAssetsClasss(assetsClasss);
                //验证数据资产是否已存在
                DamAssetsVo damAssetsVos = new DamAssetsVo();
                damAssetsVos.setAssetsName(vo.getAssetsName());
                /*damAssetsVos.setAssetsCode(vo.getAssetsCode());
                damAssetsVos.setAssetsType(vo.getAssetsType());
                damAssetsVos.setSystemId(vo.getSystemId());
                damAssetsVos.setDatasourceId(vo.getDatasourceId());*/
                DamAssetsVo damAssetsVoss = damAssetsMapper.getDamAssetsListVo(damAssetsVos);
                //数据初始化
                DamAssetsVo damAssetsVo = new DamAssetsVo();
                damAssetsVo.setAssetsName(vo.getAssetsName());
                damAssetsVo.setAssetsCode(vo.getAssetsCode());
                damAssetsVo.setAssetsType(vo.getAssetsType());
                damAssetsVo.setSystemId(vo.getSystemId());
                damAssetsVo.setDatasourceId(vo.getDatasourceId());
                damAssetsVo.setStartTime(vo.getStartTime());
                damAssetsVo.setEndTime(vo.getEndTime());
                damAssetsVo.setLevel(vo.getLevel());
                damAssetsVo.setRelId(vo.getRelId());
                damAssetsVo.setStatus(vo.getStatus());
                damAssetsVo.setAttachment(vo.getAttachment());
                damAssetsVo.setPerson(vo.getPerson());
                damAssetsVo.setCreateBy(loginUser.getUser().getNickName());
                damAssetsVo.setCreateId(loginUser.getUser().getUserId());
                damAssetsVo.setCreateTime(DateUtils.getNowDate());
                damAssetsVo.setAssetsClasss(vo.getAssetsClasss());
                if (StringUtils.isNull(damAssetsVoss)) {
                    DamAssets damAssets = new DamAssets();
                    BeanUtils.copyProperties(damAssetsVo, damAssets);
                    damAssetsMapper.insertDamAssets(damAssets);
                    //插入资产与标签中间表
                    if(damAssets.getAssetsClasss()!=null){
                        for(DamAssetsClass damAssetsClass : damAssets.getAssetsClasss()){
                            DamAssetsClassRel damAssetsClassRel = new DamAssetsClassRel();
                            damAssetsClassRel.setAssetsId(damAssets.getDamAssetsId());//资产ID
                            damAssetsClassRel.setAssetsClassId(damAssetsClass.getAssetsClassId());//标签ID
                            damAssetsClassRelMapper.insertDamAssetsClassRel(damAssetsClassRel);
                        }
                    }
                    successNum++;
                } else {
                    if (isUpdateSupport) {
                        damAssetsVo.setDamAssetsId(damAssetsVoss.getDamAssetsId());
                        DamAssets damAssets = new DamAssets();
                        BeanUtils.copyProperties(damAssetsVo, damAssets);
                        damAssetsMapper.updateDamAssets(damAssets);
                        damAssetsClassRelMapper.deleteDamAssetsClassRelById(damAssets.getDamAssetsId());
                        //插入资产与标签中间表
                        if(damAssets.getAssetsClasss()!=null){
                            for(DamAssetsClass damAssetsClass : damAssets.getAssetsClasss()){
                                DamAssetsClassRel damAssetsClassRel = new DamAssetsClassRel();
                                damAssetsClassRel.setAssetsId(damAssets.getDamAssetsId());//资产ID
                                damAssetsClassRel.setAssetsClassId(damAssetsClass.getAssetsClassId());//标签ID
                                damAssetsClassRelMapper.insertDamAssetsClassRel(damAssetsClassRel);
                            }
                        }
                        successNum++;
                    }else{
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、该数据：" /*+ vo.getSystemName() + "系统+" + vo.getDatasourceName() + "数据源+"*/ + vo.getAssetsName() + "资产名称：已存在");
                        continue;
                    }
                }
            } catch (Exception e) {
                failureNum++;
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            if (StringUtils.isNotNull(failureMsg)) {
                failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
                throw new CustomException(failureMsg.toString());
            } else {
                /*failureMsg.append("<br/>" + "系统错误，请联系管理员");*/
                throw new CustomException("系统错误，请联系管理员");
            }

        }
        if (StringUtils.isNotNull(successMsg)) {
            if (isUpdateSupport) {
                successMsg.insert(0, "恭喜您，数据更新成功！共 " + successNum + " 条");
            } else {
                successMsg.insert(0, "恭喜您，数据导入成功！共 " + successNum + " 条");
            }
        }
        return successMsg.toString();
    }
}
