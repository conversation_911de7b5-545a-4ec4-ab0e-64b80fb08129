package com.dqms.dsc.domain.vo;


import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 生命周期对象 dsc_entity
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscEntityVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 所属系统 */
    @Excel(name = "所属系统")
    private String systemName;
    /** 数据源 */
    @Excel(name = "数据源")
    private String datasourceName;
    /** 表 */
    @Excel(name = "用户/库/脚本路径")
    private String regDir;
    @Excel(name = "表")
    private String tableName;
    /** 区域类型 */
    @Excel(name = "区域类型",dictType="dsc_entity_region_type",combo={"内表","外表"})
    private String regionType;
    private String regionTypeLabel;
    /** 生命周期 */
    @Excel(name = "生命周期")
    private Long lifeCycle;
    /** 存储格式 */
    @Excel(name = "存储格式",dictType="dsc_entity_storage_format",combo={"TextFile","SequenceFile","AVRD","ORC","Parquec"})
    private String storageFormat;
    private String storageFormatLabel;
    /** 分区字段 */
    @Excel(name = "分区字段")
    private String partitionField;
    /** 分区类型 */
    @Excel(name = "分区类型",dictType="dsc_entity_run_cycle",combo={"无","日","周","月","季","年"})
    private String partitionType;
    private String partitionTypeLabel;
    /** 分区生命 */
    @Excel(name = "分区生命")
    private Long partitionLifeCycle;
    /** 分区格式 */
    @Excel(name = "分区格式",dictType="sys_data_type",combo={"文本","数值","日期","其他"})
    private String partitionFormat;
    private String partitionFormatLabel;
    /** 分桶字段 */
    @Excel(name = "分桶字段")
    private String bucketField;
    /** 分桶数量 */
    @Excel(name = "分桶数量")
    private Integer bucketNum;
    /** 归档周期 */
    @Excel(name = "归档周期",dictType="dsc_entity_run_cycle",combo={"无","日","周","月","季","年"})
    private String fileCycle;
    private String fileCycleLabel;
    /** 归档方式 */
    @Excel(name = "归档方式",dictType="dsc_entity_file_type",combo={"3年前按月归档","3年前按年归档","5年前按月归档","5年前按年归档"})
    private String fileType;
    private String fileTypeLabel;
    /** 日期字段 */
    @Excel(name = "日期字段")
    private String dateField;

    /** HDFS路径 */
    private String hdfsPath;

    /** 扩展ID */
    private Long dscEntityId;

    /** 实体ID */
    private Long entityId;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    private Long tableId;

    private Long datasourceId;

    private Long systemId;

    public String getRegDir() {
        return regDir;
    }

    public void setRegDir(String regDir) {
        this.regDir = regDir;
    }

    public void setDscEntityId(Long dscEntityId)
    {
        this.dscEntityId = dscEntityId;
    }

    public Long getDscEntityId()
    {
        return dscEntityId;
    }
    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setRegionType(String regionType)
    {
        this.regionType = regionType;
    }

    public String getRegionType()
    {
        return regionType;
    }
    public void setLifeCycle(Long lifeCycle)
    {
        this.lifeCycle = lifeCycle;
    }

    public Long getLifeCycle()
    {
        return lifeCycle;
    }
    public void setStorageFormat(String storageFormat)
    {
        this.storageFormat = storageFormat;
    }

    public String getStorageFormat()
    {
        return storageFormat;
    }
    public void setPartitionField(String partitionField)
    {
        this.partitionField = partitionField;
    }

    public String getPartitionField()
    {
        return partitionField;
    }
    public void setPartitionType(String partitionType)
    {
        this.partitionType = partitionType;
    }

    public String getPartitionType()
    {
        return partitionType;
    }
    public void setPartitionLifeCycle(Long partitionLifeCycle)
    {
        this.partitionLifeCycle = partitionLifeCycle;
    }

    public Long getPartitionLifeCycle()
    {
        return partitionLifeCycle;
    }
    public void setPartitionFormat(String partitionFormat)
    {
        this.partitionFormat = partitionFormat;
    }

    public String getPartitionFormat()
    {
        return partitionFormat;
    }
    public void setBucketField(String bucketField)
    {
        this.bucketField = bucketField;
    }

    public String getBucketField()
    {
        return bucketField;
    }
    public void setBucketNum(Integer bucketNum)
    {
        this.bucketNum = bucketNum;
    }

    public Integer getBucketNum()
    {
        return bucketNum;
    }
    public void setHdfsPath(String hdfsPath)
    {
        this.hdfsPath = hdfsPath;
    }

    public String getHdfsPath()
    {
        return hdfsPath;
    }
    public void setFileCycle(String fileCycle)
    {
        this.fileCycle = fileCycle;
    }

    public String getFileCycle()
    {
        return fileCycle;
    }
    public void setFileType(String fileType)
    {
        this.fileType = fileType;
    }

    public String getFileType()
    {
        return fileType;
    }
    public void setDateField(String dateField)
    {
        this.dateField = dateField;
    }

    public String getDateField()
    {
        return dateField;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getTableId() {
        return tableId;
    }

    public void setTableId(Long tableId) {
        this.tableId = tableId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(Long datasourceId) {
        this.datasourceId = datasourceId;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getRegionTypeLabel() {
		return regionTypeLabel;
	}

	public void setRegionTypeLabel(String regionTypeLabel) {
		this.regionTypeLabel = regionTypeLabel;
	}

	public String getStorageFormatLabel() {
		return storageFormatLabel;
	}

	public void setStorageFormatLabel(String storageFormatLabel) {
		this.storageFormatLabel = storageFormatLabel;
	}

	public String getPartitionTypeLabel() {
		return partitionTypeLabel;
	}

	public void setPartitionTypeLabel(String partitionTypeLabel) {
		this.partitionTypeLabel = partitionTypeLabel;
	}

	public String getPartitionFormatLabel() {
		return partitionFormatLabel;
	}

	public void setPartitionFormatLabel(String partitionFormatLabel) {
		this.partitionFormatLabel = partitionFormatLabel;
	}

	public String getFileCycleLabel() {
		return fileCycleLabel;
	}

	public void setFileCycleLabel(String fileCycleLabel) {
		this.fileCycleLabel = fileCycleLabel;
	}

	public String getFileTypeLabel() {
		return fileTypeLabel;
	}

	public void setFileTypeLabel(String fileTypeLabel) {
		this.fileTypeLabel = fileTypeLabel;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("dscEntityId", getDscEntityId())
                .append("entityId", getEntityId())
                .append("regionType", getRegionType())
                .append("lifeCycle", getLifeCycle())
                .append("storageFormat", getStorageFormat())
                .append("partitionField", getPartitionField())
                .append("partitionType", getPartitionType())
                .append("partitionLifeCycle", getPartitionLifeCycle())
                .append("partitionFormat", getPartitionFormat())
                .append("bucketField", getBucketField())
                .append("bucketNum", getBucketNum())
                .append("hdfsPath", getHdfsPath())
                .append("fileCycle", getFileCycle())
                .append("fileType", getFileType())
                .append("dateField", getDateField())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("createId", getCreateId())
                .append("updateId", getUpdateId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
