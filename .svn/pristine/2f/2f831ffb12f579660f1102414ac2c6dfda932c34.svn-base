package com.dqms.mdm.util.StrategyType; 

import base.BaseJunit;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.mdm.domain.MdmRegistry;
import org.junit.Test;
import org.junit.Before; 
import org.junit.After;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/** 
* OracleStrategy Tester. 
* 
* <AUTHOR> name> 
* @since <pre>10/18/2021</pre> 
* @version 1.0 
*/ 
public class OracleStrategyTest extends BaseJunit {
    @Autowired
    OracleStrategy oracleStrategy;

    @Autowired
    SysDatasourceMapper sysDatasourceMapper;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
    *
    * Method: getTableInfo(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void getTableInfo() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: getColumns(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void getColumns() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: checkTableExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void checkTableExist() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: checkProcedureExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void checkProcedureExist() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: getTablesAndViews(SysDatasource sysDatasource, String catalog, String [] types)
    *
    */
    @Test
    public void getTablesAndViews() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: getProcedures(SysDatasource sysDatasource, String catalog)
    *
    */
    @Test
    public void getProcedures() throws Exception {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(40L);

        List<Map<String, Object>> proList =  oracleStrategy.getProcedures(sysDatasource, "hs_user");
        List<Map<String, Object>> funList =  oracleStrategy.getFunctions(sysDatasource, "hs_user");
        List<Map<String, Object>>  list= oracleStrategy.getProExcludeFun(proList,funList);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey() + " : " + m.getValue());
            }
            System.out.println("------");
        }

    }

    /**
    *
    * Method: getProcedureInfo(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getProcedureInfo() throws Exception {
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("HS_BACKUPTABLE");
        mdmRegistry.setRegDir("HS_USER");
        mdmRegistry.setDatasourceId(40L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        Map<String, Object> procedureInfo = oracleStrategy.getProcedureInfo(sysDatasource, mdmRegistry);
        for (Map.Entry m : procedureInfo.entrySet()) {
            System.out.print(m.getKey() + " ");
            System.out.println(m.getValue());
        }
    }

    /**
    *
    * Method: excuteByLimit(SysDatasource sysDatasource, String sqlText, int pageSize)
    *
    */
    @Test
    public void excuteByLimit() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: quaryByPage(SysDatasource sysDatasource, String sqlText, int page, int size)
    *
    */
    @Test
    public void quaryByPage() throws Exception {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(40L);
        String sqlText = "select * from HS_USER.ABABEHAVIORINDEXARG a ; ";
        List<Map<String, Object>> maps = oracleStrategy.quaryByPage(sysDatasource, sqlText, 1, 5);
        for (Map<String, Object> stringObjectMap : maps) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("---------------------------\n");
        }
    }

    /**
    *
    * Method: getSqlCount(SysDatasource sysDatasource, String sqlText)
    *
    */
    @Test
    public void getSqlCount() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: getTableCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getTableCreate() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: getProcedureCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getProcedureCreate() throws Exception {
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("AP_USECUSETT_UFTSYSNODEID_UP");//function
        mdmRegistry.setRegDir("HS_USER");
        mdmRegistry.setDatasourceId(40L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        String procedureCreate = oracleStrategy.getProcedureCreate(sysDatasource, mdmRegistry);
        System.out.println("procedureCreate: \n"+procedureCreate);
    }

    /**
    *
    * Method: getViewCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getViewCreate() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: getDatabaseInfos(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getDatabaseInfos() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: parseStatements(SysDatasource sysDatasource, String sqlText)
    *
    */
    @Test
    public void parseStatements() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: createSchemaStatVisitor(SysDatasource sysDatasource)
    *
    */
    @Test
    public void createSchemaStatVisitor() throws Exception {
    //TODO: Test goes here...
    }

    /**
    *
    * Method: createUser(SysDatasource sysDatasource, String username, String passwd)
    *
    */
    @Test
    public void createUser() throws Exception {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(40L);
        Boolean testUser = oracleStrategy.createUser(sysDatasource, "testUser", "qazWSX");
        System.out.println("createUser："+testUser);
    }

    /**
    *
    * Method: grantBytable(SysDatasource sysDatasource, String username, String tables)
    *
    */
    @Test
    public void grantBytable() throws Exception {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(40L);
        Boolean testUser = oracleStrategy.grantBytable(sysDatasource, "testUser", "hs_user.ACCOUNTDEPLOY;hs_user.BONDCODE", "qazWSX");
        System.out.println("grantBytable："+testUser);
    }

    /**
    *
    * Method: dropUser(SysDatasource sysDatasource, String username)
    *
    */
    @Test
    public void dropUser() throws Exception {
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(40L);
        Boolean testUser = oracleStrategy.dropUser(sysDatasource, "testUser");
        System.out.println("grantBytable："+testUser);
    }
}
