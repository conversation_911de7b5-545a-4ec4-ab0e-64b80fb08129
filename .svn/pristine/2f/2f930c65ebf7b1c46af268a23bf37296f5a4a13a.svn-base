package com.dqms.task.service.impl;

import java.util.List;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.framework.web.service.TokenService;
import com.dqms.task.domain.EtlTaskSubscribe;
import com.dqms.task.enums.EtlConstants;
import com.dqms.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.task.mapper.SysSystemSubscribeMapper;
import com.dqms.task.domain.SysSystemSubscribe;
import com.dqms.task.service.ISysSystemSubscribeService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 系统结果订阅Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-02
 */
@Service
public class SysSystemSubscribeServiceImpl implements ISysSystemSubscribeService
{
    @Autowired
    private SysSystemSubscribeMapper sysSystemSubscribeMapper;

    @Autowired
    private TokenService tokenService;

    /**
     * 查询系统结果订阅
     *
     * @param systemSubscribeId 系统结果订阅ID
     * @return 系统结果订阅
     */
    @Override
    public SysSystemSubscribe selectSysSystemSubscribeById(Long systemSubscribeId)
    {
        return sysSystemSubscribeMapper.selectSysSystemSubscribeById(systemSubscribeId);
    }

    /**
     * 查询系统结果订阅列表
     *
     * @param sysSystemSubscribe 系统结果订阅
     * @return 系统结果订阅
     */
    @Override
    @DataScope(systemAlias = "t")
    public List<SysSystemSubscribe> selectSysSystemSubscribeList(SysSystemSubscribe sysSystemSubscribe)
    {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Long userId=null;
        if(sysSystemSubscribe.getUserId()==null) {
            userId=loginUser.getUser().getUserId();
        }else {
            userId=sysSystemSubscribe.getUserId();
        }
    	sysSystemSubscribe.setUserId(userId);
        return sysSystemSubscribeMapper.selectSysSystemSubscribeList(sysSystemSubscribe);
    }

    /**
     * 新增系统结果订阅
     *
     * @param sysSystemSubscribe 系统结果订阅
     * @return 结果
     */
    @Override
    @Transactional
    @DataScope(systemAlias = "t")
    public int insertSysSystemSubscribe(SysSystemSubscribe sysSystemSubscribe)
    {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Long userId=null;
        if(sysSystemSubscribe.getUserId()==null) {
            userId=loginUser.getUser().getUserId();
        }else {
            userId=sysSystemSubscribe.getUserId();
        }
        sysSystemSubscribe.setUserId(userId);
        if(sysSystemSubscribe.getSystemIds()!=null&&sysSystemSubscribe.getSystemIds().length>0){
            for(Long systemId : sysSystemSubscribe.getSystemIds()){
                SysSystemSubscribe ss = new SysSystemSubscribe();
                BeanUtils.copyBeanProp(ss, sysSystemSubscribe);
                SysSystemSubscribe subscribe = sysSystemSubscribeMapper.selectSysSystemSubscribeByUserId(systemId, userId);
                if(subscribe==null){
                    ss.setSystemId(systemId);
                    ss.setCreateTime(DateUtils.getNowDate());
                    ss.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
                    ss.setCreateBy(SecurityUtils.getLoginUser().getUsername());
                    sysSystemSubscribeMapper.insertSysSystemSubscribe(ss);
                }else{
                    ss.setSystemSubscribeId(subscribe.getSystemSubscribeId());
                    ss.setUpdateTime(DateUtils.getNowDate());
                    ss.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
                    ss.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                    sysSystemSubscribeMapper.updateSysSystemSubscribe(ss);
                }
            }
        }else{
            SysSystemSubscribe s = new SysSystemSubscribe();
            s.setCode(sysSystemSubscribe.getCode());
            s.setName(sysSystemSubscribe.getName());
            s.setUserId(sysSystemSubscribe.getUserId());
            s.setSuccessFlag(sysSystemSubscribe.getOldSuccessFlag());
            s.setErrorFlag(sysSystemSubscribe.getOldErrorFlag());
            s.setEmailFlag(sysSystemSubscribe.getOldEmailFlag());
            s.setSmsFlag(sysSystemSubscribe.getOldSmsFlag());
            s.setNoticeFlag(sysSystemSubscribe.getOldNoticeFlag());
            s.setParams(sysSystemSubscribe.getParams());
            List<SysSystemSubscribe> list = sysSystemSubscribeMapper.selectSysSystemSubscribeList(s);
            if(list!=null&&list.size()>0) {
                for(SysSystemSubscribe s1 : list) {
                    SysSystemSubscribe ss = new SysSystemSubscribe();
                    BeanUtils.copyBeanProp(ss, sysSystemSubscribe);
                    if(s1.getSystemSubscribeId()==null) {
                        ss.setSystemId(s1.getSystemId());
                        ss.setCreateTime(DateUtils.getNowDate());
                        ss.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
                        ss.setCreateBy(SecurityUtils.getLoginUser().getUsername());
                        sysSystemSubscribeMapper.insertSysSystemSubscribe(ss);
                    }else {
                        ss.setSystemSubscribeId(s1.getSystemSubscribeId());
                        ss.setUpdateTime(DateUtils.getNowDate());
                        ss.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
                        ss.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                        sysSystemSubscribeMapper.updateSysSystemSubscribe(ss);
                    }
                }
            }

        }
        return 1;
    }

    /**
     * 修改系统结果订阅
     *
     * @param sysSystemSubscribe 系统结果订阅
     * @return 结果
     */
    @Override
    public int updateSysSystemSubscribe(SysSystemSubscribe sysSystemSubscribe)
    {
        sysSystemSubscribe.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        sysSystemSubscribe.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        sysSystemSubscribe.setUpdateTime(DateUtils.getNowDate());
        return sysSystemSubscribeMapper.updateSysSystemSubscribe(sysSystemSubscribe);
    }

    /**
     * 批量删除系统结果订阅
     *
     * @param systemSubscribeIds 需要删除的系统结果订阅ID
     * @return 结果
     */
    @Override
    public int deleteSysSystemSubscribeByIds(Long[] systemSubscribeIds)
    {
        return sysSystemSubscribeMapper.deleteSysSystemSubscribeByIds(systemSubscribeIds);
    }

    /**
     * 删除系统结果订阅信息
     *
     * @param systemSubscribeId 系统结果订阅ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSysSystemSubscribeById(Long systemSubscribeId)
    {
        return sysSystemSubscribeMapper.deleteSysSystemSubscribeById(systemSubscribeId);
    }

    /**
     * 更改状态
     */
    @Override
    @Transactional
    public int updateSysSystemSubscribeStatus(SysSystemSubscribe sysSystemSubscribe)
    {
        SysSystemSubscribe subscribe = null;
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Long userId=null;
        if(sysSystemSubscribe.getUserId() == null){
            userId = loginUser.getUser().getUserId();
        }else{
            userId = sysSystemSubscribe.getUserId();
        }
        sysSystemSubscribe.setUserId(userId);

        subscribe = sysSystemSubscribeMapper.selectSysSystemSubscribeByUserId(sysSystemSubscribe.getSystemId(), userId);
        if(subscribe==null) {
            sysSystemSubscribe.setCreateTime(DateUtils.getNowDate());
            sysSystemSubscribe.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
            sysSystemSubscribe.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            if(sysSystemSubscribe.getType().equals("successFlag")) {
                sysSystemSubscribe.setSuccessFlag(EtlConstants.YES);
            }else if(sysSystemSubscribe.getType().equals("errorFlag")) {
                sysSystemSubscribe.setErrorFlag(EtlConstants.YES);
            }else if(sysSystemSubscribe.getType().equals("emailFlag")) {
                sysSystemSubscribe.setEmailFlag(EtlConstants.YES);
            }else if(sysSystemSubscribe.getType().equals("smsFlag")) {
                sysSystemSubscribe.setSmsFlag(EtlConstants.YES);
            }else if(sysSystemSubscribe.getType().equals("noticeFlag")) {
                sysSystemSubscribe.setNoticeFlag(EtlConstants.YES);
            }
            return sysSystemSubscribeMapper.insertSysSystemSubscribe(sysSystemSubscribe);
        }else {
            sysSystemSubscribe.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
            sysSystemSubscribe.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
            sysSystemSubscribe.setUpdateTime(DateUtils.getNowDate());
            if(sysSystemSubscribe.getType().equals("successFlag")) {
                if(subscribe.getSuccessFlag().equals(EtlConstants.YES)) {
                    sysSystemSubscribe.setSuccessFlag(EtlConstants.NO);
                }else {
                    sysSystemSubscribe.setSuccessFlag(EtlConstants.YES);
                }
            }else if(sysSystemSubscribe.getType().equals("errorFlag")) {
                if(subscribe.getErrorFlag().equals(EtlConstants.YES)) {
                    sysSystemSubscribe.setErrorFlag(EtlConstants.NO);
                }else {
                    sysSystemSubscribe.setErrorFlag(EtlConstants.YES);
                }
            }else if(sysSystemSubscribe.getType().equals("emailFlag")) {
                if(subscribe.getEmailFlag().equals(EtlConstants.YES)) {
                    sysSystemSubscribe.setEmailFlag(EtlConstants.NO);
                }else {
                    sysSystemSubscribe.setEmailFlag(EtlConstants.YES);
                }
            }else if(sysSystemSubscribe.getType().equals("smsFlag")) {
                if(subscribe.getSmsFlag().equals(EtlConstants.YES)) {
                    sysSystemSubscribe.setSmsFlag(EtlConstants.NO);
                }else {
                    sysSystemSubscribe.setSmsFlag(EtlConstants.YES);
                }
            }else if(sysSystemSubscribe.getType().equals("noticeFlag")) {
                if(subscribe.getNoticeFlag().equals(EtlConstants.YES)) {
                    sysSystemSubscribe.setNoticeFlag(EtlConstants.NO);
                }else {
                    sysSystemSubscribe.setNoticeFlag(EtlConstants.YES);
                }
            }
            sysSystemSubscribe.setSystemSubscribeId(subscribe.getSystemSubscribeId());
            return sysSystemSubscribeMapper.updateSysSystemSubscribe(sysSystemSubscribe);
        }
    }
}
