package com.dqms.dsc.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsc.domain.DscDesensitizationDetail;
import com.dqms.dsc.service.IDscDesensitizationDetailService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 脱敏明细Controller
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@RestController
@RequestMapping("/dsc/dscDesensitizationDetail")
public class DscDesensitizationDetailController extends BaseController
{
    @Autowired
    private IDscDesensitizationDetailService dscDesensitizationDetailService;

    /**
     * 查询脱敏明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DscDesensitizationDetail dscDesensitizationDetail)
    {
        startPage();
        List<DscDesensitizationDetail> list = dscDesensitizationDetailService.selectDscDesensitizationDetailList(dscDesensitizationDetail);
        return getDataTable(list);
    }

    /**
     * 导出脱敏明细列表
     */
    @Log(title = "脱敏明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DscDesensitizationDetail dscDesensitizationDetail)
    {
        List<DscDesensitizationDetail> list = dscDesensitizationDetailService.selectDscDesensitizationDetailList(dscDesensitizationDetail);
        ExcelUtil<DscDesensitizationDetail> util = new ExcelUtil<DscDesensitizationDetail>(DscDesensitizationDetail.class);
        return util.exportExcel(list, "dscDesensitizationDetail");
    }

    /**
     * 获取脱敏明细详细信息
     */
    @GetMapping(value = "/{desensitizationDetailId}")
    public AjaxResult getInfo(@PathVariable("desensitizationDetailId") Long desensitizationDetailId)
    {
        return AjaxResult.success(dscDesensitizationDetailService.selectDscDesensitizationDetailById(desensitizationDetailId));
    }

    /**
     * 新增脱敏明细
     */
    @Log(title = "脱敏明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DscDesensitizationDetail dscDesensitizationDetail)
    {
        return toAjax(dscDesensitizationDetailService.insertDscDesensitizationDetail(dscDesensitizationDetail));
    }

    /**
     * 修改脱敏明细
     */
    @Log(title = "脱敏明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DscDesensitizationDetail dscDesensitizationDetail)
    {
        return toAjax(dscDesensitizationDetailService.updateDscDesensitizationDetail(dscDesensitizationDetail));
    }

    /**
     * 删除脱敏明细
     */
    @Log(title = "脱敏明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{desensitizationDetailIds}")
    public AjaxResult remove(@PathVariable Long[] desensitizationDetailIds)
    {
        return toAjax(dscDesensitizationDetailService.deleteDscDesensitizationDetailByIds(desensitizationDetailIds));
    }
}
