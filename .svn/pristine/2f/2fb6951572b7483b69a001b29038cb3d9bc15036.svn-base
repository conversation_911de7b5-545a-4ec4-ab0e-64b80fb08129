package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.common.core.domain.entity.SysDept;
import com.dqms.common.core.domain.entity.SysUser;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 指标管理对象 dsm_index
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public class DsmIndex extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标ID */
    private Long indexId;
    private String indexNo;
    /** 指标编码 */
    @Excel(name = "指标编码")
    private String indexCode;

    /** 指标名称 */
    @Excel(name = "指标名称")
    private String indexName;

    /** 指标类型 */
    @Excel(name = "指标类型")
    private String indexType;

    /** 指标状态 */
    @Excel(name = "指标状态")
    private String status;

    /** 附件路径 */
    @Excel(name = "附件路径")
    private String attachment;

    /** 业务口径 */
    @Excel(name = "业务口径")
    private String definition;

    /** 规则SQL */
    @Excel(name = "规则SQL")
    private String execSql;

    /** 指标单位 */
    @Excel(name = "指标单位")
    private String unit;

    /** 计算周期 */
    @Excel(name = "计算周期")
    private String cycle;

    /** 版本 */
    @Excel(name = "版本")
    private Integer version;

    /** 版本 */
    @Excel(name = "指标分类")
    private Long indexClassId;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String indexClassName;

    /** 全路径 */
    @Excel(name = "全路径")
    private String indexClassNameFull;

    private String formula;

    private List<DsmIndex> dsmIndexs;

    private Long[] dsmIndexIds;

    private List<DsmDimension> dsmDimensions;

    private Long[] dsmDimensionIds;

    /** 数据类型 */
    @Excel(name = "数据类型")
    private String dataType;

    /** 数据长度 */
    @Excel(name = "数据长度")
    private Long columnSize;

    /** 数据精度 */
    @Excel(name = "数据精度")
    private Long decimalDigits;

    /** 是否主键 */
    @Excel(name = "是否主键")
    private String isPriKey;

    /** 是否为空 */
    @Excel(name = "是否为空")
    private String nullable;
    /** 是否落地 */
    @Excel(name = "是否落地",dictType="sys_yes_no",combo={"是","否"})
    private String ifDown;
    /** 所属系统 Id*/
    private Long systemId;
    /** 所属系统 */
    @Excel(name = "所属系统")
    private String systemName;
    /** 缺省值 */
    @Excel(name = "缺省值")
    private String defaultValue;

    /** 引用*/
    @Excel(name = "引用")
    private Long[] propIds;




    private String discernFlag;

    private String checkFlag;

    public void setIndexId(Long indexId)
    {
        this.indexId = indexId;
    }

    public Long getIndexId()
    {
        return indexId;
    }
    public void setIndexCode(String indexCode)
    {
        this.indexCode = indexCode;
    }

    public String getIndexCode()
    {
        return indexCode;
    }
    public void setIndexName(String indexName)
    {
        this.indexName = indexName;
    }

    public String getIndexName()
    {
        return indexName;
    }
    public void setIndexType(String indexType)
    {
        this.indexType = indexType;
    }

    public String getIndexType()
    {
        return indexType;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setAttachment(String attachment)
    {
        this.attachment = attachment;
    }

    public String getAttachment()
    {
        return attachment;
    }
    public void setDefinition(String definition)
    {
        this.definition = definition;
    }

    public String getDefinition()
    {
        return definition;
    }
    public void setExecSql(String execSql)
    {
        this.execSql = execSql;
    }

    public String getExecSql()
    {
        return execSql;
    }
    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }
    public void setCycle(String cycle)
    {
        this.cycle = cycle;
    }

    public String getCycle()
    {
        return cycle;
    }
    public void setVersion(Integer version)
    {
        this.version = version;
    }

    public Integer getVersion()
    {
        return version;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public Long getIndexClassId() {
		return indexClassId;
	}

	public void setIndexClassId(Long indexClassId) {
		this.indexClassId = indexClassId;
	}

	public String getIndexClassName() {
		return indexClassName;
	}

	public void setIndexClassName(String indexClassName) {
		this.indexClassName = indexClassName;
	}

	public String getIndexClassNameFull() {
		return indexClassNameFull;
	}

	public void setIndexClassNameFull(String indexClassNameFull) {
		this.indexClassNameFull = indexClassNameFull;
	}

	public String getFormula() {
		return formula;
	}

	public void setFormula(String formula) {
		this.formula = formula;
	}

	public List<DsmIndex> getDsmIndexs() {
		return dsmIndexs;
	}

	public void setDsmIndexs(List<DsmIndex> dsmIndexs) {
		this.dsmIndexs = dsmIndexs;
	}

	public Long[] getDsmIndexIds() {
		return dsmIndexIds;
	}

	public void setDsmIndexIds(Long[] dsmIndexIds) {
		this.dsmIndexIds = dsmIndexIds;
	}

	public List<DsmDimension> getDsmDimensions() {
		return dsmDimensions;
	}

	public void setDsmDimensions(List<DsmDimension> dsmDimensions) {
		this.dsmDimensions = dsmDimensions;
	}

	public Long[] getDsmDimensionIds() {
		return dsmDimensionIds;
	}

	public void setDsmDimensionIds(Long[] dsmDimensionIds) {
		this.dsmDimensionIds = dsmDimensionIds;
	}

    public Long[] getDsmIndexIdsByList() {
    	if(this.getDsmIndexs()!=null&&this.getDsmIndexs().size()>0) {
    		Long[]  ids = new Long[this.getDsmIndexs().size()];
    		int i=0;
    		for(DsmIndex dept : this.getDsmIndexs()) {
    			ids[i++]=dept.getIndexId();
    		}
    		return ids;
    	}else {
    		return null;
    	}
	}

    public Long[] getDsmDimensionIdsByList() {
    	if(this.getDsmDimensions()!=null&&this.getDsmDimensions().size()>0) {
    		Long[]  ids = new Long[this.getDsmDimensions().size()];
    		int i=0;
    		for(DsmDimension dept : this.getDsmDimensions()) {
    			ids[i++]=dept.getDimensionId();
    		}
    		return ids;
    	}else {
    		return null;
    	}
	}

	public String getIndexNo() {
		return indexNo;
	}

	public void setIndexNo(String indexNo) {
		this.indexNo = indexNo;
	}


	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public Long getColumnSize() {
		return columnSize;
	}

	public void setColumnSize(Long columnSize) {
		this.columnSize = columnSize;
	}

	public Long getDecimalDigits() {
		return decimalDigits;
	}

	public void setDecimalDigits(Long decimalDigits) {
		this.decimalDigits = decimalDigits;
	}

	public String getIsPriKey() {
		return isPriKey;
	}

	public void setIsPriKey(String isPriKey) {
		this.isPriKey = isPriKey;
	}

	public String getNullable() {
		return nullable;
	}

	public void setNullable(String nullable) {
		this.nullable = nullable;
	}

	public String getDefaultValue() {
		return defaultValue;
	}

	public void setDefaultValue(String defaultValue) {
		this.defaultValue = defaultValue;
	}


	public Long[] getPropIds() {
		return propIds;
	}

	public void setPropIds(Long[] propIds) {
		this.propIds = propIds;
	}

	public String getDiscernFlag() {
		return discernFlag;
	}

	public void setDiscernFlag(String discernFlag) {
		this.discernFlag = discernFlag;
	}

	public String getCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}

    public String getIfDown() {
        return ifDown;
    }

    public void setIfDown(String ifDown) {
        this.ifDown = ifDown;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }



    public String getSystemName() {
        return systemName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("indexId", getIndexId())
            .append("indexCode", getIndexCode())
            .append("indexName", getIndexName())
            .append("indexType", getIndexType())
            .append("status", getStatus())
            .append("attachment", getAttachment())
            .append("definition", getDefinition())
            .append("execSql", getExecSql())
            .append("unit", getUnit())
            .append("cycle", getCycle())
            .append("version", getVersion())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
