package com.dqms.mdm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.mdm.domain.MdmDataEntityShipAnalysisRel;
import com.dqms.mdm.service.IMdmDataEntityShipAnalysisRelService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 解析关联Controller
 *
 * <AUTHOR>
 * @date 2021-09-07
 */
@RestController
@RequestMapping("/mdm/mdmDdataEntityShipAnalysisRel")
public class MdmDataEntityShipAnalysisRelController extends BaseController
{
    @Autowired
    private IMdmDataEntityShipAnalysisRelService mdmDataEntityShipAnalysisRelService;

    /**
     * 查询解析关联列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDdataEntityShipAnalysisRel:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel)
    {
        startPage();
        List<MdmDataEntityShipAnalysisRel> list = mdmDataEntityShipAnalysisRelService.selectMdmDataEntityShipAnalysisRelList(mdmDataEntityShipAnalysisRel);
        return getDataTable(list);
    }

    /**
     * 导出解析关联列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDdataEntityShipAnalysisRel:export')")
    @Log(title = "解析关联", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel)
    {
        List<MdmDataEntityShipAnalysisRel> list = mdmDataEntityShipAnalysisRelService.selectMdmDataEntityShipAnalysisRelList(mdmDataEntityShipAnalysisRel);
        ExcelUtil<MdmDataEntityShipAnalysisRel> util = new ExcelUtil<MdmDataEntityShipAnalysisRel>(MdmDataEntityShipAnalysisRel.class);
        return util.exportExcel(list, "mdmDdataEntityShipAnalysisRel");
    }

    /**
     * 获取解析关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDdataEntityShipAnalysisRel:query')")
    @GetMapping(value = "/{shipId}")
    public AjaxResult getInfo(@PathVariable("shipId") Long shipId)
    {
        return AjaxResult.success(mdmDataEntityShipAnalysisRelService.selectMdmDataEntityShipAnalysisRelById(shipId));
    }

    /**
     * 新增解析关联
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDdataEntityShipAnalysisRel:add')")
    @Log(title = "解析关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel)
    {
        return toAjax(mdmDataEntityShipAnalysisRelService.insertMdmDataEntityShipAnalysisRel(mdmDataEntityShipAnalysisRel));
    }

    /**
     * 修改解析关联
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDdataEntityShipAnalysisRel:edit')")
    @Log(title = "解析关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmDataEntityShipAnalysisRel mdmDataEntityShipAnalysisRel)
    {
        return toAjax(mdmDataEntityShipAnalysisRelService.updateMdmDataEntityShipAnalysisRel(mdmDataEntityShipAnalysisRel));
    }

    /**
     * 删除解析关联
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDdataEntityShipAnalysisRel:remove')")
    @Log(title = "解析关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shipIds}")
    public AjaxResult remove(@PathVariable Long[] shipIds)
    {
        return toAjax(mdmDataEntityShipAnalysisRelService.deleteMdmDataEntityShipAnalysisRelByIds(shipIds));
    }
}
