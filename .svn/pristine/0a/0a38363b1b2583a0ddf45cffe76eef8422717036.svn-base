<template>
  <div class="head color41 font16"><span></span>{{ name }}</div>
</template>

<script>
export default {
  props: ["name"]
};
</script>

<style lang="scss" scoped>
.head {
  height: 55px;
  line-height: 55px;
  font-size: 16px;
  border-bottom: 1px solid #e5e5e5;
  span {
    display: inline-block;
    margin-right: 14px;
    margin-left: 21px;
    width: 6px;
    height: 24px;
    vertical-align: middle;
    background: #5698d2;
    border-radius: 3px;
  }
}
</style>
