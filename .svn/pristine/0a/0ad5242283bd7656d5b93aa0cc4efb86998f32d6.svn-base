package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.dsm.domain.DsmDimensionClass;
import com.dqms.dsm.domain.DsmDimensionClassTreeSelect;
import com.dqms.dsm.domain.DsmIndexClass;
import com.dqms.dsm.mapper.DsmDimensionClassMapper;
import com.dqms.dsm.service.IDsmDimensionClassService;
import com.dqms.framework.web.service.TokenService;

import javax.management.RuntimeErrorException;

/**
 * 字典分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Service
public class DsmDimensionClassServiceImpl implements IDsmDimensionClassService
{
    @Autowired
    private DsmDimensionClassMapper dsmDimensionClassMapper;

    @Autowired
    private TokenService tokenService;
    /**
     * 查询字典分类
     *
     * @param dimensionClassId 字典分类ID
     * @return 字典分类
     */
    @Override
    public DsmDimensionClass selectDsmDimensionClassById(Long dimensionClassId)
    {
        return dsmDimensionClassMapper.selectDsmDimensionClassById(dimensionClassId);
    }

    /**
     * 查询字典分类列表
     *
     * @param dsmDimensionClass 字典分类
     * @return 字典分类
     */
    @Override
    public List<DsmDimensionClass> selectDsmDimensionClassList(DsmDimensionClass dsmDimensionClass)
    {
        return dsmDimensionClassMapper.selectDsmDimensionClassList(dsmDimensionClass);
    }

    /**
     * 新增字典分类
     *
     * @param dsmDimensionClass 字典分类
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDsmDimensionClass(DsmDimensionClass dsmDimensionClass)
    {
    	DsmDimensionClass info = dsmDimensionClassMapper.selectDsmDimensionClassById(dsmDimensionClass.getParentId());
    	if(info!=null) {
    		dsmDimensionClass.setAncestors(info.getAncestors() + "," + dsmDimensionClass.getParentId());
    		dsmDimensionClass.setClassNameFull(info.getClassNameFull() + "/" + dsmDimensionClass.getClassName());
    	}else {
    		dsmDimensionClass.setAncestors("0");
    		dsmDimensionClass.setClassNameFull(dsmDimensionClass.getClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmDimensionClass.setCreateTime(DateUtils.getNowDate());
    	dsmDimensionClass.setCreateId(loginUser.getUser().getUserId());
    	dsmDimensionClass.setCreateBy(loginUser.getUser().getNickName());
    	dsmDimensionClass.setUpdateTime(DateUtils.getNowDate());
    	dsmDimensionClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmDimensionClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmDimensionClassMapper.insertDsmDimensionClass(dsmDimensionClass);
    }

    /**
     * 修改字典分类
     *
     * @param dsmDimensionClass 字典分类
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDsmDimensionClass(DsmDimensionClass dsmDimensionClass)
    {
        if(dsmDimensionClass.getDimensionClassId().equals(dsmDimensionClass.getParentId())){
            throw new RuntimeErrorException(null, "父分类不能是本身！");
        }
    	DsmDimensionClass newC = dsmDimensionClassMapper.selectDsmDimensionClassById(dsmDimensionClass.getParentId());
    	DsmDimensionClass oldC = dsmDimensionClassMapper.selectDsmDimensionClassById(dsmDimensionClass.getDimensionClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String ancestorsFlag = oldC.getAncestors() + "," + dsmDimensionClass.getDimensionClassId();
             if(newC.getAncestors().contains(ancestorsFlag)){
                 throw new RuntimeErrorException(null, "父分类不能是本身下级！");
             }
             String newAncestors = newC.getAncestors() + "," + dsmDimensionClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = newC.getClassNameFull() + "/" + dsmDimensionClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             dsmDimensionClass.setAncestors(newAncestors);
             dsmDimensionClass.setClassNameFull(newClassNameFull);
             updateClassChildren(dsmDimensionClass.getDimensionClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dsmDimensionClass.getDimensionClassId(), newClassNameFull, oldClassNameFull);
         }else if(newC==null){
             String newAncestors = "0";
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = dsmDimensionClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             dsmDimensionClass.setAncestors(newAncestors);
             dsmDimensionClass.setClassNameFull(newClassNameFull);
             updateClassChildren(dsmDimensionClass.getDimensionClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dsmDimensionClass.getDimensionClassId(), newClassNameFull, oldClassNameFull);
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmDimensionClass.setUpdateTime(DateUtils.getNowDate());
    	dsmDimensionClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmDimensionClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmDimensionClassMapper.updateDsmDimensionClass(dsmDimensionClass);
    }

    /**
     * 批量删除字典分类
     *
     * @param dimensionClassIds 需要删除的字典分类ID
     * @return 结果
     */
    @Override
    public int deleteDsmDimensionClassByIds(Long[] dimensionClassIds)
    {
        try {
            return dsmDimensionClassMapper.deleteDsmDimensionClassByIds(dimensionClassIds);
        }catch (Exception e){
            e.printStackTrace();
            if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("dsm_dimension_class_FK")){
                throw new RuntimeErrorException(null, "请先删除子分类");
            }else if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("fk_dsm_dimension_class_id")){
                throw new RuntimeErrorException(null, "分类已被引用，请勿删除");
            }else{
                throw new RuntimeErrorException(null, "删除失败，请与管理员联系："+e.getMessage());
            }
        }
    }

    /**
     * 删除字典分类信息
     *
     * @param dimensionClassId 字典分类ID
     * @return 结果
     */
    @Override
    public int deleteDsmDimensionClassById(Long dimensionClassId)
    {
        try {
            return dsmDimensionClassMapper.deleteDsmDimensionClassById(dimensionClassId);
        }catch (Exception e){
            e.printStackTrace();
            if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("dsm_dimension_class_FK")){
                throw new RuntimeErrorException(null, "请先删除子分类");
            }else if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("fk_dsm_dimension_class_id")){
                throw new RuntimeErrorException(null, "分类已被引用，请勿删除");
            }else{
                throw new RuntimeErrorException(null, "删除失败，请与管理员联系："+e.getMessage());
            }
        }
    }
    
    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateClassChildren(Long dimensionClassId, String newAncestors, String oldAncestors)
    {
        List<DsmDimensionClass> children = dsmDimensionClassMapper.selectChildrenClassById(dimensionClassId);
        for (DsmDimensionClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	dsmDimensionClassMapper.updateClassChildren(children);
        }
    }
    @Transactional
    public void updateClassNameFullChildren(Long dimensionClassId, String newClassNameFull, String oldClassNameFull)
    {
        List<DsmDimensionClass> children = dsmDimensionClassMapper.selectChildrenClassById(dimensionClassId);
        for (DsmDimensionClass child : children)
        {
            child.setClassNameFull(child.getClassNameFull().replace(oldClassNameFull, newClassNameFull));
        }
        if (children.size() > 0)
        {
        	dsmDimensionClassMapper.updateClassNameFullChildren(children);
        }
    }
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<DsmDimensionClassTreeSelect> buildDsmDimensionClassTreeSelect(List<DsmDimensionClass> dsmDimensionClass)
    {
        List<DsmDimensionClass> dsmDimensionClassTrees = buildDsmDimensionClassTree(dsmDimensionClass);
        return dsmDimensionClassTrees.stream().map(DsmDimensionClassTreeSelect::new).collect(Collectors.toList());
    }
    
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<DsmDimensionClass> buildDsmDimensionClassTree(List<DsmDimensionClass> dsmDimensionClass)
    {
        List<DsmDimensionClass> returnList = new ArrayList<DsmDimensionClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (DsmDimensionClass item : dsmDimensionClass)
        {
            tempList.add(item.getDimensionClassId());
        }
        for (Iterator<DsmDimensionClass> iterator = dsmDimensionClass.iterator(); iterator.hasNext();)
        {
        	DsmDimensionClass item = (DsmDimensionClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(item.getParentId()))
            {
                recursionFn(dsmDimensionClass, item);
                returnList.add(item);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = dsmDimensionClass;
        }
        return returnList;
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<DsmDimensionClass> list, DsmDimensionClass t)
    {
        // 得到子节点列表
        List<DsmDimensionClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DsmDimensionClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<DsmDimensionClass> getChildList(List<DsmDimensionClass> list, DsmDimensionClass t)
    {
        List<DsmDimensionClass> tlist = new ArrayList<DsmDimensionClass>();
        Iterator<DsmDimensionClass> it = list.iterator();
        while (it.hasNext())
        {
        	DsmDimensionClass n = (DsmDimensionClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDimensionClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DsmDimensionClass> list, DsmDimensionClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
