package com.dqms.dsm.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dsm.domain.DsmQuotaTag;

/**
 * 指标标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
public interface DsmQuotaTagMapper 
{
    /**
     * 查询指标标签
     * 
     * @param quotaTagId 指标标签ID
     * @return 指标标签
     */
    public DsmQuotaTag selectDsmQuotaTagById(Long quotaTagId);

    /**
     * 查询指标标签列表
     * 
     * @param dsmQuotaTag 指标标签
     * @return 指标标签集合
     */
    public List<DsmQuotaTag> selectDsmQuotaTagList(DsmQuotaTag dsmQuotaTag);

    /**
     * 新增指标标签
     * 
     * @param dsmQuotaTag 指标标签
     * @return 结果
     */
    public int insertDsmQuotaTag(DsmQuotaTag dsmQuotaTag);

    /**
     * 修改指标标签
     * 
     * @param dsmQuotaTag 指标标签
     * @return 结果
     */
    public int updateDsmQuotaTag(DsmQuotaTag dsmQuotaTag);

    /**
     * 删除指标标签
     * 
     * @param quotaTagId 指标标签ID
     * @return 结果
     */
    public int deleteDsmQuotaTagById(Long quotaTagId);

    /**
     * 批量删除指标标签
     * 
     * @param quotaTagIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmQuotaTagByIds(Long[] quotaTagIds);
    
    
    public DsmQuotaTag selectDsmQuotaTagByName(String className);
    public List<DsmQuotaTag> selectChildrenClassById(Long classId);
    public int updateTagChildren(@Param("dsmQuotaTag") List<DsmQuotaTag> dsmQuotaTag);
    public int updateTagNameFullChildren(@Param("dsmQuotaTag") List<DsmQuotaTag> dsmQuotaTag);
}
