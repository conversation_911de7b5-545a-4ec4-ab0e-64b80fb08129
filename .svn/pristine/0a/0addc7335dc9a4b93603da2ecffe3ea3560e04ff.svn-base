package com.dqms.dsc.service.impl;

import java.util.List;
import com.dqms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsc.mapper.DscMasterSystemMapper;
import com.dqms.dsc.domain.DscMasterSystem;
import com.dqms.dsc.service.IDscMasterSystemService;

/**
 * 接口权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@Service
public class DscMasterSystemServiceImpl implements IDscMasterSystemService
{
    @Autowired
    private DscMasterSystemMapper dscMasterSystemMapper;

    /**
     * 查询接口权限
     *
     * @param masterSystemId 接口权限ID
     * @return 接口权限
     */
    @Override
    public DscMasterSystem selectDscMasterSystemById(Long masterSystemId)
    {
        return dscMasterSystemMapper.selectDscMasterSystemById(masterSystemId);
    }

    /**
     * 查询接口权限列表
     *
     * @param dscMasterSystem 接口权限
     * @return 接口权限
     */
    @Override
    public List<DscMasterSystem> selectDscMasterSystemList(DscMasterSystem dscMasterSystem)
    {
        return dscMasterSystemMapper.selectDscMasterSystemList(dscMasterSystem);
    }

    /**
     * 新增接口权限
     *
     * @param dscMasterSystem 接口权限
     * @return 结果
     */
    @Override
    public int insertDscMasterSystem(DscMasterSystem dscMasterSystem)
    {
        dscMasterSystem.setCreateTime(DateUtils.getNowDate());
        return dscMasterSystemMapper.insertDscMasterSystem(dscMasterSystem);
    }

    /**
     * 修改接口权限
     *
     * @param dscMasterSystem 接口权限
     * @return 结果
     */
    @Override
    public int updateDscMasterSystem(DscMasterSystem dscMasterSystem)
    {
        return dscMasterSystemMapper.updateDscMasterSystem(dscMasterSystem);
    }

    /**
     * 批量删除接口权限
     *
     * @param masterSystemIds 需要删除的接口权限ID
     * @return 结果
     */
    @Override
    public int deleteDscMasterSystemByIds(Long[] masterSystemIds)
    {
        return dscMasterSystemMapper.deleteDscMasterSystemByIds(masterSystemIds);
    }

    /**
     * 删除接口权限信息
     *
     * @param masterSystemId 接口权限ID
     * @return 结果
     */
    @Override
    public int deleteDscMasterSystemById(Long masterSystemId)
    {
        return dscMasterSystemMapper.deleteDscMasterSystemById(masterSystemId);
    }
}
