package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmDiscernMain;

/**
 * 落标主Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-01
 */
public interface DsmDiscernMainMapper 
{
    /**
     * 查询落标主
     * 
     * @param discernMainId 落标主ID
     * @return 落标主
     */
    public DsmDiscernMain selectDsmDiscernMainById(Long discernMainId);

    /**
     * 查询落标主列表
     * 
     * @param dsmDiscernMain 落标主
     * @return 落标主集合
     */
    public List<DsmDiscernMain> selectDsmDiscernMainList(DsmDiscernMain dsmDiscernMain);

    /**
     * 新增落标主
     * 
     * @param dsmDiscernMain 落标主
     * @return 结果
     */
    public int insertDsmDiscernMain(DsmDiscernMain dsmDiscernMain);

    /**
     * 修改落标主
     * 
     * @param dsmDiscernMain 落标主
     * @return 结果
     */
    public int updateDsmDiscernMain(DsmDiscernMain dsmDiscernMain);

    /**
     * 删除落标主
     * 
     * @param discernMainId 落标主ID
     * @return 结果
     */
    public int deleteDsmDiscernMainById(Long discernMainId);

    /**
     * 批量删除落标主
     * 
     * @param discernMainIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmDiscernMainByIds();
}
