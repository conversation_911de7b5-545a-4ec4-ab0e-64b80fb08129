package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmQuotaDeptRel;
import com.dqms.dsm.service.IDsmQuotaDeptRelService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 指标与机构Controller
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
@RestController
@RequestMapping("/dsm/dsmQuotaDeptRel")
public class DsmQuotaDeptRelController extends BaseController
{
    @Autowired
    private IDsmQuotaDeptRelService dsmQuotaDeptRelService;

    /**
     * 查询指标与机构列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DsmQuotaDeptRel dsmQuotaDeptRel)
    {
        startPage();
        List<DsmQuotaDeptRel> list = dsmQuotaDeptRelService.selectDsmQuotaDeptRelList(dsmQuotaDeptRel);
        return getDataTable(list);
    }

    /**
     * 导出指标与机构列表
     */
    @Log(title = "指标与机构", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmQuotaDeptRel dsmQuotaDeptRel)
    {
        List<DsmQuotaDeptRel> list = dsmQuotaDeptRelService.selectDsmQuotaDeptRelList(dsmQuotaDeptRel);
        ExcelUtil<DsmQuotaDeptRel> util = new ExcelUtil<DsmQuotaDeptRel>(DsmQuotaDeptRel.class);
        return util.exportExcel(list, "dsmQuotaDeptRel");
    }

    /**
     * 获取指标与机构详细信息
     */
    @GetMapping(value = "/{quotaId}")
    public AjaxResult getInfo(@PathVariable("quotaId") Long quotaId)
    {
        return AjaxResult.success(dsmQuotaDeptRelService.selectDsmQuotaDeptRelById(quotaId));
    }

    /**
     * 新增指标与机构
     */
    @Log(title = "指标与机构", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmQuotaDeptRel dsmQuotaDeptRel)
    {
        return toAjax(dsmQuotaDeptRelService.insertDsmQuotaDeptRel(dsmQuotaDeptRel));
    }

    /**
     * 修改指标与机构
     */
    @Log(title = "指标与机构", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmQuotaDeptRel dsmQuotaDeptRel)
    {
        return toAjax(dsmQuotaDeptRelService.updateDsmQuotaDeptRel(dsmQuotaDeptRel));
    }

    /**
     * 删除指标与机构
     */
    @Log(title = "指标与机构", businessType = BusinessType.DELETE)
	@DeleteMapping("/{quotaIds}")
    public AjaxResult remove(@PathVariable Long[] quotaIds)
    {
        return toAjax(dsmQuotaDeptRelService.deleteDsmQuotaDeptRelByIds(quotaIds));
    }
}
