<template>
  <div class="app-container">
    <el-row v-for="(item, index) in viewList">
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <router-link :to="'/metadata/mdmDataMonitor'"><el-badge :value="item.bg"  v-if="item.bg>0"><span>{{item.name}}</span></el-badge></router-link>
			    <span v-if="item.bg==0">{{item.name}}</span>
		  </div>
	      <div style="width:100%; padding-left:100px;height:200px;text-align:center;display:table-cell;vertical-align:middle;">
		      <dv-decoration-12 style="width:150px;height:150px;">
					表<br/>{{item.zs}}<br/>列<br/>{{item.zhs+item.wzhs}}
			  </dv-decoration-12>
		  </div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>{{item.name}}(分布)</span>
		  </div>
	      <div :id="'fb_'+index" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>

	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>{{item.name}}(落标+引用)</span>
		  </div>
	      <div :id="'lb_'+index" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>{{item.name}}(注释)</span>
		  </div>
	      <div :id="'wd_'+index" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
    </el-row>
  </div>
</template>

<script>
import { getView,getRunFb,getRunLb,getRunYy} from "@/api/mdm/mdmView";
import G6 from '@antv/g6'
import backgroundImage from '@/assets/images/timg.jpg';

export default {
  name: "NeedsView",
  components: {
  },
  data() {
    return {
    	viewList: [],
    	total: 0,
    	loading: true,
        systemIdOptions: [],
        statusOptions: [],
        queryParams: {
          pageNum: 1,
          pageSize: 1000
        }
    };
  },
  mounted() {
	 this.getList(); 
  },
  watch:{
	  viewList: function() {
		  this.$nextTick(function(){
			  console.log(this.viewList);
			  	for(var i=0;i<this.viewList.length;i++){
			  		getRunFb(i,this.viewList[i].bs,this.viewList[i].st,this.viewList[i].jb,this.viewList[i].ccgc);
			  		getRunLb(i,this.viewList[i].lb+this.viewList[i].wd,this.viewList[i].wlb+this.viewList[i].wwd);
			  		getRunYy(i,this.viewList[i].zhs,this.viewList[i].wzhs);
			  	}
		  })
	  	}
  },
  methods: {
	    getList() {
	    	this.loading = true;
	    	getView(this.queryParams).then(response => {
	          this.viewList = response.data;
	          this.loading = false;
	          
	        });
	
	    },
	  showRunFb() {
    	  
      }
  },
  destroyed () {
	  
  }
};
</script>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }
  
  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }
  
  .clearfix:after {
      clear: both
  }
  
  .card{
  	margin:10px;
  }

  </style>