<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.api.mapper.ApiTemplateMappingMapper">

    <resultMap type="ApiTemplateMapping" id="ApiTemplateMappingResult">
        <result property="id"    column="id"    />
        <result property="tName"    column="t_name"    />
        <result property="tTempTable"    column="t_temp_table"    />
        <result property="tTable"    column="t_table"    />
        <result property="tRoute"    column="t_route"    />
        <result property="tFields"    column="t_fields"    />
        <result property="tRoleids"    column="t_roleids"    />
        <result property="procedureStart"    column="procedure_start"    />
        <result property="procedureEnd"    column="procedure_end"    />
        <result property="isExName"    column="is_ex_name"    />
        <result property="excelName"    column="excel_name"    />
        <result property="procedureCheck"    column="procedure_check"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectApiTemplateMappingVo">
        select id, t_name, t_temp_table, t_table, t_route, t_fields, t_roleids, procedure_start, procedure_end, is_ex_name, excel_name, procedure_check,create_id,create_by,create_time,update_id,update_by,update_time from api_template_mapping
    </sql>

    <select id="selectApiTemplateMappingList" parameterType="ApiTemplateMapping" resultMap="ApiTemplateMappingResult">
        <include refid="selectApiTemplateMappingVo"/>
        <where>
            <if test="tName != null  and tName != ''"> and t_name like concat('%', #{tName}, '%')</if>
            <if test="tTable != null  and tTable != ''"> and t_table = #{tTable}</if>
            <if test="tFields != null  and tFields != ''"> and t_fields = #{tFields}</if>
            <if test="tRoleids != null  and tRoleids != ''"> and t_roleids = #{tRoleids}</if>
            <if test="procedureStart != null  and procedureStart != ''"> and procedure_start = #{procedureStart}</if>
            <if test="procedureEnd != null  and procedureEnd != ''"> and procedure_end = #{procedureEnd}</if>
            <if test="isExName != null  and isExName != ''"> and is_ex_name like concat('%', #{isExName}, '%')</if>
            <if test="excelName != null  and excelName != ''"> and excel_name like concat('%', #{excelName}, '%')</if>
            <if test="procedureCheck != null  and procedureCheck != ''"> and procedure_check = #{procedureCheck}</if>
        </where>
    </select>

    <select id="selectApiTemplateMappingById" parameterType="Long" resultMap="ApiTemplateMappingResult">
        <include refid="selectApiTemplateMappingVo"/>
        where id = #{id}
    </select>

    <insert id="insertApiTemplateMapping" parameterType="ApiTemplateMapping" useGeneratedKeys="true" keyProperty="id">
        insert into api_template_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tName != null and tName != ''">t_name,</if>
            <if test="tTempTable != null and tTempTable != ''">t_temp_table,</if>
            <if test="tTable != null and tTable != ''">t_table,</if>
            <if test="tRoute != null and tRoute != ''">t_route,</if>
            <if test="tFields != null and tFields != ''">t_fields,</if>
            <if test="tRoleids != null">t_roleids,</if>
            <if test="procedureStart != null">procedure_start,</if>
            <if test="procedureEnd != null">procedure_end,</if>
            <if test="isExName != null">is_ex_name,</if>
            <if test="excelName != null">excel_name,</if>
            <if test="procedureCheck != null">procedure_check,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tName != null and tName != ''">#{tName},</if>
            <if test="tTempTable != null and tTempTable != ''">#{tTempTable},</if>
            <if test="tTable != null and tTable != ''">#{tTable},</if>
            <if test="tRoute != null and tRoute != ''">#{tRoute},</if>
            <if test="tFields != null and tFields != ''">#{tFields},</if>
            <if test="tRoleids != null">#{tRoleids},</if>
            <if test="procedureStart != null">#{procedureStart},</if>
            <if test="procedureEnd != null">#{procedureEnd},</if>
            <if test="isExName != null">#{isExName},</if>
            <if test="excelName != null">#{excelName},</if>
            <if test="procedureCheck != null">#{procedureCheck},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateApiTemplateMapping" parameterType="ApiTemplateMapping">
        update api_template_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="tName != null and tName != ''">t_name = #{tName},</if>
            <if test="tTempTable != null and tTempTable != ''">t_temp_table = #{tTempTable},</if>
            <if test="tTable != null and tTable != ''">t_table = #{tTable},</if>
            <if test="tRoute != null and tRoute != ''">t_route = #{tRoute},</if>
            <if test="tFields != null and tFields != ''">t_fields = #{tFields},</if>
            <if test="tRoleids != null">t_roleids = #{tRoleids},</if>
            <if test="procedureStart != null">procedure_start = #{procedureStart},</if>
            <if test="procedureEnd != null">procedure_end = #{procedureEnd},</if>
            <if test="isExName != null">is_ex_name = #{isExName},</if>
            <if test="excelName != null">excel_name = #{excelName},</if>
            <if test="procedureCheck != null">procedure_check = #{procedureCheck},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApiTemplateMappingById" parameterType="Long">
        delete from api_template_mapping where id = #{id}
    </delete>

    <delete id="deleteApiTemplateMappingByIds" parameterType="String">
        delete from api_template_mapping where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 添加模板映射查询表名 -->
    <select id="getTableNames" resultType="java.util.Map">
        select table_name val,table_comment text
        from information_schema.tables
        where table_schema='dqms' and table_type='base table'
        order by table_name
    </select>

    <!-- 1、	模板映射管理通过表名实现自动映射关系 -->
    <select id="getTableMapping" resultType="java.util.Map">
        SELECT COLUMN_NAME column_name,COLUMN_COMMENT commentstring FROM information_schema.COLUMNS WHERE TABLE_NAME= #{table_name} AND TABLE_SCHEMA='dqms'
    </select>
    <select id="getTempTable" resultType="java.util.Map">
        ${_parameter}
    </select>
    <select id="getTempForTable" resultType="java.util.Map">
        select * from  ${table_name}
    </select>
    <update id="updateSql">
        ${_parameter}
    </update>

    <delete id="cleanTempTable">
        truncate table ${tableName}
    </delete>

    <insert id="insertSqlWocAll">
        insert into ${table_name}
            (${cols}) values ${valAll}
    </insert>
</mapper>
