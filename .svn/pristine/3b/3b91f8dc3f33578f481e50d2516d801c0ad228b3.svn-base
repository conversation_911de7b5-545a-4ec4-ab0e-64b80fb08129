package com.dqms.api.mapper;

import java.util.List;
import com.dqms.api.domain.ApiTableManager;

/**
 * 数据管控Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-15
 */
public interface ApiTableManagerMapper 
{
    /**
     * 查询数据管控
     * 
     * @param entityId 数据管控ID
     * @return 数据管控
     */
    public ApiTableManager selectApiTableManagerById(Long entityId);

    /**
     * 查询数据管控列表
     * 
     * @param apiTableManager 数据管控
     * @return 数据管控集合
     */
    public List<ApiTableManager> selectApiTableManagerList(ApiTableManager apiTableManager);
    public List<ApiTableManager> selectApiTableManagerBySystemList(ApiTableManager apiTableManager);
    public List<ApiTableManager> listSystem(Long entityId);
    public List<ApiTableManager> selectApiTableManagerListByEntityId(Long entityId);

    /**
     * 新增数据管控
     * 
     * @param apiTableManager 数据管控
     * @return 结果
     */
    public int insertApiTableManager(ApiTableManager apiTableManager);
    public int deleteApiTableManager(ApiTableManager apiTableManager);

    /**
     * 修改数据管控
     * 
     * @param apiTableManager 数据管控
     * @return 结果
     */
    public int updateApiTableManager(ApiTableManager apiTableManager);

    /**
     * 删除数据管控
     * 
     * @param entityId 数据管控ID
     * @return 结果
     */
    public int deleteApiTableManagerById(Long entityId);

    /**
     * 批量删除数据管控
     * 
     * @param entityIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteApiTableManagerByIds(Long[] entityIds);
}
