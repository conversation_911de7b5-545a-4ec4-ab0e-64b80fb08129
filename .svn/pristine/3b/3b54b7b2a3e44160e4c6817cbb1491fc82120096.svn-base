package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmQuotaClass;
import com.dqms.dsm.domain.DsmQuotaTag;
import com.dqms.dsm.service.IDsmQuotaTagService;
import com.dqms.common.utils.poi.ExcelUtil;

/**
 * 指标标签Controller
 *
 * <AUTHOR>
 * @date 2022-08-01
 */
@RestController
@RequestMapping("/dsm/dsmQuotaTag")
public class DsmQuotaTagController extends BaseController
{
    @Autowired
    private IDsmQuotaTagService dsmQuotaTagService;

    /**
     * 查询指标标签列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmQuotaTag:list')")
    @GetMapping("/list")
    public AjaxResult list(DsmQuotaTag dsmQuotaTag)
    {
        List<DsmQuotaTag> list = dsmQuotaTagService.selectDsmQuotaTagList(dsmQuotaTag);
        return AjaxResult.success(list);
    }

    /**
     * 导出指标标签列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmQuotaTag:export')")
    @Log(title = "指标标签", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmQuotaTag dsmQuotaTag)
    {
        List<DsmQuotaTag> list = dsmQuotaTagService.selectDsmQuotaTagList(dsmQuotaTag);
        ExcelUtil<DsmQuotaTag> util = new ExcelUtil<DsmQuotaTag>(DsmQuotaTag.class);
        return util.exportExcel(list, "dsmQuotaTag");
    }

    /**
     * 获取指标标签详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmQuotaTag:query')")
    @GetMapping(value = "/{quotaTagId}")
    public AjaxResult getInfo(@PathVariable("quotaTagId") Long quotaTagId)
    {
        return AjaxResult.success(dsmQuotaTagService.selectDsmQuotaTagById(quotaTagId));
    }

    /**
     * 新增指标标签
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmQuotaTag:add')")
    @Log(title = "指标标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmQuotaTag dsmQuotaTag)
    {
        return toAjax(dsmQuotaTagService.insertDsmQuotaTag(dsmQuotaTag));
    }

    /**
     * 修改指标标签
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmQuotaTag:edit')")
    @Log(title = "指标标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmQuotaTag dsmQuotaTag)
    {
        return toAjax(dsmQuotaTagService.updateDsmQuotaTag(dsmQuotaTag));
    }

    /**
     * 删除指标标签
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmQuotaTag:remove')")
    @Log(title = "指标标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{quotaTagIds}")
    public AjaxResult remove(@PathVariable Long[] quotaTagIds)
    {
        return toAjax(dsmQuotaTagService.deleteDsmQuotaTagByIds(quotaTagIds));
    }
    
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DsmQuotaTag dsmQuotaTag)
    {
        List<DsmQuotaTag> dsmQuotaTags = dsmQuotaTagService.selectDsmQuotaTagList(dsmQuotaTag);
        return AjaxResult.success(dsmQuotaTagService.buildDsmQuotaTagTreeSelect(dsmQuotaTags));
    }
}
