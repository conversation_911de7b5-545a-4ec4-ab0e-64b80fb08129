<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmQuotaTagRelMapper">
    
    <resultMap type="DsmQuotaTagRel" id="DsmQuotaTagRelResult">
        <result property="quotaId"    column="quota_id"    />
        <result property="tagId"    column="tag_id"    />
    </resultMap>

    <sql id="selectDsmQuotaTagRelVo">
        select quota_id, tag_id from dsm_quota_tag_rel
    </sql>

    <select id="selectDsmQuotaTagRelList" parameterType="DsmQuotaTagRel" resultMap="DsmQuotaTagRelResult">
        <include refid="selectDsmQuotaTagRelVo"/>
        <where>  
            <if test="quotaId != null "> and quota_id = #{quotaId}</if>
            <if test="tagId != null "> and tag_id = #{tagId}</if>
        </where>
    </select>
    
    <select id="selectDsmQuotaTagRelById" parameterType="Long" resultMap="DsmQuotaTagRelResult">
        <include refid="selectDsmQuotaTagRelVo"/>
        where quota_id = #{quotaId}
    </select>
        
    <insert id="insertDsmQuotaTagRel" parameterType="DsmQuotaTagRel">
        insert into dsm_quota_tag_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quotaId != null">quota_id,</if>
            <if test="tagId != null">tag_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quotaId != null">#{quotaId},</if>
            <if test="tagId != null">#{tagId},</if>
         </trim>
    </insert>

    <update id="updateDsmQuotaTagRel" parameterType="DsmQuotaTagRel">
        update dsm_quota_tag_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagId != null">tag_id = #{tagId},</if>
        </trim>
        where quota_id = #{quotaId}
    </update>

    <delete id="deleteDsmQuotaTagRelById" parameterType="Long">
        delete from dsm_quota_tag_rel where quota_id = #{quotaId}
    </delete>

    <delete id="deleteDsmQuotaTagRelByIds" parameterType="String">
        delete from dsm_quota_tag_rel where quota_id in 
        <foreach item="quotaId" collection="array" open="(" separator="," close=")">
            #{quotaId}
        </foreach>
    </delete>
</mapper>