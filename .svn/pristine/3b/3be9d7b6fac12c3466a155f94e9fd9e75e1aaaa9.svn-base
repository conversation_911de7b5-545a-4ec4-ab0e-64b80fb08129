<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dic.mapper.DicFileDefineMapper">
    
    <resultMap type="DicFileDefine" id="DicFileDefineResult">
        <result property="fileDefineId"    column="file_define_id"    />
        <result property="name"    column="name"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"   />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
    </resultMap>

    <sql id="selectDicFileDefineVo">
        select t.file_define_id, t.name, t.task_id, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time ,ta.task_name ,t.table_name ,t.datasource_id,td.name as datasource_name 
        from dic_file_define t left join etl_task ta ON t.task_id=ta.task_id
		LEFT JOIN sys_datasource td ON t.datasource_id=td.datasource_id
    </sql>

    <select id="selectDicFileDefineList" parameterType="DicFileDefine" resultMap="DicFileDefineResult">
        <include refid="selectDicFileDefineVo"/>
        <where>  
            <if test="name != null "> and t.name like concat('%', #{name}, '%')</if>
            <if test="tableName != null "> and t.table_name like concat('%', #{tableName}, '%')</if>
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
        </where>
    </select>
    
    <select id="selectDicFileDefineById" parameterType="Long" resultMap="DicFileDefineResult">
        <include refid="selectDicFileDefineVo"/>
        where t.file_define_id = #{fileDefineId}
    </select>
    
    <insert id="insertDicFileDefine" parameterType="DicFileDefine" useGeneratedKeys="true" keyProperty="fileDefineId">
        insert into dic_file_define
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="taskId != null">task_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tableName != null">table_name,</if>
            <if test="datasourceId != null">datasource_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
         </trim>
    </insert>

    <update id="updateDicFileDefine" parameterType="DicFileDefine">
        update dic_file_define
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
        </trim>
        where file_define_id = #{fileDefineId}
    </update>

    <delete id="deleteDicFileDefineById" parameterType="Long">
        delete from dic_file_define where file_define_id = #{fileDefineId}
    </delete>

    <delete id="deleteDicFileDefineByIds" parameterType="String">
        delete from dic_file_define where file_define_id in 
        <foreach item="fileDefineId" collection="array" open="(" separator="," close=")">
            #{fileDefineId}
        </foreach>
    </delete>
</mapper>