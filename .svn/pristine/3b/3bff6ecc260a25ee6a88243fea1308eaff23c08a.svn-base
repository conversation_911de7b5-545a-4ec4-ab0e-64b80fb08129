<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.task.mapper.EtlTaskScheduleMapper">
    
    <resultMap type="EtlTaskSchedule" id="EtlTaskScheduleResult">
        <result property="taskScheduleId"    column="task_schedule_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="scheduleName"    column="schedule_name"    />
        <result property="expression"    column="expression"    />
        <result property="status"    column="status"    />
        <result property="runStatus"    column="run_status"    />
        <result property="isLine"    column="is_line"    />
        <result property="isGroup"    column="is_group"    />
        <result property="isConcurrent"    column="is_concurrent"    />
        <result property="isTemp"    column="is_temp"    />
        <result property="execType"    column="exec_type"    />
        <result property="preScheduleId"    column="pre_schedule_id"    />
        <result property="taskCalendarClassId"    column="task_calendar_class_id"    />
        <result property="year"    column="year_"    />
        <result property="mon"    column="mon_"    />
        <result property="remark"    column="remark"    />
        <result property="day"    column="day_"    />
        <result property="min"    column="min_"    />
        <result property="sec"    column="sec_"    />
        <result property="week"    column="week_"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="jobId"    column="job_id"    />
        <result property="taskCalendarClassName"    column="task_calendar_class_name_full"    />
        <result property="dateCheckType"    column="date_check_type"    />
        <result property="dateCheckNum"    column="date_check_num"    />
        <result property="only"    column="only"    />
        <association property="etlTask"    column="task_id" javaType="EtlTask" resultMap="EtlTaskResult" />
    </resultMap>
    <resultMap type="EtlTaskScheduleVo" id="EtlTaskScheduleVoResult">
        <result property="taskScheduleId"    column="task_schedule_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="scheduleName"    column="schedule_name"    />
        <result property="expression"    column="expression"    />
        <result property="status"    column="status"    />
        <result property="runStatus"    column="run_status"    />
        <result property="isLine"    column="is_line"    />
        <result property="isGroup"    column="is_group"    />
        <result property="isConcurrent"    column="is_concurrent"    />
        <result property="isTemp"    column="is_temp"    />
        <result property="execType"    column="exec_type"    />
        <result property="preScheduleId"    column="pre_schedule_id"    />
        <result property="taskCalendarClassId"    column="task_calendar_class_id"    />
        <result property="taskCalendarClassName"    column="task_calendar_class_Name"    />
        <result property="year"    column="year_"    />
        <result property="mon"    column="mon_"    />
        <result property="remark"    column="remark"    />
        <result property="day"    column="day_"    />
        <result property="min"    column="min_"    />
        <result property="sec"    column="sec_"    />
        <result property="week"    column="week_"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="jobId"    column="job_id"    />
        <result property="taskCalendarClassName"    column="task_calendar_class_name_full"    />
        <result property="dateCheckType"    column="date_check_type"    />
        <result property="dateCheckNum"    column="date_check_num"    />
        <association property="etlTask"    column="task_id" javaType="EtlTask" resultMap="EtlTaskResult" />
    </resultMap>

    <resultMap type="EtlTask" id="EtlTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
    </resultMap>
    
    <sql id="selectEtlTaskScheduleVo">
        select s.task_schedule_id, s.task_id, s.schedule_name, s.expression, s.status, s.run_status, s.is_line, s.is_group, s.is_concurrent, s.is_temp, s.exec_type, s.pre_schedule_id, s.task_calendar_class_id, s.year_, s.mon_, s.remark, s.day_, s.min_, s.sec_, s.week_,s.job_id,
         s.create_by, s.update_by, s.create_id, s.update_id, s.create_time, s.update_time ,t.task_code, t.task_name ,c.task_calendar_class_name_full,s.date_check_type,s.date_check_num
         from etl_task_schedule s
         left join etl_task t on s.task_id=t.task_id
         left join etl_task_calendar_class c on s.task_calendar_class_id=c.task_calendar_class_id
    </sql>

    <select id="selectEtlTaskScheduleList" parameterType="EtlTaskSchedule" resultMap="EtlTaskScheduleResult">
         <include refid="selectEtlTaskScheduleVo"/>
        <where>  
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
            <if test="taskName != null and taskName != ''"> and t.task_name = #{taskName}</if>
            <if test="scheduleName != null  and scheduleName != ''"> and s.schedule_name like concat('%', #{scheduleName}, '%')</if>
            <if test="expression != null  and expression != ''"> and s.expression = #{expression}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
            <if test="runStatus != null  and runStatus != ''"> and s.run_status = #{runStatus}</if>
            <if test="isLine != null  and isLine != ''"> and s.is_line = #{isLine}</if>
            <if test="isGroup != null  and isGroup != ''"> and s.is_group = #{isGroup}</if>
            <if test="isConcurrent != null  and isConcurrent != ''"> and s.is_concurrent = #{isConcurrent}</if>
            <if test="isTemp != null  and isTemp != ''"> and s.is_temp = #{isTemp}</if>
            <if test="execType != null  and execType != ''"> and s.exec_type = #{execType}</if>
            <if test="preScheduleId != null "> and s.pre_schedule_id = #{preScheduleId}</if>
            <if test="taskCalendarClassId != null "> and s.task_calendar_class_id = #{taskCalendarClassId}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by s.create_time desc
    </select>
    
     <select id="selectEtlTaskScheduleListByPage" parameterType="EtlTaskSchedule" resultMap="EtlTaskScheduleResult">
         select s.task_schedule_id, s.task_id, s.schedule_name, s.expression, s.status, s.run_status, s.is_line, s.is_group, s.is_concurrent, s.is_temp, s.exec_type, s.pre_schedule_id, s.task_calendar_class_id, s.year_, s.mon_, s.remark, s.day_, s.min_, s.sec_, s.week_,s.job_id,
         s.create_by, s.update_by, s.create_id, s.update_id, s.create_time, s.update_time ,t.task_code, t.task_name ,c.task_calendar_class_name_full,s.date_check_type,s.date_check_num,
        CASE (SELECT COUNT(1) FROM sys_role r LEFT JOIN sys_user_role ur ON r.role_id=ur.role_id
		WHERE ur.user_id=#{createId} AND r.data_scope=6) WHEN 0 THEN 'Y' ELSE 
		(CASE (SELECT COUNT(1) FROM sys_user_task_group g WHERE g.task_group_id=t.task_group_id AND g.user_id=#{createId}) WHEN 0 THEN 'N' ELSE 'Y' END)
		 END only
         from etl_task_schedule s
         left join etl_task t on s.task_id=t.task_id
         left join etl_task_calendar_class c on s.task_calendar_class_id=c.task_calendar_class_id
        <where>  
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
            <if test="taskName != null and taskName != ''"> and t.task_name = #{taskName}</if>
            <if test="scheduleName != null  and scheduleName != ''"> and s.schedule_name like concat('%', #{scheduleName}, '%')</if>
            <if test="expression != null  and expression != ''"> and s.expression = #{expression}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
            <if test="runStatus != null  and runStatus != ''"> and s.run_status = #{runStatus}</if>
            <if test="isLine != null  and isLine != ''"> and s.is_line = #{isLine}</if>
            <if test="isGroup != null  and isGroup != ''"> and s.is_group = #{isGroup}</if>
            <if test="isConcurrent != null  and isConcurrent != ''"> and s.is_concurrent = #{isConcurrent}</if>
            <if test="isTemp != null  and isTemp != ''"> and s.is_temp = #{isTemp}</if>
            <if test="execType != null  and execType != ''"> and s.exec_type = #{execType}</if>
            <if test="preScheduleId != null "> and s.pre_schedule_id = #{preScheduleId}</if>
            <if test="taskCalendarClassId != null "> and s.task_calendar_class_id = #{taskCalendarClassId}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by s.create_time desc
    </select>   
    <select id="selectEtlTaskScheduleById" parameterType="Long" resultMap="EtlTaskScheduleResult">
        <include refid="selectEtlTaskScheduleVo"/>
        where s.task_schedule_id = #{taskScheduleId}
    </select>
        
    <insert id="insertEtlTaskSchedule" parameterType="EtlTaskSchedule" useGeneratedKeys="true" keyProperty="taskScheduleId">
        insert into etl_task_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskScheduleId != null">task_schedule_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="scheduleName != null and scheduleName != ''">schedule_name,</if>
            <if test="expression != null and expression != ''">expression,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="runStatus != null and runStatus != ''">run_status,</if>
            <if test="isLine != null and isLine != ''">is_line,</if>
            <if test="isGroup != null and isGroup != ''">is_group,</if>
            <if test="isConcurrent != null and isConcurrent != ''">is_concurrent,</if>
            <if test="isTemp != null and isTemp != ''">is_temp,</if>
            <if test="execType != null and execType != ''">exec_type,</if>
            <if test="preScheduleId != null">pre_schedule_id,</if>
            <if test="taskCalendarClassId != null">task_calendar_class_id,</if>
            <if test="year != null">year_,</if>
            <if test="mon != null">mon_,</if>
            <if test="remark != null">remark,</if>
            <if test="day != null">day_,</if>
            <if test="min != null">min_,</if>
            <if test="sec != null">sec_,</if>
            <if test="week != null">week_,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="jobId != null">job_id,</if>
            <if test="dateCheckType != null">date_check_type,</if>
            <if test="dateCheckNum != null">date_check_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskScheduleId != null">#{taskScheduleId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="scheduleName != null and scheduleName != ''">#{scheduleName},</if>
            <if test="expression != null and expression != ''">#{expression},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="runStatus != null and runStatus != ''">#{runStatus},</if>
            <if test="isLine != null and isLine != ''">#{isLine},</if>
            <if test="isGroup != null and isGroup != ''">#{isGroup},</if>
            <if test="isConcurrent != null and isConcurrent != ''">#{isConcurrent},</if>
            <if test="isTemp != null and isTemp != ''">#{isTemp},</if>
            <if test="execType != null and execType != ''">#{execType},</if>
            <if test="preScheduleId != null">#{preScheduleId},</if>
            <if test="taskCalendarClassId != null">#{taskCalendarClassId},</if>
            <if test="year != null">#{year},</if>
            <if test="mon != null">#{mon},</if>
            <if test="remark != null">#{remark},</if>
            <if test="day != null">#{day},</if>
            <if test="min != null">#{min},</if>
            <if test="sec != null">#{sec},</if>
            <if test="week != null">#{week},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="jobId != null">#{jobId},</if>
            <if test="dateCheckType != null">#{dateCheckType},</if>
            <if test="dateCheckNum != null">#{dateCheckNum},</if>
         </trim>
    </insert>

    <update id="updateEtlTaskSchedule" parameterType="EtlTaskSchedule">
        update etl_task_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="scheduleName != null and scheduleName != ''">schedule_name = #{scheduleName},</if>
            <if test="expression != null and expression != ''">expression = #{expression},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="runStatus != null and runStatus != ''">run_status = #{runStatus},</if>
            <if test="isLine != null and isLine != ''">is_line = #{isLine},</if>
            <if test="isGroup != null and isGroup != ''">is_group = #{isGroup},</if>
            <if test="isConcurrent != null and isConcurrent != ''">is_concurrent = #{isConcurrent},</if>
            <if test="isTemp != null and isTemp != ''">is_temp = #{isTemp},</if>
            <if test="execType != null and execType != ''">exec_type = #{execType},</if>
            <if test="preScheduleId != null">pre_schedule_id = #{preScheduleId},</if>
            <if test="taskCalendarClassId != null">task_calendar_class_id = #{taskCalendarClassId},</if>
            <if test="taskCalendarClassId == null">task_calendar_class_id = null,</if>
            <if test="year != null">year_ = #{year},</if>
            <if test="mon != null">mon_ = #{mon},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="day != null">day_ = #{day},</if>
            <if test="min != null">min_ = #{min},</if>
            <if test="sec != null">sec_ = #{sec},</if>
            <if test="week != null">week_ = #{week},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="dateCheckType != null">date_check_type = #{dateCheckType},</if>
            <if test="dateCheckNum != null">date_check_num = #{dateCheckNum},</if>
        </trim>
        where task_schedule_id = #{taskScheduleId}
    </update>

    <delete id="deleteEtlTaskScheduleById" parameterType="Long">
        delete from etl_task_schedule where task_schedule_id = #{taskScheduleId}
    </delete>

    <delete id="deleteEtlTaskScheduleByIds" parameterType="String">
        delete from etl_task_schedule where task_schedule_id in 
        <foreach item="taskScheduleId" collection="array" open="(" separator="," close=")">
            #{taskScheduleId}
        </foreach>
    </delete>

    <select id="selectEtlTaskScheduleVoList" parameterType="EtlTaskSchedule" resultMap="EtlTaskScheduleVoResult">
        select s.task_schedule_id, s.task_id,t.task_name, s.schedule_name, s.expression, s.status, s.run_status, s.is_line, s.is_group, s.is_concurrent, s.is_temp, s.exec_type, s.pre_schedule_id, s.task_calendar_class_id,c.task_calendar_class_Name, s.year_, s.mon_, s.remark, s.day_, s.min_, s.sec_, s.week_,s.job_id,
        s.create_by, s.update_by, s.create_id, s.update_id, s.create_time, s.update_time ,t.task_code, t.task_name ,c.task_calendar_class_name_full,s.date_check_type,s.date_check_num
        from etl_task_schedule s
        left join etl_task t on s.task_id=t.task_id
        left join etl_task_calendar_class c on s.task_calendar_class_id=c.task_calendar_class_id
        <where>
            <if test="taskId != null "> and s.task_id = #{taskId}</if>
            <if test="scheduleName != null  and scheduleName != ''"> and s.schedule_name like concat('%', #{scheduleName}, '%')</if>
            <if test="expression != null  and expression != ''"> and s.expression = #{expression}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
            <if test="runStatus != null  and runStatus != ''"> and s.run_status = #{runStatus}</if>
            <if test="isLine != null  and isLine != ''"> and s.is_line = #{isLine}</if>
            <if test="isGroup != null  and isGroup != ''"> and s.is_group = #{isGroup}</if>
            <if test="isConcurrent != null  and isConcurrent != ''"> and s.is_concurrent = #{isConcurrent}</if>
            <if test="isTemp != null  and isTemp != ''"> and s.is_temp = #{isTemp}</if>
            <if test="execType != null  and execType != ''"> and s.exec_type = #{execType}</if>
            <if test="preScheduleId != null "> and s.pre_schedule_id = #{preScheduleId}</if>
            <if test="taskCalendarClassId != null "> and s.task_calendar_class_id = #{taskCalendarClassId}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by s.create_time desc
    </select>

</mapper>