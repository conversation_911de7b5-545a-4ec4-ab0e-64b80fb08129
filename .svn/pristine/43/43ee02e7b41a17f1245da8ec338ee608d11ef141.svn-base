package com.dqms.task.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.task.domain.EtlTaskCalendarClass;
import com.dqms.task.domain.EtlTaskClass;

/**
 * 调度日历管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
public interface EtlTaskCalendarClassMapper 
{
    /**
     * 查询调度日历管理
     * 
     * @param taskCalendarClassId 调度日历管理ID
     * @return 调度日历管理
     */
    public EtlTaskCalendarClass selectEtlTaskCalendarClassById(Long taskCalendarClassId);

    /**
     * 查询调度日历管理列表
     * 
     * @param etlTaskCalendarClass 调度日历管理
     * @return 调度日历管理集合
     */
    public List<EtlTaskCalendarClass> selectEtlTaskCalendarClassList(EtlTaskCalendarClass etlTaskCalendarClass);

    /**
     * 新增调度日历管理
     * 
     * @param etlTaskCalendarClass 调度日历管理
     * @return 结果
     */
    public int insertEtlTaskCalendarClass(EtlTaskCalendarClass etlTaskCalendarClass);

    /**
     * 修改调度日历管理
     * 
     * @param etlTaskCalendarClass 调度日历管理
     * @return 结果
     */
    public int updateEtlTaskCalendarClass(EtlTaskCalendarClass etlTaskCalendarClass);

    /**
     * 删除调度日历管理
     * 
     * @param taskCalendarClassId 调度日历管理ID
     * @return 结果
     */
    public int deleteEtlTaskCalendarClassById(Long taskCalendarClassId);

    /**
     * 批量删除调度日历管理
     * 
     * @param taskCalendarClassIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskCalendarClassByIds(Long[] taskCalendarClassIds);
    
    /**
     * 查询任务分类
     * 
     * @param taskClassName 任务分类Name
     * @return 任务分类
     */
    public EtlTaskCalendarClass selectEtlTaskCalendarClassByName(@Param("taskCalendarClassName") String taskCalendarClassName);
    
    /**
     * 根据ID查询所有子任务分类
     *
     * @param deptId 任务分类ID
     * @return 任务分类列表
     */
    public List<EtlTaskCalendarClass> selectChildrenEtlTaskCalendarClassById(Long taskCalendarClassId);
    
    /**
     * 修改子元素关系
     *
     * @param taskClasss 子元素
     * @return 结果
     */
    public int updateEtlTaskCalendarClassChildren(@Param("taskCalendarClasss") List<EtlTaskCalendarClass> taskCalendarClasss);
    public int updateEtlTaskCalendarClassNameFullChildren(@Param("taskCalendarClasss") List<EtlTaskCalendarClass> taskCalendarClasss);
}
