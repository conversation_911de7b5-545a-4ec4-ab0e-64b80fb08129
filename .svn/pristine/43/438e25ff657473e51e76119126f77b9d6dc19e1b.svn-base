package com.dqms.system.service;

import java.util.List;

import com.dqms.system.domain.SysNotice;

/**
 * 公告 服务层
 * 
 * <AUTHOR>
 */
public interface ISysNoticeService
{
    /**
     * 查询公告信息
     * 
     * @param noticeId 公告ID
     * @return 公告信息
     */
    SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    List<SysNotice> selectNoticeList(SysNotice notice);
    /**
     * 查询我的公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    List<SysNotice> selectMyNoticeList(SysNotice notice);
    /**
     * 查询我的公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    List<SysNotice> selectMyNoticeGgList(SysNotice notice);

    SysNotice getMultiIndexDescription();

    SysNotice getSingleIndexDescription();
    /**
     * 新增公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    int insertNotice(SysNotice notice);


    /**
     * 修改公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    int updateNotice(SysNotice notice);

    /**
     * 修改全部的阅读状态
     * @return 结果
     */
    int updateNoticesByIds(String receiverId);

    /**
     *根据id修改公告的阅读状态
     * @param noticeId 公告id
     * @param status 阅读状态
     * @return 结果
     */
    int updateNoticeStatusById(Long noticeId, String status);
    /**
     *根据id修改公告的阅读状态-非消息类
     * @param noticeId 公告id
     * @param status 阅读状态
     * @return 结果
     */
    int updateNoticeStatusByFxx(Long noticeId,String status, String UserNo);
    /**
     * 删除公告信息
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    int deleteNoticeById(Long noticeId);
    
    /**
     * 批量删除公告信息
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    int deleteNoticeByIds(Long[] noticeIds);

    /**
     * 查看未读的消息
     * @param receiver_id
     * @return
     */
    int selectNoticeByReceiverId(String receiver_id);
}
