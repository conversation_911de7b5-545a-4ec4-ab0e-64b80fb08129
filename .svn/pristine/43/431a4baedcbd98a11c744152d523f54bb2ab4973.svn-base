package com.dqms.mdm.service;

import java.util.List;

import com.dqms.common.core.domain.entity.SysUser;
import com.dqms.mdm.domain.MdmTheme;

/**
 * 主题管理Service接口
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
public interface IMdmThemeService
{
    /**
     * 查询主题管理
     *
     * @param themeId 主题管理ID
     * @return 主题管理
     */
    public MdmTheme selectMdmThemeById(Long themeId);

    /**
     * 查询主题管理列表
     *
     * @param mdmTheme 主题管理
     * @return 主题管理集合
     */
    public List<MdmTheme> selectMdmThemeList(MdmTheme mdmTheme);

    /**
     * 新增主题管理
     *
     * @param mdmTheme 主题管理
     * @return 结果
     */
    public int insertMdmTheme(MdmTheme mdmTheme);

    /**
     * 修改主题管理
     *
     * @param mdmTheme 主题管理
     * @return 结果
     */
    public int updateMdmTheme(MdmTheme mdmTheme);

    /**
     * 批量删除主题管理
     *
     * @param themeIds 需要删除的主题管理ID
     * @return 结果
     */
    public int deleteMdmThemeByIds(Long[] themeIds);

    /**
     * 删除主题管理信息
     *
     * @param themeId 主题管理ID
     * @return 结果
     */
    public int deleteMdmThemeById(Long themeId);

    List<Integer> selectuserIdsByThemeId(Long themeid);

    public String importTheme(List<MdmTheme> themeList, Boolean isUpdateSupport);

    public String checkThemeNameUnique(MdmTheme mdmTheme);

    public String checkThemeCodeUnique(MdmTheme mdmTheme);

    public String checkThemeCodeUniqueByName(MdmTheme mdmTheme);

    public List<MdmTheme> selectMdmThemeAll();
}
