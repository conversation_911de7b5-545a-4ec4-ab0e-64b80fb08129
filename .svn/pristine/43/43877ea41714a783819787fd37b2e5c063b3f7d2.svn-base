package com.dqms.dic.service;

import java.util.List;
import com.dqms.dic.domain.DicManualDataInstallRule;

/**
 * 字段规则Service接口
 * 
 * <AUTHOR>
 * @date 2022-06-28
 */
public interface IDicManualDataInstallRuleService 
{
    /**
     * 查询字段规则
     * 
     * @param manualDataInstallId 字段规则ID
     * @return 字段规则
     */
    public DicManualDataInstallRule selectDicManualDataInstallRuleById(Long manualDataInstallId);

    /**
     * 查询字段规则列表
     * 
     * @param dicManualDataInstallRule 字段规则
     * @return 字段规则集合
     */
    public List<DicManualDataInstallRule> selectDicManualDataInstallRuleList(DicManualDataInstallRule dicManualDataInstallRule);

    /**
     * 新增字段规则
     * 
     * @param dicManualDataInstallRule 字段规则
     * @return 结果
     */
    public int insertDicManualDataInstallRule(DicManualDataInstallRule dicManualDataInstallRule);

    /**
     * 修改字段规则
     * 
     * @param dicManualDataInstallRule 字段规则
     * @return 结果
     */
    public int updateDicManualDataInstallRule(DicManualDataInstallRule dicManualDataInstallRule);

    /**
     * 批量删除字段规则
     * 
     * @param manualDataInstallIds 需要删除的字段规则ID
     * @return 结果
     */
    public int deleteDicManualDataInstallRuleByIds(Long[] manualDataInstallIds);

    /**
     * 删除字段规则信息
     * 
     * @param manualDataInstallId 字段规则ID
     * @return 结果
     */
    public int deleteDicManualDataInstallRuleById(Long manualDataInstallId);
}
