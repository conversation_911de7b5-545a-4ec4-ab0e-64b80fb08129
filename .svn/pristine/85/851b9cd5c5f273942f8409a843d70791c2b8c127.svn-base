package com.dqms.dsc.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsc.domain.DscEntityPropDept;
import com.dqms.dsc.service.IDscEntityPropDeptService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 字段应用部门Controller
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@RestController
@RequestMapping("/dsc/dscEntityPropDept")
public class DscEntityPropDeptController extends BaseController
{
    @Autowired
    private IDscEntityPropDeptService dscEntityPropDeptService;

    /**
     * 查询字段应用部门列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropDept:list')")
    @GetMapping("/list")
    public TableDataInfo list(DscEntityPropDept dscEntityPropDept)
    {
        startPage();
        List<DscEntityPropDept> list = dscEntityPropDeptService.selectDscEntityPropDeptList(dscEntityPropDept);
        return getDataTable(list);
    }

    /**
     * 导出字段应用部门列表
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropDept:export')")
    @Log(title = "字段应用部门", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DscEntityPropDept dscEntityPropDept)
    {
        List<DscEntityPropDept> list = dscEntityPropDeptService.selectDscEntityPropDeptList(dscEntityPropDept);
        ExcelUtil<DscEntityPropDept> util = new ExcelUtil<DscEntityPropDept>(DscEntityPropDept.class);
        return util.exportExcel(list, "dscEntityPropDept");
    }

    /**
     * 获取字段应用部门详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropDept:query')")
    @GetMapping(value = "/{entityPropId}")
    public AjaxResult getInfo(@PathVariable("entityPropId") Long entityPropId)
    {
        return AjaxResult.success(dscEntityPropDeptService.selectDscEntityPropDeptById(entityPropId));
    }

    /**
     * 新增字段应用部门
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropDept:add')")
    @Log(title = "字段应用部门", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DscEntityPropDept dscEntityPropDept)
    {
        return toAjax(dscEntityPropDeptService.insertDscEntityPropDept(dscEntityPropDept));
    }

    /**
     * 修改字段应用部门
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropDept:edit')")
    @Log(title = "字段应用部门", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DscEntityPropDept dscEntityPropDept)
    {
        return toAjax(dscEntityPropDeptService.updateDscEntityPropDept(dscEntityPropDept));
    }

    /**
     * 删除字段应用部门
     */
    @PreAuthorize("@ss.hasPermi('dsc:dscEntityPropDept:remove')")
    @Log(title = "字段应用部门", businessType = BusinessType.DELETE)
	@DeleteMapping("/{entityPropIds}")
    public AjaxResult remove(@PathVariable Long[] entityPropIds)
    {
        return toAjax(dscEntityPropDeptService.deleteDscEntityPropDeptByIds(entityPropIds));
    }
}
