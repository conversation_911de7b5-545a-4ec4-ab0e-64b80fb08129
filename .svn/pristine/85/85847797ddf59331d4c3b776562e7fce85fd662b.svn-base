<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.api.mapper.ApiTemplateLogMapper">

    <resultMap type="ApiTemplateLog" id="ApiTemplateLogResult">
        <id property="logId" column="log_id"></id>
        <result property="importOperator"    column="import_operator"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateId"    column="template_id"    />
        <result property="exname"    column="exname"    />
        <result property="importResult"    column="import_result"    />
        <result property="importTime"    column="import_time"    />
    </resultMap>

    <sql id="selectApiTemplateLogVo">
        select log_id, import_operator, template_name, template_id, exname, import_result, import_time from api_template_log
    </sql>

    <select id="selectApiTemplateLogList" parameterType="ApiTemplateLog" resultMap="ApiTemplateLogResult">
        <include refid="selectApiTemplateLogVo"/>
        <where>
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="exname != null  and exname != ''"> and exname like concat('%', #{exname}, '%')</if>
            <if test="importTime != null "> and import_time = #{importTime}</if>
        </where>
    </select>

    <select id="selectApiTemplateLogById" parameterType="String" resultMap="ApiTemplateLogResult">
        <include refid="selectApiTemplateLogVo"/>
        where log_id = #{logId}
    </select>

    <insert id="insertApiTemplateLog" parameterType="ApiTemplateLog">
        insert into api_template_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="importOperator != null">import_operator,</if>
            <if test="templateName != null">template_name,</if>
            <if test="templateId != null">template_id,</if>
            <if test="exname != null">exname,</if>
            <if test="importResult != null">import_result,</if>
            <if test="importTime != null">import_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="importOperator != null">#{importOperator},</if>
            <if test="templateName != null">#{templateName},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="exname != null">#{exname},</if>
            <if test="importResult != null">#{importResult},</if>
            <if test="importTime != null">#{importTime},</if>
         </trim>
    </insert>

    <update id="updateApiTemplateLog" parameterType="ApiTemplateLog">
        update api_template_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="exname != null">exname = #{exname},</if>
            <if test="importResult != null">import_result = #{importResult},</if>
            <if test="importTime != null">import_time = #{importTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteApiTemplateLogById" parameterType="String">
        delete from api_template_log where log_id = #{logId}
    </delete>

    <delete id="deleteApiTemplateLogByIds" parameterType="String">
        delete from api_template_log where log_id in
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>
