package com.dqms.dic.domain;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据交换配置对象 dic_data_exchange
 *
 * <AUTHOR>
 * @date 2021-11-25
 */
public class DicDataExchange extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long exchangeId;

    /** 源数据源ID */

    private Long srcDatasourceId;

    @Excel(name = "源数据源")
    private String srcDatasourceName;

    /** 源Schema */
    @Excel(name = "源Schema")
    private String srcSchema;

    /** 源表名 */
    @Excel(name = "源表名")
    private String srcTableName;

    /** 目标数据源ID */

    private Long tarDatasourceId;

    @Excel(name = "目标数据源")
    private String tarDatasourceName;

    /** 目标表名 */
    @Excel(name = "目标表名")
    private String tarTableName;

    /** 目标Schema */
    @Excel(name = "目标表Schema")
    private String tarSchema;

    /** 执行代理机ID */
    private Long agentId;

    @Excel(name = "执行代理机")
    private String agentName;

    /** 增量条件 */
    @Excel(name = "增量条件")
    private String querySql;

    /** 后置SQL  */
    @Excel(name = "后置条件")
    private String postSql;

    /** 分区方式 */
    @Excel(name = "分区方式",dictType="partition_type",combo={"无","日","月","季","年"})
    private String partitionType;

    /** 分区字段类型 */
    @Excel(name = "分区字段类型",dictType="partition_col_type",combo={"STRING","INT","VARCHAR"})
    private String partitionColType;

    /** 表注释 */
    @Excel(name = "表注释")
    private String tableComment;

    /** 分桶字段 */
    @Excel(name = "分桶字段")
    private String bucketCol;

    /** 分桶数量 */
    @Excel(name = "分桶数量")
    private Long bucketNum;

    /** HDFS路径 */
    private String hdfsPath;

    /** 建表方式 */
    @Excel(name = "建表方式",dictType="create_type",combo={"自动","手动"})
    private String createType;

    /** 写入类型 */
    @Excel(name = "写入类型",dictType="writer_type",combo={"DataX","sqoop"})
    private String writerType;
    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    private int eltdateIndex;

    private String preSql;
    @Valid
    private List<MdmDataEntityProp> props;

    private String jsonScript;

    public String getPreSql() {
        return preSql;
    }

    public void setPreSql(String preSql) {
        this.preSql = preSql;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getJsonScript() {
        return jsonScript;
    }

    public void setJsonScript(String jsonScript) {
        this.jsonScript = jsonScript;
    }

    public int getEltdateIndex() {
        return eltdateIndex;
    }

    public void setEltdateIndex(int eltdateIndex) {
        this.eltdateIndex = eltdateIndex;
    }

    public List<MdmDataEntityProp> getProps() {
        return props;
    }

    public void setProps(List<MdmDataEntityProp> props) {
        this.props = props;
    }

    public String getSrcDatasourceName() {
        return srcDatasourceName;
    }

    public void setSrcDatasourceName(String srcDatasourceName) {
        this.srcDatasourceName = srcDatasourceName;
    }

    public String getTarDatasourceName() {
        return tarDatasourceName;
    }

    public void setTarDatasourceName(String tarDatasourceName) {
        this.tarDatasourceName = tarDatasourceName;
    }

    public void setExchangeId(Long exchangeId)
    {
        this.exchangeId = exchangeId;
    }

    public Long getExchangeId()
    {
        return exchangeId;
    }
    public void setSrcDatasourceId(Long srcDatasourceId)
    {
        this.srcDatasourceId = srcDatasourceId;
    }

    public Long getSrcDatasourceId()
    {
        return srcDatasourceId;
    }
    public void setSrcTableName(String srcTableName)
    {
        this.srcTableName = srcTableName;
    }

    public String getSrcTableName()
    {
        return srcTableName;
    }
    public void setSrcSchema(String srcSchema)
    {
        this.srcSchema = srcSchema;
    }

    public String getSrcSchema()
    {
        return srcSchema;
    }
    public void setTarDatasourceId(Long tarDatasourceId)
    {
        this.tarDatasourceId = tarDatasourceId;
    }

    public Long getTarDatasourceId()
    {
        return tarDatasourceId;
    }
    public void setTarTableName(String tarTableName)
    {
        this.tarTableName = tarTableName;
    }

    public String getTarTableName()
    {
        return tarTableName;
    }
    public void setTarSchema(String tarSchema)
    {
        this.tarSchema = tarSchema;
    }

    public String getTarSchema()
    {
        return tarSchema;
    }
    public void setAgentId(Long agentId)
    {
        this.agentId = agentId;
    }

    public Long getAgentId()
    {
        return agentId;
    }
    public void setWriterType(String writerType)
    {
        this.writerType = writerType;
    }

    public String getWriterType()
    {
        return writerType;
    }
    public void setQuerySql(String querySql)
    {
        this.querySql = querySql;
    }

    public String getQuerySql()
    {
        return querySql;
    }
    public void setPostSql(String postSql)
    {
        this.postSql = postSql;
    }

    public String getPostSql()
    {
        return postSql;
    }
    public void setPartitionType(String partitionType)
    {
        this.partitionType = partitionType;
    }

    public String getPartitionType()
    {
        return partitionType;
    }
    public void setPartitionColType(String partitionColType)
    {
        this.partitionColType = partitionColType;
    }

    public String getPartitionColType()
    {
        return partitionColType;
    }
    public void setTableComment(String tableComment)
    {
        this.tableComment = tableComment;
    }

    public String getTableComment()
    {
        return tableComment;
    }
    public void setBucketCol(String bucketCol)
    {
        this.bucketCol = bucketCol;
    }

    public String getBucketCol()
    {
        return bucketCol;
    }
    public void setBucketNum(Long bucketNum)
    {
        this.bucketNum = bucketNum;
    }

    public Long getBucketNum()
    {
        return bucketNum;
    }
    public void setHdfsPath(String hdfsPath)
    {
        this.hdfsPath = hdfsPath;
    }

    public String getHdfsPath()
    {
        return hdfsPath;
    }
    public void setCreateType(String createType)
    {
        this.createType = createType;
    }

    public String getCreateType()
    {
        return createType;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("exchangeId", getExchangeId())
            .append("srcDatasourceId", getSrcDatasourceId())
            .append("srcTableName", getSrcTableName())
            .append("srcSchema", getSrcSchema())
            .append("tarDatasourceId", getTarDatasourceId())
            .append("tarTableName", getTarTableName())
            .append("tarSchema", getTarSchema())
            .append("agentId", getAgentId())
            .append("writerType", getWriterType())
            .append("querySql", getQuerySql())
            .append("postSql", getPostSql())
            .append("partitionType", getPartitionType())
            .append("partitionColType", getPartitionColType())
            .append("tableComment", getTableComment())
            .append("bucketCol", getBucketCol())
            .append("bucketNum", getBucketNum())
            .append("hdfsPath", getHdfsPath())
            .append("createType", getCreateType())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
