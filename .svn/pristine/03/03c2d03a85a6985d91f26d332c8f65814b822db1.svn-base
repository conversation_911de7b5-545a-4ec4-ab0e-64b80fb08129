package com.dqms.dqm.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Treeselect树结构实体类
 *
*/
public class DqmValidationClassTreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DqmValidationClassTreeSelect> children;

    public DqmValidationClassTreeSelect()
    {

    }

    public DqmValidationClassTreeSelect(DqmValidationClass dqmValidationClass)
    {
        this.id = dqmValidationClass.getValidationClassId();
        this.label = dqmValidationClass.getClassName();
        this.children = dqmValidationClass.getChildren().stream().map(DqmValidationClassTreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<DqmValidationClassTreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<DqmValidationClassTreeSelect> children)
    {
        this.children = children;
    }
}
