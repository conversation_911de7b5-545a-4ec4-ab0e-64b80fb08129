package com.dqms.utils;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportExcelUtil {


    private final static String excel2003L = ".xls"; // 2003- 版本的excel
    private final static String excel2007U = ".xlsx"; // 2007+ 版本的excel
    private static Logger log = LoggerFactory.getLogger("ImportExcelUtil");
    /**
     * 将流中的Excel数据转成List<Map>
     *
     * @param in
     *            输入流
     * @param fileName
     *            文件名（判断Excel版本）
     * @param mapping
     *            字段名称映射
     * @return
     * @throws Exception
     */
    public static List<Map<String, Object>> parseExcel(InputStream in, String fileName,
                                                       Map<String, String> mapping,Integer isColor) throws Exception {
        // 根据文件名来创建Excel工作薄
        Workbook work = getWorkbook(in, fileName);
        FormulaEvaluator evaluator = work.getCreationHelper().createFormulaEvaluator();
        if (null == work) {
            throw new Exception("创建Excel工作薄为空！");
        }
        Sheet sheet = null;
        Row row = null;
        Cell cell = null;
        // 返回数据
        List<Map<String, Object>> ls = new ArrayList<Map<String, Object>>();

        // 遍历Excel中所有的sheet
        for (int i = 0; i < work.getNumberOfSheets(); i++) {
            sheet = work.getSheetAt(i);
            if (sheet == null)
                continue;

            // 取第一行标题
            row = sheet.getRow(0);
            //取第一行的列数
            short cellNum = 0;
            String title[] = null;
            if (row != null) {
                title = new String[row.getLastCellNum()];
                cellNum = row.getLastCellNum();
                for (int y = row.getFirstCellNum(); y < row.getLastCellNum(); y++) {
                    cell = row.getCell(y);
                    title[y] = ((String) getCellValue(cell,evaluator)).replaceAll("\r","").replaceAll("\n","").replaceAll(" ", "");
                }

            } else
                continue;

            log.info("************映射关系："+mapping.toString()+"*********************************************");
            log.info("************excle列标题："+title.toString()+"*********************************************");
            // 遍历当前sheet中的所有行
            for (int j = 1; j < sheet.getLastRowNum() + 1; j++) {
                row = sheet.getRow(j);
                if(row!=null){
                    Map<String, Object> m = new HashMap<String, Object>();
                    // 遍历所有的列
                    short newCellNum = row.getLastCellNum();
                    if (cellNum < newCellNum){//如果第一行标题的列数小于其他行的列数则以第一行为标准
                        newCellNum = cellNum;
                    }
                    for (int y = row.getFirstCellNum(); y < newCellNum; y++) {
                        cell = row.getCell(y);
                        if (isColor.equals(1) && isColor != null) {//是否只导入有底色的内容:1：是；2：否
                            if(cell!=null){
                                CellStyle style = cell.getCellStyle();
                                XSSFCellStyle xs = (XSSFCellStyle) style;
                                XSSFColor color=xs.getFillForegroundXSSFColor();
                                String key = title[y];
                                if(color!=null){
                                    //System.out.println(color.getARgb()+","+color.getARGBHex()+","+color.getTint());
                                    // log.info(JSON.toJSONString(key));
                                    String c = color.getARGBHex();
                                    Double tint = color.getTint();
                                    if (c.equals("FFFFFFFF") && tint.equals(0.0)){//底色为白色的单元格数据不导入
                                        m.put(mapping.get(key), "");
                                    }else{//底色为白色的单元格数据不导入
                                        m.put(mapping.get(key), getCellValue(cell,evaluator));
                                    }
                                }else{//没有底色的单元格数据不导入
                                    m.put(mapping.get(key), "");
                                }
                            }
                        }else {
                            String key = title[y];
                            m.put(mapping.get(key), getCellValue(cell,evaluator));
                        }
                    }
                    ls.add(m);
                }

            }

        }
        log.info("************转List之后数据格式："+ls.toString()+"*********************************************");
        return ls;
    }

    /**
     * 描述：根据文件后缀，自适应上传文件的版本
     *
     * @param inStr
     *            ,fileName
     * @return
     * @throws Exception
     */
    public static Workbook getWorkbook(InputStream inStr, String fileName) throws Exception {
        Workbook wb = null;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if (excel2003L.equals(fileType)) {
            wb = new HSSFWorkbook(inStr); // 2003-
        } else if (excel2007U.equals(fileType)) {
            wb = new XSSFWorkbook(inStr); // 2007+
        } else {
            throw new Exception("解析的文件格式有误！");
        }
        return wb;
    }

    /**
     * 描述：对表格中数值进行格式化
     *
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell, FormulaEvaluator evaluator) {
        Object value = null;
        DecimalFormat df = new DecimalFormat("#####.########"); // 格式化number String字符
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd"); // 日期格式化
        DecimalFormat df2 = new DecimalFormat("0.0000"); // 格式化数字
        if(cell.getCellType()==CellType.STRING){
            value = cell.getRichStringCellValue().getString();
        }else if(cell.getCellType() == CellType.NUMERIC){
            if ("General".equals(cell.getCellStyle().getDataFormatString())) {
                value = String.valueOf(df.format(cell.getNumericCellValue()));
            } else if ("m/d/yy".equals(cell.getCellStyle().getDataFormatString()) || "yyyy\\-mm\\-dd".equals(cell.getCellStyle().getDataFormatString())) {
                value = sdf.format(cell.getDateCellValue());
            } else {
                value = df2.format(cell.getNumericCellValue());
            }
        }else if(cell.getCellType() == CellType.BLANK){

        }
        else if(cell.getCellType() == CellType.FORMULA){
            value=getCellValue(evaluator.evaluate(cell));
        }
        return value;
    }

    private static String getCellValue(CellValue cell) {
        String cellValue = null;
        if(cell.getCellType()==CellType.STRING){
            System.out.print("String :");
            cellValue=cell.getStringValue();
        }else if(cell.getCellType()==CellType.NUMERIC){
            System.out.print("NUMERIC:");
            cellValue=String.valueOf(cell.getNumberValue());
        }else if(cell.getCellType()==CellType.FORMULA){
            System.out.print("FORMULA:");
            cellValue = String.valueOf(cell.getNumberValue());
        }
        return cellValue;
    }
}


