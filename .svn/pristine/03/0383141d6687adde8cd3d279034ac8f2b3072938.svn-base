package com.dqms.mdm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.mdm.domain.MdmCollectFastLog;
import com.dqms.mdm.service.IMdmCollectFastLogService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 元数据快速注册日志Controller
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@RestController
@RequestMapping("/mdm/mdmCollectFastLog")
public class MdmCollectFastLogController extends BaseController
{
    @Autowired
    private IMdmCollectFastLogService mdmCollectFastLogService;

    /**
     * 查询元数据快速注册日志列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectFastLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmCollectFastLog mdmCollectFastLog)
    {
        startPage();
        List<MdmCollectFastLog> list = mdmCollectFastLogService.selectMdmCollectFastLogList(mdmCollectFastLog);
        return getDataTable(list);
    }

    /**
     * 导出元数据快速注册日志列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectFastLog:export')")
    @Log(title = "元数据快速注册日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmCollectFastLog mdmCollectFastLog)
    {
        List<MdmCollectFastLog> list = mdmCollectFastLogService.selectMdmCollectFastLogList(mdmCollectFastLog);
        ExcelUtil<MdmCollectFastLog> util = new ExcelUtil<MdmCollectFastLog>(MdmCollectFastLog.class);
        return util.exportExcel(list, "mdmCollectFastLog");
    }

    /**
     * 获取元数据快速注册日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectFastLog:query')")
    @GetMapping(value = "/{datasourceId}")
    public AjaxResult getInfo(@PathVariable("datasourceId") Long datasourceId)
    {
        return AjaxResult.success(mdmCollectFastLogService.selectMdmCollectFastLogById(datasourceId));
    }

    /**
     * 新增元数据快速注册日志
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectFastLog:add')")
    @Log(title = "元数据快速注册日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmCollectFastLog mdmCollectFastLog)
    {
        return toAjax(mdmCollectFastLogService.insertMdmCollectFastLog(mdmCollectFastLog));
    }

    /**
     * 修改元数据快速注册日志
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectFastLog:edit')")
    @Log(title = "元数据快速注册日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmCollectFastLog mdmCollectFastLog)
    {
        return toAjax(mdmCollectFastLogService.updateMdmCollectFastLog(mdmCollectFastLog));
    }

    /**
     * 删除元数据快速注册日志
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmCollectFastLog:remove')")
    @Log(title = "元数据快速注册日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{datasourceIds}")
    public AjaxResult remove(@PathVariable Long[] datasourceIds)
    {
        return toAjax(mdmCollectFastLogService.deleteMdmCollectFastLogByIds(datasourceIds));
    }
}
