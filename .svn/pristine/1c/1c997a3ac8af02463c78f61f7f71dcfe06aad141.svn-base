package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.constant.MdmConstants;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.dic.domain.DicDataExchange;
import com.dqms.dsm.domain.DsmModelEntityProp;
import com.dqms.dsm.domain.DsmModelEntityPropTemp;
import com.dqms.dsm.domain.DsmModelEntityShip;
import com.dqms.dsm.domain.DsmModelEntityShipTemp;
import com.dqms.dsm.domain.DsmModelEntityTemp;
import com.dqms.dsm.mapper.DsmModelEntityTempMapper;
import com.dqms.dsm.service.IDsmModelEntityPropTempService;
import com.dqms.dsm.service.IDsmModelEntityShipTempService;
import com.dqms.dsm.service.IDsmModelEntityTempService;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.util.MetaDataContext;

/**
 * 模型实例Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
@Service
public class DsmModelEntityTempServiceImpl implements IDsmModelEntityTempService
{
    @Autowired
    private DsmModelEntityTempMapper dsmModelEntityTempMapper;    
    
    @Autowired
    private IDsmModelEntityPropTempService dsmModelEntityPropTempService;
    
    @Autowired
    private IDsmModelEntityShipTempService dsmModelEntityShipTempService;
    
    @Autowired
    private MetaDataContext metaDataContext;

    /**
     * 查询模型实例
     *
     * @param modelEntityTempId 模型实例ID
     * @return 模型实例
     */
    @Override
    public DsmModelEntityTemp selectDsmModelEntityTempById(Long modelEntityTempId)
    {
        return dsmModelEntityTempMapper.selectDsmModelEntityTempById(modelEntityTempId);
    }

    /**
     * 查询模型实例列表
     *
     * @param dsmModelEntityTemp 模型实例
     * @return 模型实例
     */
    @Override
    public List<DsmModelEntityTemp> selectDsmModelEntityTempList(DsmModelEntityTemp dsmModelEntityTemp)
    {
        return dsmModelEntityTempMapper.selectDsmModelEntityTempList(dsmModelEntityTemp);
    }

    /**
     * 新增模型实例
     *
     * @param dsmModelEntityTemp 模型实例
     * @return 结果
     */
    @Override
    public int insertDsmModelEntityTemp(DsmModelEntityTemp dsmModelEntityTemp)
    {
    	dsmModelEntityTemp.setCreateTime(DateUtils.getNowDate());
    	dsmModelEntityTemp.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntityTemp.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntityTemp.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntityTemp.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntityTemp.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntityTemp.setCreateScript(sqlGeneration(dsmModelEntityTemp));
    	dsmModelEntityTemp.setStatus(MdmConstants.APP_WAITING);
    	int i = dsmModelEntityTempMapper.insertDsmModelEntityTemp(dsmModelEntityTemp);
    	Map<String,Long> propMap =new HashMap<>();
    	if(dsmModelEntityTemp.getDsmModelEntityProps()!=null&&dsmModelEntityTemp.getDsmModelEntityProps().size()>0) {
    		for(DsmModelEntityPropTemp prop : dsmModelEntityTemp.getDsmModelEntityProps()) {
    			prop.setModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
    			prop.setCreateTime(DateUtils.getNowDate());
    			prop.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			prop.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			prop.setUpdateTime(DateUtils.getNowDate());
    			prop.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			prop.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityPropTempService.insertDsmModelEntityPropTemp(prop);
    			propMap.put(prop.getPropName(), prop.getModelEntityPropId());
    		}
    	}
    	
    	if(dsmModelEntityTemp.getDsmModelEntityShips()!=null&&dsmModelEntityTemp.getDsmModelEntityShips().size()>0) {
    		for(DsmModelEntityShipTemp ship : dsmModelEntityTemp.getDsmModelEntityShips()) {
    			if(propMap.get(ship.getSrcModelEntityPropName())==null) {
    				throw new RuntimeException(ship.getSrcModelEntityPropName()+"不存在");
    			}else {
    				ship.setSrcModelEntityPropId(propMap.get(ship.getSrcModelEntityPropName()));
    			}
    			DsmModelEntityPropTemp prop = dsmModelEntityPropTempService.selectDsmModelEntityPropTempById(ship.getTarModelEntityPropId());
    			ship.setSrcModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
    			ship.setTarModelEntityId(prop.getModelEntityId());
    			ship.setCreateTime(DateUtils.getNowDate());
    			ship.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			ship.setUpdateTime(DateUtils.getNowDate());
    			ship.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityShipTempService.insertDsmModelEntityShipTemp(ship);
    		}
    	}
        return i;
    }

    public String sqlGeneration(DsmModelEntityTemp dsmModelEntity) {
    	DicDataExchange dicDataExchange = new DicDataExchange();
    	dicDataExchange.setTarDatasourceId(dsmModelEntity.getDatasourceId());
    	dicDataExchange.setBucketCol(dsmModelEntity.getBucketCol());
    	dicDataExchange.setBucketNum(dsmModelEntity.getBucketNum());
    	dicDataExchange.setPartitionType(dsmModelEntity.getPartitionType());
    	dicDataExchange.setPartitionColType(dsmModelEntity.getPartitionColType());
    	dicDataExchange.setTarSchema(dsmModelEntity.getTableSchema());
    	dicDataExchange.setTarTableName(dsmModelEntity.getTableName());
    	dicDataExchange.setTableComment(dsmModelEntity.getTableComment());
    	if(dsmModelEntity.getDsmModelEntityProps()!=null&&dsmModelEntity.getDsmModelEntityProps().size()>0) {
    		List<MdmDataEntityProp> sqlVoProps = new ArrayList<MdmDataEntityProp>();
    		int i =0;
    		for(DsmModelEntityPropTemp prop : dsmModelEntity.getDsmModelEntityProps()) {
    			MdmDataEntityProp p = new MdmDataEntityProp();
    			p.setColumnSize(prop.getColumnSize()==null?null:Integer.parseInt(prop.getColumnSize().toString()));
    			p.setDataType(prop.getDataType());
    			p.setDecimalDigits(prop.getDecimalDigits()==null?null:Integer.parseInt(prop.getDecimalDigits().toString()));
    			p.setDefaultValue(prop.getDefaultValue());
    			p.setIsPriKey(prop.getIsPriKey());
    			p.setNullable(prop.getNullable());
    			p.setPropComment(prop.getPropComment());
    			//p.setPropDesc(prop.getPropDesc());
    			p.setPropName(prop.getPropName());
    			p.setRemark(prop.getRemark());
    			p.setOrderId(i++);
    			sqlVoProps.add(p);
    		}
    		dicDataExchange.setProps(sqlVoProps);
    	}
        return metaDataContext.createSqlGeneration(dicDataExchange);
    }
    /**
     * 修改模型实例
     *
     * @param dsmModelEntityTemp 模型实例
     * @return 结果
     */
    @Override
    public int updateDsmModelEntityTemp(DsmModelEntityTemp dsmModelEntityTemp)
    {
    	dsmModelEntityTemp.setUpdateTime(DateUtils.getNowDate());
    	dsmModelEntityTemp.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	dsmModelEntityTemp.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	dsmModelEntityTemp.setCreateScript(sqlGeneration(dsmModelEntityTemp));
    	int i = dsmModelEntityTempMapper.updateDsmModelEntityTemp(dsmModelEntityTemp);
    	
		DsmModelEntityPropTemp dsmModelEntityPropTemp = new DsmModelEntityPropTemp();
		dsmModelEntityPropTemp.setModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
		List<DsmModelEntityPropTemp> list = dsmModelEntityPropTempService.selectDsmModelEntityPropTempList(dsmModelEntityPropTemp);
		Map<String,Long> map =new HashMap<>();
		Map<String,Long> propMap =new HashMap<>();
		for(DsmModelEntityPropTemp prop : list) {
			map.put(prop.getPropName(), prop.getModelEntityPropTempId());
		}
    	if(dsmModelEntityTemp.getDsmModelEntityProps()!=null&&dsmModelEntityTemp.getDsmModelEntityProps().size()>0) {
    		for(DsmModelEntityPropTemp prop : dsmModelEntityTemp.getDsmModelEntityProps()) {
    			if(map.get(prop.getPropName())==null) {
    				prop.setModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
        			prop.setCreateTime(DateUtils.getNowDate());
        			prop.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        			prop.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        			prop.setUpdateTime(DateUtils.getNowDate());
        			prop.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        			prop.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        			dsmModelEntityPropTempService.insertDsmModelEntityPropTemp(prop);
        			propMap.put(prop.getPropName(), prop.getModelEntityPropId());
    			}else {
    				prop.setModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
    				prop.setModelEntityPropId(map.get(prop.getPropName()));
        			prop.setUpdateTime(DateUtils.getNowDate());
        			prop.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        			prop.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        			dsmModelEntityPropTempService.updateDsmModelEntityPropTemp(prop);
        			propMap.put(prop.getPropName(), map.get(prop.getPropName()));
        			map.remove(prop.getPropName());
    			}
    			
    		}
    	}
    	for (Map.Entry<String,Long> entry : map.entrySet()) { 
			dsmModelEntityPropTempService.deleteDsmModelEntityPropTempById(entry.getValue());
		}
    	
    	dsmModelEntityShipTempService.deleteDsmModelEntityShipTempByDsmModelEntityId(dsmModelEntityTemp.getModelEntityId());
    	if(dsmModelEntityTemp.getDsmModelEntityShips()!=null&&dsmModelEntityTemp.getDsmModelEntityShips().size()>0) {
    		for(DsmModelEntityShipTemp ship : dsmModelEntityTemp.getDsmModelEntityShips()) {
    			if(propMap.get(ship.getSrcModelEntityPropName())==null) {
    				throw new RuntimeException(ship.getSrcModelEntityPropName()+"不存在");
    			}else {
    				ship.setSrcModelEntityPropId(propMap.get(ship.getSrcModelEntityPropName()));
    			}
    			DsmModelEntityPropTemp prop = dsmModelEntityPropTempService.selectDsmModelEntityPropTempById(ship.getTarModelEntityPropId());
    			ship.setSrcModelEntityId(dsmModelEntityTemp.getModelEntityTempId());
    			ship.setTarModelEntityId(prop.getModelEntityId());
    			ship.setCreateTime(DateUtils.getNowDate());
    			ship.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    			ship.setUpdateTime(DateUtils.getNowDate());
    			ship.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    			ship.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    			dsmModelEntityShipTempService.insertDsmModelEntityShipTemp(ship);
    		}
    	}
        return i;
    }

    /**
     * 批量删除模型实例
     *
     * @param modelEntityTempIds 需要删除的模型实例ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityTempByIds(Long[] modelEntityTempIds)
    {
        return dsmModelEntityTempMapper.deleteDsmModelEntityTempByIds(modelEntityTempIds);
    }

    /**
     * 删除模型实例信息
     *
     * @param modelEntityTempId 模型实例ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityTempById(Long modelEntityTempId)
    {
        return dsmModelEntityTempMapper.deleteDsmModelEntityTempById(modelEntityTempId);
    }
    
    @Override
    public int applyDsmModelEntityTempByIds(Long[] modelEntityTempIds)
    {
    	for(Long id : modelEntityTempIds) {
    		DsmModelEntityTemp temp = dsmModelEntityTempMapper.selectDsmModelEntityTempById(id);
    		temp.setStatus(MdmConstants.APP_APPROVED);
    		dsmModelEntityTempMapper.updateDsmModelEntityTemp(temp);
    	}
        return 1;
    }
}
