package com.dqms.dsm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsm.domain.DsmModelSqlCheck;
import com.dqms.dsm.service.IDsmModelSqlCheckService;

/**
 * 脚本检核Controller
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
@RestController
@RequestMapping("/dsm/dsmModelSqlCheck")
public class DsmModelSqlCheckController extends BaseController
{
    @Autowired
    private IDsmModelSqlCheckService dsmModelSqlCheckService;

    /**
     * 查询脚本检核列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelSqlCheck:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmModelSqlCheck dsmModelSqlCheck)
    {
        startPage();
        List<DsmModelSqlCheck> list = dsmModelSqlCheckService.selectDsmModelSqlCheckList(dsmModelSqlCheck);
        return getDataTable(list);
    }

    /**
     * 导出脚本检核列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelSqlCheck:export')")
    @Log(title = "脚本检核", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmModelSqlCheck dsmModelSqlCheck)
    {
        List<DsmModelSqlCheck> list = dsmModelSqlCheckService.selectDsmModelSqlCheckList(dsmModelSqlCheck);
        ExcelUtil<DsmModelSqlCheck> util = new ExcelUtil<DsmModelSqlCheck>(DsmModelSqlCheck.class);
        return util.exportExcel(list, "dsmModelSqlCheck");
    }

    /**
     * 获取脚本检核详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelSqlCheck:query')")
    @GetMapping(value = "/{modelSqlCheckId}")
    public AjaxResult getInfo(@PathVariable("modelSqlCheckId") Long modelSqlCheckId)
    {
        return AjaxResult.success(dsmModelSqlCheckService.selectDsmModelSqlCheckById(modelSqlCheckId));
    }

    /**
     * 新增脚本检核
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelSqlCheck:add')")
    @Log(title = "脚本检核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmModelSqlCheck dsmModelSqlCheck)
    {
        return toAjax(dsmModelSqlCheckService.insertDsmModelSqlCheck(dsmModelSqlCheck));
    }

    /**
     * 修改脚本检核
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelSqlCheck:edit')")
    @Log(title = "脚本检核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmModelSqlCheck dsmModelSqlCheck)
    {
        return toAjax(dsmModelSqlCheckService.updateDsmModelSqlCheck(dsmModelSqlCheck));
    }

    /**
     * 删除脚本检核
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmModelSqlCheck:remove')")
    @Log(title = "脚本检核", businessType = BusinessType.DELETE)
	@DeleteMapping("/{modelSqlCheckIds}")
    public AjaxResult remove(@PathVariable Long[] modelSqlCheckIds)
    {
        return toAjax(dsmModelSqlCheckService.deleteDsmModelSqlCheckByIds(modelSqlCheckIds));
    }
    
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<DsmModelSqlCheck> util = new ExcelUtil<DsmModelSqlCheck>(DsmModelSqlCheck.class);
        return util.importTemplateExcel("脚本数据");
    }
    @Log(title = "脚本校验导入", businessType = BusinessType.IMPORT )
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DsmModelSqlCheck> util = new ExcelUtil<DsmModelSqlCheck>(DsmModelSqlCheck.class);
        List<DsmModelSqlCheck> list = util.importExcel(file.getInputStream());
        String message = dsmModelSqlCheckService.importDsmModelSqlCheck(list, updateSupport);
        return AjaxResult.success(message);
    }
}
