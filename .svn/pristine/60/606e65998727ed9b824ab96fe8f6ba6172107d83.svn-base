import request from '@/utils/request'

// 查询接口参数列表
export function listApiDefineParam(query) {
  return request({
    url: '/api/apiDefineParam/list',
    method: 'get',
    params: query
  })
}

// 查询接口参数详细
export function getApiDefineParam(defineParamId) {
  return request({
    url: '/api/apiDefineParam/' + defineParamId,
    method: 'get'
  })
}

// 新增接口参数
export function addApiDefineParam(data) {
  return request({
    url: '/api/apiDefineParam',
    method: 'post',
    data: data
  })
}

// 修改接口参数
export function updateApiDefineParam(data) {
  return request({
    url: '/api/apiDefineParam',
    method: 'put',
    data: data
  })
}

// 删除接口参数
export function delApiDefineParam(defineParamId) {
  return request({
    url: '/api/apiDefineParam/' + defineParamId,
    method: 'delete'
  })
}

// 导出接口参数
export function exportApiDefineParam(query) {
  return request({
    url: '/api/apiDefineParam/export',
    method: 'get',
    params: query
  })
}