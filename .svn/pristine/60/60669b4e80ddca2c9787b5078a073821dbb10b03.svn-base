<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="数据源" prop="datasourceId">
        <el-select v-model="queryParams.datasourceId" placeholder="请选择数据源" clearable size="small" filterable>
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="源系统" prop="systemId">
        <el-select v-model="queryParams.systemId" placeholder="请选择源系统" clearable size="small" filterable>
          <el-option
            v-for="item in systemOptions"
            :key="item.systemId"
            :label="item.name"
            :value="item.systemId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属分层" prop="layerId">
        <el-select v-model="queryParams.layerId" placeholder="请选择所属分层" clearable size="small" filterable>
          <el-option
            v-for="item in layerOptions"
            :key="item.layerId"
            :label="item.layerName"
            :value="item.layerId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属主题" prop="themeId">
        <el-select v-model="queryParams.themeId" placeholder="请选择所属主题" clearable size="small" filterable>
          <el-option
            v-for="item in themeOptions"
            :key="item.themeId"
            :label="item.themeName"
            :value="item.themeId"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="元类型" prop="metaType">-->
<!--        <el-select v-model="queryParams.metaType" placeholder="请选择元类型" clearable size="small">-->
<!--          <el-option-->
<!--            v-for="dict in metaTypeOptions"-->
<!--            :key="dict.dictValue"-->
<!--            :label="dict.dictLabel"-->
<!--            :value="dict.dictValue"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="采集方式" prop="acqMode">
        <el-select v-model="queryParams.acqMode" placeholder="请选择采集方式" clearable size="small">
          <el-option
            v-for="dict in acqModeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="acqResultState">
        <el-select v-model="queryParams.acqResultState" placeholder="请选择采集结果状态" clearable size="small">
          <el-option
            v-for="dict in acqResultStateOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="daterangeStartTime"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['mdm:mdmCollectFastLog:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['mdm:mdmCollectFastLog:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['mdm:mdmCollectFastLog:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['mdm:mdmCollectFastLog:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>-->
<!--    </el-row>-->

    <el-table v-loading="loading" border :data="mdmCollectFastLogList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="数据源" align="center" prop="datasourceName" width="120"/>
      <el-table-column label="所属系统" align="center" prop="systemName" width="120"/>
      <el-table-column label="所属分层" align="center" prop="layerName" />
      <el-table-column label="所属主题" align="center" prop="themeName" />
<!--      <el-table-column label="元类型" align="center" prop="metaType" :formatter="metaTypeFormat" />-->
      <el-table-column label="采集方式" align="center" prop="acqMode" :formatter="acqModeFormat" />
      <el-table-column label="结果状态" align="center" prop="acqResultState"  >
        <template slot-scope="scope" >
          <el-button
            v-if="scope.row.acqResultState==0"
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="handleView(scope.row)"
          >失败</el-button>
          <span v-if="scope.row.acqResultState==1">成功</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="快速采集说明" align="center" prop="acqDesc" width="200"/>
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" >-->
<!--        <template slot-scope="scope" v-if="scope.row.acqResultState==0">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleView(scope.row)"-->
<!--            v-hasPermi="['mdm:mdmCollectHis:edit']"-->
<!--          >异常信息</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['mdm:mdmCollectFastLog:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['mdm:mdmCollectFastLog:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog title="异常信息详细" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="24">
            <div style="white-space: pre-wrap"  v-html="form.errorMsg" ></div>
            <!--            <el-form-item label="异常信息：">{{ form.errorMsg }}</el-form-item>-->
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMdmCollectFastLog, getMdmCollectFastLog, delMdmCollectFastLog, addMdmCollectFastLog, updateMdmCollectFastLog, exportMdmCollectFastLog } from "@/api/mdm/mdmCollectFastLog";
import { listDatasourceAll, listSystem } from '@/api/basic/datasource'
import { listMdmThemeAll } from '@/api/mdm/mdmTheme'
import { listMdmLayerAll } from '@/api/mdm/mdmLayer'

export default {
  name: "MdmCollectFastLog",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 元数据快速注册日志表格数据
      mdmCollectFastLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 元类型字典
      metaTypeOptions: [],
      // 采集方式字典
      acqModeOptions: [],
      // 采集结果状态字典
      acqResultStateOptions: [],
      // 开始时间时间范围
      daterangeStartTime: [],
      //应用系统选项
      systemOptions: [],
      //数据源选项
      dataSourceOptions: [],
      //主题选项
      themeOptions: [],
      //分层选项
      layerOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        datasourceId: null,
        systemId: null,
        layerId: null,
        themeId: null,
        metaType: null,
        acqMode: null,
        acqResultState: null,
        startTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("mdm_meta_type").then(response => {
      this.metaTypeOptions = response.data;
    });
    this.getDicts("mdm_acq_mode").then(response => {
      this.acqModeOptions = response.data;
    });
    this.getDicts("acq_result_state").then(response => {
      this.acqResultStateOptions = response.data;
    });
    this.getSystem();
    this.getDataSource();
    this.getMdmTheme();
    this.getMdmLayer();
  },
  methods: {
    /** 查询元数据快速注册日志列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeStartTime && '' != this.daterangeStartTime) {
        this.queryParams.params["beginStartTime"] = this.daterangeStartTime[0];
        this.queryParams.params["endStartTime"] = this.daterangeStartTime[1];
      }
      listMdmCollectFastLog(this.queryParams).then(response => {
        this.mdmCollectFastLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 元类型字典翻译
    metaTypeFormat(row, column) {
      return this.selectDictLabel(this.metaTypeOptions, row.metaType);
    },
    // 采集方式字典翻译
    acqModeFormat(row, column) {
      return this.selectDictLabel(this.acqModeOptions, row.acqMode);
    },
    // 采集结果状态字典翻译
    acqResultStateFormat(row, column) {
      return this.selectDictLabel(this.acqResultStateOptions, row.acqResultState);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        datasourceId: null,
        systemId: null,
        layerId: null,
        themeId: null,
        metaType: null,
        acqMode: null,
        acqResultState: null,
        errorMsg: null,
        startTime: null,
        endTime: null,
        acqDesc: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeStartTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.datasourceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加元数据快速注册日志";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const datasourceId = row.datasourceId || this.ids
      getMdmCollectFastLog(datasourceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改元数据快速注册日志";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.datasourceId != null) {
            updateMdmCollectFastLog(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMdmCollectFastLog(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const datasourceIds = row.datasourceId || this.ids;
      this.$confirm('是否确认删除元数据快速注册日志编号为"' + datasourceIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delMdmCollectFastLog(datasourceIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 异常详细信息按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有元数据快速注册日志数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportMdmCollectFastLog(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    /** 获取应用系统 */
    getSystem() {
      listSystem().then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    getMdmTheme() {
      listMdmThemeAll().then(response => {
        this.themeOptions = response.data;
      });
    },
    getMdmLayer() {
      listMdmLayerAll().then(response => {
        this.layerOptions = response.data;
      });
    }
  }
};
</script>
