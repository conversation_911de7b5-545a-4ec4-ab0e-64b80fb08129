<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dic.mapper.DicManualDataDefineUserMapper">
    
    <resultMap type="DicManualDataDefineUser" id="DicManualDataDefineUserResult">
        <result property="manualDataId"    column="manual_data_id"    />
        <result property="userId"    column="user_id"    />
        <result property="flag"    column="flag"    />
        <result property="nickName"    column="nick_name"    />
        <result property="userName"    column="user_name"    />
    </resultMap>

    <sql id="selectDicManualDataDefineUserVo">
        select u.nick_name,u.user_name ,d.manual_data_id, u.user_id ,CASE WHEN d.manual_data_id IS NULL THEN 'N' ELSE 'Y' END AS flag
         from sys_user u left join dic_manual_data_define_user d on u.user_id=d.user_id 
    </sql>

    <select id="selectDicManualDataDefineUserList" parameterType="DicManualDataDefineUser" resultMap="DicManualDataDefineUserResult">
        <include refid="selectDicManualDataDefineUserVo"/>
        and d.manual_data_id = #{manualDataId}
        <where>  
            <if test="userId != null "> and d.user_id = #{userId}</if>
            <if test="nickName != null "> and u.nick_name = #{nickName}</if>
        </where>
    </select>
    
    <select id="selectDicManualDataDefineUserById" parameterType="Long" resultMap="DicManualDataDefineUserResult">
        <include refid="selectDicManualDataDefineUserVo"/>
        where manual_data_id = #{manualDataId}
    </select>
        
    <insert id="insertDicManualDataDefineUser" parameterType="DicManualDataDefineUser">
        insert into dic_manual_data_define_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="manualDataId != null">manual_data_id,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="manualDataId != null">#{manualDataId},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>
        
    <delete id="deleteDicManualDataDefineUser" parameterType="DicManualDataDefineUser">
        delete from dic_manual_data_define_user where manual_data_id=#{manualDataId} and user_id=#{userId}
    </delete>
    
    <update id="updateDicManualDataDefineUser" parameterType="DicManualDataDefineUser">
        update dic_manual_data_define_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where manual_data_id = #{manualDataId}
    </update>

    <delete id="deleteDicManualDataDefineUserById" parameterType="Long">
        delete from dic_manual_data_define_user where manual_data_id = #{manualDataId}
    </delete>

    <delete id="deleteDicManualDataDefineUserByIds" parameterType="String">
        delete from dic_manual_data_define_user where manual_data_id in 
        <foreach item="manualDataId" collection="array" open="(" separator="," close=")">
            #{manualDataId}
        </foreach>
    </delete>
</mapper>