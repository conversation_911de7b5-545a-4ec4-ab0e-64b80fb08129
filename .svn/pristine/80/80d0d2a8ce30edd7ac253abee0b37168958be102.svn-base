import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import ParentView from "@/components/ParentView";

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: resolve => require(["@/views/redirect"], resolve)
      }
    ]
  },
  {
    path: "/login",
    component: resolve => require(["@/views/login"], resolve),
    hidden: true
  },
  {
    path: "/damIndex",
    component: resolve => require(["@/views/dam/damIndex"], resolve),
    hidden: true
  },
  {
    path: "/damAssetsView",
    component: resolve => require(["@/views/dam/damAssetsView"], resolve),
    hidden: true
  },
  {
    path: "/damEntitypropsafe",
    component: resolve => require(["@/views/dam/damEntitypropsafe"], resolve),
    hidden: true
  },
  {
    path: "/damDataSecurityClassificationStandard",
    component: resolve => require(["@/views/dam/damDataSecurityClassificationStandard"], resolve),
    hidden: true
  },
  {
    path: "/damSensitiveDataClassification",
    component: resolve => require(["@/views/dam/damSensitiveDataClassification"], resolve),
    hidden: true
  },
  {
    path: "/loginSSO",
    component: resolve => require(["@/views/loginSSO"], resolve),
    hidden: true
  },
  {
    path: "/404",
    component: resolve => require(["@/views/error/404"], resolve),
    hidden: true
  },
  {
    path: "/401",
    component: resolve => require(["@/views/error/401"], resolve),
    hidden: true
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: resolve => require(["@/views/index"], resolve),
        name: "首页",
        meta: { title: "首页", icon: "dashboard", noCache: true, affix: true }
      }
    ]
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: resolve =>
          require(["@/views/system/user/profile/index"], resolve),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" }
      }
    ]
  },
  {
    path: "/dict",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "type/data/:dictId(\\d+)",
        component: resolve => require(["@/views/system/dict/data"], resolve),
        name: "Data",
        meta: { title: "字典数据", icon: "" }
      }
    ]
  },
  {
    path: "/dqm",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "detail/data",
        component: resolve => require(["@/views/dqm/dqmDetail/index"], resolve),
        name: "dqmData",
        meta: { title: "明细数据", icon: "" }
      }
    ]
  },
  {
    path: "/job",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "log",
        component: resolve => require(["@/views/monitor/job/log"], resolve),
        name: "JobLog",
        meta: { title: "调度日志" }
      }
    ]
  },
  {
    path: "/gen",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "edit/:tableId(\\d+)",
        component: resolve => require(["@/views/tool/gen/editTable"], resolve),
        name: "GenEdit",
        meta: { title: "修改生成配置" }
      }
    ]
  },
  {
    path: "/assetsSearch",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "assets/detail/:damAssetsId(\\d+)",
        component: resolve =>
          require(["@/views/assets/assetsSearch/detail"], resolve),
        name: "assetsData",
        meta: { title: "资产明细", icon: "" }
      }
    ]
  },
  {
    path: "/apiDefine",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "api/debug/:defineId(\\d+)",
        component: resolve =>
          require(["@/views/api/apiDefine/debug"], resolve),
        name: "apiData",
        meta: { title: "接口联调", icon: "" }
      }
    ]
  },
  {
    path: "/dsmMasterData",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dsm/deploy/:masterDataId(\\d+)",
        component: resolve =>
          require(["@/views/dsm/dsmMasterData/deploy"], resolve),
        name: "Data",
        meta: { title: "主数据配置", icon: "" }
      }
    ]
  },
  {
    path: "/dsmMasterDataEnter",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dsm/enter/:masterDataId(\\d+)",
        component: resolve =>
          require(["@/views/dsm/dsmMasterDataEnter/enter"], resolve),
        name: "DataEnter",
        meta: { title: "主数据录入 ", icon: "" }
      }
    ]
  },
  {
    path: "/dic",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dicManualDataDeploy/:manualDataId(\\d+)",
        component: resolve =>
          require(["@/views/dic/dicManualDataDefine/deploy"], resolve),
        name: "dicManualDataDeploy",
        meta: { title: "信息配置", icon: "" }
      }
    ]
  },
  {
    path: "/dic",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dicManualDataManager/:manualDataId(\\d+)",
        component: resolve =>
          require(["@/views/dic/dicManualDataDefine/manager"], resolve),
        name: "dicManualDataManager",
        meta: { title: "数据管理", icon: "" }
      }
    ]
  },
  {
    path: "/dic",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dicManualDataApply/:manualDataId(\\d+)",
        component: resolve =>
          require(["@/views/dic/dicManualDataDefine/apply"], resolve),
        name: "dicManualDataApply",
        meta: { title: "数据审核", icon: "" }
      }
    ]
  },   
  {
    path: "/dic",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dicManualDataEnter",
        component: resolve =>
          require(["@/views/dic/dicManualDataDefine/enter"], resolve),
        name: "dicManualDataEnter",
        meta: { title: "数据录入", icon: "" }
      }
    ]
  },
    {
    path: "/mdm",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "mdmDataEntityShip/:entityId(\\d+)",
        component: resolve =>
          require(["@/views/mdm/mdmDataEntityShip/ship"], resolve),
        name: "mdmDataEntityShip",
        meta: { title: "血缘关系", icon: "" }
      }
    ]
  },
    {
    path: "/mdm",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "mdmThemeMap/:themeId(\\d+)",
        component: resolve =>
          require(["@/views/mdm/mdmTheme/map"], resolve),
        name: "mdmThemeMap",
        meta: { title: "主题模型", icon: "" }
      }
    ]
  }
];

export default new Router({
  mode: "history", // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
});
