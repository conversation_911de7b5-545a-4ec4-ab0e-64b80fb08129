package com.dqms.dsc.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Treeselect树结构实体类
 *
*/
public class DscEntityPropClassTreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DscEntityPropClassTreeSelect> children;

    public DscEntityPropClassTreeSelect()
    {

    }

    public DscEntityPropClassTreeSelect(DscEntityPropClass dscEntityPropClass)
    {
        this.id = dscEntityPropClass.getEntityPropClassId();
        this.label = dscEntityPropClass.getClassName();
        this.children = dscEntityPropClass.getChildren().stream().map(DscEntityPropClassTreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<DscEntityPropClassTreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<DscEntityPropClassTreeSelect> children)
    {
        this.children = children;
    }
}
