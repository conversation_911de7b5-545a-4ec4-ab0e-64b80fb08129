package com.dqms.dsm.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dsm.domain.DsmIndexClass;
import com.dqms.dsm.domain.DsmStandardClass;

/**
 * 指标分类Mapper接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmIndexClassMapper
{
    /**
     * 查询指标分类
     *
     * @param indexClassId 指标分类ID
     * @return 指标分类
     */
    public DsmIndexClass selectDsmIndexClassById(Long indexClassId);
    public DsmIndexClass selectDsmIndexClassByName(String indexClassName);

    /**
     * 查询指标分类列表
     *
     * @param dsmIndexClass 指标分类
     * @return 指标分类集合
     */
    public List<DsmIndexClass> selectDsmIndexClassList(DsmIndexClass dsmIndexClass);

    /**
     * 新增指标分类
     *
     * @param dsmIndexClass 指标分类
     * @return 结果
     */
    public int insertDsmIndexClass(DsmIndexClass dsmIndexClass);

    /**
     * 修改指标分类
     *
     * @param dsmIndexClass 指标分类
     * @return 结果
     */
    public int updateDsmIndexClass(DsmIndexClass dsmIndexClass);

    /**
     * 删除指标分类
     *
     * @param indexClassId 指标分类ID
     * @return 结果
     */
    public int deleteDsmIndexClassById(Long indexClassId);

    /**
     * 批量删除指标分类
     *
     * @param indexClassIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmIndexClassByIds(Long[] indexClassIds);

    /**
     * 根据ID查询所有子任务分类
     *
     * @param deptId 任务分类ID
     * @return 任务分类列表
     */
    public List<DsmIndexClass> selectChildrenClassById(Long indexClassId);
    public int updateClassChildren(@Param("dsmIndexClass") List<DsmIndexClass> dsmIndexClass);
    public int updateClassNameFullChildren(@Param("dsmIndexClass") List<DsmIndexClass> dsmIndexClass);
}
