package com.dqms.dsm.service.impl;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.enums.DamConstants;
import com.dqms.dam.service.IDamAssetsService;
import com.dqms.dsm.domain.DsmQuota;
import com.dqms.dsm.domain.DsmQuotaCurrencyRel;
import com.dqms.dsm.domain.DsmQuotaDeptRel;
import com.dqms.dsm.domain.DsmQuotaTagRel;
import com.dqms.dsm.mapper.DsmQuotaCurrencyRelMapper;
import com.dqms.dsm.mapper.DsmQuotaDeptRelMapper;
import com.dqms.dsm.mapper.DsmQuotaMapper;
import com.dqms.dsm.mapper.DsmQuotaTagRelMapper;
import com.dqms.dsm.service.IDsmQuotaService;
import com.dqms.framework.web.service.TokenService;
import com.dqms.system.service.ISysConfigService;

/**
 * 指标信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-01
 */
@Service
public class DsmQuotaServiceImpl implements IDsmQuotaService
{
    @Autowired
    private DsmQuotaMapper dsmQuotaMapper;

    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private ISysConfigService sysConfigService;
    
    @Autowired
    private DsmQuotaTagRelMapper dsmQuotaTagRelMapper;
    
    @Autowired
    private DsmQuotaDeptRelMapper dsmQuotaDeptRelMapper;
    
    @Autowired
    private DsmQuotaCurrencyRelMapper dsmQuotaCurrencyRelMapper;
    
    @Autowired
    private IDamAssetsService damAssetsService;

    /**
     * 查询指标信息
     *
     * @param quotaId 指标信息ID
     * @return 指标信息
     */
    @Override
    public DsmQuota selectDsmQuotaById(Long quotaId)
    {
        return dsmQuotaMapper.selectDsmQuotaById(quotaId);
    }

    /**
     * 查询指标信息列表
     *
     * @param dsmQuota 指标信息
     * @return 指标信息
     */
    @Override
    public List<DsmQuota> selectDsmQuotaList(DsmQuota dsmQuota)
    {
        return dsmQuotaMapper.selectDsmQuotaList(dsmQuota);
    }

    /**
     * 新增指标信息
     *
     * @param dsmQuota 指标信息
     * @return 结果
     */
    @Override
    public int insertDsmQuota(DsmQuota dsmQuota,Long oldId)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmQuota.setCreateTime(DateUtils.getNowDate());
    	dsmQuota.setCreateId(loginUser.getUser().getUserId());
    	dsmQuota.setCreateBy(loginUser.getUser().getNickName());
    	dsmQuota.setUpdateTime(DateUtils.getNowDate());
    	dsmQuota.setUpdateId(loginUser.getUser().getUserId());
    	dsmQuota.setUpdateBy(loginUser.getUser().getNickName());
    	dsmQuota.setQuotaNo(UUID.randomUUID().toString());
    	DsmQuota yz = dsmQuotaMapper.selectDsmQuotaByCode(dsmQuota);
    	if(yz!=null) {
    		throw new RuntimeException("指标编码已经存在！");
    	}
    	yz = dsmQuotaMapper.selectDsmQuotaByName(dsmQuota);
     	if(yz!=null) {
     		throw new RuntimeException("指标名称已经存在！");
     	}
     	int i=dsmQuotaMapper.insertDsmQuota(dsmQuota);
     	
     	if(dsmQuota.getTags()!=null) {
     		for(Long tag : dsmQuota.getTags()) {
     			DsmQuotaTagRel dsmQuotaTagRel = new DsmQuotaTagRel();
     			dsmQuotaTagRel.setQuotaId(dsmQuota.getQuotaId());
     			dsmQuotaTagRel.setTagId(tag);
     			dsmQuotaTagRelMapper.insertDsmQuotaTagRel(dsmQuotaTagRel);
     		}
     		
     	}
     	if(dsmQuota.getCurrencys()!=null) {
     		for(String currency : dsmQuota.getCurrencys()) {
     			DsmQuotaCurrencyRel dsmQuotaCurrencyRel = new DsmQuotaCurrencyRel();
     			dsmQuotaCurrencyRel.setQuotaId(dsmQuota.getQuotaId());
     			dsmQuotaCurrencyRel.setCurrencyId(currency);
     			dsmQuotaCurrencyRelMapper.insertDsmQuotaCurrencyRel(dsmQuotaCurrencyRel);
     		}
     		
     	}
     	if(dsmQuota.getDepts()!=null) {
     		for(Long dept : dsmQuota.getDepts()) {
     			DsmQuotaDeptRel dsmQuotaDeptRel = new DsmQuotaDeptRel();
     			dsmQuotaDeptRel.setQuotaId(dsmQuota.getQuotaId());
     			dsmQuotaDeptRel.setDeptId(dept);
     			dsmQuotaDeptRelMapper.insertDsmQuotaDeptRel(dsmQuotaDeptRel);
     		}
     		
     	}
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	DamAssets damAssets = new DamAssets();
        	if(oldId!=null) {
        		damAssets = damAssetsService.selectDamAssetsByRel(oldId.toString(), DamConstants.DSM_TYPE_INDEX);
            	if(damAssets!=null) {
                	damAssets.setAssetsName(dsmQuota.getQuotaName());
                    damAssets.setAssetsType(DamConstants.DSM_TYPE_INDEX);
                    damAssets.setAssetsCode(DamConstants.DSM_TYPE_INDEX+dsmQuota.getQuotaCode());
                    damAssets.setStatus(dsmQuota.getStatus());
                    damAssets.setRelId(dsmQuota.getQuotaId()+"");
                    StringBuffer s = new StringBuffer();
            		s.append("指标名称：").append(dsmQuota.getQuotaName()).append("  ")
            		 .append("指标编码：").append(dsmQuota.getQuotaCode()).append("  ")
            		 .append("业务定义：").append(dsmQuota.getDefinition()).append("  ");
            		damAssets.setRemark(s.toString());
                    damAssetsService.updateDamAssets(damAssets);
            	}
        	}else {
            	damAssets.setAssetsName(dsmQuota.getQuotaName());
                damAssets.setAssetsType(DamConstants.DSM_TYPE_INDEX);
                damAssets.setAssetsCode(DamConstants.DSM_TYPE_INDEX+dsmQuota.getQuotaCode());
                damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
                damAssets.setRelId(dsmQuota.getQuotaId()+"");
                StringBuffer s = new StringBuffer();
        		s.append("指标名称：").append(dsmQuota.getQuotaName()).append("  ")
        		 .append("指标编码：").append(dsmQuota.getQuotaCode()).append("  ")
        		 .append("业务定义：").append(dsmQuota.getDefinition()).append("  ");
        		damAssets.setRemark(s.toString());
                damAssetsService.insertDamAssets(damAssets);
        	}

        }
        return i;
    }

    /**
     * 修改指标信息
     *
     * @param dsmQuota 指标信息
     * @return 结果
     */
    @Override
    public int updateDsmQuota(DsmQuota dsmQuota)
    {
        dsmQuota.setUpdateTime(DateUtils.getNowDate());
        return dsmQuotaMapper.updateDsmQuota(dsmQuota);
    }

    /**
     * 批量删除指标信息
     *
     * @param quotaIds 需要删除的指标信息ID
     * @return 结果
     */
    @Override
    public int deleteDsmQuotaByIds(Long[] quotaIds)
    {
        return dsmQuotaMapper.deleteDsmQuotaByIds(quotaIds);
    }

    /**
     * 删除指标信息信息
     *
     * @param quotaId 指标信息ID
     * @return 结果
     */
    @Override
    public int deleteDsmQuotaById(Long quotaId)
    {
        return dsmQuotaMapper.deleteDsmQuotaById(quotaId);
    }
}
