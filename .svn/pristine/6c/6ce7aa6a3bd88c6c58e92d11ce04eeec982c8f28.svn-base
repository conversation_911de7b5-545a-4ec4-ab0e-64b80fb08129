import request from '@/utils/request'

// 查询接口权限列表
export function listDscMasterSystem(query) {
  return request({
    url: '/dsc/dscMasterSystem/list',
    method: 'get',
    params: query
  })
}

// 查询接口权限详细
export function getDscMasterSystem(masterSystemId) {
  return request({
    url: '/dsc/dscMasterSystem/' + masterSystemId,
    method: 'get'
  })
}

// 新增接口权限
export function addDscMasterSystem(data) {
  return request({
    url: '/dsc/dscMasterSystem',
    method: 'post',
    data: data
  })
}

// 修改接口权限
export function updateDscMasterSystem(data) {
  return request({
    url: '/dsc/dscMasterSystem',
    method: 'put',
    data: data
  })
}

// 删除接口权限
export function delDscMasterSystem(masterSystemId) {
  return request({
    url: '/dsc/dscMasterSystem/' + masterSystemId,
    method: 'delete'
  })
}

// 导出接口权限
export function exportDscMasterSystem(query) {
  return request({
    url: '/dsc/dscMasterSystem/export',
    method: 'get',
    params: query
  })
}