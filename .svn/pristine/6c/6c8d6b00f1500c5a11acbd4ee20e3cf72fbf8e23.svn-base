package com.dqms.task.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.basic.domain.SysAgent;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysAgentMapper;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.entity.SysRole;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.DictUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dqm.service.IDqmValidationRuleCateService;
import com.dqms.dsc.service.IDscEntityService;
import com.dqms.framework.web.service.TokenService;
import com.dqms.mdm.service.IMdmRegistryService;
import com.dqms.system.domain.SysUserTaskGroup;
import com.dqms.system.mapper.SysUserTaskGroupMapper;
import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskClass;
import com.dqms.task.domain.EtlTaskGroup;
import com.dqms.task.domain.vo.EtlTaskVo;
import com.dqms.task.enums.EtlTaskType;
import com.dqms.task.mapper.EtlTaskClassMapper;
import com.dqms.task.mapper.EtlTaskGroupMapper;
import com.dqms.task.mapper.EtlTaskMapper;
import com.dqms.task.service.IEtlTaskService;

/**
 * 任务管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-11
 */
@Service
public class EtlTaskServiceImpl implements IEtlTaskService
{
    @Autowired
    private EtlTaskMapper etlTaskMapper;
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private EtlTaskClassMapper etlTaskClassMapper;
    
    @Autowired
    private EtlTaskGroupMapper etlTaskGroupMapper;
    
    @Autowired
    private SysAgentMapper sysAgentMapper;
    
    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;
    
    @Autowired
    private SysSystemMapper sysSystemMapper;
    
    @Autowired
    private IMdmRegistryService mdmRegistryService;
    
    @Autowired
    private IDscEntityService dscEntityService;
    
    @Autowired
    private IDqmValidationRuleCateService dqmValidationRuleCateService;    
    
    @Autowired
    private SysUserTaskGroupMapper userTaskGroupMapper;
    /**
     * 查询任务管理
     *
     * @param taskId 任务管理ID
     * @return 任务管理
     */
    @Override
    public EtlTask selectEtlTaskById(Long taskId)
    {
        return etlTaskMapper.selectEtlTaskById(taskId);
    }

    /**
     * 查询任务管理列表
     *
     * @param etlTask 任务管理
     * @return 任务管理
     */
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTask> selectEtlTaskList(EtlTask etlTask)
    {
        return etlTaskMapper.selectEtlTaskList(etlTask);
    }
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTaskVo> selectEtlTaskVoList(EtlTask etlTask)
    {
        return etlTaskMapper.selectEtlTaskVoList(etlTask);
    }
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTask> selectEtlTaskListPage(EtlTask etlTask)
    {
    	etlTask.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        return etlTaskMapper.selectEtlTaskListPage(etlTask);
    }
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTask> selectEtlTaskRelByGroupList(EtlTask etlTask)
    {
        return etlTaskMapper.selectEtlTaskRelByGroupList(etlTask);
    }
    /**
     * 新增任务管理
     *
     * @param etlTask 任务管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEtlTask(EtlTask etlTask)
    {
    	EtlTask t = etlTaskMapper.selectEtlTaskByTaskName(etlTask.getTaskName());
    	if(t!=null) {
        	throw new CustomException("任务名称已经存在！");
        }
    	//验证用户是否有分组的管理权限
    	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
    	boolean flag=false;
    	for(SysRole role : roles) {
    		if(role.getDataScope().equals("6")) {
    			flag=true;
    		}
    	}
    	if(flag) {
    		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
    		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
    		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
    		if(groups==null||groups.size()==0) {
    			throw new CustomException("用户没有该分组管理权限！");
    		}
    	}
    	
    	etlTask.setCreateTime(DateUtils.getNowDate());
    	etlTask.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	etlTask.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	etlTask.setUpdateTime(DateUtils.getNowDate());
    	etlTask.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	etlTask.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	int i = etlTaskMapper.insertEtlTask(etlTask);
    	if(etlTask.getType().equals(EtlTaskType.MDM.name())&&etlTask.getIds()!=null&&etlTask.getIds().length>0) {
    		mdmRegistryService.addMdmRegistryAndTaskRel(etlTask.getTaskId(), etlTask.getIds());
    	}else if(etlTask.getType().equals(EtlTaskType.DQM.name())&&etlTask.getIds()!=null&&etlTask.getIds().length>0) {
    		dqmValidationRuleCateService.addDqmValidationRuleCateAndTaskRel(etlTask.getTaskId(), etlTask.getIds());
    	}else if(etlTask.getType().equals(EtlTaskType.DLC.name())&&etlTask.getIds()!=null&&etlTask.getIds().length>0) {
    		dscEntityService.addDscEntityAndTaskRel(etlTask.getTaskId(), etlTask.getIds());
    	}
        return i;
    }

    /**
     * 修改任务管理
     *
     * @param etlTask 任务管理
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEtlTask(EtlTask etlTask)
    {
    	//验证用户是否有分组的管理权限
    	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
    	boolean flagR=false;
    	for(SysRole role : roles) {
    		if(role.getDataScope().equals("6")) {
    			flagR=true;
    		}
    	}
    	if(flagR) {
    		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
    		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
    		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
    		if(groups==null||groups.size()==0) {
    			throw new CustomException("用户没有该分组管理权限！");
    		}
    	}
    	
    	EtlTask t = etlTaskMapper.selectEtlTaskByTaskName(etlTask.getTaskName());
    	if(t!=null&&!etlTask.getTaskId().equals(t.getTaskId())) {
        	throw new CustomException("任务名称已经存在！");
        }
    	etlTask.setUpdateTime(DateUtils.getNowDate());
    	etlTask.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	etlTask.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
    	
    	if(etlTask.getType().equals(EtlTaskType.MDM.name())) {
    		mdmRegistryService.delMdmRegistryAndTaskRel(etlTask.getTaskId());
    		if(etlTask.getIds()!=null&&etlTask.getIds().length>0) {
    			mdmRegistryService.addMdmRegistryAndTaskRel(etlTask.getTaskId(), etlTask.getIds());	
    		}
    	}else if(etlTask.getType().equals(EtlTaskType.DQM.name())) {
    		dqmValidationRuleCateService.delDqmValidationRuleCateAndTaskRel(etlTask.getTaskId());
    		if(etlTask.getIds()!=null&&etlTask.getIds().length>0) {
    			dqmValidationRuleCateService.addDqmValidationRuleCateAndTaskRel(etlTask.getTaskId(), etlTask.getIds());
    		}
    		
    	}else if(etlTask.getType().equals(EtlTaskType.DLC.name())) {
    		dscEntityService.delDscEntityAndTaskRel(etlTask.getTaskId());
    		if(etlTask.getIds()!=null&&etlTask.getIds().length>0) {
    			dscEntityService.addDscEntityAndTaskRel(etlTask.getTaskId(), etlTask.getIds());
    		}
    	}
    	EtlTask et =new EtlTask();
    	boolean flag=false;
    	if(etlTask.getAgentId()==null) {
    		et.setAgentId(0L);
    		flag=true;
    	}
    	if(StringUtils.isEmpty(etlTask.getTimeCheck())) {
    		et.setTimeCheck("00:00");
    		flag=true;
    	}
    	if(etlTask.getPreDatasourceId()==null) {
    		et.setPreDatasourceId(0L);
    		flag=true;
    	}
    	if(etlTask.getPosDatasourceId()==null) {
    		et.setPosDatasourceId(0L);
    		flag=true;
    	}
    	if(etlTask.getDatasourceId()==null) {
    		et.setDatasourceId(0L);
    		flag=true;
    	}
    	if(flag) {
    		et.setTaskId(etlTask.getTaskId());
    		etlTaskMapper.updateEtlTaskNull(et);
    	}
    	
        return etlTaskMapper.updateEtlTask(etlTask);
    }

    /**
     * 批量删除任务管理
     *
     * @param taskIds 需要删除的任务管理ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskByIds(Long[] taskIds)
    {
    	for(Long taskId : taskIds) {
        	//验证用户是否有分组的管理权限
        	EtlTask etlTask = etlTaskMapper.selectEtlTaskById(taskId);
        	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        	boolean flagR=false;
        	for(SysRole role : roles) {
        		if(role.getDataScope().equals("6")) {
        			flagR=true;
        		}
        	}
        	if(flagR) {
        		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
        		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
        		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
        		if(groups==null||groups.size()==0) {
        			throw new CustomException("用户没有该分组管理权限！");
        		}
        	}
    	}

    	try{
        	return etlTaskMapper.deleteEtlTaskByIds(taskIds);
		}catch(Exception e){
    		if(e.getMessage().contains("fk_etl_task_schedule_task")){
    			throw new RuntimeException("任务已配置调度计划，请先删除调度计划");
			}else{
				e.printStackTrace();
				throw new RuntimeException(e.getMessage());
			}
		}
    }

    /**
     * 删除任务管理信息
     *
     * @param taskId 任务管理ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskById(Long taskId)
    {
    	//验证用户是否有分组的管理权限
    	EtlTask etlTask = etlTaskMapper.selectEtlTaskById(taskId);
    	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
    	boolean flagR=false;
    	for(SysRole role : roles) {
    		if(role.getDataScope().equals("6")) {
    			flagR=true;
    		}
    	}
    	if(flagR) {
    		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
    		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
    		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
    		if(groups==null||groups.size()==0) {
    			throw new CustomException("用户没有该分组管理权限！");
    		}
    	}
    	
        return etlTaskMapper.deleteEtlTaskById(taskId);
    }
    
    /**
     * 查询未关联任务管理列表
     */
    @Override
    public List<EtlTask> selectEtlTaskListW(EtlTask etlTask)
    {
        return etlTaskMapper.selectEtlTaskListW(etlTask);
    }
    
    /**
     * 查询任务管理列表
     *
     * @param etlTask 任务管理
     * @return 任务管理
     */
    @Override
    public List<EtlTask> selectEtlTaskChainList(EtlTask etlTask)
    {
    	Map<Long, EtlTask> etlTaskMap = new HashMap<Long, EtlTask>();
    	Long[] tasks= {etlTask.getTaskId()};
    	List<EtlTask> list = new ArrayList<EtlTask>();
    	EtlTask task = etlTaskMapper.selectEtlTaskById(etlTask.getTaskId());
    	if(task.getAxesx().equals("0")) {
    		task.setAxesx(800+"");
		}
		if(task.getAxesy().equals("0")) {
			task.setAxesy(50+"");
		}
    	list.add(task);
    	selectEtlTaskChainPos(tasks,list,etlTaskMap,1100);
    	selectEtlTaskChainPre(tasks,list,etlTaskMap,500);
        return list;
    }
    
    @Override
    public List<EtlTask> selectEtlTaskGroupList(EtlTask etlTask)
    {
    	Map<Long, EtlTask> etlTaskMap = new HashMap<Long, EtlTask>();
    	
    	List<EtlTask> list = etlTaskMapper.selectEtlTaskByGroupToStart(etlTask.getTaskGroupId());
    	if(list!=null&&list.size()>0) {
    		Long[] taskss=new Long[list.size()];
    		int i=0;
	    	for(EtlTask task :list) {
	    		taskss[i++]=task.getTaskId();
	    		if(task.getAxesx().equals("0")) {
	        		task.setAxesx(800+"");
	    		}
	    		if(task.getAxesy().equals("0")) {
	    			task.setAxesy(50+"");
	    		}
	        	
	    	}
	    	selectEtlTaskChainPos(taskss,list,etlTaskMap,100);
    	}
        return list;
    }
    
    public void selectEtlTaskChainPos(Long[] tasks,List<EtlTask> list,Map<Long, EtlTask> etlTaskMap,int x)
    {
    	List<EtlTask> listL = etlTaskMapper.selectEtlTaskListByPos(tasks);
    	if(listL!=null&&listL.size()>0) {
    		Map<Long, Long> map = new HashMap<Long, Long>();
    		int y=0;
    		for(EtlTask t : listL) {
    			y=y+50;
    			if(etlTaskMap.get(t.getTaskId())==null) {
    				list.add(t);
    				etlTaskMap.put(t.getTaskId(), t);
    				map.put(t.getTaskId(), t.getTaskId());
    			}else {
    				t=etlTaskMap.get(t.getTaskId());
    				if(t.getAxesx().equals("0")) {
        				t.setAxesx(x+"");
        			}
        			if(t.getAxesy().equals("0")) {
        				t.setAxesy(y+"");
        			}
    			}
    			
    		}
    		if(map.size()>0) {
	    		Long[] taskss=new Long[map.size()];
	    		int i=0;
	    		for(Map.Entry<Long, Long> entry : map.entrySet()){
	    			taskss[i++]=entry.getKey();
	    		}
	    		selectEtlTaskChainPos(taskss,list,etlTaskMap,x+300);
    		}
    	}
    }
    public void selectEtlTaskChainPre(Long[] tasks,List<EtlTask> list,Map<Long, EtlTask> etlTaskMap,int x)
    {
    	List<EtlTask> listL = etlTaskMapper.selectEtlTaskListByPre(tasks);
    	if(listL!=null&&listL.size()>0) {
    		Map<Long, Long> map = new HashMap<Long, Long>();
    		int y=0;
    		for(EtlTask t : listL) {
    			y=y+50;
    			if(etlTaskMap.get(t.getTaskId())==null) {
    				list.add(t);
    				etlTaskMap.put(t.getTaskId(), t);
    				map.put(t.getTaskId(), t.getTaskId());
    			}else {
    				t=etlTaskMap.get(t.getTaskId());
    				if(t.getAxesx().equals("0")) {
        				t.setAxesx(x+"");
        			}
        			if(t.getAxesy().equals("0")) {
        				t.setAxesy(y+"");
        			}
    			}
    			
    		}
    		if(map.size()>0) {
	    		Long[] taskss=new Long[map.size()];
	    		int i=0;
	    		for(Map.Entry<Long, Long> entry : map.entrySet()){
	    			taskss[i++]=entry.getKey();
	    		}
	    		selectEtlTaskChainPre(taskss,list,etlTaskMap,x-300);
    		}
    	}
    } 
    /**
     *修改预计时间
     * 
     * @param taskIds 任务监控ID
     * @return 结果
     */
    @Override
    public int updateExpectEndTime(Long taskId)
    {
        return etlTaskMapper.updateExpectEndTime(taskId);
    }
    
    /**
     * 导入任务数据
     * 
     * @param etlTaskRelationList 任务数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    @Transactional
    public String importEtlTask(List<EtlTaskVo> etlTaskList, Boolean isUpdateSupport)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (StringUtils.isNull(etlTaskList) || etlTaskList.size() == 0)
        {
            throw new CustomException("导入任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (EtlTaskVo vo : etlTaskList)
        {
            try
            {
            	EtlTask t =new EtlTask();
            	BeanUtils.copyBeanProp(t, vo);
            	EtlTask task = etlTaskMapper.selectEtlTaskByTaskName(vo.getTaskName());
                if(StringUtils.isNull(task)||(StringUtils.isNotNull(task)&&isUpdateSupport)) {
                	EtlTaskClass etlTaskClass = etlTaskClassMapper.selectEtlTaskClassByName(vo.getTaskClassName());
                	if(StringUtils.isNull(etlTaskClass)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 分类名称未定义");
                        continue;
                	}
                	t.setTaskClassId(etlTaskClass.getTaskClassId());
                	EtlTaskGroup etlTaskGroup = etlTaskGroupMapper.selectEtlTaskGroupByName(vo.getTaskGroupName());
                	if(StringUtils.isNull(etlTaskGroup)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 分组名称未定义");
                        continue;
                	}
                	t.setTaskGroupId(etlTaskGroup.getTaskGroupId());
                	SysSystem sysSystem = sysSystemMapper.selectSysSystemByName(vo.getSystemName());
                	if(StringUtils.isNull(sysSystem)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 系统名称未定义");
                        continue;
                	}
                	t.setSystemId(sysSystem.getSystemId());
                	String type=DictUtils.getDictValue("etl_task_type", vo.getTypeLabel());
                	if(StringUtils.isEmpty(type)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 类型名称未定义");
                        continue;
                	}
                	if(vo.getTypeLabel().indexOf("自定义")!=-1){
						if(StringUtils.isNull(vo.getAgentName())){
							failureNum++;
							failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 代理机名称未填写");
							continue;
						}
						if(StringUtils.isNull(vo.getScriptPath())){
							failureNum++;
							failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 脚本路径未填写");
							continue;
						}
					}
                	t.setType(type);
                	String flag=DictUtils.getDictValue("sys_yes_no", vo.getFlagLabel());
                	if(StringUtils.isEmpty(flag)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 是否重要名称未定义");
                        continue;
                	}
                	t.setFlag(flag);
                	String level=DictUtils.getDictValue("sys_important_level", vo.getLevelLabel());
                	if(StringUtils.isEmpty(level)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 优先级名称未定义");
                        continue;
                	}
                	t.setLevel(level);
                	String status=DictUtils.getDictValue("etl_task_status", vo.getStatusLabel());
                	if(StringUtils.isEmpty(status)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 状态名称未定义");
                        continue;
                	}
                	t.setStatus(status);
                	String errorContinue=DictUtils.getDictValue("sys_yes_no", vo.getErrorContinueLabel());
                	if(StringUtils.isEmpty(errorContinue)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 失败终止名称未填写");
                        continue;
                	}
                	t.setErrorContinue(errorContinue);     
                	String dateCheck=DictUtils.getDictValue("etl_task_date_check", vo.getDateCheckLabel());
                	if(StringUtils.isEmpty(dateCheck)){
                		failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 日期校验未填写");
                        continue;
                	}
                	t.setDateCheck(dateCheck);
                	if(StringUtils.isNotEmpty(vo.getAgentName())) {
                		SysAgent sysAgent = sysAgentMapper.selectSysAgentByName(vo.getAgentName());
                    	if(StringUtils.isNull(sysAgent)){
                    		failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 代理机名称未定义");
                            continue;
                    	}
                    	t.setAgentId(sysAgent.getAgentId());
                	}
                	
                	if(StringUtils.isNotEmpty(vo.getPreDatasourceName())) {
                		SysDatasource preSysDatasource = sysDatasourceMapper.selectSysDatasourceByName(vo.getPreDatasourceName());
                    	if(StringUtils.isNull(preSysDatasource)){
                    		failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 前置数据源名称未定义");
                            continue;
                    	}
                    	t.setPreDatasourceId(preSysDatasource.getDatasourceId());
                	}
                	
                	if(StringUtils.isNotEmpty(vo.getPosDatasourceName())) {
                		SysDatasource posSysDatasource = sysDatasourceMapper.selectSysDatasourceByName(vo.getPosDatasourceName());
                    	if(StringUtils.isNull(posSysDatasource)){
                    		failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 后置数据源名称未定义");
                            continue;
                    	}
                    	t.setPosDatasourceId(posSysDatasource.getDatasourceId());
                	}



                	if(StringUtils.isNotEmpty(vo.getDatasourceName())) {
                		SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceByName(vo.getDatasourceName());
                    	if(StringUtils.isNull(sysDatasource)){
                    		failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 数据源名称未定义");
                            continue;
                    	}
                    	t.setDatasourceId(sysDatasource.getDatasourceId());
                	}
                	if(t.getAxesx()==null||t.getAxesx().equals("")) {
                		t.setAxesx("0");
                	}
                	if(t.getAxesy()==null||t.getAxesy().equals("")) {
                		t.setAxesy("0");
                	}
                	
                }
                if (StringUtils.isNull(task))
                {
                    t.setCreateTime(DateUtils.getNowDate());
                	t.setCreateId(loginUser.getUser().getUserId());
                	t.setCreateBy(loginUser.getUser().getNickName());
                	t.setUpdateTime(DateUtils.getNowDate());
                	t.setUpdateId(loginUser.getUser().getUserId());
                	t.setUpdateBy(loginUser.getUser().getNickName());
                    this.insertEtlTask(t);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + vo.getTaskName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                	t.setTaskId(task.getTaskId());
                	t.setUpdateTime(DateUtils.getNowDate());
                	t.setUpdateId(loginUser.getUser().getUserId());
                	t.setUpdateBy(loginUser.getUser().getNickName());
                    this.updateEtlTask(t);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + vo.getTaskName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + vo.getTaskName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
