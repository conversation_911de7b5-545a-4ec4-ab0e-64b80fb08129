import request from '@/utils/request'

// 查询维度字典关系列表
export function listDsmDimensionDetailRel(query) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/list',
    method: 'get',
    params: query
  })
}

export function listDsmDimensionDetailUnRel(query) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/unlist',
    method: 'get',
    params: query
  })
}

// 查询维度字典关系详细
export function getDsmDimensionDetailRel(souDimensionDetailId) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/' + souDimensionDetailId,
    method: 'get'
  })
}

// 新增维度字典关系
export function addDsmDimensionDetailRel(data) {
  return request({
    url: '/dsm/dsmDimensionDetailRel',
    method: 'post',
    data: data
  })
}

// 修改维度字典关系
export function updateDsmDimensionDetailRel(data) {
  return request({
    url: '/dsm/dsmDimensionDetailRel',
    method: 'put',
    data: data
  })
}

// 删除维度字典关系
export function delDsmDimensionDetailRel(souDimensionDetailId) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/' + souDimensionDetailId,
    method: 'delete'
  })
}
// 删除维度字典关系
export function delDsmDimensionDetailRelByPk(data) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/delDsmDimensionDetailRelByPk',
    method: 'put',
    data: data
  })
}
// 导出维度字典关系
export function exportDsmDimensionDetailRel(query) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/export',
    method: 'get',
    params: query
  })
}

// 导出维度字典关系模板
export function exportTemplate_rel(query) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/exportTemplate',
    method: 'get',
    params: query
  })
}
export function exportTemplate_rel_demo(query) {
  return request({
    url: '/dsm/dsmDimensionDetailRel/exportTemplateDemo',
    method: 'get',
    params: query
  })
}
export function exportimportDimensionMdmDataEntityShip(query) {
  return request({
    url: '/dsm/dsmMdmRel/exportimportDimensionMdmDataEntityShip/',
    method: 'get',
    params: query
  })
}
