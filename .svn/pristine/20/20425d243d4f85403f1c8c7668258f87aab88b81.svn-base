<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmQuotaClassMapper">
    
    <resultMap type="DsmQuotaClass" id="DsmQuotaClassResult">
        <result property="quotaClassId"    column="quota_class_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDsmQuotaClassVo">
        select quota_class_id, parent_id, ancestors, class_name, class_name_full, order_num, create_by, update_by, create_id, update_id, create_time, update_time from dsm_quota_class
    </sql>

    <select id="selectDsmQuotaClassList" parameterType="DsmQuotaClass" resultMap="DsmQuotaClassResult">
        <include refid="selectDsmQuotaClassVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="classNameFull != null  and classNameFull != ''"> and class_name_full = #{classNameFull}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
    </select>
    
    <select id="selectDsmQuotaClassById" parameterType="Long" resultMap="DsmQuotaClassResult">
        <include refid="selectDsmQuotaClassVo"/>
        where quota_class_id = #{quotaClassId}
    </select>
        
    <insert id="insertDsmQuotaClass" parameterType="DsmQuotaClass" useGeneratedKeys="true" keyProperty="quotaClassId">
        insert into dsm_quota_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="className != null">class_name,</if>
            <if test="classNameFull != null">class_name_full,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="className != null">#{className},</if>
            <if test="classNameFull != null">#{classNameFull},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDsmQuotaClass" parameterType="DsmQuotaClass">
        update dsm_quota_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="classNameFull != null">class_name_full = #{classNameFull},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where quota_class_id = #{quotaClassId}
    </update>

    <delete id="deleteDsmQuotaClassById" parameterType="Long">
        delete from dsm_quota_class where quota_class_id = #{quotaClassId}
    </delete>

    <delete id="deleteDsmQuotaClassByIds" parameterType="String">
        delete from dsm_quota_class where quota_class_id in 
        <foreach item="quotaClassId" collection="array" open="(" separator="," close=")">
            #{quotaClassId}
        </foreach>
    </delete>
    
    <select id="selectDsmQuotaClassByName" parameterType="String" resultMap="DsmQuotaClassResult">
        <include refid="selectDsmQuotaClassVo"/>
        where class_name = #{className}
    </select>
    
    <select id="selectChildrenClassById" parameterType="Long" resultMap="DsmQuotaClassResult">
		select * from dsm_quota_class where find_in_set(#{classId}, ancestors)
	</select>
	
	<update id="updateClassChildren" parameterType="java.util.List">
	    update dsm_quota_class set ancestors =
	    <foreach collection="dsmQuotaClass" item="item" index="index"
	        separator=" " open="case quota_class_id" close="end">
	        when #{item.quotaClassId} then #{item.ancestors}
	    </foreach>
	    where quota_class_id in
	    <foreach collection="dsmQuotaClass" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.quotaClassId}
	    </foreach>
	</update>
	
	<update id="updateClassNameFullChildren" parameterType="java.util.List">
	    update dsm_quota_class set ancestors =
	    <foreach collection="dsmQuotaClass" item="item" index="index"
	        separator=" " open="case quota_class_id" close="end">
	        when #{item.quotaClassId} then #{item.classNameFull}
	    </foreach>
	    where quota_class_id in
	    <foreach collection="dsmQuotaClass" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.quotaClassId}
	    </foreach>
	</update>      
</mapper>