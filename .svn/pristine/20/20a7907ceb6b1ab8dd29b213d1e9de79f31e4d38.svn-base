<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmDimensionDetailMapper">
    
    <resultMap type="DsmDimensionDetail" id="DsmDimensionDetailResult">
        <result property="dimensionDetailId"    column="dimension_detail_id"    />
        <result property="dimensionId"    column="dimension_id"    />
        <result property="dimensionName"    column="dimension_name"    />
        <result property="dimensionCode"    column="dimension_code"    />
        <result property="detailName"    column="detail_name"    />
        <result property="detailCode"    column="detail_code"    />
        <result property="operateType"    column="operate_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <association property="dsmDimension"    column="dimension_id" javaType="DsmDimension" resultMap="DsmDimensionResult" />

    </resultMap>

    <resultMap type="DsmDimensionDetailVo" id="DsmDimensionDetailVoResult">
        <result property="dimensionId"    column="dimension_id"    />
        <result property="dimensionName"    column="dimension_name"    />
        <result property="dimensionCode"    column="dimension_code"    />
        <result property="dimensionType"    column="dimension_type"    />
        <result property="dimensionTypeName"    column="dimension_type_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="showType"    column="show_type"    />
        <result property="showTypeName"    column="show_type_name"    />
        <result property="dataType"    column="data_type"    />
        <result property="dataTypeName"    column="data_type_name"    />
        <result property="status"    column="status"    />
        <result property="statusName"    column="status_name"    />
        <result property="execSql"    column="exec_sql"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dimensionClassId"    column="dimension_class_id"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="taskMsg"    column="task_msg"    />
        <result property="taskId"    column="task_id"    />
        <result property="className"    column="class_name"    />
        <result property="classNameFull"    column="class_name_full"    />
        <result property="discernFlag"    column="discern_flag"    />
        <result property="checkFlag"    column="check_flag"    />

        <result property="dimensionDetailId"    column="dimension_detail_id"    />
        <result property="detailName"    column="detail_name"    />
        <result property="detailCode"    column="detail_code"    />
        <result property="operateType"    column="operate_type"    />
        <result property="operateTypeName"    column="operate_type_name"    />
        <result property="detailStatus"    column="detail_status"    />
    </resultMap>

    <resultMap type="DsmDimension" id="DsmDimensionResult">
        <result property="dimensionId"    column="dimension_id"    />
        <result property="dimensionName"    column="dimension_name"    />
        <result property="dimensionCode"    column="dimension_code"    />
    </resultMap>

    <sql id="selectDsmDimensionDetailVo">
select
	t.dimension_detail_id,
	dd.dimension_name ,
		concat(dd.dimension_code,'-',ddc.class_name) as dimension_code,

	t.dimension_id,
	t.detail_name,
	t.detail_code,
	t.operate_type,
	t.status
from
	dsm_dimension_detail t
	left join dsm_dimension dd on t.dimension_id =dd.dimension_id
	left join dsm_dimension_class ddc on dd.dimension_class_id =ddc.dimension_class_id
	 </sql>

    <select id="selectDsmDimensionDetailList" parameterType="DsmDimensionDetail" resultMap="DsmDimensionDetailResult">
        <include refid="selectDsmDimensionDetailVo"/>
        <where>  
            <if test="dimensionId != null and dimensionId != '' "> and dd.dimension_id = #{dimensionId}</if>
            <if test="dimensionName != null and dimensionName != '' "> and dd.dimension_name = #{dimensionName}</if>
            <if test="dimensionCode != null and dimensionCode != '' "> and dd.dimension_code = #{dimensionCode}</if>
            <if test="detailName != null  and detailName != ''"> and t.detail_name like concat('%', #{detailName}, '%')</if>
            <if test="detailCode != null  and detailCode != ''"> and t.detail_code = #{detailCode}</if>
            <if test="operateType != null  and operateType != ''"> and t.operate_type = #{operateType}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDsmDimensionDetailById" parameterType="Long" resultMap="DsmDimensionDetailResult">
        <include refid="selectDsmDimensionDetailVo"/>
        where dimension_detail_id = #{dimensionDetailId}
    </select>
        
    <insert id="insertDsmDimensionDetail" parameterType="DsmDimensionDetail" useGeneratedKeys="true" keyProperty="dimensionDetailId">
        insert into dsm_dimension_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dimensionId != null">dimension_id,</if>
            <if test="detailName != null and detailName != ''">detail_name,</if>
            <if test="detailCode != null and detailCode != ''">detail_code,</if>
            <if test="operateType != null and operateType != ''">operate_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dimensionId != null">#{dimensionId},</if>
            <if test="detailName != null and detailName != ''">#{detailName},</if>
            <if test="detailCode != null and detailCode != ''">#{detailCode},</if>
            <if test="operateType != null and operateType != ''">#{operateType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateDsmDimensionDetail" parameterType="DsmDimensionDetail">
        update dsm_dimension_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="dimensionId != null">dimension_id = #{dimensionId},</if>
            <if test="detailName != null and detailName != ''">detail_name = #{detailName},</if>
            <if test="detailCode != null and detailCode != ''">detail_code = #{detailCode},</if>
            <if test="operateType != null and operateType != ''">operate_type = #{operateType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where dimension_detail_id = #{dimensionDetailId}
    </update>

    <delete id="deleteDsmDimensionDetailById" parameterType="Long">
        delete from dsm_dimension_detail where dimension_detail_id = #{dimensionDetailId}
    </delete>

    <delete id="deleteDsmDimensionDetailByIds" parameterType="String">
        delete from dsm_dimension_detail where dimension_detail_id in 
        <foreach item="dimensionDetailId" collection="array" open="(" separator="," close=")">
            #{dimensionDetailId}
        </foreach>
    </delete>

    <delete id="deleteDsmDimensionDetailByDimensionId" parameterType="Long">
        delete from dsm_dimension_detail where dimension_id = #{dimensionId}
    </delete>

    <sql id="selectDsmDimensionDetailVo2">
        SELECT
            t.dimension_id,
            t.dimension_name,
            t.dimension_code,
            t.dimension_type,
						typ.dict_label dimension_type_name,
            t.system_id,
            e.NAME system_name,
            t.datasource_id,
            f.`name` datasource_name,
            t.show_type,
						sho.dict_label show_type_name,
            t.data_type,
						dat.dict_label data_type_name,
            t.STATUS,
						sta.dict_label STATUS_name,
            t.exec_sql,
            t.create_by,
            t.update_by,
            t.create_id,
            t.update_id,
            t.create_time,
            t.update_time,
            t.dimension_class_id,
            t.task_status,
            t.task_id,
            t.task_msg,
            c.class_name,
            c.class_name_full,
            t.discern_flag,
            t.check_flag,
            d.dimension_detail_id,
            d.detail_code,
            d.detail_name,
            d.operate_type,
						ope.dict_label operate_type_name,
            d.`status` detail_status
        FROM
            dsm_dimension t
            LEFT JOIN dsm_dimension_class c ON t.dimension_class_id = c.dimension_class_id
            LEFT JOIN dsm_dimension_detail d ON t.dimension_id = d.dimension_id
            LEFT JOIN sys_system e ON e.system_id = t.system_id
            LEFT JOIN sys_datasource f ON t.datasource_id = f.datasource_id
						left join sys_dict_data typ on typ.dict_value = t.dimension_type and typ.dict_type = 'dsm_dimension_type'
						left join sys_dict_data sho on sho.dict_value = t.show_type and sho.dict_type = 'dsm_dimension_show_type'
						left join sys_dict_data dat on dat.dict_value = t.data_type and dat.dict_type = 'sys_data_type'
						left join sys_dict_data sta on sta.dict_value = t.`status` and sta.dict_type = 'dsm_standard_status'
						left join sys_dict_data ope on ope.dict_value = d.operate_type and ope.dict_type = 'etl_task_trigger_type'
    </sql>
    <select id="selectDsmDimensionDetailVoList" parameterType="DsmDimensionDetailVo" resultMap="DsmDimensionDetailVoResult">
        <include refid="selectDsmDimensionDetailVo2"/>
        <where>
            <if test="dimensionName != null  and dimensionName != ''"> and t.dimension_name like concat('%', #{dimensionName}, '%')</if>
            <if test="dimensionCode != null  and dimensionCode != ''"> and t.dimension_code = #{dimensionCode}</if>
            <if test="dimensionType != null  and dimensionType != ''"> and t.dimension_type = #{dimensionType}</if>
            <if test="systemId != null "> and t.system_id = #{systemId}</if>
            <if test="datasourceId != null "> and t.datasource_id = #{datasourceId}</if>
            <if test="showType != null  and showType != ''"> and t.show_type = #{showType}</if>
            <if test="dataType != null  and dataType != ''"> and t.data_type = #{dataType}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="execSql != null  and execSql != ''"> and t.exec_sql = #{execSql}</if>
            <if test="dimensionClassId != null  and dimensionClassId != ''"> and (c.ancestors like concat('%,', #{dimensionClassId}, '%') or t.dimension_class_id = #{dimensionClassId})</if>
            <if test="taskStatus != null  and taskStatus != ''"> and t.task_status = #{taskStatus}</if>
            <if test="taskId != null  and taskId != ''"> and t.task_id = #{taskId}</if>
            <if test="discernFlag != null "> and t.discern_flag = #{discernFlag}</if>
            <if test="checkFlag != null "> and t.check_flag = #{checkFlag}</if>
        </where>
    </select>



    <select id="getDsmDimensionDetailById" parameterType="Long" resultMap="DsmDimensionDetailResult">
       select
	t.dimension_detail_id,
	dd.dimension_name ,
		concat(dd.dimension_code,'-',ddc.class_name) as dimension_code,

	t.dimension_id,
	t.detail_name,
	t.detail_code,
	t.operate_type,
	t.status
from
	dsm_dimension_detail t
	left join dsm_dimension dd on t.dimension_id =dd.dimension_id
	left join dsm_dimension_class ddc on dd.dimension_class_id =ddc.dimension_class_id
        where dimension_detail_id = #{dimensionDetailId}
    </select>

</mapper>