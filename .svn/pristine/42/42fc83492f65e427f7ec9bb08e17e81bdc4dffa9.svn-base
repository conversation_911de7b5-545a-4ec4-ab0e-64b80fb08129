<template>
   <el-table v-loading="loading" :data="dsmStandardMdmRelList">
    <el-table-column label="引用情况">
     <!-- <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="系统" align="center" prop="systemName" />
      <el-table-column
        label="数据源"
        align="center"
        prop="datasourceName"
      />
      <el-table-column label="用户" align="center" prop="tableSchema" />
      <el-table-column label="表" align="center" prop="tableName" />
      <el-table-column label="字段" align="center" prop="propName" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot="header" slot-scope="scope">
          <el-button
            slot="append"
            icon="el-icon-share"
            @click="addRelMdmForm"
            size="mini"
            type="text"
            :disabled="iFDisable == 'read'"
            >添加</el-button
          >
        </template>
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="delRelMdm(scope.row, scope.$index)"
            :disabled="iFDisable == 'read'"
            v-hasPermi="['dsm:dsmStandard:remove']"
            >解除</el-button
          >
        </template>
      </el-table-column>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
    props:['loading','dsmStandardMdmRelList','iFDisable'],
    methods:{
      delRelMdm(row,index) {
        this.$emit('delRelMdm',row,index)
      },
      addRelMdmForm() {
        this.$emit('addRelMdmForm')
      }
    }
}
</script>

<style>

</style>
