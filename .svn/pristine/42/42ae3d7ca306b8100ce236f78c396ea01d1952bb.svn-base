import request from '@/utils/request'

// 查询工作流程模型列表
export function listModel(query) {
  return request({
    url: '/wfs/model/list',
    method: 'get',
    params: query
  })
}

// 查询工作流程模型详细
export function getModel(modelId) {
  return request({
    url: '/wfs/model/' + modelId,
    method: 'get'
  })
}

// 新增工作流程模型
export function addModel(data) {
  return request({
    url: '/wfs/model',
    method: 'post',
    data: data
  })
}

// 修改工作流程模型
export function updateModel(data) {
  return request({
    url: '/wfs/model',
    method: 'put',
    data: data
  })
}

// 删除工作流程模型
export function delModel(modelId) {
  return request({
    url: '/wfs/model/' + modelId,
    method: 'delete'
  })
}

// 导出工作流程模型
export function exportModel(query) {
  return request({
    url: '/wfs/model/export',
    method: 'get',
    params: query
  })
}