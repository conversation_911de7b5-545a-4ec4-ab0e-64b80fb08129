package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmModelEntityShip;
import com.dqms.dsm.service.IDsmModelEntityShipService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 模型关系Controller
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@RestController
@RequestMapping("/dsm/dsmModelEntityShip")
public class DsmModelEntityShipController extends BaseController
{
    @Autowired
    private IDsmModelEntityShipService dsmModelEntityShipService;

    /**
     * 查询模型关系列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DsmModelEntityShip dsmModelEntityShip)
    {
        startPage();
        List<DsmModelEntityShip> list = dsmModelEntityShipService.selectDsmModelEntityShipList(dsmModelEntityShip);
        return getDataTable(list);
    }

    /**
     * 导出模型关系列表
     */
    @Log(title = "模型关系", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmModelEntityShip dsmModelEntityShip)
    {
        List<DsmModelEntityShip> list = dsmModelEntityShipService.selectDsmModelEntityShipList(dsmModelEntityShip);
        ExcelUtil<DsmModelEntityShip> util = new ExcelUtil<DsmModelEntityShip>(DsmModelEntityShip.class);
        return util.exportExcel(list, "dsmModelEntityShip");
    }

    /**
     * 获取模型关系详细信息
     */
    @GetMapping(value = "/{modelEntityShipId}")
    public AjaxResult getInfo(@PathVariable("modelEntityShipId") Long modelEntityShipId)
    {
        return AjaxResult.success(dsmModelEntityShipService.selectDsmModelEntityShipById(modelEntityShipId));
    }

    /**
     * 新增模型关系
     */
    @Log(title = "模型关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmModelEntityShip dsmModelEntityShip)
    {
        return toAjax(dsmModelEntityShipService.insertDsmModelEntityShip(dsmModelEntityShip));
    }

    /**
     * 修改模型关系
     */
    @Log(title = "模型关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmModelEntityShip dsmModelEntityShip)
    {
        return toAjax(dsmModelEntityShipService.updateDsmModelEntityShip(dsmModelEntityShip));
    }

    /**
     * 删除模型关系
     */
    @Log(title = "模型关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{modelEntityShipIds}")
    public AjaxResult remove(@PathVariable Long[] modelEntityShipIds)
    {
        return toAjax(dsmModelEntityShipService.deleteDsmModelEntityShipByIds(modelEntityShipIds));
    }
}
