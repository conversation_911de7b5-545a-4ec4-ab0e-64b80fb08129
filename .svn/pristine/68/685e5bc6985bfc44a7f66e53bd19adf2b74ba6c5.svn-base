package com.dqms.dsc.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.entity.SysDept;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.*;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dqm.domain.DqmValidationMould;
import com.dqms.dsc.domain.vo.DscEntityPropClassVo;
import com.dqms.dsm.domain.*;
import com.dqms.dsm.domain.vo.DsmStandardVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.dsc.domain.DscEntityPropClass;
import com.dqms.dsc.domain.DscEntityPropClassTreeSelect;
import com.dqms.dsc.mapper.DscEntityPropClassMapper;
import com.dqms.dsc.service.IDscEntityPropClassService;
import com.dqms.framework.web.service.TokenService;

import javax.management.RuntimeErrorException;

/**
 * 分级分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@Service
public class DscEntityPropClassServiceImpl implements IDscEntityPropClassService
{
    @Autowired
    private DscEntityPropClassMapper dscEntityPropClassMapper;

    @Autowired
    private TokenService tokenService;
    /**
     * 查询分级分类
     *
     * @param entityPropClassId 分级分类ID
     * @return 分级分类
     */
    @Override
    public DscEntityPropClass selectDscEntityPropClassById(Long entityPropClassId)
    {
        return dscEntityPropClassMapper.selectDscEntityPropClassById(entityPropClassId);
    }

    public List<DscEntityPropClass> getGrandList(){
        return dscEntityPropClassMapper.getGrandList();
    }
    public List<DscEntityPropClass> getParentList(DscEntityPropClass dscEntityPropClass){
        return dscEntityPropClassMapper.getParentList(dscEntityPropClass);
    }
    public List<DscEntityPropClass> getEntityPropClassList(DscEntityPropClass dscEntityPropClass){
        return dscEntityPropClassMapper.getEntityPropClassList(dscEntityPropClass);
    }


    /**
     * 查询分级分类列表
     *
     * @param dscEntityPropClass 分级分类
     * @return 分级分类
     */
    @Override
    public List<DscEntityPropClass> selectDscEntityPropClassList(DscEntityPropClass dscEntityPropClass)
    {
        return dscEntityPropClassMapper.selectDscEntityPropClassList(dscEntityPropClass);
    }

    @Override
    public List<DscEntityPropClassVo> selectDscEntityPropClassListVo(DscEntityPropClassVo dscEntityPropClassVo)
    {
        return dscEntityPropClassMapper.selectDscEntityPropClassListVo(dscEntityPropClassVo);
    }

    /**
     * 新增分级分类
     *
     * @param dscEntityPropClass 分级分类
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDscEntityPropClass(DscEntityPropClass dscEntityPropClass)
    {
    	DscEntityPropClass info = dscEntityPropClassMapper.selectDscEntityPropClassById(dscEntityPropClass.getParentId());
    	if(info!=null) {
    		dscEntityPropClass.setAncestors(info.getAncestors() + "," + dscEntityPropClass.getParentId());
    		dscEntityPropClass.setClassNameFull(info.getClassNameFull() + "/" + dscEntityPropClass.getClassName());
    	}else {
    		dscEntityPropClass.setAncestors("0");
    		dscEntityPropClass.setClassNameFull(dscEntityPropClass.getClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dscEntityPropClass.setCreateTime(DateUtils.getNowDate());
    	dscEntityPropClass.setCreateId(loginUser.getUser().getUserId());
    	dscEntityPropClass.setCreateBy(loginUser.getUser().getNickName());
    	dscEntityPropClass.setUpdateTime(DateUtils.getNowDate());
    	dscEntityPropClass.setUpdateId(loginUser.getUser().getUserId());
    	dscEntityPropClass.setUpdateBy(loginUser.getUser().getNickName());
        return dscEntityPropClassMapper.insertDscEntityPropClass(dscEntityPropClass);
    }

    /**
     * 修改分级分类
     *
     * @param dscEntityPropClass 分级分类
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDscEntityPropClass(DscEntityPropClass dscEntityPropClass)
    {
        if(dscEntityPropClass.getEntityPropClassId().equals(dscEntityPropClass.getParentId())){
            throw new RuntimeErrorException(null, "父分类不能是本身！");
        }
    	DscEntityPropClass newC = dscEntityPropClassMapper.selectDscEntityPropClassById(dscEntityPropClass.getParentId());
    	DscEntityPropClass oldC = dscEntityPropClassMapper.selectDscEntityPropClassById(dscEntityPropClass.getEntityPropClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String ancestorsFlag = oldC.getAncestors() + "," + dscEntityPropClass.getEntityPropClassId();
             if(newC.getAncestors().contains(ancestorsFlag)){
                 throw new RuntimeErrorException(null, "父分类不能是本身下级！");
             }
             String newAncestors = newC.getAncestors() + "," + dscEntityPropClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = newC.getClassNameFull() + "/" + dscEntityPropClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             dscEntityPropClass.setAncestors(newAncestors);
             dscEntityPropClass.setClassNameFull(newClassNameFull);
             updateClassChildren(dscEntityPropClass.getEntityPropClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dscEntityPropClass.getEntityPropClassId(), newClassNameFull, oldClassNameFull);
         }else if(newC==null){
             String newAncestors = "0";
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = dscEntityPropClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             dscEntityPropClass.setAncestors(newAncestors);
             dscEntityPropClass.setClassNameFull(newClassNameFull);
             updateClassChildren(dscEntityPropClass.getEntityPropClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dscEntityPropClass.getEntityPropClassId(), newClassNameFull, oldClassNameFull);
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dscEntityPropClass.setUpdateTime(DateUtils.getNowDate());
    	dscEntityPropClass.setUpdateId(loginUser.getUser().getUserId());
    	dscEntityPropClass.setUpdateBy(loginUser.getUser().getNickName());
        return dscEntityPropClassMapper.updateDscEntityPropClass(dscEntityPropClass);
    }

    /**
     * 批量删除分级分类
     *
     * @param entityPropClassIds 需要删除的分级分类ID
     * @return 结果
     */
    @Override
    public int deleteDscEntityPropClassByIds(Long[] entityPropClassIds)
    {
        for(Long entityPropClassId : entityPropClassIds){
            List<DscEntityPropClass> dscEntityPropClasses = dscEntityPropClassMapper.selectChildrenClassById(entityPropClassId);
            if(dscEntityPropClasses!=null && dscEntityPropClasses.size()>0){
                throw new RuntimeErrorException(null, "请先删除子分类！");
            }
        }
        return dscEntityPropClassMapper.deleteDscEntityPropClassByIds(entityPropClassIds);
    }

    /**
     * 删除分级分类信息
     *
     * @param entityPropClassId 分级分类ID
     * @return 结果
     */
    @Override
    public int deleteDscEntityPropClassById(Long entityPropClassId)
    {
        List<DscEntityPropClass> dscEntityPropClasses = dscEntityPropClassMapper.selectChildrenClassById(entityPropClassId);
        if(dscEntityPropClasses!=null && dscEntityPropClasses.size()>0){
            throw new RuntimeErrorException(null, "请先删除子分类！");
        }
        return dscEntityPropClassMapper.deleteDscEntityPropClassById(entityPropClassId);
    }

    /**
     * 修改子元素关系
     *
     * @param entityPropClassId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateClassChildren(Long entityPropClassId, String newAncestors, String oldAncestors)
    {
        List<DscEntityPropClass> children = dscEntityPropClassMapper.selectChildrenClassById(entityPropClassId);
        for (DscEntityPropClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	dscEntityPropClassMapper.updateClassChildren(children);
        }
    }
    @Transactional
    public void updateClassNameFullChildren(Long assetsClassId, String newClassNameFull, String oldClassNameFull)
    {
        List<DscEntityPropClass> children = dscEntityPropClassMapper.selectChildrenClassById(assetsClassId);
        for (DscEntityPropClass child : children)
        {
            child.setClassNameFull(child.getClassNameFull().replace(oldClassNameFull, newClassNameFull));
        }
        if (children.size() > 0)
        {
        	dscEntityPropClassMapper.updateClassNameFullChildren(children);
        }
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param dscEntityPropClass 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<DscEntityPropClassTreeSelect> buildDscEntityPropClassTreeSelect(List<DscEntityPropClass> dscEntityPropClass)
    {
        List<DscEntityPropClass> dscEntityPropClassTrees = buildDscEntityPropClassTree(dscEntityPropClass);
        return dscEntityPropClassTrees.stream().map(DscEntityPropClassTreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 构建前端所需要树结构
     *
     * @param dscEntityPropClass 部门列表
     * @return 树结构列表
     */
    @Override
    public List<DscEntityPropClass> buildDscEntityPropClassTree(List<DscEntityPropClass> dscEntityPropClass)
    {
        List<DscEntityPropClass> returnList = new ArrayList<DscEntityPropClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (DscEntityPropClass item : dscEntityPropClass)
        {
            tempList.add(item.getEntityPropClassId());
        }
        for (Iterator<DscEntityPropClass> iterator = dscEntityPropClass.iterator(); iterator.hasNext();)
        {
        	DscEntityPropClass item = (DscEntityPropClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(item.getParentId()))
            {
                recursionFn(dscEntityPropClass, item);
                returnList.add(item);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = dscEntityPropClass;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<DscEntityPropClass> list, DscEntityPropClass t)
    {
        // 得到子节点列表
        List<DscEntityPropClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DscEntityPropClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<DscEntityPropClass> getChildList(List<DscEntityPropClass> list, DscEntityPropClass t)
    {
        List<DscEntityPropClass> tlist = new ArrayList<DscEntityPropClass>();
        Iterator<DscEntityPropClass> it = list.iterator();
        while (it.hasNext())
        {
        	DscEntityPropClass n = (DscEntityPropClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getEntityPropClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DscEntityPropClass> list, DscEntityPropClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
    /**
     * 导入数据分类
     *
     * @param dscEntityPropClassVoList 任务数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importDscEntityPropClass(List<DscEntityPropClassVo> dscEntityPropClassVoList, Boolean isUpdateSupport)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNull(dscEntityPropClassVoList) || dscEntityPropClassVoList.size() == 0) {
            throw new CustomException("导入任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DscEntityPropClassVo vo : dscEntityPropClassVoList) {
            try {
                /*----------非空检查begain-------------------*/
                if(vo.getClassName()==null && vo.getClassName().length()==0){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、分类名称：" + vo.getClassName() + "为空");
                    continue;
                }

                if(vo.getClassNameFull()==null && vo.getClassNameFull().length()==0){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、全路径：" + vo.getClassNameFull() + "为空");
                    continue;
                }
                /*----------非空检查end-------------------*/
                DscEntityPropClassVo t = new DscEntityPropClassVo();
                BeanUtils.copyBeanProp(t, vo);
                String className = t.getClassName();//分类名称
                String classNameFull = t.getClassNameFull();//全路径
                String[] classNameFullv = classNameFull.split("/");//所有节点数组
                String ancestors="0";//祖级列表
                Long parentId=0L;//父分类
                String fullName="";//拼接全路径
                int count = 0;
                for (String classNameFullvs:classNameFullv) {
                    fullName=fullName+"/"+classNameFullvs;
                    DscEntityPropClass dscEntityPropClass =new DscEntityPropClass();
                    dscEntityPropClass.setClassNameFull(classNameFullvs);
                    dscEntityPropClass.setAncestors(ancestors);
                    DscEntityPropClass dscEntityPropClassList = dscEntityPropClassMapper.selectDscEntityPropClassByClassNameFullvs(dscEntityPropClass);
                    if(StringUtils.isNull(dscEntityPropClassList)){
                        DscEntityPropClass depc = new DscEntityPropClass();
                        depc.setClassName(classNameFullvs);
                        depc.setClassNameFull(fullName.replaceFirst("/", ""));
                        depc.setOrderNum(vo.getOrderNum());
                        depc.setAncestors(ancestors);
                        depc.setParentId(parentId);
                        depc.setGuideLevel(vo.getGuideLevel());
                        depc.setEnterpriseLevel(vo.getEnterpriseLevel());
                        depc.setSensitivityLevel(vo.getSensitivityLevel());
                        depc.setClassRemit(vo.getClassRemit());
                        depc.setCreateTime(DateUtils.getNowDate());
                        depc.setCreateId(loginUser.getUser().getUserId());
                        depc.setCreateBy(loginUser.getUser().getNickName());
                        count=dscEntityPropClassMapper.insertDscEntityPropClass(depc);
                        parentId=depc.getEntityPropClassId();
                        ancestors+=","+parentId;
                    }else{
                        parentId=dscEntityPropClassList.getEntityPropClassId();
                        ancestors+=","+parentId;
                    }

                }
                if(count==0){
                    if(isUpdateSupport){
                        DscEntityPropClass dscEntityPropClassSp = new DscEntityPropClass();
                        dscEntityPropClassSp.setEntityPropClassId(parentId);
                        dscEntityPropClassSp.setClassName(vo.getClassName());
                        dscEntityPropClassSp.setClassNameFull(vo.getClassNameFull());
                        dscEntityPropClassSp.setOrderNum(vo.getOrderNum());

                        dscEntityPropClassSp.setGuideLevel(vo.getGuideLevel());
                        dscEntityPropClassSp.setEnterpriseLevel(vo.getEnterpriseLevel());
                        dscEntityPropClassSp.setSensitivityLevel(vo.getSensitivityLevel());
                        dscEntityPropClassSp.setClassRemit(vo.getClassRemit());

                        dscEntityPropClassSp.setUpdateTime(DateUtils.getNowDate());
                        dscEntityPropClassSp.setUpdateBy(loginUser.getUser().getNickName());
                        dscEntityPropClassMapper.updateDscEntityPropClass(dscEntityPropClassSp);
                        successNum++;
                    }else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、该数据：" + vo.getClassNameFull() + " 已分类");
                        continue;
                    }
                }else {
                    successNum++;
                }
            }catch(Exception e) {
                failureNum++;
            }
        }
        if (failureNum > 0) {
            if(StringUtils.isNotNull(failureMsg)) {
                failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
                throw new CustomException(failureMsg.toString());
            }else{
                throw new CustomException("系统异常，请联系管理员");
            }
        }
        if(StringUtils.isNotNull(successMsg)) {
            if(isUpdateSupport){
                successMsg.insert(0, "恭喜您，数据更新成功！共 " + successNum + " 条");
            }
            else{
                successMsg.insert(0, "恭喜您，数据导入成功！共 " + successNum + " 条");}
        }
        return successMsg.toString();
    }
    @Override
    public  List<DscEntityPropClass> selectClassName(DscEntityPropClass dscEntityPropClass)
    {
        return dscEntityPropClassMapper.selectClassName(dscEntityPropClass);
    }
    @Override
    public  List<DscEntityPropClass> selectClassNameFull(DscEntityPropClass dscEntityPropClass)
    {
        return dscEntityPropClassMapper.selectClassNameFull(dscEntityPropClass);
    }
}
