package com.dqms.dam.service;

import java.util.List;

import com.dqms.dam.domain.DamAssetsClass;
import com.dqms.dam.domain.DamAssetsClassTreeSelect;

/**
 * 资产分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-05-28
 */
public interface IDamAssetsClassService 
{
    /**
     * 查询资产分类
     * 
     * @param assetsClassId 资产分类ID
     * @return 资产分类
     */
    public DamAssetsClass selectDamAssetsClassById(Long assetsClassId);

    /**
     * 查询资产分类列表
     * 
     * @param damAssetsClass 资产分类
     * @return 资产分类集合
     */
    public List<DamAssetsClass> selectDamAssetsClassList(DamAssetsClass damAssetsClass);

    /**
     * 新增资产分类
     * 
     * @param damAssetsClass 资产分类
     * @return 结果
     */
    public int insertDamAssetsClass(DamAssetsClass damAssetsClass);

    /**
     * 修改资产分类
     * 
     * @param damAssetsClass 资产分类
     * @return 结果
     */
    public int updateDamAssetsClass(DamAssetsClass damAssetsClass);

    /**
     * 批量删除资产分类
     * 
     * @param assetsClassIds 需要删除的资产分类ID
     * @return 结果
     */
    public int deleteDamAssetsClassByIds(Long[] assetsClassIds);

    /**
     * 删除资产分类信息
     * 
     * @param assetsClassId 资产分类ID
     * @return 结果
     */
    public int deleteDamAssetsClassById(Long assetsClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param dqmValidationClass 任务分类列表
     * @return 树结构列表
     */
    public List<DamAssetsClass> buildDamAssetsClassTree(List<DamAssetsClass> damAssetsClass);
    /**
     * 构建前端所需要下拉树结构
     *
     * @param damAssetsClass 任务分类列表
     * @return 下拉树结构列表
     */
    public List<DamAssetsClassTreeSelect> buildDamAssetsClassTreeSelect(List<DamAssetsClass> damAssetsClass);
}
