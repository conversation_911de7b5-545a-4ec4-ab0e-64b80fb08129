package com.dqms.mdm.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.mdm.mapper.MdmCollectFastLogMapper;
import com.dqms.mdm.domain.MdmCollectFastLog;
import com.dqms.mdm.service.IMdmCollectFastLogService;

/**
 * 元数据快速注册日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@Service
public class MdmCollectFastLogServiceImpl implements IMdmCollectFastLogService
{
    @Autowired
    private MdmCollectFastLogMapper mdmCollectFastLogMapper;

    /**
     * 查询元数据快速注册日志
     *
     * @param datasourceId 元数据快速注册日志ID
     * @return 元数据快速注册日志
     */
    @Override
    public MdmCollectFastLog selectMdmCollectFastLogById(Long datasourceId)
    {
        return mdmCollectFastLogMapper.selectMdmCollectFastLogById(datasourceId);
    }

    /**
     * 查询元数据快速注册日志列表
     *
     * @param mdmCollectFastLog 元数据快速注册日志
     * @return 元数据快速注册日志
     */
    @Override
    public List<MdmCollectFastLog> selectMdmCollectFastLogList(MdmCollectFastLog mdmCollectFastLog)
    {
        return mdmCollectFastLogMapper.selectMdmCollectFastLogList(mdmCollectFastLog);
    }

    /**
     * 新增元数据快速注册日志
     *
     * @param mdmCollectFastLog 元数据快速注册日志
     * @return 结果
     */
    @Override
    public int insertMdmCollectFastLog(MdmCollectFastLog mdmCollectFastLog)
    {
        return mdmCollectFastLogMapper.insertMdmCollectFastLog(mdmCollectFastLog);
    }

    /**
     * 修改元数据快速注册日志
     *
     * @param mdmCollectFastLog 元数据快速注册日志
     * @return 结果
     */
    @Override
    public int updateMdmCollectFastLog(MdmCollectFastLog mdmCollectFastLog)
    {
        return mdmCollectFastLogMapper.updateMdmCollectFastLog(mdmCollectFastLog);
    }

    /**
     * 批量删除元数据快速注册日志
     *
     * @param datasourceIds 需要删除的元数据快速注册日志ID
     * @return 结果
     */
    @Override
    public int deleteMdmCollectFastLogByIds(Long[] datasourceIds)
    {
        return mdmCollectFastLogMapper.deleteMdmCollectFastLogByIds(datasourceIds);
    }

    /**
     * 删除元数据快速注册日志信息
     *
     * @param datasourceId 元数据快速注册日志ID
     * @return 结果
     */
    @Override
    public int deleteMdmCollectFastLogById(Long datasourceId)
    {
        return mdmCollectFastLogMapper.deleteMdmCollectFastLogById(datasourceId);
    }
}
