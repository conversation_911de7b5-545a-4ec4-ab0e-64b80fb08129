package com.dqms.api.job;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.api.domain.ApiDefine;
import com.dqms.api.domain.ApiDefineHis;
import com.dqms.api.enums.ApiConstants;
import com.dqms.api.service.IApiDefineHisService;
import com.dqms.api.service.IApiDefineService;
import com.dqms.common.utils.spring.SpringUtils;



public class ApiJob implements Callable {
	private static final Logger log = LoggerFactory.getLogger(ApiJob.class);
	private HttpServletRequest req = null;
	private ApiDefineHis apiDefineHis = null;
	private ApiDefine apiDefine = null;
	public ApiJob(HttpServletRequest req,ApiDefineHis apiDefineHis,ApiDefine apiDefine) {
		this.req=req;
		this.apiDefineHis=apiDefineHis;
		this.apiDefine=apiDefine;
	}
	
	@Override
	public Object call() throws Exception {

    	Map<String ,Object> map =new HashMap<String ,Object>();
    	
    	String page = req.getParameter("page");
    	if(page==null) {
    		page="0";
    	}
    	String pageSize = req.getParameter("pageSize");
    	if(pageSize==null) {
    		pageSize="100";
    	}
    	String param = req.getParameter("param");
    	
		apiDefineHis.setStatus(ApiConstants.TASK_CALENDER_STATUS_RUNNING);
		apiDefineHis.setMasesge("API加载中");
		SpringUtils.getBean(IApiDefineHisService.class).updateApiDefineHis(apiDefineHis);
		return SpringUtils.getBean(IApiDefineService.class).getData(page,pageSize, param,"API",apiDefineHis,apiDefine);
	}
	


}
