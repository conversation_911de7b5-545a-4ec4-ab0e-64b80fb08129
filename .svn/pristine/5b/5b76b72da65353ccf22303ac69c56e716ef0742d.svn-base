package com.dqms.task.job;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.service.ISysDatasourceService;
import com.dqms.common.core.redis.RedisCache;
import com.dqms.common.utils.DateTimeUtils;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ip.IpUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.system.service.ISysConfigService;
import com.dqms.task.domain.EtlTaskCalendarDetail;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.domain.EtlTaskSchedule;
import com.dqms.task.domain.EtlTaskScheduleHis;
import com.dqms.task.enums.EtlConstants;
import com.dqms.task.enums.EtlTaskInstanceStatus;
import com.dqms.task.service.IEtlTaskCalendarDetailService;
import com.dqms.task.service.IEtlTaskInstanceService;
import com.dqms.task.service.IEtlTaskScheduleHisService;
import com.dqms.task.service.IEtlTaskScheduleService;
import com.dqms.utils.JdbcTemplateUtils;
import com.dqms.utils.ThreadPoolUtils;


/**
 * 模板调度任务
 *
 * <AUTHOR>
 */
@Component("taskJob")
public class TaskJob {
	private static final Logger log = LoggerFactory.getLogger(TaskJob.class);
	
	//当前运行的任务，主要用于避免并发时添加重复任务
	private synchronized static boolean addTaskId(String batchId,String taskId){
		if(SpringUtils.getBean(RedisCache.class).getCacheObject("ETL-TASK:"+batchId+"-"+taskId)!=null) {
			return false;
		}else {
			SpringUtils.getBean(RedisCache.class).setCacheObject("ETL-TASK:"+batchId+"-"+taskId, "TRUE", 15, TimeUnit.DAYS);
			return true;
		}
	}
	
	public void run(Long taskScheduleId){
		System.out.println(taskScheduleId+"：调度开始执行");
		String batchId = UUID.randomUUID().toString();
		EtlTaskScheduleHis his = new EtlTaskScheduleHis();
		his.setTaskScheduleId(taskScheduleId);
		his.setStartTime(DateUtils.getNowDate());
		his.setStatus(EtlTaskInstanceStatus.RUNNING.name());
		//插入调度日志
		SpringUtils.getBean(IEtlTaskScheduleHisService.class).insertEtlTaskScheduleHis(his);
		EtlTaskSchedule schedule = SpringUtils.getBean(IEtlTaskScheduleService.class).selectEtlTaskScheduleById(taskScheduleId);
		schedule.setStatus(null);
		schedule.setTaskScheduleId(taskScheduleId);
		schedule.setRunStatus(EtlTaskInstanceStatus.RUNNING.name());
		SpringUtils.getBean(IEtlTaskScheduleService.class).updateEtlTaskScheduleStutas(schedule);
		String flag = null;
		EtlTaskCalendarDetail detail = null;
		try {
			StringBuffer loadDate=new StringBuffer();
			if(schedule.getTaskCalendarClassId()!=null) {
				detail = SpringUtils.getBean(IEtlTaskCalendarDetailService.class).selectMinLoadDateByTaskCalendarClassId(schedule.getTaskCalendarClassId());
				if(detail==null) {
					his.setEndTime(DateUtils.getNowDate());
					his.setStatus(EtlTaskInstanceStatus.JUMP.name());
					his.setMessage("无待执行交易日信息！");
					SpringUtils.getBean(IEtlTaskScheduleHisService.class).updateEtlTaskScheduleHis(his);
					schedule.setRunStatus(EtlTaskInstanceStatus.JUMP.name());
					SpringUtils.getBean(IEtlTaskScheduleService.class).updateEtlTaskScheduleStutas(schedule);
					return;
				}else {
					log.info("翻牌日期为："+detail.getLoadDate());
					loadDate.append(detail.getLoadDate());
					detail.setStatus(EtlConstants.TASK_CALENDER_STATUS_FINISHED);
					detail.setStartTime(DateUtils.getNowDate());
					detail.setBatchId(batchId);
					SpringUtils.getBean(IEtlTaskCalendarDetailService.class).updateEtlTaskCalendarDetail(detail);
				}
			} else {
				flag = check(schedule,loadDate);
				if(flag!=null) {//不符合交易设置，跳过执行
					his.setEndTime(DateUtils.getNowDate());
					his.setStatus(EtlTaskInstanceStatus.JUMP.name());
					his.setMessage(flag);
					SpringUtils.getBean(IEtlTaskScheduleHisService.class).updateEtlTaskScheduleHis(his);
					schedule.setRunStatus(EtlTaskInstanceStatus.JUMP.name());
					SpringUtils.getBean(IEtlTaskScheduleService.class).updateEtlTaskScheduleStutas(schedule);
					return;
				}
			}
			log.info("loadDate为"+loadDate);
			if(schedule.getIsConcurrent().equals(EtlConstants.NO)) {//并发时不做判断
				Long checkTime = Long.parseLong(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.schedule.check.time"));
				boolean lastSchedulerUnFinished = true;
				while(lastSchedulerUnFinished){
					int unCount = SpringUtils.getBean(IEtlTaskInstanceService.class).getUnTaskEtlTaskBySchedule(taskScheduleId);
					if(unCount>0){
						log.info("检测到上一次调度未完成，"+checkTime+"秒后继续检测");
						Thread.sleep(checkTime*1000);
					}else {
						lastSchedulerUnFinished=false;
						log.info("检测到上一次调度已完成，继续执行");
					}
				}
			}
			EtlTaskInstance etlTaskInstance = new EtlTaskInstance();
			etlTaskInstance.setLoadDate(loadDate.toString());
			etlTaskInstance.setSchedulerId(taskScheduleId);
			etlTaskInstance.setBatchId(batchId);
			etlTaskInstance.setTaskId(schedule.getTaskId());
			etlTaskInstance.setTaskIds(new Long[]{schedule.getTaskId()});
			etlTaskInstance.setIp(IpUtils.getHostIp());
			SpringUtils.getBean(IEtlTaskInstanceService.class).insertEtlTaskInstanceByDd(etlTaskInstance,schedule);
			his.setEndTime(DateUtils.getNowDate());
			his.setStatus(EtlTaskInstanceStatus.SUCCESS.name());
			SpringUtils.getBean(IEtlTaskScheduleHisService.class).updateEtlTaskScheduleHis(his);
			schedule.setRunStatus(EtlTaskInstanceStatus.SUCCESS.name());
			SpringUtils.getBean(IEtlTaskScheduleService.class).updateEtlTaskScheduleStutas(schedule);
			if(detail!=null) {
				detail.setStatus(EtlConstants.TASK_CALENDER_STATUS_SUCCESS);
				SpringUtils.getBean(IEtlTaskCalendarDetailService.class).updateEtlTaskCalendarDetail(detail);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage());
			his.setEndTime(DateUtils.getNowDate());
			his.setStatus(EtlTaskInstanceStatus.FAIL.name());
			SpringUtils.getBean(IEtlTaskScheduleHisService.class).updateEtlTaskScheduleHis(his);
			schedule.setRunStatus(EtlTaskInstanceStatus.FAIL.name());
			schedule.setStatus(null);
			SpringUtils.getBean(IEtlTaskScheduleService.class).updateEtlTaskScheduleStutas(schedule);
			if(detail!=null) {
				detail.setStatus(EtlConstants.TASK_CALENDER_STATUS_FAILED);
				SpringUtils.getBean(IEtlTaskCalendarDetailService.class).updateEtlTaskCalendarDetail(detail);
			}
		}
		
		System.out.println(taskScheduleId+"：调度完成执行");
		
	}
	
	public String check(EtlTaskSchedule schedule,StringBuffer loadDate){
		try {
		
			if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_ZRR_DQR.equals(schedule.getExecType())) {
				loadDate.append(DateTimeUtils.now2StrDate().replaceAll("-", ""));
			}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_ZRR_QYR.equals(schedule.getExecType())) {
				loadDate.append(DateTimeUtils.addDate(DateTimeUtils.now2StrDate(), 0, 0, -1).replaceAll("-", ""));
			}else {
				String curDate=DateTimeUtils.now2StrDate().replaceAll("-", "");
				String lastWorkDate="";
				String isWorkDate="";
				String dataSourceName = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.dataSourceName");
				String colCurDate = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.curDate");
				String colIsWorkDay = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.isWorkDate");
				String colLstWorkDate = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.lastWortDate");
				String valueYes = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.yesValue");
				String table = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.tableName");
				String sql = "select t."+colIsWorkDay+" as IS_WORK_DATE,t."+colLstWorkDate+" as  LAST_WORK_DATE from "+table+" t where t."+colCurDate+"="+curDate;
				log.info("交易日查询SQL："+sql);
				Map<String, Object> paramMap =  new HashMap<String, Object>();
				paramMap.put("curDate", Integer.parseInt(curDate));
				SysDatasource sysDatasource= SpringUtils.getBean(ISysDatasourceService.class).selectSysDatasourceByName(dataSourceName);
				JdbcTemplate jdbc = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
				List<Map<String,Object>> dates = jdbc.queryForList(sql);
				if(dates.size()==0) {
					log.info("交易日表未配置数据！");
					return "交易日表未配置数据！";
				}
				for (Map<String,Object> m : dates) {
					lastWorkDate = m.get("LAST_WORK_DATE").toString();
					isWorkDate = m.get("IS_WORK_DATE").toString();
				}
				if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_ZRR_QJYYR.equals(schedule.getExecType())) {
					loadDate.append(lastWorkDate);
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_JYR_QZRR.equals(schedule.getExecType())) {
					if(isWorkDate.equals(valueYes)) {
						loadDate.append(DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", ""));
					}else {
						log.info("交易日表未配置数据！");
						return "当前日期为非交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_JYR_QJYR.equals(schedule.getExecType())) {
					if(isWorkDate.equals(valueYes)) {
						loadDate.append(lastWorkDate.replaceAll("-", ""));
					}else {
						log.info("当前日期为非交易日，跳过执行！");
						return "当前日期为非交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_JYR_DQR.equals(schedule.getExecType())) {
					if(isWorkDate.equals(valueYes)) {
						loadDate.append(curDate);
					}else {
						log.info("当前日期为非交易日，跳过执行！");
						return "当前日期为非交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_FJYR_QZRR.equals(schedule.getExecType())) {
					if(!isWorkDate.equals(valueYes)) {
						loadDate.append(DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", ""));
					}else {
						log.info("当前日期为交易日，跳过执行！");
						return "当前日期为交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_FJYR_QJYR.equals(schedule.getExecType())) {
					if(!isWorkDate.equals(valueYes)) {
						loadDate.append(lastWorkDate.replaceAll("-", ""));
					}else {
						log.info("当前日期为交易日，跳过执行！");
						return "当前日期为交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_FJYR_DQR.equals(schedule.getExecType())) {
					if(!isWorkDate.equals(valueYes)) {
						loadDate.append(curDate);
					}else {
						log.info("当前日期为非交易日，跳过执行！");
						return "当前日期为非交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_QZRR_JYR.equals(schedule.getExecType())) {
					String yesday= DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", "");
					if(lastWorkDate.equals(yesday)) {
						loadDate.append(yesday);
					}else {
						log.info("前一日为非交易日，跳过执行！");
						return "前一日为非交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_QZRR_FJYR.equals(schedule.getExecType())) {
					String yesday= DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", "");
					if(!lastWorkDate.equals(yesday)) {
						loadDate.append(yesday);
					}else {
						log.info("前一日为交易日，跳过执行！");
						return "前一日为交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_DYJYR_QJYYR.equals(schedule.getExecType())) {
					if(isWorkDate.equals(valueYes)) {
						String yesday= DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", "");
						if(lastWorkDate.equals(yesday)) {
							log.info("前一日为交易日（不满足第一个交易日），跳过执行！");
							return "前一日为交易日（不满足第一个交易日），跳过执行！";
						}else {
							loadDate.append(lastWorkDate);
						}
					}else {
						log.info("当前日期为非交易日（不满足第一个交易日），跳过执行！");
						return "当前日期为非交易日（不满足第一个交易日），跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_CRJYR_OR.equals(schedule.getExecType())) {
					String tomorrow= DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, 1).replaceAll("-", "");
					String sqlT = "select t."+colIsWorkDay+" as IS_WORK_DATE,t."+colLstWorkDate+" as  LAST_WORK_DATE from "+table+" t where t."+colCurDate+"="+tomorrow;
					List<Map<String,Object>> datesTom = jdbc.queryForList(sqlT);
					if(datesTom.size()==0) {
						log.info("次日交易日表未配置数据！");
						return "次日交易日表未配置数据！";
					}
					String tomIsWorkDate=null;
					for (Map<String,Object> m : datesTom) {
						tomIsWorkDate = m.get("IS_WORK_DATE").toString();
					}
					if(tomIsWorkDate.equals(valueYes)) {
						if(isWorkDate.equals(valueYes)) {
							loadDate.append(curDate);
						}else {
							loadDate.append(lastWorkDate);
						}
					}else {
						log.info("次日不是交易日，跳过执行！");
						return "次日不是交易日，跳过执行！";
					}
				}else if(EtlConstants.TASK_SCHEDULE_EXEC_TYPE_FJYIR_DYJYR_QYR.equals(schedule.getExecType())) {
					if(isWorkDate.equals(valueYes)) {
						String yesday= DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", "");
						if(lastWorkDate.equals(yesday)) {
							log.info("前一日为交易日（不满足第一个交易日），跳过执行！");
							return "前一日为交易日（不满足第一个交易日），跳过执行！";
						}else {
							loadDate.append(DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", ""));
						}
					}else {
						loadDate.append(DateTimeUtils.addDate(getFormatDate(curDate), 0, 0, -1).replaceAll("-", ""));
					}
				}
				
			}
			
			if(!EtlConstants.SCHEDULE_DATE_CHECK_N.equals(schedule.getDateCheckType())) {
				String curDate=DateTimeUtils.now2StrDate().replaceAll("-", "");
				String dataSourceName = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.dataSourceName");
				String colCurDate = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.curDate");
				String colIsWorkDay = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.isWorkDate");
				String valueYes = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.yesValue");
				String table = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.work_date.tableName");
				SysDatasource sysDatasource= SpringUtils.getBean(ISysDatasourceService.class).selectSysDatasourceByName(dataSourceName);
				JdbcTemplate jdbc = JdbcTemplateUtils.getJdbcTemplate(sysDatasource);
				String sql = "";
				List<Map<String,Object>> dates = null;
				if(EtlConstants.SCHEDULE_DATE_CHECK_W.equals(schedule.getDateCheckType())) {
					sql = "select count(*) as  WORK_DATE_NUM from "+table+" t where t."+colCurDate+"<="+curDate +" and t."+colCurDate+">="+getWeekDate() +" and t."+colIsWorkDay+"='"+valueYes+"'";
					log.info("交易日查询SQL："+sql);
					dates=jdbc.queryForList(sql);
					if(dates.size()==0) {
						return "交易日表未配置数据！";
					}
					int dateNum = 0;
					for (Map<String,Object> m : dates) {
						dateNum=Integer.parseInt(m.get("WORK_DATE_NUM").toString());
					}
					if(dateNum!=schedule.getDateCheckNum()) {
						log.info("非每周第"+schedule.getDateCheckNum()+"个交易日，跳过执行！本次为第"+dateNum+"个交易日！");
						return "非每周第"+schedule.getDateCheckNum()+"个交易日，跳过执行！本次为第"+dateNum+"个交易日！";
					}
				}else if(EtlConstants.SCHEDULE_DATE_CHECK_M.equals(schedule.getDateCheckType())) {
					sql = "select count(*) as  WORK_DATE_NUM from "+table+" t where t."+colCurDate+"<="+curDate +" and t."+colCurDate+">="+getMonthDate() +" and t."+colIsWorkDay+"='"+valueYes+"'";
					log.info("交易日查询SQL："+sql);
					dates = jdbc.queryForList(sql);
					if(dates.size()==0) {
						return "交易日表未配置数据！";
					}
					int dateNum = 0;
					for (Map<String,Object> m : dates) {
						dateNum=Integer.parseInt(m.get("WORK_DATE_NUM").toString());
					}
					if(dateNum!=schedule.getDateCheckNum()) {
						log.info("非每月第"+schedule.getDateCheckNum()+"个交易日，跳过执行！本次为第"+dateNum+"个交易日！");
						return "非每月第"+schedule.getDateCheckNum()+"个交易日，跳过执行！本次为第"+dateNum+"个交易日！";
					}
				}else if(EtlConstants.SCHEDULE_DATE_CHECK_Q.equals(schedule.getDateCheckType())) {
					sql = "select count(*) as  WORK_DATE_NUM from "+table+" t where t."+colCurDate+"<="+curDate +" and t."+colCurDate+">="+getQuarterDate() +" and t."+colIsWorkDay+"='"+valueYes+"'";
					log.info("交易日查询SQL："+sql);
					dates = jdbc.queryForList(sql);
					if(dates.size()==0) {
						return "交易日表未配置数据！";
					}
					int dateNum = 0;
					for (Map<String,Object> m : dates) {
						dateNum=Integer.parseInt(m.get("WORK_DATE_NUM").toString());
					}
					if(dateNum!=schedule.getDateCheckNum()) {
						log.info("非每季第"+schedule.getDateCheckNum()+"个交易日，跳过执行！dateNum"+dateNum+"个交易日！");
						return "非每季第"+schedule.getDateCheckNum()+"个交易日，跳过执行！dateNum"+dateNum+"个交易日！";
					}
				}else if(EtlConstants.SCHEDULE_DATE_CHECK_N.equals(schedule.getDateCheckType())) {
					sql = "select count(*) as  WORK_DATE_NUM from "+table+" t where t."+colCurDate+"<="+curDate +" and t."+colCurDate+">="+getYearDate() +" and t."+colIsWorkDay+"='"+valueYes+"'";
					log.info("交易日查询SQL："+sql);
					dates = jdbc.queryForList(sql);
					if(dates.size()==0) {
						return "交易日表未配置数据！";
					}
					int dateNum = 0;
					for (Map<String,Object> m : dates) {
						dateNum=Integer.parseInt(m.get("WORK_DATE_NUM").toString());
					}
					if(dateNum!=schedule.getDateCheckNum()) {
						log.info("非每季第"+schedule.getDateCheckNum()+"个交易日，跳过执行！dateNum"+dateNum+"个交易日！");
						return "非每季第"+schedule.getDateCheckNum()+"个交易日，跳过执行！dateNum"+dateNum+"个交易日！";
					}
				}
			}
			return null;
		}catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException( "交易日查询发生错误！");
		}
	}
	
	public static String getFormatDate(String lastWorkDate) {
		String date="";
		if(lastWorkDate.length()>=8) {
			date=lastWorkDate.substring(0, 4)+"-"+lastWorkDate.substring(4, 6)+"-"+lastWorkDate.substring(6, 8);
		}
		return date;
	}
	/**
     * 获取当前时间所在周的周一和周日的日期时间
     * @return
     */
    public static String getWeekDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar cal = Calendar.getInstance();
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if(dayWeek==1){
            dayWeek = 8;
        }

        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - dayWeek);// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        Date mondayDate = cal.getTime();
        String weekBegin = sdf.format(mondayDate);

        return weekBegin.replaceAll("-", "");
    }
    
    public static String getMonthDate() {
    	return DateTimeUtils.now2StrDateYYYYMM()+"01";
    }
    
    public static String getQuarterDate() {
    	int i=DateTimeUtils.getQuarter(DateTimeUtils.now2StrDateYYYYMM(), DateTimeUtils.FORMAT_yyyyMM);
    	if(i==1) {
    		return DateTimeUtils.nowYear()+"0101";
    	}else if(i==2) {
    		return DateTimeUtils.nowYear()+"0401";
    	}else if(i==3) {
    		return DateTimeUtils.nowYear()+"0701";
    	}else{
    		return DateTimeUtils.nowYear()+"1001";
    	}
    }
    
    public static String getYearDate() {
    	
    	return DateTimeUtils.nowYear()+"0101";
    }    
    
    public static void taskRun(String batchId,int priorityNo ,Long taskId){
    	EtlTaskInstance instance = new EtlTaskInstance();
    	instance.setBatchId(batchId);
    	instance.setPriorityNo(priorityNo);
    	if(taskId!=null) {
    		instance.setTaskId(taskId);	
    	}
    	
		List<EtlTaskInstance> list = SpringUtils.getBean(IEtlTaskInstanceService.class).selectEtlTaskInstanceListByAbleRun(instance);
		int i =0;
		if(list==null||list.size()==0) {//如果当前层级无待执行任务，则检查分层内任务是否已经全部完成
			int succcess = SpringUtils.getBean(IEtlTaskInstanceService.class).getUnTaskEtlTaskByBatchIdAndPriorityNo(instance);
	    	if(succcess==0) {
				instance.setPriorityNo(priorityNo+1);
				instance.setTaskId(null);
				list = SpringUtils.getBean(IEtlTaskInstanceService.class).selectEtlTaskInstanceListByAbleRun(instance);
	    	}
		}
		StringBuilder str = new StringBuilder();
		for (EtlTaskInstance t : list) {
	    	if(!addTaskId(batchId,t.getTaskId().toString()))continue;
			i++;
			try{
				str.append(t.getEtlTask().getTaskName()).append(" ");
				if(t.getEtlTask().getAgentId()==null) {
					ThreadPoolUtils.addTask(new TaskRunThread(t));
				}else {
					ThreadPoolUtils.addGroupTask(t.getEtlTask().getTaskGroupId(),new TaskRunThread(t));
				}
			}catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage());
			}
		}
		if(list.size()>0){
			log.info("检测"+list.size()+"个任务可加入线程池，已加入"+(list.size()-i)+"个，本次加入"+i+"个加入到线程池");
			log.info(str.toString());
		}else{
			log.info("未检测到可执行任务！");
		}
    }


}
