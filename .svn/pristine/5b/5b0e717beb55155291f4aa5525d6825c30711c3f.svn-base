package com.dqms.dsm.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmStandardSrcSystemMapper;
import com.dqms.dsm.domain.DsmStandardSrcSystem;
import com.dqms.dsm.service.IDsmStandardSrcSystemService;

/**
 * 标准来源系统Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Service
public class DsmStandardSrcSystemServiceImpl implements IDsmStandardSrcSystemService
{
    @Autowired
    private DsmStandardSrcSystemMapper dsmStandardSrcSystemMapper;

    /**
     * 查询标准来源系统
     *
     * @param standardId 标准来源系统ID
     * @return 标准来源系统
     */
    @Override
    public List<DsmStandardSrcSystem> selectDsmStandardSrcSystemById(Long standardId)
    {
        return dsmStandardSrcSystemMapper.selectDsmStandardSrcSystemById(standardId);
    }

    /**
     * 查询标准来源系统列表
     *
     * @param dsmStandardSrcSystem 标准来源系统
     * @return 标准来源系统
     */
    @Override
    public List<DsmStandardSrcSystem> selectDsmStandardSrcSystemList(DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        return dsmStandardSrcSystemMapper.selectDsmStandardSrcSystemList(dsmStandardSrcSystem);
    }

    /**
     * 新增标准来源系统
     *
     * @param dsmStandardSrcSystem 标准来源系统
     * @return 结果
     */
    @Override
    public int insertDsmStandardSrcSystem(DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        return dsmStandardSrcSystemMapper.insertDsmStandardSrcSystem(dsmStandardSrcSystem);
    }

    /**
     * 修改标准来源系统
     *
     * @param dsmStandardSrcSystem 标准来源系统
     * @return 结果
     */
    @Override
    public int updateDsmStandardSrcSystem(DsmStandardSrcSystem dsmStandardSrcSystem)
    {
        return dsmStandardSrcSystemMapper.updateDsmStandardSrcSystem(dsmStandardSrcSystem);
    }

    /**
     * 批量删除标准来源系统
     *
     * @param standardIds 需要删除的标准来源系统ID
     * @return 结果
     */
    @Override
    public int deleteDsmStandardSrcSystemByIds(Long[] standardIds)
    {
        return dsmStandardSrcSystemMapper.deleteDsmStandardSrcSystemByIds(standardIds);
    }

    /**
     * 删除标准来源系统信息
     *
     * @param standardId 标准来源系统ID
     * @return 结果
     */
    @Override
    public int deleteDsmStandardSrcSystemById(Long standardId)
    {
        return dsmStandardSrcSystemMapper.deleteDsmStandardSrcSystemById(standardId);
    }
}
