<template>
  <div class="home" id="test">
    <dv-border-box-1 class="zichan zi_yuan" :key="key" style="color:#A1F1EC;"
      >数据资产平台</dv-border-box-1
    >

    <div>
      <!-- <dv-border-box-8 class="big_icon system" :key="'1' + key"
        ><img src="~@/assets/home/<USER>"
      /></dv-border-box-8> -->
      <dv-decoration-12
        style="width:150px;height:150px;"
        class="big_icon2 system2"
      >
        <img src="~@/assets/home/<USER>" />
      </dv-decoration-12>

      <dv-border-box-10 :dur="15" class="center" :key="'2' + key">
        <div class="center_div">
          <img src="~@/assets/home/<USER>" class="center_data" />

          <dv-border-box-1 class="center_small center_small1" :key="'3' + key">
            补录<br />平台
          </dv-border-box-1>

          <dv-border-box-1 class="center_small center_small2" :key="'4' + key">
            数据<br />交换
          </dv-border-box-1>

          <dv-border-box-1 class="center_small center_small3" :key="'5' + key"
            >数据<br />质量</dv-border-box-1
          >

          <dv-border-box-1 class="center_small center_small4" :key="'6' + key"
            >数据<br />模型</dv-border-box-1
          >

          <dv-border-box-1 class="center_small center_small5" :key="'7' + key"
            >数据<br />标准</dv-border-box-1
          >

          <dv-border-box-1 class="center_small center_small6" :key="'8' + key"
            >风险<br />系统</dv-border-box-1
          >

          <dv-border-box-1 class="center_small center_small7" :key="'9' + key"
            >安全<br />中心</dv-border-box-1
          >

          <dv-border-box-1 class="center_small center_small8" :key="'10' + key"
            >任务<br />中心</dv-border-box-1
          >

          <div>
            <div class="line_type1 center_line1"></div>
            <div class="line_type1 center_line2"></div>
            <div class="line center_line3"></div>
            <div class="line center_line4"></div>
            <div class="line center_line5"></div>
            <div class="line center_line6"></div>
            <div class="line center_line7"></div>
            <div class="line center_line8"></div>
            <div class="line center_line9"></div>
            <div class="line center_line10"></div>
            <div class="line center_line11"></div>
            <div class="line center_line12"></div>
            <div class="line center_line13 pillar"></div>
            <div class="line center_line14 pillar"></div>
            <div class="line center_line15 pillar"></div>
            <div class="line center_line16 small_pillar"></div>
            <div class="line center_line17 small_pillar"></div>
            <div class="line center_line18 small_pillar"></div>
            <div class="line center_line19"></div>
          </div>

          <img src="~@/assets/home/<USER>" class="curve" />
          <dv-decoration-9 style="color:#A1F1EC;" class="radius_img radius1"
            >ODS</dv-decoration-9
          >
          <dv-decoration-9 style="color:#A1F1EC;" class="radius_img radius2"
            >EDW</dv-decoration-9
          >
          <dv-decoration-9 style="color:#A1F1EC;" class="radius_img radius3"
            >DDW</dv-decoration-9
          >

          <div class="center_sanjiao">
            <img src="~@/assets/home/<USER>" class="center_up1" />
            <img src="~@/assets/home/<USER>" class="center_up2" />
            <img src="~@/assets/home/<USER>" class="center_up3" />

            <img
              src="~@/assets/home/<USER>"
              class="center_down"
            />
          </div>

          <div class="center_sanjiao_lr">
            <img
              src="~@/assets/home/<USER>"
              class="center_right1"
            />
            <img
              src="~@/assets/home/<USER>"
              class="center_right2"
            />
            <img
              src="~@/assets/home/<USER>"
              class="center_right3"
            />
            <img
              src="~@/assets/home/<USER>"
              class="center_right4"
            />

            <img
              src="~@/assets/home/<USER>"
              class="center_right5"
            />
            <img
              src="~@/assets/home/<USER>"
              class="center_left2"
            />
          </div>
        </div>
      </dv-border-box-10>

      <div>
        <dv-border-box-1
          :key="'11' + key"
          style="color:#A1F1EC;"
          class="small_icon small1"
        >
          数据需求
        </dv-border-box-1>
        <dv-border-box-1
          :key="'12' + key"
          style="color:#A1F1EC;"
          class="small_icon small2"
        >
          数据服务
        </dv-border-box-1>
        <dv-border-box-1
          :key="'13' + key"
          style="color:#A1F1EC;"
          class="small_icon small3"
        >
          报表平台
        </dv-border-box-1>
        <dv-border-box-1
          :key="'14' + key"
          style="color:#A1F1EC;"
          class="small_icon small4"
        >
          API接口
        </dv-border-box-1>
        <!-- <dv-border-box-8 class="big_icon user" :key="'15' + key"
          ><img src="~@/assets/home/<USER>"
        /></dv-border-box-8> -->
        <dv-border-box-8
          class="big_icon2 user2"
          :key="'15' + key"
          style="height:80px;"
        >
          <dv-decoration-11 :key="'16' + key">
            <img src="~@/assets/home/<USER>" />
          </dv-decoration-11>
        </dv-border-box-8>

        <dv-border-box-8
          class="big_icon2 application2"
          :reverse="true"
          :key="'17' + key"
          style="height:80px;"
        >
          <dv-decoration-11 :key="'18' + key">
            <img src="~@/assets/home/<USER>" /><br />
          </dv-decoration-11>
        </dv-border-box-8>
        <!-- <dv-border-box-8 class="big_icon application" :key="'16' + key"
          ><img src="~@/assets/home/<USER>"
        /></dv-border-box-8> -->
      </div>

      <div>
        <div class="right_line_1 right_line1"></div>
        <div class="right_line_1 right_line2"></div>

        <div class="right_line_2 right_line3"></div>
        <div class="right_line_2 right_line4"></div>

        <div class="right_line_3"></div>
      </div>

      <div class="right_sanjiao">
        <img
          src="~@/assets/home/<USER>"
          class="right_sanjiao1"
        />
        <img
          src="~@/assets/home/<USER>"
          class="right_sanjiao2"
        />
      </div>
    </div>

    <dv-border-box-1
      class="yuan zi_yuan"
      :key="'19' + key"
      style="color:#A1F1EC;"
      >元数据
    </dv-border-box-1>

    <img src="~@/assets/home/<USER>" class="arrow arrow_left" />
    <img
      src="~@/assets/home/<USER>"
      class="arrow arrow_right_1"
    />
    <img
      src="~@/assets/home/<USER>"
      class="arrow arrow_right_2"
    />
    <img
      src="~@/assets/home/<USER>"
      class="arrow arrow_right_3"
    />
    <img
      src="~@/assets/home/<USER>"
      class="arrow arrow_right_4"
    />
    <img src="~@/assets/home/<USER>" class="arrow arrow_down2" />
    <img src="~@/assets/home/<USER>" class="arrow index_arrow_up" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      key: 1
    };
  },
  mounted() {
    var elementResizeDetectorMaker = require("element-resize-detector"); //导入
    // 创建实例
    var erd = elementResizeDetectorMaker();
    // 创建实例带参
    var erdUltraFast = elementResizeDetectorMaker({
      strategy: "scroll", //<- For ultra performance.
      callOnAdd: true,
      debug: true
    });
    //监听id为test的元素 大小变化
    erd.listenTo(document.getElementById("test"), element => {
      var width = element.offsetWidth;
      var height = element.offsetHeight;

      //此处开始重载当前页面
      this.$nextTick(function() {
        this.key++;
      });
    });
  }
};
</script>

<style lang="scss" scoped>
.home {
  height: calc(100vh - 100px);
  background: url("~@/assets/home/<USER>") no-repeat 0 0;
  background-size: cover;
  display: inline-table;
  width: 100%;
  position: relative;
}
::v-deep .dv-decoration-11 .decoration-content {
  img {
    width: calc(37% - 4px);
    height: calc(32% - 4px);
  }
}
::v-deep .border-box-content {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: calc(100% - 4px);
    height: calc(100% - 4px);
  }
}
.zi_yuan {
  width: 10.6%;
  height: 8.3%;
}

.big_icon {
  width: 9.4%;
  height: 16.7%;
  position: absolute;
}
.big_icon2 {
  width: 10.6%;
  height: 8.3%;
  position: absolute;
}
.system2 {
  left: 5.2%;
  top: 41.8%;
}
.system {
  left: 5.2%;
  top: 45.8%;
}
.user2 {
  left: 85.4%;
  top: 37.73%;
}
.application2 {
  left: 85.4%;
  top: 71.13%;
}
.user {
  left: 85.4%;
  top: 33.33%;
}

.application {
  left: 85.4%;
  top: 66.67%;
}

.small_icon {
  width: 8%;
  height: 10.2%;
  position: absolute;
  left: 71.4%;
}

.small1 {
  top: 19.9%;
}
.small2 {
  top: 36.6%;
}
.small3 {
  top: 53.24%;
}
.small4 {
  top: 69.9%;
}
.zichan {
  position: absolute;
  left: 38.6%;
  top: 5.56%;
}
.yuan {
  position: absolute;
  left: 38.6%;
  top: 86.2%;
}
.center {
  width: 51.04%;
  height: 60.2%;
  position: absolute;
  top: 19.9%;
  left: 18.5%;
}

.arrow {
  height: 7.6%;
  width: 4.03%;
  position: absolute;
}
.arrow_left {
  top: 46.2%;
  left: 16.5%;
}
.arrow_down2 {
  left: 41.9%;
  top: 77.5%;
}
.index_arrow_up {
  left: 41.9%;
  top: 15.4%;
}
.arrow_right_1 {
  left: 67.4%;
  top: 21.2%;
}
.arrow_right_2 {
  left: 67.4%;
  top: 37.9%;
}
.arrow_right_3 {
  left: 67.4%;
  top: 54.5%;
}
.arrow_right_4 {
  left: 67.4%;
  top: 71.2%;
}

.right_line_1 {
  width: 3.125%;
  height: 1px;
  background: #00f5ff;
  position: absolute;
  left: 79.03%;
}
.right_line1 {
  top: 25%;
}
.right_line2 {
  top: 58.3%;
}
.right_line3 {
  top: 41.76%;
}
.right_line4 {
  top: 75%;
}
.right_line_2 {
  width: 6.25%;
  height: 1px;
  background: #00f5ff;
  position: absolute;
  left: 79.03%;
}
.right_line_3 {
  width: 1px;
  height: 33.33%;
  background: #00f5ff;
  position: absolute;
  left: 82.1%;
  top: 25%;
}

.right_sanjiao {
  img {
    width: 0.52%;
    height: 1.48%;
    position: absolute;
    left: 85%;
  }
  .right_sanjiao1 {
    top: 41%;
  }
  .right_sanjiao2 {
    top: 74.4%;
  }
}

.center_div {
  // border: 1px solid #00f5ff;
  display: flex;
  align-items: center;
  justify-content: center;
  // width: calc(100% - 6px);
  // height: calc(100% - 6px);
  .center_small {
    width: 11.22%;
    height: 16.9%;
    position: absolute;
    color: #a1f1ec;
  }
  .center_data {
    width: 11.22%;
    height: 3.38%;
    position: absolute;
    top: 6.3%;
    left: 44.4%;
  }
  .center_small1 {
    top: 54.6%;
    left: 7.65%;
  }
  .center_small2 {
    top: 28.46%;
    left: 7.65%;
  }
  .center_small3 {
    top: 15.4%;
    left: 26.02%;
  }
  .center_small4 {
    top: 15.4%;
    left: 44.39%;
  }
  .center_small5 {
    top: 15.4%;
    left: 62.76%;
  }
  .center_small6 {
    left: 81.12%;
    top: 20.15%;
  }
  .center_small7 {
    left: 81.12%;
    top: 41.54%;
  }
  .center_small8 {
    top: 67.69%;
    left: 44.39%;
  }
  .radius_img {
    height: 12.31%;
    width: 8.16%;
    position: absolute;
  }
  .curve {
    height: 6.15%;
    width: 36.84%;
    position: absolute;
    top: 37.85%;
    left: 31.63%;
  }
  .center_sanjiao {
    img {
      height: 1.54%;
      width: 1.63%;
      position: absolute;
    }
  }
  .center_sanjiao_lr {
    img {
      width: 1.02%;
      height: 2.46%;
      position: absolute;
    }
  }
}

.line_type1 {
  height: 1px;
  width: 4.29%;
  background: #00f5ff;
  position: absolute;
  left: 18.5%;
}
.center_line1 {
  top: 36.77%;
}
.center_line2 {
  top: 63.9%;
}
.line {
  position: absolute;
  background: #00f5ff;
}
.center_line3 {
  width: 1px;
  height: 27.5%;
  left: 22.8%;
  top: 36.8%;
}
.center_line4 {
  width: 1px;
  height: 5.2%;
  left: 13.27%;
  top: 71%;
}
.center_line5 {
  height: 1px;
  width: 31.12%;
  left: 13.27%;
  top: 76.15%;
}
.center_line6 {
  height: 1px;
  width: 31.12%;
  left: 55.61%;
  top: 76.15%;
}
.center_line7 {
  width: 1px;
  height: 18.48%;
  left: 86.63%;
  top: 58%;
}
.center_line8 {
  height: 1px;
  width: 7.5%;
  top: 50%;
  right: 4px;
}
.center_line9 {
  height: 1px;
  width: 8.87%;
  top: 50%;
  left: 72.5%;
}
.radius1 {
  left: 27.55%;
  top: 43.85%;
}
.radius2 {
  left: 45.92%;
  top: 43.85%;
}
.radius3 {
  left: 64.29%;
  top: 43.85%;
}
.center_line10 {
  height: 1px;
  width: 10.2%;
  top: 50%;
  left: 54.08%;
}
.center_line11 {
  height: 1px;
  width: 10.2%;
  top: 50%;
  left: 35.71%;
}
.center_line12 {
  height: 1px;
  width: 4.79%;
  top: 50%;
  left: 22.76%;
}
.pillar {
  width: 1px;
  height: 11.99%;
  top: 31.8%;
}
.center_line13 {
  left: 31.63%;
}
.center_line14 {
  left: 50%;
}
.center_line15 {
  left: 68.37%;
}
.small_pillar {
  width: 1px;
  height: 6%;
  top: 56.14%;
}
.center_line16 {
  left: 31.63%;
}
.center_line17 {
  left: 50%;
  height: 12.4%;
}
.center_line18 {
  left: 68.37%;
}
.center_line19 {
  width: 36.84%;
  height: 1px;
  top: 61.85%;
  left: 31.63%;
}
.center_up1 {
  top: 31.6%;
  left: 30.8%;
}
.center_up2 {
  top: 31.6%;
  left: 49.18%;
}
.center_up3 {
  top: 31.6%;
  left: 67.55%;
}
.center_down {
  left: 49.18%;
  top: 66.58%;
}
.center_right1 {
  top: 48.8%;
  left: 26.53%;
}
.center_right2 {
  top: 48.8%;
  left: 44.9%;
}
.center_right3 {
  top: 48.8%;
  left: 63.27%;
}
.center_right4 {
  top: 75.2%;
  left: 43.7%;
}

.center_right5 {
  top: 48.8%;
  left: 80.7%;
}

.center_left2 {
  top: 75.2%;
  left: 55.14%;
}
</style>
