package com.dqms.dam.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.domain.vo.DamAssetsVo;

/**
 * 数据资产Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-08
 */
public interface DamAssetsMapper 
{
    /**
     * 查询数据资产
     * 
     * @param damAssetsId 数据资产ID
     * @return 数据资产
     */
    public DamAssets selectDamAssetsById(Long damAssetsId);
    public DamAssetsVo selectDamAssetsVoById(Long damAssetsId);
    public DamAssets selectDamAssetsByRel(@Param("relId")String relId ,@Param("assetsType")String assetsType);

    /**
     * 查询数据资产列表
     * 
     * @param damAssets 数据资产
     * @return 数据资产集合
     */
    public List<DamAssets> selectDamAssetsList(DamAssets damAssets);

    public List<DamAssetsVo> selectDamAssetsVoList(DamAssetsVo damAssetsVo);

    public DamAssetsVo getDamAssetsListVo(DamAssetsVo damAssetsVo);

    public DamAssetsVo selectDamAssetsClassRel(DamAssetsVo damAssetsVo);
    
    public List<DamAssets> selectDamAssetsListOrderDy(DamAssets damAssets);
    public List<DamAssets> selectDamAssetsListOrderPf(DamAssets damAssets);
    public List<DamAssets> selectDamAssetsListOrderYy(DamAssets damAssets);
    public List<DamAssets> selectDamAssetsListOrderAq(DamAssets damAssets);
    public List<DamAssets> selectDamAssetsListOrderLbl(DamAssets damAssets);
    public List<DamAssets> selectDamAssetsListOrderZsl(DamAssets damAssets);
    /**
     * 查询我的数据资产列表
     * 
     * @param damAssets 数据资产
     * @return 数据资产集合
     */
    public List<DamAssets> selectMyDamAssetsList(DamAssets damAssets);

    /**
     * 新增数据资产
     * 
     * @param damAssets 数据资产
     * @return 结果
     */
    public int insertDamAssets(DamAssets damAssets);



    /**
     * 修改数据资产
     * 
     * @param damAssets 数据资产
     * @return 结果
     */
    public int updateDamAssets(DamAssets damAssets);

    /**
     * 删除数据资产
     * 
     * @param damAssetsId 数据资产ID
     * @return 结果
     */
    public int deleteDamAssetsById(Long damAssetsId);

    /**
     * 批量删除数据资产
     * 
     * @param damAssetsIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDamAssetsByIds(Long[] damAssetsIds);
}
