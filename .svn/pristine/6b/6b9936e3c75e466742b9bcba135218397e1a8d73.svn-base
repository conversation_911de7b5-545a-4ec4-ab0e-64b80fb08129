<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="加密ID" prop="encryptionId">
        <el-input
          v-model="queryParams.encryptionId"
          placeholder="请输入加密ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="实体ID" prop="entityId">
        <el-input
          v-model="queryParams.entityId"
          placeholder="请输入实体ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="字段ID" prop="propId">
        <el-input
          v-model="queryParams.propId"
          placeholder="请输入字段ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="加密类型" prop="runType">
        <el-select v-model="queryParams.runType" placeholder="请选择加密类型" clearable size="small">
          <el-option
            v-for="dict in runTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dsc:dscEncryptionDetail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dsc:dscEncryptionDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dsc:dscEncryptionDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dsc:dscEncryptionDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dscEncryptionDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="明细ID" align="center" prop="encryptionDetailId" />
      <el-table-column label="加密ID" align="center" prop="encryptionId" />
      <el-table-column label="实体ID" align="center" prop="entityId" />
      <el-table-column label="字段ID" align="center" prop="propId" />
      <el-table-column label="加密类型" align="center" prop="runType" :formatter="runTypeFormat" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dsc:dscEncryptionDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dsc:dscEncryptionDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改加密明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="加密ID" prop="encryptionId">
          <el-input v-model="form.encryptionId" placeholder="请输入加密ID" clearable/>
        </el-form-item>
        <el-form-item label="实体ID" prop="entityId">
          <el-input v-model="form.entityId" placeholder="请输入实体ID" clearable/>
        </el-form-item>
        <el-form-item label="字段ID" prop="propId">
          <el-input v-model="form.propId" placeholder="请输入字段ID" clearable/>
        </el-form-item>
        <el-form-item label="加密类型" prop="runType">
          <el-select v-model="form.runType" placeholder="请选择加密类型" clearable>
            <el-option
              v-for="dict in runTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDscEncryptionDetail, getDscEncryptionDetail, delDscEncryptionDetail, addDscEncryptionDetail, updateDscEncryptionDetail, exportDscEncryptionDetail } from "@/api/dsc/dscEncryptionDetail";

export default {
  name: "DscEncryptionDetail",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 加密明细表格数据
      dscEncryptionDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 加密类型字典
      runTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        encryptionId: null,
        entityId: null,
        propId: null,
        runType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        encryptionId: [
          { required: true, message: "加密ID不能为空", trigger: "blur" }
        ],
        entityId: [
          { required: true, message: "实体ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.runTypeOptions = response.data;
    });
  },
  methods: {
    /** 查询加密明细列表 */
    getList() {
      this.loading = true;
      listDscEncryptionDetail(this.queryParams).then(response => {
        this.dscEncryptionDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 加密类型字典翻译
    runTypeFormat(row, column) {
      return this.selectDictLabel(this.runTypeOptions, row.runType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        encryptionDetailId: null,
        encryptionId: null,
        entityId: null,
        propId: null,
        runType: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.encryptionDetailId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加加密明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const encryptionDetailId = row.encryptionDetailId || this.ids
      getDscEncryptionDetail(encryptionDetailId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改加密明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.encryptionDetailId != null) {
            updateDscEncryptionDetail(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDscEncryptionDetail(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const encryptionDetailIds = row.encryptionDetailId || this.ids;
      this.$confirm('是否确认删除加密明细编号为"' + encryptionDetailIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDscEncryptionDetail(encryptionDetailIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有加密明细数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDscEncryptionDetail(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
