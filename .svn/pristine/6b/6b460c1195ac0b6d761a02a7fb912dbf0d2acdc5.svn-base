package com.dqms.dsm.domain;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 数据标准分类对象 dsm_standard_class
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public class DsmStandardClass extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 标准分类Id */
    private Long standardClassId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String standardClassName;

    /** 全路径 */
    @Excel(name = "全路径")
    private String standardClassNameFull;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    /** 父菜单名称 */
    private String parentName;

    /** 父菜单ID */
    private Long parentId;

    /** 显示顺序 */
    private Integer orderNum;

    /** 祖级列表 */
    private String ancestors;

    /** 子部门 */
    private List<DsmStandardClass> children = new ArrayList<DsmStandardClass>();

    public void setStandardClassId(Long standardClassId)
    {
        this.standardClassId = standardClassId;
    }

    public Long getStandardClassId()
    {
        return standardClassId;
    }
    public void setStandardClassName(String standardClassName)
    {
        this.standardClassName = standardClassName;
    }

    public String getStandardClassName()
    {
        return standardClassName;
    }
    public void setStandardClassNameFull(String standardClassNameFull)
    {
        this.standardClassNameFull = standardClassNameFull;
    }

    public String getStandardClassNameFull()
    {
        return standardClassNameFull;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    
    public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public String getAncestors() {
		return ancestors;
	}

	public void setAncestors(String ancestors) {
		this.ancestors = ancestors;
	}

	public List<DsmStandardClass> getChildren() {
		return children;
	}

	public void setChildren(List<DsmStandardClass> children) {
		this.children = children;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("standardClassId", getStandardClassId())
            .append("parentId", getParentId())
            .append("ancestors", getAncestors())
            .append("standardClassName", getStandardClassName())
            .append("standardClassNameFull", getStandardClassNameFull())
            .append("orderNum", getOrderNum())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
