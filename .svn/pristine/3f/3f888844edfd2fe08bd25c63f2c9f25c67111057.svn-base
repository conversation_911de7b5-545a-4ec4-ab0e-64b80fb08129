package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.dsm.domain.DsmMasterData;
import com.dqms.dsm.domain.DsmMasterDataClass;
import com.dqms.dsm.domain.DsmMasterDataClassTreeSelect;
import com.dqms.dsm.mapper.DsmMasterDataClassMapper;
import com.dqms.dsm.mapper.DsmMasterDataMapper;
import com.dqms.dsm.service.IDsmMasterDataClassService;
import com.dqms.framework.web.service.TokenService;

/**
 * 主数据分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@Service
public class DsmMasterDataClassServiceImpl implements IDsmMasterDataClassService
{
    @Autowired
    private DsmMasterDataClassMapper dsmMasterDataClassMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private DsmMasterDataMapper dsmMasterDataMapper;
    /**
     * 查询主数据分类
     *
     * @param masterDataClassId 主数据分类ID
     * @return 主数据分类
     */
    @Override
    public DsmMasterDataClass selectDsmMasterDataClassById(Long masterDataClassId)
    {
        return dsmMasterDataClassMapper.selectDsmMasterDataClassById(masterDataClassId);
    }

    /**
     * 查询主数据分类列表
     *
     * @param dsmMasterDataClass 主数据分类
     * @return 主数据分类
     */
    @Override
    public List<DsmMasterDataClass> selectDsmMasterDataClassList(DsmMasterDataClass dsmMasterDataClass)
    {
        return dsmMasterDataClassMapper.selectDsmMasterDataClassList(dsmMasterDataClass);
    }

    /**
     * 新增主数据分类
     *
     * @param dsmMasterDataClass 主数据分类
     * @return 结果
     */
    @Override
    public int insertDsmMasterDataClass(DsmMasterDataClass dsmMasterDataClass)
    {
    	DsmMasterDataClass info = dsmMasterDataClassMapper.selectDsmMasterDataClassById(dsmMasterDataClass.getParentId());
    	if(info!=null) {
    		dsmMasterDataClass.setAncestors(info.getAncestors() + "," + dsmMasterDataClass.getParentId());
    		dsmMasterDataClass.setClassNameFull(info.getClassNameFull() + "/" + dsmMasterDataClass.getClassName());
    	}else {
    		dsmMasterDataClass.setAncestors("0");
    		dsmMasterDataClass.setClassNameFull(dsmMasterDataClass.getClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmMasterDataClass.setCreateTime(DateUtils.getNowDate());
    	dsmMasterDataClass.setCreateId(loginUser.getUser().getUserId());
    	dsmMasterDataClass.setCreateBy(loginUser.getUser().getNickName());
    	dsmMasterDataClass.setUpdateTime(DateUtils.getNowDate());
    	dsmMasterDataClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmMasterDataClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmMasterDataClassMapper.insertDsmMasterDataClass(dsmMasterDataClass);
    }

    /**
     * 修改主数据分类
     *
     * @param dsmMasterDataClass 主数据分类
     * @return 结果
     */
    @Override
    public int updateDsmMasterDataClass(DsmMasterDataClass dsmMasterDataClass)
    {
    	DsmMasterDataClass newC = dsmMasterDataClassMapper.selectDsmMasterDataClassById(dsmMasterDataClass.getParentId());
    	DsmMasterDataClass oldC = dsmMasterDataClassMapper.selectDsmMasterDataClassById(dsmMasterDataClass.getMasterDataClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String newAncestors = newC.getAncestors() + "," + dsmMasterDataClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = newC.getClassNameFull() + "/" + dsmMasterDataClass.getClassName();
             String oldClassNameFull = oldC.getClassNameFull();
             dsmMasterDataClass.setAncestors(newAncestors);
             dsmMasterDataClass.setClassNameFull(newClassNameFull);
             updateClassChildren(dsmMasterDataClass.getMasterDataClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dsmMasterDataClass.getMasterDataClassId(), newClassNameFull, oldClassNameFull);
         }else if(newC==null){
        	 dsmMasterDataClass.setAncestors("0");
        	 dsmMasterDataClass.setClassName(dsmMasterDataClass.getClassName());
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmMasterDataClass.setUpdateTime(DateUtils.getNowDate());
    	dsmMasterDataClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmMasterDataClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmMasterDataClassMapper.updateDsmMasterDataClass(dsmMasterDataClass);
    }

    /**
     * 批量删除主数据分类
     *
     * @param masterDataClassIds 需要删除的主数据分类ID
     * @return 结果
     */
    @Override
    public int deleteDsmMasterDataClassByIds(Long[] masterDataClassIds)
    {
        return dsmMasterDataClassMapper.deleteDsmMasterDataClassByIds(masterDataClassIds);
    }

    /**
     * 删除主数据分类信息
     *
     * @param masterDataClassId 主数据分类ID
     * @return 结果
     */
    @Override
    public int deleteDsmMasterDataClassById(Long masterDataClassId)
    {
        return dsmMasterDataClassMapper.deleteDsmMasterDataClassById(masterDataClassId);
    }
    
    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateClassChildren(Long taskClassId, String newAncestors, String oldAncestors)
    {
        List<DsmMasterDataClass> children = dsmMasterDataClassMapper.selectChildrenClassById(taskClassId);
        for (DsmMasterDataClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	dsmMasterDataClassMapper.updateClassChildren(children);
        }
    }
    @Transactional
    public void updateClassNameFullChildren(Long taskClassId, String newClassNameFull, String oldClassNameFull)
    {
        List<DsmMasterDataClass> children = dsmMasterDataClassMapper.selectChildrenClassById(taskClassId);
        for (DsmMasterDataClass child : children)
        {
            child.setClassNameFull(child.getClassNameFull().replace(oldClassNameFull, newClassNameFull));
        }
        if (children.size() > 0)
        {
        	dsmMasterDataClassMapper.updateClassNameFullChildren(children);
        }
    }
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<DsmMasterDataClassTreeSelect> buildDsmMasterDataClassTreeSelect(List<DsmMasterDataClass> dsmMasterDataClasss)
    {
        List<DsmMasterDataClass> dsmMasterDataClasssTrees = buildDsmMasterDataClassTree(dsmMasterDataClasss);
        return dsmMasterDataClasssTrees.stream().map(DsmMasterDataClassTreeSelect::new).collect(Collectors.toList());
    }
    
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<DsmMasterDataClass> buildDsmMasterDataClassTree(List<DsmMasterDataClass> dsmMasterDataClasss)
    {
        List<DsmMasterDataClass> returnList = new ArrayList<DsmMasterDataClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (DsmMasterDataClass dsmMasterDataClass : dsmMasterDataClasss)
        {
            tempList.add(dsmMasterDataClass.getMasterDataClassId());
        }
        for (Iterator<DsmMasterDataClass> iterator = dsmMasterDataClasss.iterator(); iterator.hasNext();)
        {
        	DsmMasterDataClass dsmMasterDataClass = (DsmMasterDataClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dsmMasterDataClass.getParentId()))
            {
                recursionFn(dsmMasterDataClasss, dsmMasterDataClass);
                returnList.add(dsmMasterDataClass);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = dsmMasterDataClasss;
        }
        return returnList;
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<DsmMasterDataClass> list, DsmMasterDataClass t)
    {
        // 得到子节点列表
        List<DsmMasterDataClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DsmMasterDataClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<DsmMasterDataClass> getChildList(List<DsmMasterDataClass> list, DsmMasterDataClass t)
    {
        List<DsmMasterDataClass> tlist = new ArrayList<DsmMasterDataClass>();
        Iterator<DsmMasterDataClass> it = list.iterator();
        while (it.hasNext())
        {
        	DsmMasterDataClass n = (DsmMasterDataClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getMasterDataClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DsmMasterDataClass> list, DsmMasterDataClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
    
    @Override
    public List<DsmMasterDataClassTreeSelect> buildDsmMasterDataClassTreeSelectAll(List<DsmMasterDataClass> dsmMasterDataClasss)
    {
        for(DsmMasterDataClass ss : dsmMasterDataClasss) {
        	ss.setType("Y");
        }
        List<DsmMasterData> list = dsmMasterDataMapper.selectDsmMasterDataList(new DsmMasterData());
        for(DsmMasterData data : list ) {
        	DsmMasterDataClass cla = new DsmMasterDataClass();
        	cla.setClassName(data.getMasterDataName());
        	cla.setMasterDataClassId(0-data.getMasterDataId());
        	cla.setParentId(Long.parseLong(data.getMasterDataClassId()));
        	cla.setType("N");
        	dsmMasterDataClasss.add(cla);
        }
        List<DsmMasterDataClass> dsmMasterDataClasssTrees = buildDsmMasterDataClassTree(dsmMasterDataClasss);
        return dsmMasterDataClasssTrees.stream().map(DsmMasterDataClassTreeSelect::new).collect(Collectors.toList());
    }
}
