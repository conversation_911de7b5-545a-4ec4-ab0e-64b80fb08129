package com.dqms.dqm.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateTimeUtils;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dqm.domain.DqmValidationClass;
import com.dqms.dqm.domain.DqmValidationProblem;
import com.dqms.dqm.domain.DqmValidationRuleCate;
import com.dqms.dqm.domain.DqmValidationTask;
import com.dqms.dqm.domain.vo.DqmValidationRuleCateVo;
import com.dqms.dqm.enums.DqmConstants;
import com.dqms.dqm.mapper.DqmValidationClassMapper;
import com.dqms.dqm.mapper.DqmValidationRuleCateMapper;
import com.dqms.dqm.mapper.DqmValidationSubscriptionMapper;
import com.dqms.dqm.service.IDqmValidationProblemService;
import com.dqms.dqm.service.IDqmValidationRuleCateService;
import com.dqms.dqm.service.IDqmValidationTaskService;
import com.dqms.framework.web.service.TokenService;
import com.dqms.mdm.domain.MdmDataEntity;
import com.dqms.mdm.domain.MdmDataEntityProp;
import com.dqms.mdm.mapper.MdmDataEntityMapper;
import com.dqms.mdm.mapper.MdmDataEntityPropMapper;
import com.dqms.mdm.util.MetaDataContext;
import com.dqms.system.service.ISysConfigService;
import com.dqms.task.enums.EtlTaskInstanceStatus;
import com.dqms.utils.NoticeByDqmRunThread;
import com.dqms.utils.StringUtils;
import com.dqms.utils.ThreadPoolUtils;

/**
 * 检核规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-21
 */
@Service
public class DqmValidationRuleCateServiceImpl implements IDqmValidationRuleCateService
{
    @Autowired
    private DqmValidationRuleCateMapper dqmValidationRuleCateMapper;
    @Autowired
    private IDqmValidationTaskService dqmValidationTaskService;
    @Autowired
    private IDqmValidationProblemService dqmValidationProblemService;
    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;
    @Autowired
    private MetaDataContext metaDataContext;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private DqmValidationSubscriptionMapper dqmValidationSubscriptionMapper;
    @Autowired
    private SysSystemMapper sysSystemMapper;
    @Autowired
    MdmDataEntityMapper mdmDataEntityMapper;
    @Autowired
    MdmDataEntityPropMapper mdmDataEntityPropMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    DqmValidationClassMapper dqmValidationClassMapper;

    /**
     * 查询检核规则
     *
     * @param validationRuleCateId 检核规则ID
     * @return 检核规则
     */
    @Override
    public DqmValidationRuleCate selectDqmValidationRuleCateById(Long validationRuleCateId)
    {
        return dqmValidationRuleCateMapper.selectDqmValidationRuleCateById(validationRuleCateId);
    }

    /**
     * 查询检核规则列表
     *
     * @param dqmValidationRuleCate 检核规则
     * @return 检核规则
     */
    @Override
    @DataScope(systemAlias = "t")
    public List<DqmValidationRuleCate> selectDqmValidationRuleCateList(DqmValidationRuleCate dqmValidationRuleCate)
    {
        return dqmValidationRuleCateMapper.selectDqmValidationRuleCateList(dqmValidationRuleCate);
    }
    @DataScope(systemAlias = "t")
    @Override
    public List<DqmValidationRuleCateVo> selectDqmValidationRuleCateVoList(DqmValidationRuleCate dqmValidationRuleCate)
    {
        return dqmValidationRuleCateMapper.selectDqmValidationRuleCateVoList(dqmValidationRuleCate);
    }
    @DataScope(systemAlias = "t")
    @Override
    public List<DqmValidationRuleCateVo> exportDqmValidationRuleCateVoList(DqmValidationRuleCate dqmValidationRuleCate)
    {
        return dqmValidationRuleCateMapper.exportDqmValidationRuleCateVoList(dqmValidationRuleCate);
    }

    @Override
    @DataScope(systemAlias = "t")
    public List<DqmValidationRuleCate> selectDqmValidationRuleCateListByUser(DqmValidationRuleCate dqmValidationRuleCate)
    {
    	dqmValidationRuleCate.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());;
        return dqmValidationRuleCateMapper.selectDqmValidationRuleCateListByUser(dqmValidationRuleCate);
    }
    /**
     * 新增检核规则
     *
     * @param dqmValidationRuleCate 检核规则
     * @return 结果
     */
    @Override
    public int insertDqmValidationRuleCate(DqmValidationRuleCate dqmValidationRuleCate)
    {
        dqmValidationRuleCate.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        dqmValidationRuleCate.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        dqmValidationRuleCate.setCreateTime(DateUtils.getNowDate());
        dqmValidationRuleCate.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        dqmValidationRuleCate.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        dqmValidationRuleCate.setUpdateTime(DateUtils.getNowDate());
        List<DqmValidationRuleCate> t=dqmValidationRuleCateMapper.selectDqmValidationRuleCateByName(dqmValidationRuleCate);
        if(t!=null&&t.size()!=0){
            throw new RuntimeException("规则名称已经存在！");
        }
        return dqmValidationRuleCateMapper.insertDqmValidationRuleCate(dqmValidationRuleCate);
    }

    /**
     * 修改检核规则
     *
     * @param dqmValidationRuleCate 检核规则
     * @return 结果
     */
    @Override
    public int updateDqmValidationRuleCate(DqmValidationRuleCate dqmValidationRuleCate)
    {
        dqmValidationRuleCate.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
        dqmValidationRuleCate.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        dqmValidationRuleCate.setUpdateTime(DateUtils.getNowDate());
        List<DqmValidationRuleCate> t=dqmValidationRuleCateMapper.selectDqmValidationRuleCateByName(dqmValidationRuleCate);
        if(t.size()>1){
            throw new RuntimeException("规则名称已经存在！");
        }else if(t.size()==1){
            if(!t.get(0).getValidationRuleCateId().equals(dqmValidationRuleCate.getValidationRuleCateId())){
                throw new RuntimeException("规则名称已经存在！");
            }
        }
        return dqmValidationRuleCateMapper.updateDqmValidationRuleCate(dqmValidationRuleCate);
    }

    /**
     * 批量删除检核规则
     *
     * @param validationRuleCateIds 需要删除的检核规则ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationRuleCateByIds(Long[] validationRuleCateIds)
    {
//        List<DqmValidationRuleCate> dqmValidationRuleCates = dqmValidationRuleCateMapper.selectDqmValidationRuleCateListByIdsForTask(validationRuleCateIds);
//        if(dqmValidationRuleCates.size() > 0){
//            throw new RuntimeException("数据质量与任务有关联，请先在任务中删除关联");
//        }

        return dqmValidationRuleCateMapper.deleteDqmValidationRuleCateByIds(validationRuleCateIds);
    }

    /**
     * 删除检核规则信息
     *
     * @param validationRuleCateId 检核规则ID
     * @return 结果
     */
    @Override
    public int deleteDqmValidationRuleCateById(Long validationRuleCateId)
    {
        return dqmValidationRuleCateMapper.deleteDqmValidationRuleCateById(validationRuleCateId);
    }

    @Override
    public int executorDqmValidationRuleCate(DqmValidationRuleCate dqmValidationRuleCate,String exeType,String loadDate) {
    	loadDate=loadDate.replaceAll("-", "");
        List<Map<String, Object>> problemData = new ArrayList<Map<String,Object>>();
        DqmValidationTask task = new DqmValidationTask();
        int totalCount = 0;
        boolean needSaveError = false;
        //查询规则
        DqmValidationRuleCate rule = dqmValidationRuleCateMapper.selectDqmValidationRuleCateById(dqmValidationRuleCate.getValidationRuleCateId());
        BeanUtils.copyProperties(rule,task);
        task.setStartTime(DateUtils.getNowDate());
        task.setValidationRuleCateId(rule.getValidationRuleCateId().intValue());
        task.setValidationTaskName(rule.getValidationName());
        task.setExecutionStyle(exeType);
        task.setExecuteState(EtlTaskInstanceStatus.RUNNING.name());
        task.setLoadDate(loadDate);
        dqmValidationTaskService.insertDqmValidationTask(task);
        try{
            int limit = Integer.parseInt(sysConfigService.selectConfigByKey("dsm.dimension.load.limit"));
            if(limit<=0) {
                limit=1000;
            }
            SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(rule.getDatasourceId());
            String problemSql=rule.getProblemSql().replaceAll("\\$\\{LOAD_DATE\\}", loadDate).replaceAll("\\$\\{TODAY\\}", DateTimeUtils.getToday_YYYYMMDD()).replaceAll("\\$\\{YESTERDAY\\}", DateTimeUtils.getYesterday_YYYYMMDD());
            String totalSql=rule.getTotalSql();
            List<Map<String,Object>> datas = metaDataContext.queryByPage(sysDatasource, problemSql, 1, limit);
            if (StringUtils.isNotEmpty(totalSql)) {
            	totalSql=totalSql.replaceAll("\\$\\{LOAD_DATE\\}", loadDate).replaceAll("\\$\\{TODAY\\}", DateTimeUtils.getToday_YYYYMMDD()).replaceAll("\\$\\{YESTERDAY\\}", DateTimeUtils.getYesterday_YYYYMMDD());
                totalCount = metaDataContext.getSqlCount(sysDatasource, totalSql);
            }
            Long totalSum = new Long(totalCount);
            int threshold = 0;
            Integer problem = 0;
            problemData=datas;
            if(rule.getThreshold()!=null&&rule.getThreshold().trim().toLowerCase().startsWith("select")&&rule.getThresholdDatasourceId()!=null&&!rule.getThresholdDatasourceId().equals(0L)) {
            	SysDatasource thSysDatasource = sysDatasourceMapper.selectSysDatasourceById(rule.getThresholdDatasourceId());
            	List<Map<String,Object>> kdatas = metaDataContext.queryByPage(thSysDatasource,rule.getThreshold().replaceAll("\\$\\{LOAD_DATE\\}", loadDate).replaceAll("\\$\\{TODAY\\}", DateTimeUtils.getToday_YYYYMMDD()).replaceAll("\\$\\{YESTERDAY\\}",DateTimeUtils.getYesterday_YYYYMMDD()), 1, limit);
            	LinkedHashMap<Integer,String> lHead=new LinkedHashMap<>();
            	if(datas!=null&&datas.size()>0) {
            		int ii=0;
            		for(String key : datas.get(0).keySet()){
            			lHead.put(ii, key);
            			ii++;
            		}
            	}
            	LinkedHashMap<Integer,String> rHead=new LinkedHashMap<>();
            	LinkedHashMap<String,String> rHeadName=new LinkedHashMap<>();
            	if(kdatas!=null&&kdatas.size()>0) {
            		int ii=0;
            		for(String key : kdatas.get(0).keySet()){
            			rHead.put(ii, key);
            			rHeadName.put(key, key);
            			ii++;
            		}
            	}
            	if(rHead.size()==0&&lHead.size()!=0) {//目标表无数据、来源有数据
            		for(Map<String,Object> ob : datas) {
            			ob.put("目标", "空");
            		}
                    problemData=datas;
                    problem = datas.size()-kdatas.size();
                    needSaveError = true;
            	}else if(rHead.size()!=0&&lHead.size()==0) {//来源表有数据、目标无数据
            		for(Map<String,Object> ob : kdatas) {
            			ob.put("源头", "空");
            		}
                    problemData=kdatas;
                    problem = datas.size()-kdatas.size();
                    needSaveError = true;
            	}else if(rHead.size()==0&&lHead.size()==0){//都没有数据
            		problem = 0;
            	}else if(rHead.size()!=lHead.size()) {//列头数量不一致
            		Map<String, Object> m= new HashMap<>();
                    m.put("源头列", lHead.size());
                    m.put("目标列", rHead.size());
                    m.put("说明", "数据列数不匹配");
                    problem = lHead.size()-rHead.size();
                    problemData.clear();
                    problemData.add(m);
                    needSaveError = true;
            	}else if(lHead.equals(rHead)) {//列头完全一致直接对比数据
            		if(!datas.equals(kdatas)) {
                        Map<String, Object> m= new HashMap<>();
                        m.put("结果", JSONArray.toJSONString(datas));
                        m.put("对比", JSONArray.toJSONString(kdatas));
                        problem = datas.size()-kdatas.size();
                        problemData.clear();
                        problemData.add(m);
                        needSaveError = true;
                	}else {
                		problem = 0;
                	}
        		}else if(datas.size()!=kdatas.size()) {//数据量不一致
            		Map<String, Object> m= new HashMap<>();
                    m.put("源头数量", datas.size());
                    m.put("目标数量", kdatas.size());
                    m.put("说明", "数据条数不匹配");
                    problem = datas.size()-kdatas.size();
                    problemData.clear();
                    problemData.add(m);
                    needSaveError = true;
            	}else{
            		problemData = new ArrayList<Map<String,Object>>();
            		for(int i=0;i<datas.size();i++) {
            			LinkedHashMap<String, Object> pb = new LinkedHashMap<>();
            			problemData.add(pb);
            			Map<String, Object> data = datas.get(i);
            			int j=0;
            			boolean flag=false;
            			for(String key : data.keySet()) {
            				pb.put(key, data.get(key));
            				if(rHeadName.get(key)==null) {
            					pb.put(rHead.get(j), kdatas.get(i).get(rHead.get(j)));
            					String yv=data.get(key)==null?"":data.get(key).toString();
            					String mv=kdatas.get(i).get(rHead.get(j))==null?"":kdatas.get(i).get(rHead.get(j)).toString();
            					if(yv.equals(mv)) {
            						if(pb.get("对比")==null) {
            							pb.put("对比", "√");
            						}
            					}else {
            						pb.put("对比", "×");
            						flag=true;
            					}
            				}
            				j++;
            			}
            			if(flag) {
            				problem+=1;
            			}
            		}
            		if(problem>0) {
            			needSaveError = true;
            		}else {
            			needSaveError = false;
            		}
                    
            		
            	}
            	
            	
            	
            }else {
            	if(datas!=null&&datas.size()==1&&datas.get(0)!=null&&datas.get(0).size()==1) {
            		problem = Integer.parseInt(datas.get(0).values().toArray()[0].toString());
            	}else {
            		problem = metaDataContext.getSqlCount(sysDatasource, problemSql);
                    
            	}
            	
            	threshold = Integer.parseInt(rule.getThreshold());
        		task.setThreshold(Long.parseLong(rule.getThreshold()));
                // 比较类型
                String thresholdJudge = rule.getThresholdJudge();
                switch (thresholdJudge) {
                    // >=
                    case "0":
                        needSaveError = problem >= threshold;
                        break;
                    // >
                    case "1":
                        needSaveError = problem > threshold;
                        break;
                    // =
                    case "2":
                        needSaveError = problem == threshold;
                        break;
                    //<
                    case "3":
                        needSaveError = problem < threshold;
                        break;
                    //<=
                    case "4":
                        needSaveError = problem <= threshold;
                        break;
                    //!=
                    case "5":
                        needSaveError = problem != threshold;
                        break;
                    default:
                        break;
                }
            }
            
            //插入日志表
            task.setTotalResult(totalSum.intValue());
            task.setErrorResult(problem);
            task.setExecuteState(EtlTaskInstanceStatus.SUCCESS.name());
            task.setEndTime(DateUtils.getNowDate());
            if(!needSaveError){
                task.setExecuteResult(DqmConstants.DQMS_EXECUTION_RESULT_SUCCESS);
            }else{
                task.setExecuteResult(DqmConstants.DQMS_EXECUTION_RESULT_FAIL);
                // 插入问题明细管理表
                DqmValidationProblem dqmValidationProblem = new DqmValidationProblem();
                BeanUtils.copyProperties(task,dqmValidationProblem);
                dqmValidationProblem.setDqmValidationTaskId(task.getDqmValidationTaskId().intValue());
                dqmValidationProblem.setErrorResult(problem);
                dqmValidationProblem.setTotalResult(totalSum.intValue());
                dqmValidationProblem.setJsonResult(JSONArray.toJSONString(problemData));
                dqmValidationProblem.setExecutionStyle(exeType);
                dqmValidationProblem.setThreshold(task.getThreshold());
                dqmValidationProblemService.insertDqmValidationProblem(dqmValidationProblem);
                ThreadPoolUtils.addTask(-5L,0,new NoticeByDqmRunThread(rule.getValidationRuleCateId(),  "数据质量异常通知["+rule.getValidationName()+"]",rule.getValidationName()+"存在异常情况！详情请至问题管理模块查看。执行时间为："+DateUtils.getTime()+"。"));
            }
                
            dqmValidationTaskService.updateDqmValidationTask(task);
        }catch (Exception e){
        	e.printStackTrace();
            String errorMsg = e.getMessage();
            task.setExecuteState(EtlTaskInstanceStatus.FAIL.name());
            task.setErrorMsg(errorMsg);
            task.setEndTime(DateUtils.getNowDate());
            dqmValidationTaskService.updateDqmValidationTask(task);
            return 1;
        }

        return 1;
    }
    @Override
    public List<DqmValidationRuleCate> selectDqmValidationRuleCateListByTaskId(Long taskId) {
        return dqmValidationRuleCateMapper.selectDqmValidationRuleCateListByTaskId(taskId);
    }

    @Override
    public List<DqmValidationRuleCate> selectDqmValidationRuleCateListByPage(Long datasourceId,int page ,int size) {
        return dqmValidationRuleCateMapper.selectDqmValidationRuleCateListByPage(datasourceId,page,size);
    }

    @Override
    public List<DqmValidationRuleCate> selectUnDqmValidationRuleCateListByTaskId(DqmValidationRuleCate dqmValidationRuleCate) {
        return dqmValidationRuleCateMapper.selectUnDqmValidationRuleCateListByTaskId(dqmValidationRuleCate);
    }

    @Override
    public int addDqmValidationRuleCateAndTaskRel(Long taskId, Long[] regIds) {
        return dqmValidationRuleCateMapper.addDqmValidationRuleCateAndTaskRel(taskId, regIds);
    }

    @Override
    public int delDqmValidationRuleCateAndTaskRel(Long taskId) {
        return dqmValidationRuleCateMapper.delDqmValidationRuleCateAndTaskRel(taskId);
    }

    @Override
    @DataScope(systemAlias = "t")
    public List<Map<String, Object>> getRuleCateSystem(DqmValidationRuleCate dqmValidationRuleCate) {
        return dqmValidationRuleCateMapper.getRuleCateSystem(dqmValidationRuleCate);
    }

    @Override
    @DataScope(systemAlias = "t")
    public List<Map<String, Object>> getRuleCateStatus(DqmValidationRuleCate dqmValidationRuleCate) {
        return dqmValidationRuleCateMapper.getRuleCateStatus(dqmValidationRuleCate);
    }
    
    @Override
    @DataScope(systemAlias = "t")
    public List<Map<String, Object>> getRuleCateType(DqmValidationRuleCate dqmValidationRuleCate) {
        return dqmValidationRuleCateMapper.getRuleCateType(dqmValidationRuleCate);
    }

    @Override
    @Transactional
    public String  importDqmValidationRuleCate(List<DqmValidationRuleCateVo> dqmValidationRuleCateVoList,boolean updateSupport){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (com.dqms.common.utils.StringUtils.isNull(dqmValidationRuleCateVoList) || dqmValidationRuleCateVoList.size() == 0)
        {
            throw new CustomException("导入核验规则数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int errorRow=1;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DqmValidationRuleCateVo vo : dqmValidationRuleCateVoList)
        {
            errorRow++;
            DqmValidationRuleCate t = new DqmValidationRuleCate();
            BeanUtils.copyBeanProp(t, vo);
            try
            {
                //非空判断
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getValidationName())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 规则名称不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getValidationRuleName())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 分类名称不能为空");
                    continue;
                }else{
                    DqmValidationClass dqmValidationClass=new DqmValidationClass();
                    dqmValidationClass.setClassName(vo.getValidationRuleName());
                    DqmValidationClass classByName = dqmValidationClassMapper.selectDqmValidationClassByName(dqmValidationClass);
                    if(classByName!=null){
                        t.setValidationClassId(classByName.getValidationClassId());
                    }else{
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 分类未定义");
                        continue;
                    }
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getProblemSql())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 检查sql不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getThresholdJudge())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 阈值判断不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getThreshold())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 阈值不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getLevel())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 级别不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getStatus())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + " 状态不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getDatasourceName())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + "  数据源名称不能为空");
                    continue;
                }
                if(com.dqms.common.utils.StringUtils.isEmpty(vo.getSystemName())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、核验规则 " + vo.getValidationName() + "  所属系统不能为空");
                    continue;
                }
                MdmDataEntity mdmDataEntity =new MdmDataEntity();
                if(!com.dqms.common.utils.StringUtils.isEmpty(vo.getTableName())){
                    String [] scamaAndTable=vo.getTableName().split("\\.");
                    if(scamaAndTable.length==2){
                        MdmDataEntityProp tarMdmDataEntityProp = new MdmDataEntityProp();
                        tarMdmDataEntityProp.setSystemName(vo.getSystemName());
                        tarMdmDataEntityProp.setTableSchema( vo.getTableName().split("\\.")[0]);
                        tarMdmDataEntityProp.setTableName(vo.getTableName().split("\\.")[1]);
                        tarMdmDataEntityProp.setPropName(vo.getFieldName());
                        MdmDataEntityProp tarMdmDataEntityPropData = mdmDataEntityPropMapper.getDataByOther(tarMdmDataEntityProp);
                        mdmDataEntity.setTableName(vo.getTableName().split("\\.")[1]);
                        mdmDataEntity.setSystemName(t.getSystemName());
                        mdmDataEntity.setTableSchema(vo.getTableName().split("\\.")[0]);
                        mdmDataEntity=mdmDataEntityMapper.selectMdmDataEntityByTableName(mdmDataEntity);
                        if(mdmDataEntity!=null){
                            t.setTableName(mdmDataEntity.getEntityId().toString());
                        }else{
                            t.setTableName(null);
                        }
                        if(tarMdmDataEntityPropData!=null){
                            t.setFieldName(tarMdmDataEntityPropData.getPropId().toString());
                        }else{
                            t.setFieldName(null);
                        }
                    }else{
                        t.setTableName(null);
                        t.setFieldName(null);
                    }
                }
                SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceByName(vo.getDatasourceName());
                if(com.dqms.common.utils.StringUtils.isNull(sysDatasource)){
                    failureNum++;
                    String msg = com.dqms.common.utils.StringUtils.format("<br/>{}、核验规则 {}  数据源:[{}] 未定义",failureNum,vo.getValidationName(),vo.getDatasourceName());
                    failureMsg.append(msg);
                    continue;
                }
                t.setDatasourceId(sysDatasource.getDatasourceId());

                SysSystem sysSystem = sysSystemMapper.selectSysSystemByName(vo.getSystemName());
                if(com.dqms.common.utils.StringUtils.isNull(sysSystem)){
                    failureNum++;
                    String msg = com.dqms.common.utils.StringUtils.format("<br/>{}、核验规则 {}  源系统:[{}] 未定义",failureNum,vo.getValidationName(),vo.getSystemName());
                    failureMsg.append(msg);
                    continue;
                }
                t.setSystemId(sysSystem.getSystemId());
                DqmValidationRuleCate forHave=new DqmValidationRuleCate();
                forHave.setValidationName(t.getValidationName());
                List<DqmValidationRuleCate> l=dqmValidationRuleCateMapper.selectDqmValidationRuleCateByName(forHave);
                if(!updateSupport) {
                    if (l != null && l.size() != 0) {
                        failureNum++;
                        String msg = com.dqms.common.utils.StringUtils.format("<br/>{}、核验规则 {}  名称已存在", failureNum, vo.getValidationName());
                        failureMsg.append(msg);
                        continue;
                    }else{
                        successNum++;
                        this.insertDqmValidationRuleCate(t);
                    }
                }else{
                    if(l.size()==1){
                        t.setValidationRuleCateId(l.get(0).getValidationRuleCateId());
                        this.updateDqmValidationRuleCate(t);
                        successNum++;
                    }else{
                    	successNum++;
                        this.insertDqmValidationRuleCate(t);
                    }
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = com.dqms.common.utils.StringUtils.format("<br/>{}、核验规则 {} 导入失败：请检查第"+errorRow+"行数据",failureNum,vo.getValidationName());
                failureMsg.append(msg + e.getMessage());

            }
        }
        if (failureNum > 0)
        {
            String msg = com.dqms.common.utils.StringUtils.format("很抱歉，导入失败！共 {} 条数据不正确，错误如下：",failureNum);
            failureMsg.insert(0,msg);
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            String msg = com.dqms.common.utils.StringUtils.format("恭喜您，数据已全部导入成功！共 {} 条，数据如下：",successNum);
            successMsg.insert(0,msg);
        }
        return successMsg.toString();
    }
}
