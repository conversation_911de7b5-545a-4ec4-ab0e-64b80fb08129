package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 标准落标对象 dsm_discern
 *
 * <AUTHOR>
 * @date 2021-05-31
 */
public class DsmDiscern extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 落标Id */
    private Long discernId;

    /** 一级推荐 */
    @Excel(name = "一级推荐")
    private Long prodIdOne;

    /** 二级推荐 */
    @Excel(name = "二级推荐")
    private Long prodIdTwo;

    /** 三级推荐 */
    @Excel(name = "三级推荐")
    private Long prodIdThree;

    /** 四级推荐 */
    @Excel(name = "四级推荐")
    private Long propIdFour;

    /** 五级推荐 */
    @Excel(name = "五级推荐")
    private Long propIdFive;

    /** 标准ID */
    @Excel(name = "标准ID")
    private Long dsmId;
    private Long prodId;
    
    private String dsmName;
    private String prodIdOneName;
    private String prodIdTwoName;
    private String prodIdThreeName;
    private String propIdFourName;
    private String propIdFiveName;
    private String[] discernTypes;
    private Long[] systems;
    private String[] discernTypeLabels;
    private String[] systemLabels;
    
    /** 推荐类型 */
    @Excel(name = "推荐类型")
    private String discernType;

    public void setDiscernId(Long discernId)
    {
        this.discernId = discernId;
    }

    public Long getDiscernId()
    {
        return discernId;
    }
    public void setProdIdOne(Long prodIdOne)
    {
        this.prodIdOne = prodIdOne;
    }

    public Long getProdIdOne()
    {
        return prodIdOne;
    }
    public void setProdIdTwo(Long prodIdTwo)
    {
        this.prodIdTwo = prodIdTwo;
    }

    public Long getProdIdTwo()
    {
        return prodIdTwo;
    }
    public void setProdIdThree(Long prodIdThree)
    {
        this.prodIdThree = prodIdThree;
    }

    public Long getProdIdThree()
    {
        return prodIdThree;
    }
    public void setPropIdFour(Long propIdFour)
    {
        this.propIdFour = propIdFour;
    }

    public Long getPropIdFour()
    {
        return propIdFour;
    }
    public void setPropIdFive(Long propIdFive)
    {
        this.propIdFive = propIdFive;
    }

    public Long getPropIdFive()
    {
        return propIdFive;
    }
    public void setDsmId(Long dsmId)
    {
        this.dsmId = dsmId;
    }

    public Long getDsmId()
    {
        return dsmId;
    }
    public void setDiscernType(String discernType)
    {
        this.discernType = discernType;
    }

    public String getDiscernType()
    {
        return discernType;
    }
    
    public String getDsmName() {
		return dsmName;
	}

	public void setDsmName(String dsmName) {
		this.dsmName = dsmName;
	}

	public String getProdIdOneName() {
		return prodIdOneName;
	}

	public void setProdIdOneName(String prodIdOneName) {
		this.prodIdOneName = prodIdOneName;
	}

	public String getProdIdTwoName() {
		return prodIdTwoName;
	}

	public void setProdIdTwoName(String prodIdTwoName) {
		this.prodIdTwoName = prodIdTwoName;
	}

	public String getProdIdThreeName() {
		return prodIdThreeName;
	}

	public void setProdIdThreeName(String prodIdThreeName) {
		this.prodIdThreeName = prodIdThreeName;
	}

	public String getPropIdFourName() {
		return propIdFourName;
	}

	public void setPropIdFourName(String propIdFourName) {
		this.propIdFourName = propIdFourName;
	}

	public String getPropIdFiveName() {
		return propIdFiveName;
	}

	public void setPropIdFiveName(String propIdFiveName) {
		this.propIdFiveName = propIdFiveName;
	}

	public Long[] getSystems() {
		return systems;
	}

	public void setSystems(Long[] systems) {
		this.systems = systems;
	}

	public String[] getDiscernTypeLabels() {
		return discernTypeLabels;
	}

	public void setDiscernTypeLabels(String[] discernTypeLabels) {
		this.discernTypeLabels = discernTypeLabels;
	}

	public String[] getDiscernTypes() {
		return discernTypes;
	}

	public void setDiscernTypes(String[] discernTypes) {
		this.discernTypes = discernTypes;
	}

	public String[] getSystemLabels() {
		return systemLabels;
	}

	public void setSystemLabels(String[] systemLabels) {
		this.systemLabels = systemLabels;
	}

	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("discernId", getDiscernId())
            .append("prodIdOne", getProdIdOne())
            .append("prodIdTwo", getProdIdTwo())
            .append("prodIdThree", getProdIdThree())
            .append("propIdFour", getPropIdFour())
            .append("propIdFive", getPropIdFive())
            .append("dsmId", getDsmId())
            .append("discernType", getDiscernType())
            .append("createTime", getCreateTime())
            .toString();
    }
}
