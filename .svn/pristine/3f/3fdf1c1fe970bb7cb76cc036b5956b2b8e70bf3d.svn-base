package com.dqms.dsc.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 接口权限对象 dsc_master_system
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscMasterSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 系统权限 */
    private Long masterSystemId;

    /** 系统 */
    @Excel(name = "系统")
    private Long systemId;

    /** 接口 */
    @Excel(name = "接口")
    private Long mdmId;

    public void setMasterSystemId(Long masterSystemId)
    {
        this.masterSystemId = masterSystemId;
    }

    public Long getMasterSystemId()
    {
        return masterSystemId;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setMdmId(Long mdmId)
    {
        this.mdmId = mdmId;
    }

    public Long getMdmId()
    {
        return mdmId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("masterSystemId", getMasterSystemId())
            .append("systemId", getSystemId())
            .append("mdmId", getMdmId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
