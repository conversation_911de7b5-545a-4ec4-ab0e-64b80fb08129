package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmModelEntityShip;

/**
 * 模型关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-11-23
 */
public interface DsmModelEntityShipMapper 
{
    /**
     * 查询模型关系
     * 
     * @param modelEntityShipId 模型关系ID
     * @return 模型关系
     */
    public DsmModelEntityShip selectDsmModelEntityShipById(Long modelEntityShipId);

    /**
     * 查询模型关系列表
     * 
     * @param dsmModelEntityShip 模型关系
     * @return 模型关系集合
     */
    public List<DsmModelEntityShip> selectDsmModelEntityShipList(DsmModelEntityShip dsmModelEntityShip);

    /**
     * 新增模型关系
     * 
     * @param dsmModelEntityShip 模型关系
     * @return 结果
     */
    public int insertDsmModelEntityShip(DsmModelEntityShip dsmModelEntityShip);

    /**
     * 修改模型关系
     * 
     * @param dsmModelEntityShip 模型关系
     * @return 结果
     */
    public int updateDsmModelEntityShip(DsmModelEntityShip dsmModelEntityShip);

    /**
     * 删除模型关系
     * 
     * @param modelEntityShipId 模型关系ID
     * @return 结果
     */
    public int deleteDsmModelEntityShipById(Long modelEntityShipId);

    /**
     * 批量删除模型关系
     * 
     * @param modelEntityShipIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmModelEntityShipByIds(Long[] modelEntityShipIds);
    
    public int deleteDsmModelEntityShipByDsmModelEntityId(Long modelEntityId);
}
