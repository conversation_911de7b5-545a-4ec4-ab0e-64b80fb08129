package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmStandardMdmRel;

/**
 * 标准落地Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-26
 */
public interface DsmStandardMdmRelMapper 
{
    /**
     * 查询标准落地
     * 
     * @param standardId 标准落地ID
     * @return 标准落地
     */
    public DsmStandardMdmRel selectDsmStandardMdmRelById(Long standardId);

    /**
     * 查询标准落地列表
     * 
     * @param dsmStandardMdmRel 标准落地
     * @return 标准落地集合
     */
    public List<DsmStandardMdmRel> selectDsmStandardMdmRelList(DsmStandardMdmRel dsmStandardMdmRel);
    
    /**
     * 查询标准未落地列表
     * 
     * @param dsmStandardMdmRel 标准落地
     * @return 标准落地集合
     */
    public List<DsmStandardMdmRel> selectUnDsmStandardMdmRelList(DsmStandardMdmRel dsmStandardMdmRel);

    /**
     * 新增标准落地
     * 
     * @param dsmStandardMdmRel 标准落地
     * @return 结果
     */
    public int insertDsmStandardMdmRel(DsmStandardMdmRel dsmStandardMdmRel);

    /**
     * 修改标准落地
     * 
     * @param dsmStandardMdmRel 标准落地
     * @return 结果
     */
    public int updateDsmStandardMdmRel(DsmStandardMdmRel dsmStandardMdmRel);

    /**
     * 删除标准落地
     * 
     * @param standardId 标准落地ID
     * @return 结果
     */
    public int deleteDsmStandardMdmRelById(Long standardId);

    /**
     * 批量删除标准落地
     * 
     * @param standardIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmStandardMdmRelByIds(Long[] standardIds);
    
    public int deleteDsmStandardMdmRelByPk(DsmStandardMdmRel dsmStandardMdmRel);


    public int importInsertDsmStandardMdmRel(List<DsmStandardMdmRel> dsmStandardMdmRelList);

    public int deleteDsmStandardMdmRelALL(List<DsmStandardMdmRel> dsmStandardMdmRelList);

    public List<DsmStandardMdmRel> selectDsmStandardMdmRelListSingle(DsmStandardMdmRel vo);
}
