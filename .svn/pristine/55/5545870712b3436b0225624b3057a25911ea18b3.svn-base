package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 主数据配置对象 dsm_master_data_install
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public class DsmMasterDataInstall extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long masterDataInstallId;

    /** 主数据 */
    @Excel(name = "主数据")
    private Long masterDataId;
    
    /** 字段ID */
    @Excel(name = "字段ID")
    private Long modelEntityPropId;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    /** 属性名称 */
    @Excel(name = "属性名称")
    private String propName;

    /** 属性注释 */
    @Excel(name = "属性注释")
    private String propComment;

    /** 组件类型 */
    @Excel(name = "组件类型")
    private String type;

    /** 占位提示 */
    @Excel(name = "占位提示")
    private String placeholder;

    /** 组件宽度 */
    @Excel(name = "组件宽度")
    private Integer width;

    /** 默认值 */
    @Excel(name = "默认值")
    private String defaultValue;

    /** 最大长度 */
    @Excel(name = "最大长度")
    private Long columnSize;

    /** 标签宽度 */
    @Excel(name = "标签宽度")
    private Long labelWidth;

    /** 前缀 */
    @Excel(name = "前缀")
    private String prefix;

    /** 后缀 */
    @Excel(name = "后缀")
    private String postfix;

    /** 能否清空 */
    @Excel(name = "能否清空")
    private String clearable;

    /** 是否必填 */
    @Excel(name = "是否必填")
    private String nullable;

    /** 关联维度 */
    @Excel(name = "关联维度")
    private Long relId;
    private String relName;
    
    private List<DsmMasterDataRule> dsmMasterDataRules;
    
    private List<DsmDimensionDetail> options;   
    private Long[] rules;

    public void setMasterDataInstallId(Long masterDataInstallId)
    {
        this.masterDataInstallId = masterDataInstallId;
    }

    public Long getMasterDataInstallId()
    {
        return masterDataInstallId;
    }
    public void setModelEntityPropId(Long modelEntityPropId)
    {
        this.modelEntityPropId = modelEntityPropId;
    }

    public Long getModelEntityPropId()
    {
        return modelEntityPropId;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setPropName(String propName)
    {
        this.propName = propName;
    }

    public String getPropName()
    {
        return propName;
    }
    public void setPropComment(String propComment)
    {
        this.propComment = propComment;
    }

    public String getPropComment()
    {
        return propComment;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setPlaceholder(String placeholder)
    {
        this.placeholder = placeholder;
    }

    public String getPlaceholder()
    {
        return placeholder;
    }
    public void setWidth(Integer width)
    {
        this.width = width;
    }

    public Integer getWidth()
    {
        return width;
    }
    public void setDefaultValue(String defaultValue)
    {
        this.defaultValue = defaultValue;
    }

    public String getDefaultValue()
    {
        return defaultValue;
    }
    public void setColumnSize(Long columnSize)
    {
        this.columnSize = columnSize;
    }

    public Long getColumnSize()
    {
        return columnSize;
    }
    public void setLabelWidth(Long labelWidth)
    {
        this.labelWidth = labelWidth;
    }

    public Long getLabelWidth()
    {
        return labelWidth;
    }
    public void setPrefix(String prefix)
    {
        this.prefix = prefix;
    }

    public String getPrefix()
    {
        return prefix;
    }
    public void setPostfix(String postfix)
    {
        this.postfix = postfix;
    }

    public String getPostfix()
    {
        return postfix;
    }
    public void setClearable(String clearable)
    {
        this.clearable = clearable;
    }

    public String getClearable()
    {
        return clearable;
    }
    public void setNullable(String nullable)
    {
        this.nullable = nullable;
    }

    public String getNullable()
    {
        return nullable;
    }
    public void setRelId(Long relId)
    {
        this.relId = relId;
    }

    public Long getRelId()
    {
        return relId;
    }

    public Long getMasterDataId() {
		return masterDataId;
	}

	public void setMasterDataId(Long masterDataId) {
		this.masterDataId = masterDataId;
	}

	public List<DsmMasterDataRule> getDsmMasterDataRules() {
		return dsmMasterDataRules;
	}

	public void setDsmMasterDataRules(List<DsmMasterDataRule> dsmMasterDataRules) {
		this.dsmMasterDataRules = dsmMasterDataRules;
	}

	public Long[] getRules() {
		return rules;
	}

	public void setRules(Long[] rules) {
		this.rules = rules;
	}

	public String getRelName() {
		return relName;
	}

	public void setRelName(String relName) {
		this.relName = relName;
	}

	public List<DsmDimensionDetail> getOptions() {
		return options;
	}

	public void setOptions(List<DsmDimensionDetail> options) {
		this.options = options;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("masterDataInstallId", getMasterDataInstallId())
            .append("modelEntityPropId", getModelEntityPropId())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("propName", getPropName())
            .append("updateTime", getUpdateTime())
            .append("propComment", getPropComment())
            .append("type", getType())
            .append("placeholder", getPlaceholder())
            .append("width", getWidth())
            .append("defaultValue", getDefaultValue())
            .append("columnSize", getColumnSize())
            .append("labelWidth", getLabelWidth())
            .append("prefix", getPrefix())
            .append("postfix", getPostfix())
            .append("clearable", getClearable())
            .append("nullable", getNullable())
            .append("relId", getRelId())
            .toString();
    }
}