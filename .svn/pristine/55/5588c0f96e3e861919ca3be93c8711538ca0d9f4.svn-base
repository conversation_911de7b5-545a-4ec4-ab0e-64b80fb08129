<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.mdm.mapper.MdmDataEntityTempMapper">

    <resultMap type="MdmDataEntityTemp" id="MdmDataEntityTempResult">
        <result property="entityId"    column="entity_id"    />
        <result property="registryId"    column="registry_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableComment"    column="table_comment"    />
        <result property="tableSchema"    column="table_schema"    />
        <result property="sqlScripts"    column="sql_scripts"    />
        <result property="batchId"    column="batch_id"    />
        <result property="operType"    column="oper_type"    />
        <result property="modifyFields"    column="modify_fields"    />
         <result property="createBy"    column="create_by"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createId"    column="create_id"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectMdmDataEntityTempVo">
        select entity_id, registry_id, table_name, table_comment, table_schema, sql_scripts, batch_id, oper_type,modify_fields, create_by, update_id, create_time, update_time, create_id, update_by from mdm_data_entity_temp
    </sql>

    <select id="selectMdmDataEntityTempList" parameterType="MdmDataEntityTemp" resultMap="MdmDataEntityTempResult">
        <include refid="selectMdmDataEntityTempVo"/>
        <where>
            <if test="registryId != null "> and registry_id = #{registryId}</if>
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="tableComment != null  and tableComment != ''"> and table_comment = #{tableComment}</if>
            <if test="tableSchema != null  and tableSchema != ''"> and table_schema = #{tableSchema}</if>
            <if test="sqlScripts != null  and sqlScripts != ''"> and sql_scripts = #{sqlScripts}</if>
            <if test="batchId != null  and batchId != ''"> and batch_id = #{batchId}</if>
            <if test="operType != null  and operType != ''"> and oper_type = #{operType}</if>
            <if test="versionNo != null  and versionNo != ''"> and version_no = #{versionNo}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
        </where>
    </select>

    <select id="selectMdmDataEntityTempById" parameterType="Long" resultMap="MdmDataEntityTempResult">
        <include refid="selectMdmDataEntityTempVo"/>
        where entity_id = #{entityId}
    </select>

    <select id="selectMdmDataEntityTempByRegId" parameterType="Long" resultMap="MdmDataEntityTempResult">
        <include refid="selectMdmDataEntityTempVo"/>
        where registry_id = #{regId}
    </select>

    <insert id="insertMdmDataEntityTemp" parameterType="MdmDataEntityTemp" useGeneratedKeys="true" keyProperty="entityId">
        insert into mdm_data_entity_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registryId != null">registry_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="tableComment != null">table_comment,</if>
            <if test="tableSchema != null">table_schema,</if>
            <if test="sqlScripts != null">sql_scripts,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="operType != null">oper_type,</if>
            <if test="modifyFields != null">modify_fields,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registryId != null">#{registryId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="tableComment != null">#{tableComment},</if>
            <if test="tableSchema != null">#{tableSchema},</if>
            <if test="sqlScripts != null">#{sqlScripts},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="operType != null">#{operType},</if>
            <if test="modifyFields != null">#{modifyFields},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateMdmDataEntityTemp" parameterType="MdmDataEntityTemp">
        update mdm_data_entity_temp
        <trim prefix="SET" suffixOverrides=",">
            <if test="registryId != null">registry_id = #{registryId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="tableComment != null">table_comment = #{tableComment},</if>
            <if test="tableSchema != null">table_schema = #{tableSchema},</if>
            <if test="sqlScripts != null">sql_scripts = #{sqlScripts},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="operType != null">oper_type = #{operType},</if>
            <if test="modifyFields != null">modify_fields = #{modifyFields},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where entity_id = #{entityId}
    </update>

    <delete id="deleteMdmDataEntityTempById" parameterType="Long">
        delete from mdm_data_entity_temp where entity_id = #{entityId}
    </delete>

    <delete id="deleteMdmDataEntityTempByIds" parameterType="String">
        delete from mdm_data_entity_temp where entity_id in
        <foreach item="entityId" collection="array" open="(" separator="," close=")">
            #{entityId}
        </foreach>
    </delete>
    <select id="selectOneByRegistryId" resultMap="MdmDataEntityTempResult">
        select
        <include refid="selectMdmDataEntityTempVo"/>
        from mdm_data_entity_temp
        where
        registry_id = #{registryId,jdbcType=NUMERIC}
    </select>
    <delete id="deleteByRegistryId">
        delete
        from mdm_data_entity_temp
        where registry_id = #{registryId,jdbcType=NUMERIC}
    </delete>
    <delete id="deleteByRegistryIds" parameterType="String">
        delete from mdm_data_entity_temp where registry_id in
        <foreach item="regId" collection="array" open="(" separator="," close=")">
            #{regId}
        </foreach>
    </delete>
</mapper>
