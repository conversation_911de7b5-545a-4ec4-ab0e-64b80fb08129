package com.dqms.api.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 接口权限对象 api_define_system
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
public class ApiDefineSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 接口ID */
    @Excel(name = "接口ID")
    private Long defineId;

    /** 应用系统 */
    @Excel(name = "应用系统")
    private String systemName;
    private Long systemId;
    
    private String flag;

    public void setDefineId(Long defineId)
    {
        this.defineId = defineId;
    }

    public Long getDefineId()
    {
        return defineId;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("defineId", getDefineId())
            .append("systemId", getSystemId())
            .toString();
    }
}
