package com.dqms.dic.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dic.domain.DicManualDataInstall;
import com.dqms.dic.service.IDicManualDataInstallService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 模板配置Controller
 *
 * <AUTHOR>
 * @date 2022-06-28
 */
@RestController
@RequestMapping("/dic/dicManualDataInstall")
public class DicManualDataInstallController extends BaseController
{
    @Autowired
    private IDicManualDataInstallService dicManualDataInstallService;

    /**
     * 查询模板配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DicManualDataInstall dicManualDataInstall)
    {
        startPage();
        List<DicManualDataInstall> list = dicManualDataInstallService.selectDicManualDataInstallList(dicManualDataInstall);
        return getDataTable(list);
    }

    /**
     * 导出模板配置列表
     */
    @Log(title = "模板配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DicManualDataInstall dicManualDataInstall)
    {
        List<DicManualDataInstall> list = dicManualDataInstallService.selectDicManualDataInstallList(dicManualDataInstall);
        ExcelUtil<DicManualDataInstall> util = new ExcelUtil<DicManualDataInstall>(DicManualDataInstall.class);
        return util.exportExcel(list, "dicManualDataInstall");
    }

    /**
     * 获取模板配置详细信息
     */
    @GetMapping(value = "/{manualDataInstallId}")
    public AjaxResult getInfo(@PathVariable("manualDataInstallId") Long manualDataInstallId)
    {
        return AjaxResult.success(dicManualDataInstallService.selectDicManualDataInstallById(manualDataInstallId));
    }

    /**
     * 新增模板配置
     */
    @Log(title = "模板配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DicManualDataInstall dicManualDataInstall)
    {
        return toAjax(dicManualDataInstallService.insertDicManualDataInstall(dicManualDataInstall));
    }

    /**
     * 修改模板配置
     */
    @Log(title = "模板配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DicManualDataInstall dicManualDataInstall)
    {
        return toAjax(dicManualDataInstallService.updateDicManualDataInstall(dicManualDataInstall));
    }

    /**
     * 删除模板配置
     */
    @Log(title = "模板配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{manualDataInstallIds}")
    public AjaxResult remove(@PathVariable Long[] manualDataInstallIds)
    {
        return toAjax(dicManualDataInstallService.deleteDicManualDataInstallByIds(manualDataInstallIds));
    }
}
