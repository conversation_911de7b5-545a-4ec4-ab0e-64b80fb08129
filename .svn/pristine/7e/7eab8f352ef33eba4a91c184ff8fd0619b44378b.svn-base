<template>
  <div class="app-container">
    <el-row>
	  <el-col :span="6" >
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>任务分布</span>
		  </div>
	      <div id="runFb" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>执行总览</span>
		  </div>
	      <div id="runZl" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="6">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>完成率</span>
		  </div>
	      <div id="runWcl" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
      <el-col :span="6">
	  	<el-card :body-style="{ padding: '0px' }" class="card" shadow="always" >
	      <div slot="header" class="clearfix">
			    <span>线程池</span>
		  </div>
	      <div id="runThread" style="width:100%; height:200px"></div>
	    </el-card>
	   </el-col>
	  
	  <el-col :span="21">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>任务组执行情况</span>
		  </div>
	      <div id="runGroup" style="width:100%; height:300px"></div>
	    </el-card>
	  </el-col>
	  
	   <el-col :span="3">
		  	<el-card :body-style="{ padding: '0px' }" class="card" shadow="always" >
		      <div slot="header" class="clearfix">
				    <span>代理机</span>
			  </div>
			  <div style="height:300px">
			  	  <div v-for="(item, index) in agentList" :key="'agent'+index" >
				  	<el-alert  type="success" style="margin-top:2px" show-icon v-if="item.status === '0'">{{item.agentName}}</el-alert>
				  	<el-alert  type="info" effect="dark" style="margin-top:2px" show-icon v-if="item.status === '1'">{{item.agentName}}</el-alert>
				  	<el-alert  type="info" effect="dark" style="margin-top:2px" show-icon v-if="item.status === '9'">{{item.agentName}}</el-alert>
				  </div>
		      </div>
		    </el-card>
	   </el-col>

	  <el-col :span="8">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>耗时排名</span>
		  </div>
	      <!-- <div id="runHs" style="width:100%; height:200px"></div> -->
		  <div style="width:100%; height:200px" class="taskView_scrollbar">
			<div v-for="(item, index) in hsList" :key="'taskGroups'+index" >
				<el-alert   
					type="info"  style="margin-top:2px" 
				>{{item.taskName}}（平均时长：{{item.overtime}}秒）</el-alert>
			</div>
	      </div>
	    </el-card>
	  </el-col>
	  		    
	  <el-col :span="8">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>时间分布</span>
		  </div>
	      <div id="runTime" style="width:100%; height:200px"></div>
	    </el-card>
	  </el-col>
	  
	  <el-col :span="8">
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	      <div slot="header" class="clearfix">
			    <span>异常任务</span>
		  </div>
		  <div style="width:100%; height:200px" class="taskView_scrollbar">
			<div v-for="(item, index) in ycList" :key="'taskGroup'+index" >
				<el-alert   
					type="info" effect="dark" 
					style="margin-top:2px" 
					v-if="item.type === 'FAIL'"
				>{{item.name}}</el-alert>
				<el-alert   
					type="info"  style="margin-top:2px" 
					v-if="item.type === 'RUNNING'"
				>{{item.name}}</el-alert>
			</div>
	      </div>
	    </el-card>
	  </el-col>
	  
    </el-row>
  </div>
</template>

<script>
import { getRunFb,getRunZl,getRunWcl,getRunGroup,getRunYc,getRunHs,getRunTime,getRunThread} from "@/api/task/taskView";
import { listAgent,getStatus} from "@/api/basic/agent";
import G6 from '@antv/g6'
import backgroundImage from '@/assets/images/timg.jpg';

export default {
  name: "TaskView",
  components: {
  },
  data() {
    return {
    	ycList:[],
    	agentList:[],
		hsList:[]
    };
  },
  mounted() {
	  this.showRunFb();
	  this.showRunZl();
	  this.showRunWcl();
	  this.showRunGroup();
	  this.showRunYc();
	  this.showRunHs();
	  this.showRunTime();
	  this.showRunThread();
	  this.showAgent();
  },
  methods: {
	  showRunFb() {
    	  getRunFb();
      },
      showRunZl() {
    	  getRunZl();
      },
      showRunWcl() {
    	  getRunWcl();
      },
      showRunGroup() {
    	  getRunGroup();
      },
      showRunYc() {
    	  getRunYc().then(response => {
              this.ycList = response.data;
            });
      },
      showRunHs() {
    	  getRunHs().then(response => {
              this.hsList = response.data;
        	});;
      },
      showRunTime() {
    	  getRunTime();
      },
      showRunThread() {
    	  getRunThread();
      },
      showAgent() {
    	  let queryParams={pageNum: 1,pageSize: 1000};
          listAgent(queryParams).then(response => {
              this.agentList = response.rows;
      			for(var i=0;i<this.agentList.length;i++){
      				getStatus(this.agentList[i].agentId).then(res => {
      					for(var j=0;j<this.agentList.length;j++){
      						if(this.agentList[j].agentId==res.data.agentId){
      						this.agentList[j].status=res.data.status;
      						}
      					}
      				})
             	}
              
            });
      }
  },
  destroyed () {
	  
  }
};
</script>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }
  
  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }
  
  .clearfix:after {
      clear: both
  }
  
  .card{
  	margin:10px;
  }
  .taskView_scrollbar{
	overflow:scroll;
	scrollbar-width: none; /* firefox */
  	-ms-overflow-style: none; /* IE 10+ */
  }
  .taskView_scrollbar::-webkit-scrollbar {
	display: none; /* Chrome Safari */
  }
  </style>