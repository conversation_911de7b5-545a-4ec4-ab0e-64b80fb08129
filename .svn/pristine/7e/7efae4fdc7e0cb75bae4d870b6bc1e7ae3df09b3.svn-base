import request from "@/utils/request";

// 查询分级分类列表
export function listDscEntityPropClass(query) {
  return request({
    url: "/dsc/dscEntityPropClass/list",
    method: "get",
    params: query
  });
}

// 查询分级分类详细
export function getDscEntityPropClass(entityPropClassId) {
  return request({
    url: "/dsc/dscEntityPropClass/" + entityPropClassId,
    method: "get"
  });
}

// 导出数据分类
export function exportDscStandard(query) {
  return request({
    url: "/dsc/dscEntityPropClass/export",
    method: "get",
    params: query
  });
}

// 新增分级分类
export function addDscEntityPropClass(data) {
  return request({
    url: "/dsc/dscEntityPropClass",
    method: "post",
    data: data
  });
}

// 修改分级分类
export function updateDscEntityPropClass(data) {
  return request({
    url: "/dsc/dscEntityPropClass",
    method: "put",
    data: data
  });
}

// 删除分级分类
export function delDscEntityPropClass(entityPropClassId) {
  return request({
    url: "/dsc/dscEntityPropClass/" + entityPropClassId,
    method: "delete"
  });
}

// 导出分级分类
export function exportDscEntityPropClass(query) {
  return request({
    url: "/dsc/dscEntityPropClass/export",
    method: "get",
    params: query
  });
}

//查询分类下拉树结构
export function treeselect() {
  return request({
    url: "/dsc/dscEntityPropClass/treeselect",
    method: "get"
  });
}

// 下载数据分类导入模板
export function importTemplate() {
  return request({
    url: "/dsc/dscEntityPropClass/importTemplate",
    method: "get"
  });
}
