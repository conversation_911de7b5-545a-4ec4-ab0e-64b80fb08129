import request from "@/utils/request";

// 查询分级分类列表
export function listDscEntityProp(query) {
  return request({
    url: "/dsc/dscEntityProp/list",
    method: "get",
    params: query
  });
}

// 查询分级分类详细
export function getDscEntityProp(dscEntityPropId) {
  return request({
    url: "/dsc/dscEntityProp/" + dscEntityPropId,
    method: "get"
  });
}

// 新增分级分类
export function addDscEntityProp(data) {
  return request({
    url: "/dsc/dscEntityProp",
    method: "post",
    data: data
  });
}

// 修改分级分类
export function updateDscEntityProp(data) {
  return request({
    url: "/dsc/dscEntityProp",
    method: "put",
    data: data
  });
}

// 删除分级分类
export function delDscEntityProp(dscEntityPropId) {
  return request({
    url: "/dsc/dscEntityProp/" + dscEntityPropId,
    method: "delete"
  });
}

// 导出分级分类
export function exportDscEntityProp(query) {
  return request({
    url: "/dsc/dscEntityProp/export",
    method: "get",
    params: query
  });
}

// 下载数据分级导入模板
export function importTemplate() {
  return request({
    url: "/dsc/dscEntityProp/importTemplate",
    method: "get"
  });
}

// 数据分类
export function selectClassName() {
  return request({
    url: "/dsc/dscEntityPropClass/selectClassName",
    method: "get"
  });
}

// 祖级路径
export function selectClassNameFull() {
  return request({
    url: "/dsc/dscEntityPropClass/selectClassNameFull",
    method: "get"
  });
}

// 查询分层管理下拉列表
export function findAllEntityProp(shipId) {
  return request({
    url: '/mdm/mdmDataEntityShip/findAllEntityProp/' + shipId,
    method: 'get'
  })
}


// 查询分层管理下拉列表
export function findAllEntity(query) {
  return request({
    url: '/mdm/mdmDataEntityShip/findAllEntityTable',
    method: 'get',
    params: query
  })
}
