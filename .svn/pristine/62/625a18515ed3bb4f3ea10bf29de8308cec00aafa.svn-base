package com.dqms.task.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.vo.EtlTaskVo;

/**
 * 任务管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-11
 */
public interface EtlTaskMapper 
{
    /**
     * 查询任务管理
     * 
     * @param taskId 任务管理ID
     * @return 任务管理
     */
    public EtlTask selectEtlTaskById(Long taskId);
    public List<EtlTask> selectEtlTaskByIds(Long[] taskIds);
    
    /**
     * 查询任务名称是否存在
     * 
     * @param taskName 任务名称
     * @return 结果
     */
    public EtlTask selectEtlTaskByTaskName(String taskName);

    /**
     * 查询任务管理列表
     * 
     * @param etlTask 任务管理
     * @return 任务管理集合
     */
    public List<EtlTask> selectEtlTaskList(EtlTask etlTask);
    public List<EtlTaskVo> selectEtlTaskVoList(EtlTask etlTask);
    public List<EtlTask> selectEtlTaskListPage(EtlTask etlTask);
    public List<EtlTask> selectEtlTaskRelByGroupList(EtlTask etlTask);
    /**
     * 新增任务管理
     * 
     * @param etlTask 任务管理
     * @return 结果
     */
    public int insertEtlTask(EtlTask etlTask);

    /**
     * 修改任务管理
     * 
     * @param etlTask 任务管理
     * @return 结果
     */
    public int updateEtlTask(EtlTask etlTask);
    public int updateEtlTaskNull(EtlTask etlTask);

    /**
     * 删除任务管理
     * 
     * @param taskId 任务管理ID
     * @return 结果
     */
    public int deleteEtlTaskById(Long taskId);

    /**
     * 批量删除任务管理
     * 
     * @param taskIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskByIds(Long[] taskIds);
    /**
     * 查询前置任务管理列表
     */
    public List<EtlTask> selectEtlTaskListW(EtlTask etlTask);
    /**
     * 根据任务关系查询所有全部前后任务
     * 
     * @param etlTask 任务管理
     * @return 任务管理集合
     */
    public List<EtlTask> selectEtlTaskListByRelation(Long[] taskIds);
    /**
     * 根据任务关系查询所有后置任务
     * 
     * @param etlTask 任务管理
     * @return 任务管理集合
     */
    public List<EtlTask> selectEtlTaskListByPos(Long[] taskIds);
    public List<EtlTask> selectEtlTaskListByPre(Long[] taskIds);
    
    /**
     * 修改任务平均时间
     * 
     * @param taskId
     * @return 结果
     */
    public int updateExpectEndTime(Long taskId);
    /**
     * 根据分组查询组内没有前置的所有任务
     * 
     * @param taskGroupId
     * @return 结果
     */
    public List<EtlTask> selectEtlTaskByGroupToStart(@Param("taskGroupId")Long taskGroupId);

}
