import request from '@/utils/request'

// 查询调度日历明细列表
export function listTaskCalendarDetail(query) {
  return request({
    url: '/task/taskCalendarDetail/list',
    method: 'get',
    params: query
  })
}

// 查询调度日历明细详细
export function getTaskCalendarDetail(taskCalendarDetailId) {
  return request({
    url: '/task/taskCalendarDetail/' + taskCalendarDetailId,
    method: 'get'
  })
}

// 新增调度日历明细
export function addTaskCalendarDetail(data) {
  return request({
    url: '/task/taskCalendarDetail',
    method: 'post',
    data: data
  })
}

// 修改调度日历明细
export function updateTaskCalendarDetail(data) {
  return request({
    url: '/task/taskCalendarDetail',
    method: 'put',
    data: data
  })
}

// 删除调度日历明细
export function delTaskCalendarDetail(taskCalendarDetailId) {
  return request({
    url: '/task/taskCalendarDetail/' + taskCalendarDetailId,
    method: 'delete'
  })
}

// 导出调度日历明细
export function exportTaskCalendarDetail(query) {
  return request({
    url: '/task/taskCalendarDetail/export',
    method: 'get',
    params: query
  })
}

//下载任务导入模板
export function importTemplate() {
  return request({
    url: '/task/taskCalendarDetail/importTemplate',
    method: 'get'
  })
}