package com.dqms.dsc.service;

import java.util.List;

import com.dqms.dsc.domain.DscEntityPropClass;
import com.dqms.dsc.domain.DscEntityPropClassTreeSelect;
import com.dqms.dsc.domain.vo.DscEntityPropClassVo;
import com.dqms.dsm.domain.vo.DsmStandardVo;

/**
 * 分级分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscEntityPropClassService 
{
    /**
     * 查询分级分类
     * 
     * @param entityPropClassId 分级分类ID
     * @return 分级分类
     */
    public DscEntityPropClass selectDscEntityPropClassById(Long entityPropClassId);

    /**
     * 查询分级分类列表
     * 
     * @param dscEntityPropClass 分级分类
     * @return 分级分类集合
     */
    public List<DscEntityPropClass> selectDscEntityPropClassList(DscEntityPropClass dscEntityPropClass);

    public List<DscEntityPropClassVo> selectDscEntityPropClassListVo(DscEntityPropClassVo dscEntityPropClassVo);


    /**
     * 新增分级分类
     * 
     * @param dscEntityPropClass 分级分类
     * @return 结果
     */
    public int insertDscEntityPropClass(DscEntityPropClass dscEntityPropClass);

    /**
     * 修改分级分类
     * 
     * @param dscEntityPropClass 分级分类
     * @return 结果
     */
    public int updateDscEntityPropClass(DscEntityPropClass dscEntityPropClass);

    /**
     * 批量删除分级分类
     * 
     * @param entityPropClassIds 需要删除的分级分类ID
     * @return 结果
     */
    public int deleteDscEntityPropClassByIds(Long[] entityPropClassIds);

    /**
     * 删除分级分类信息
     * 
     * @param entityPropClassId 分级分类ID
     * @return 结果
     */
    public int deleteDscEntityPropClassById(Long entityPropClassId);
    
    /**
     * 构建前端所需要树结构
     *
     * @param dscEntityPropClass 任务分类列表
     * @return 树结构列表
     */
    public List<DscEntityPropClass> buildDscEntityPropClassTree(List<DscEntityPropClass> dscEntityPropClass);
    /**
     * 构建前端所需要下拉树结构
     *
     * @param dscEntityPropClass 任务分类列表
     * @return 下拉树结构列表
     */
    public List<DscEntityPropClassTreeSelect> buildDscEntityPropClassTreeSelect(List<DscEntityPropClass> dscEntityPropClass);
    /**
     * 导入分级分类列表
     * @param dscEntityPropClassVoList
     * @param isUpdateSupport
     * @return
     */
    public String importDscEntityPropClass(List<DscEntityPropClassVo> dscEntityPropClassVoList, Boolean isUpdateSupport);

    /**
     * 查询数据分类名称列表
     *
     * @param dscEntityPropClass 分级分类
     * @return 分级分类集合
     */
    public List<DscEntityPropClass> selectClassName(DscEntityPropClass dscEntityPropClass);

    public List<DscEntityPropClass> selectClassNameFull(DscEntityPropClass dscEntityPropClass);

    public List<DscEntityPropClass> getGrandList();
    public List<DscEntityPropClass> getParentList(DscEntityPropClass dscEntityPropClass);
    public List<DscEntityPropClass> getEntityPropClassList(DscEntityPropClass dscEntityPropClass);
}
