package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmQuotaTagRel;
import com.dqms.dsm.service.IDsmQuotaTagRelService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 指标与标签Controller
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
@RestController
@RequestMapping("/dsm/dsmQuotaTagRel")
public class DsmQuotaTagRelController extends BaseController
{
    @Autowired
    private IDsmQuotaTagRelService dsmQuotaTagRelService;

    /**
     * 查询指标与标签列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DsmQuotaTagRel dsmQuotaTagRel)
    {
        startPage();
        List<DsmQuotaTagRel> list = dsmQuotaTagRelService.selectDsmQuotaTagRelList(dsmQuotaTagRel);
        return getDataTable(list);
    }

    /**
     * 导出指标与标签列表
     */
    @Log(title = "指标与标签", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmQuotaTagRel dsmQuotaTagRel)
    {
        List<DsmQuotaTagRel> list = dsmQuotaTagRelService.selectDsmQuotaTagRelList(dsmQuotaTagRel);
        ExcelUtil<DsmQuotaTagRel> util = new ExcelUtil<DsmQuotaTagRel>(DsmQuotaTagRel.class);
        return util.exportExcel(list, "dsmQuotaTagRel");
    }

    /**
     * 获取指标与标签详细信息
     */
    @GetMapping(value = "/{quotaId}")
    public AjaxResult getInfo(@PathVariable("quotaId") Long quotaId)
    {
        return AjaxResult.success(dsmQuotaTagRelService.selectDsmQuotaTagRelById(quotaId));
    }

    /**
     * 新增指标与标签
     */
    @Log(title = "指标与标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmQuotaTagRel dsmQuotaTagRel)
    {
        return toAjax(dsmQuotaTagRelService.insertDsmQuotaTagRel(dsmQuotaTagRel));
    }

    /**
     * 修改指标与标签
     */
    @Log(title = "指标与标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmQuotaTagRel dsmQuotaTagRel)
    {
        return toAjax(dsmQuotaTagRelService.updateDsmQuotaTagRel(dsmQuotaTagRel));
    }

    /**
     * 删除指标与标签
     */
    @Log(title = "指标与标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{quotaIds}")
    public AjaxResult remove(@PathVariable Long[] quotaIds)
    {
        return toAjax(dsmQuotaTagRelService.deleteDsmQuotaTagRelByIds(quotaIds));
    }
}
