package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmStandardTarDept;

/**
 * 标准应用部门Mapper接口
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmStandardTarDeptMapper
{
    /**
     * 查询标准应用部门
     *
     * @param standardId 标准应用部门ID
     * @return 标准应用部门
     */
    public List<DsmStandardTarDept> selectDsmStandardTarDeptById(Long standardId);

    /**
     * 查询标准应用部门列表
     *
     * @param dsmStandardTarDept 标准应用部门
     * @return 标准应用部门集合
     */
    public List<DsmStandardTarDept> selectDsmStandardTarDeptList(DsmStandardTarDept dsmStandardTarDept);

    /**
     * 新增标准应用部门
     *
     * @param dsmStandardTarDept 标准应用部门
     * @return 结果
     */
    public int insertDsmStandardTarDept(DsmStandardTarDept dsmStandardTarDept);

    /**
     * 修改标准应用部门
     *
     * @param dsmStandardTarDept 标准应用部门
     * @return 结果
     */
    public int updateDsmStandardTarDept(DsmStandardTarDept dsmStandardTarDept);

    /**
     * 删除标准应用部门
     *
     * @param standardId 标准应用部门ID
     * @return 结果
     */
    public int deleteDsmStandardTarDeptById(Long standardId);

    /**
     * 批量删除标准应用部门
     *
     * @param standardIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmStandardTarDeptByIds(Long[] standardIds);
}
