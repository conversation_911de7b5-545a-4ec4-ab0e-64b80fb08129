let notice = {
    code: 200,
    msg: "查询成功",
    rows: [
        {
            createBy: "管理员",//公告发布者
            createTime: "2020-09-22 15:07:02",//发布时间
            deptId: null,
            endTime:'2021-07-16',//公告展示结束日期
            startTime:'2021-06-16',//公告展示开始日期
            isStick:true,//是否置顶
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1423,
            noticeTitle: "安全须知1",
            noticeType: "2",
            readNum:55,//已读数量
            readUsers:['小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明','小明'],//已读用户
            status: "0",//公告状态 0(正常) 1(关闭)
            updateBy: "管理员",//公告修改者
            updateTime: "2021-03-19 15:58:48",//公告修改时间
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1424,
            noticeTitle: "安全须知2",
            noticeType: "2",
            readNum:56,
            readUsers:[],
            status: "1",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1425,
            noticeTitle: "安全须知3",
            noticeType: "2",
            readNum:33,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1426,
            noticeTitle: "安全须知4",
            noticeType: "2",
            readNum:51,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1427,
            noticeTitle: "安全须知5",
            noticeType: "2",
            readNum:52,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1428,
            noticeTitle: "安全须知6",
            noticeType: "2",
            readNum:12,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1429,
            noticeTitle: "安全须知7",
            noticeType: "2",
            readNum:23,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1430,
            noticeTitle: "安全须知8",
            noticeType: "2",
            readNum:46,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1431,
            noticeTitle: "安全须知9",
            noticeType: "2",
            readNum:26,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        },
        {
            createBy: "管理员",
            createTime: "2020-09-22 15:07:02",
            endTime:'2021-07-16',
            startTime:'2021-06-16',
            isStick:false,
            deptId: null,
            noticeContent: "<p>知晓并确认：严格遵守国家网络安全、保密法律法规，依照《数据管理办法（暂行）》等公司内部管理制度使用平台及数据，确保安全合规。</p><p>对因未落实大数据平台管理要求而出现下列情形的，公司将对直接责任人及相关负责人按照《员工处分办法（试行）》、《业务风险问责制实施暂行办法》等规定进行问责，若情节严重构成犯罪，将被依法追究刑事责任。该等情形包括但不限于：</p><p>（一）冒用、串用、盗用、泄漏、私自发布或出售大数据平台账号和口令等。</p><p>（二）因账号安全管理或操作不善导致数据被泄露、篡改或破坏等。</p><p>（三）迟报、谎报、瞒报和漏报数据安全事件等重要情况。</p><p>（四）非法和违规收集、存储、窃取、提供或买卖数据。</p>",
            noticeId: 1432,
            noticeTitle: "安全须知10",
            noticeType: "2",
            readNum:16,
            readUsers:[],
            status: "0",
            updateBy: "管理员",
            updateTime: "2021-03-19 15:58:48",
        }
    ],
    total: 10
}
export default notice;