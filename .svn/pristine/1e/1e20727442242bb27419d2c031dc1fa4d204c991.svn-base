package com.dqms.dsm.service.impl;

import java.util.List;
import com.dqms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmModelEntityShipMapper;
import com.dqms.dsm.domain.DsmModelEntityShip;
import com.dqms.dsm.service.IDsmModelEntityShipService;

/**
 * 模型关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@Service
public class DsmModelEntityShipServiceImpl implements IDsmModelEntityShipService
{
    @Autowired
    private DsmModelEntityShipMapper dsmModelEntityShipMapper;

    /**
     * 查询模型关系
     *
     * @param modelEntityShipId 模型关系ID
     * @return 模型关系
     */
    @Override
    public DsmModelEntityShip selectDsmModelEntityShipById(Long modelEntityShipId)
    {
        return dsmModelEntityShipMapper.selectDsmModelEntityShipById(modelEntityShipId);
    }

    /**
     * 查询模型关系列表
     *
     * @param dsmModelEntityShip 模型关系
     * @return 模型关系
     */
    @Override
    public List<DsmModelEntityShip> selectDsmModelEntityShipList(DsmModelEntityShip dsmModelEntityShip)
    {
        return dsmModelEntityShipMapper.selectDsmModelEntityShipList(dsmModelEntityShip);
    }

    /**
     * 新增模型关系
     *
     * @param dsmModelEntityShip 模型关系
     * @return 结果
     */
    @Override
    public int insertDsmModelEntityShip(DsmModelEntityShip dsmModelEntityShip)
    {
        dsmModelEntityShip.setCreateTime(DateUtils.getNowDate());
        return dsmModelEntityShipMapper.insertDsmModelEntityShip(dsmModelEntityShip);
    }

    /**
     * 修改模型关系
     *
     * @param dsmModelEntityShip 模型关系
     * @return 结果
     */
    @Override
    public int updateDsmModelEntityShip(DsmModelEntityShip dsmModelEntityShip)
    {
        dsmModelEntityShip.setUpdateTime(DateUtils.getNowDate());
        return dsmModelEntityShipMapper.updateDsmModelEntityShip(dsmModelEntityShip);
    }

    /**
     * 批量删除模型关系
     *
     * @param modelEntityShipIds 需要删除的模型关系ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityShipByIds(Long[] modelEntityShipIds)
    {
        return dsmModelEntityShipMapper.deleteDsmModelEntityShipByIds(modelEntityShipIds);
    }

    /**
     * 删除模型关系信息
     *
     * @param modelEntityShipId 模型关系ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelEntityShipById(Long modelEntityShipId)
    {
        return dsmModelEntityShipMapper.deleteDsmModelEntityShipById(modelEntityShipId);
    }
    
    @Override
    public int deleteDsmModelEntityShipByDsmModelEntityId(Long modelEntityId)
    {
        return dsmModelEntityShipMapper.deleteDsmModelEntityShipByDsmModelEntityId(modelEntityId);
    }
}
