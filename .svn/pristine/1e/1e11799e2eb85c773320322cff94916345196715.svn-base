package com.dqms.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dqms.api.domain.ApiDefine;
import com.dqms.api.domain.ApiDefineHis;
import com.dqms.api.enums.ApiConstants;
import com.dqms.api.job.ApiJob;
import com.dqms.api.service.IApiDefineHisService;
import com.dqms.api.service.IApiDefineService;
import com.dqms.basic.service.ISysSystemService;
import com.dqms.common.constant.HttpStatus;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.ip.IpUtils;
import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.system.service.ISysConfigService;
import com.dqms.utils.ThreadPoolUtils;

/**
 * 接口管理Controller
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@RestController
public class ApiController extends BaseController
{
	private static final Logger log = LoggerFactory.getLogger(ApiController.class);

    @Autowired
    private IApiDefineService apiDefineService;

    @Autowired
    private ISysSystemService sysSystemService;

    @Autowired
    private IApiDefineHisService apiDefineHisService;

    @PostMapping("/api")
    public Object api()
    {
    	HttpServletRequest req = ServletUtils.getRequest();
    	ApiDefineHis apiDefineHis = new ApiDefineHis();
    	apiDefineHis.setStartTime(DateUtils.getNowDate());
		apiDefineHis.setStatus(ApiConstants.TASK_CALENDER_STATUS_READY);
    	String ip = IpUtils.getIpAddr(req);
    	log.info("API接口调用：地址"+ip);
    	apiDefineHis.setIpAddress(ip);
		apiDefineHis.setMasesge("接口排队中等待执行");
		apiDefineHisService.insertApiDefineHis(apiDefineHis);

    	Map<String ,Object> map =new HashMap<String ,Object>();
    	String system = req.getParameter("system");
    	String token = req.getParameter("token");
    	String page = req.getParameter("page");
    	String pageSize = req.getParameter("pageSize");
    	String api = req.getParameter("api");
    	if(page==null) {
    		page="0";
    	}

    	ApiDefine apiDefine = new ApiDefine();
    	apiDefine.setDefineCode(api);
    	apiDefine.setStatus(ApiConstants.STATUS_ABLE);
    	List<ApiDefine> apilist = apiDefineService.selectApiDefineList(apiDefine);
    	if(apilist!=null&&apilist.size()>0) {
    		apiDefine=apilist.get(0);
			apiDefineHis.setDefineId(apiDefine.getDefineId());
    	}else {
    		apiDefineHis.setEndTime(DateUtils.getNowDate());
    		apiDefineHis.setStatus(ApiConstants.TASK_CALENDER_STATUS_FAILED);
    		apiDefineHis.setMasesge("接口不存在或已禁用");
    		SpringUtils.getBean(IApiDefineHisService.class).updateApiDefineHis(apiDefineHis);
    		return new AjaxResult(HttpStatus.ERROR, "接口不存在或已禁用", map);
    	}

    	SysSystem sysSystem = new SysSystem();
    	sysSystem.setCode(system);
    	List<SysSystem> list= SpringUtils.getBean(ISysSystemService.class).selectSysSystemList(sysSystem);
    	if(list==null||list.size()==0) {
    		apiDefineHis.setEndTime(DateUtils.getNowDate());
    		apiDefineHis.setStatus(ApiConstants.TASK_CALENDER_STATUS_FAILED);
    		apiDefineHis.setMasesge("系统不正确，禁止API调用");
    		SpringUtils.getBean(IApiDefineHisService.class).updateApiDefineHis(apiDefineHis);
    		return new AjaxResult(HttpStatus.ERROR, "系统不正确，禁止API调用", map);
    	}else {
    		SysSystem SysSystem=list.get(0);
    		apiDefineHis.setSystemId(SysSystem.getSystemId());
    		if(!SysSystem.getPassword().equals(token)) {
        		apiDefineHis.setEndTime(DateUtils.getNowDate());
        		apiDefineHis.setStatus(ApiConstants.TASK_CALENDER_STATUS_FAILED);
        		apiDefineHis.setMasesge( "系统令牌不正确，禁止API调用");
        		SpringUtils.getBean(IApiDefineHisService.class).updateApiDefineHis(apiDefineHis);
    			return new AjaxResult(HttpStatus.ERROR, "系统令牌不正确，禁止API调用", map);
    		}else {
    			if(SysSystem.getIpAddress()!=null&&!SysSystem.getIpAddress().equals("")&&SysSystem.getIpAddress().indexOf(ip)<0) {
            		apiDefineHis.setEndTime(DateUtils.getNowDate());
            		apiDefineHis.setStatus(ApiConstants.TASK_CALENDER_STATUS_FAILED);
            		apiDefineHis.setMasesge( "非法IP，需要配置应用IP白名单，禁止API调用");
            		SpringUtils.getBean(IApiDefineHisService.class).updateApiDefineHis(apiDefineHis);
    				return new AjaxResult(HttpStatus.ERROR, "非法IP，需要配置应用IP白名单，禁止API调用", map);
    			}
    		}
    	}
    	if(apiDefine.getDefineType().equals(ApiConstants.TASK_JDBC)) {
    		return apiDefineService.getJDBC( req.getParameter("param"),"API",apiDefineHis,apiDefine);
    	}else if(apiDefine.getDefineType().equals(ApiConstants.TASK_TCP)) {
    		return apiDefineService.getTCP( req.getParameter("param"),"API",apiDefineHis,apiDefine);
    	}else if(apiDefine.getDefineType().equals(ApiConstants.TASK_KAFKA)) {
    		return apiDefineService.getKAFKA( page,pageSize, req.getParameter("topic"),"API",apiDefineHis,apiDefine);
    	}else if(apiDefine.getDefineType().equals(ApiConstants.TASK_TASK)) {
    		return apiDefineService.getTASK(req.getParameter("param"),"API",apiDefineHis,apiDefine);
    	}else {
        	int thread = Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("api.thread.num"));
        	Future future = ThreadPoolUtils.addTaskCallable(-2L, thread, new ApiJob(req,apiDefineHis,apiDefine));
        	try {
    			return future.get();
    		} catch (InterruptedException | ExecutionException e) {
    			// TODO Auto-generated catch block
    			e.printStackTrace();
    			return new AjaxResult(HttpStatus.ERROR,e.getMessage(), new HashMap<String ,Object>());
    		}
    	}

    }
    @PostMapping("/apiTest")
    public Object apiTest()
    {
    	HttpServletRequest req = ServletUtils.getRequest();
    	ApiDefineHis apiDefineHis = new ApiDefineHis();
    	apiDefineHis.setStartTime(DateUtils.getNowDate());
    	String ip = IpUtils.getIpAddr(req);
    	log.info("API接口调用：地址"+ip);
    	apiDefineHis.setIpAddress(ip);
    	String api = req.getParameter("api");
    	String page = req.getParameter("page");
    	String param = req.getParameter("param");
    	String pageSize = req.getParameter("pageSize");
    	if(page==null) {
    		page="0";
    	}
    	ApiDefine apiDefine = new ApiDefine();
    	apiDefine.setDefineCode(api);
    	//apiDefine.setStatus(ApiConstants.STATUS_ABLE);
    	List<ApiDefine> apilist = apiDefineService.selectApiDefineList(apiDefine);
    	if(apilist!=null&&apilist.size()>0) {
    		apiDefine=apilist.get(0);
			apiDefineHis.setDefineId(apiDefine.getDefineId());
    	}
    	if(apiDefine.getDefineType().equals(ApiConstants.TASK_JDBC)) {
    		return apiDefineService.getJDBC( param,"TEST",apiDefineHis,apiDefine);
    	}else if(apiDefine.getDefineType().equals(ApiConstants.TASK_TCP)) {
    		return apiDefineService.getTCP( param,"TEST",apiDefineHis,apiDefine);
    	}else if(apiDefine.getDefineType().equals(ApiConstants.TASK_KAFKA)) {
    		return apiDefineService.getKAFKA( page,pageSize, req.getParameter("topic"),"TEST",apiDefineHis,apiDefine);
    	}else if(apiDefine.getDefineType().equals(ApiConstants.TASK_TASK)) {
    		return apiDefineService.getTASK( req.getParameter("param"),"TEST",apiDefineHis,apiDefine);
    	}else {
    		return apiDefineService.getData( page,pageSize, param,"TEST",apiDefineHis,apiDefine);
    	}
		
    }
}
