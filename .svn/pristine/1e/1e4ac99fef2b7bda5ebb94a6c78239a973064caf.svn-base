package com.dqms.task.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.domain.vo.EtlTaskAssets;
import com.dqms.task.domain.vo.EtlTaskInstanceVo;
import com.dqms.task.domain.vo.TaskRunFbVo;
import com.dqms.task.domain.vo.TaskRunTimeVo;

/**
 * 任务监控Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-15
 */
public interface EtlTaskInstanceMapper 
{
    /**
     * 查询任务监控
     * 
     * @param taskInstanceId 任务监控ID
     * @return 任务监控
     */
    public EtlTaskInstance selectEtlTaskInstanceById(Long taskInstanceId);

    /**
     * 查询任务监控列表
     * 
     * @param etlTaskInstance 任务监控
     * @return 任务监控集合
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceList(EtlTaskInstance etlTaskInstance);
    public List<EtlTaskInstanceVo> selectEtlTaskInstanceVoList(EtlTaskInstance etlTaskInstance);
    public List<EtlTaskInstance> selectEtlTaskInstanceListByPage(EtlTaskInstance etlTaskInstance);
    
    /**
     * 根据批次查询任务监控列表
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceListByBatch(EtlTaskInstance etlTaskInstance);

    /**
     * 新增任务监控
     * 
     * @param etlTaskInstance 任务监控
     * @return 结果
     */
    public int insertEtlTaskInstance(EtlTaskInstance etlTaskInstance);

    /**
     * 修改任务监控
     * 
     * @param etlTaskInstance 任务监控
     * @return 结果
     */
    public int updateEtlTaskInstance(EtlTaskInstance etlTaskInstance);
    public int updateEtlTaskInstanceHisByInstance(EtlTaskInstance etlTaskInstance);

    /**
     * 删除任务监控
     * 
     * @param taskInstanceId 任务监控ID
     * @return 结果
     */
    public int deleteEtlTaskInstanceById(Long taskInstanceId);

    /**
     * 批量删除任务监控
     * 
     * @param taskInstanceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskInstanceByIds(Long[] taskInstanceIds);
    
    /**
     * 批量增加任务监控
     * 
     * @param etlTaskInstances List
     * @return 结果
     */
    void batchAdd(@Param("etlTaskInstances")List<EtlTaskInstance> etlTaskInstances);
    /**
     * 批量增加任务监控历史根据批次
     * 
     * @param String batchId
     * @return 结果
     */
    void batchAddHis(@Param("batchId")String batchId);
	/**
	 * 根据taskIds删除任务实例
	 * @param taskIds
	 */
	void deleteEtlTaskInstanceByTaskIds(EtlTaskInstance etlTaskInstance);
	
    /**
     * 查询任务Ids分组查所有监控列表
     * 
     * @param etlTaskInstance 任务监控
     * @return 任务监控集合
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceListByIds(EtlTaskInstance etlTaskInstance);
    
    /**
     * 查询所有可以执行的任务列表
     * 
     * @param etlTaskInstance 任务监控
     * @return 任务监控集合
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceListByAbleRun(EtlTaskInstance etlTaskInstance);
    
    /**
     * 开始任务
     * 
     * @param taskInstanceId 任务监控ID
     * @return 结果
     */
    public int startEtlTaskInstance(@Param("taskInstanceId")Long taskInstanceId,@Param("date")Date date);
    
    /**
     *根据任务ID终止所有后续任务及组任务
     * 
     * @param taskId 任务监ID
     * @return 结果
     */
    public int terminateEtlTaskInstanceByIds(@Param("taskIds")Long[] taskIds, @Param("batchId")String batchId);
    
    /**
     *修改PriorityNo不为0的所有任务为终止
     * 
     * @param batchId 批次
     * @return 结果
     */
    public int terminateEtlTaskInstanceByPosGroup(@Param("batchId")String batchId);
    
    /**
     *终止批次所有任务
     * 
     * @param batchId 批次
     * @return 结果
     */
    public int terminateAllEtlTaskInstance(@Param("batchId")String batchId);
    
    /**
     * 查看当前组内任务是否都已经完成
     * 
     * @param EtlTaskInstance etlTaskInstanceID
     * @return 结果
     */
    public int getUnTaskEtlTaskByBatchIdAndPriorityNo(EtlTaskInstance etlTaskInstance);
    
    /**
     * 删除全部任务监控
     * 
     * @param batchId
     * @return 结果
     */
    public int deleteAllEtlTaskInstance();
    
    /**
     * 查询任务监控
     * 
     * @param taskInstanceId 任务监控ID
     * @return 任务监控
     */
    public EtlTaskInstance selectBreakpointEtlTaskInstanceById(Long taskInstanceId);
    
    /**
     *终止前查询所有失败和终止的任务
     * 
     * @param batchId 批次
     * @return 结果
     */
    public List<EtlTaskInstance> selectEtlTaskInstanceByFailAndTermination(@Param("batchId")String batchId);
    /**
     *重置所有失败和终止任务为待执行
     * 
     * @param batchId 批次
     * @return 结果
     */
    public int resetErrorAllEtlTaskInstance(@Param("batchId")String batchId);
    
    /**
     * 查看调度上次是否都已经完成
     * 
     * @param taskScheduleId taskScheduleId
     * @return 结果
     */
    public int getUnTaskEtlTaskBySchedule(@Param("taskScheduleId")Long taskScheduleId);
    
    public List<TaskRunFbVo> getRunFb();
    public List<TaskRunFbVo> getRunZl();
    public List<TaskRunFbVo> getRunWcl();
    public List<TaskRunFbVo> getRunGroup();
    public List<EtlTask> getRunHs();
    public List<TaskRunTimeVo> getRunTime();
    public List<EtlTask> getRunYc(@Param("coefficient")float coefficient);
    
    public List<EtlTaskInstance> selectEtlTaskInstanceListByPos(@Param("array")Long[] array,@Param("batchId")String batchId);
    public List<EtlTaskInstance> selectEtlTaskInstanceListByPre(@Param("array")Long[] array,@Param("batchId")String batchId);
    
}
