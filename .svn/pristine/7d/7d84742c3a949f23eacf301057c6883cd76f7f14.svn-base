<template>
  <div class="app-container">
    <el-row>
      <el-col
        :span="4"
        v-for="(item, index) in agentList"
        :key="'agent' + index"
      >
        <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
          <img src="@/assets/images/agent.png" class="image" />
          <div style="padding: 14px;">
            <span style="color: #228B22;" v-show="item.status === '0'"
              ><i class="el-icon-connection"></i>{{ item.agentName }}</span
            >
            <span style="color: #F56C6C;" v-show="item.status === '1'"
              ><i class="el-icon-link"></i>{{ item.agentName }}</span
            >
            <span style="color: #909399;" v-show="item.status === '9'"
              ><i class="el-icon-loading"></i>{{ item.agentName }}</span
            >
            <div class="bottom clearfix">
              <time class="time">
                <span><i class="el-icon-place"></i>{{ item.serverIp }}</span>
              </time>
              <el-button type="text" class="button" @click="handleDelete(item)"
                ><i class="el-icon-delete"></i
              ></el-button>
              <el-button type="text" class="button" @click="handleUpdate(item)"
                ><i class="el-icon-edit"></i
              ></el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4" :key="'agent'">
        <el-card :body-style="{ padding: '0px' }" class="card" shadow="hover">
          <img src="@/assets/images/add.png" class="image" @click="handleAdd" />
          <div style="padding: 14px;">
            <span @click="handleAdd" style="color: #A9A9A9;">点击扩展</span>
            <div class="bottom clearfix">
              <time class="time"> &nbsp;</time>
              <el-button type="text" class="button" @click="handleAdd"
                ><i class="el-icon-circle-plus-outline"></i
              ></el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 添加或修改应用系统对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="639px"
      append-to-body
      class="dialog"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="代理记编码" prop="agentCode">
              <el-input
                v-model="form.agentCode"
                placeholder="请输入代理记编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代理机名称" prop="agentName">
              <el-input
                v-model="form.agentName"
                placeholder="请输入代理机名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="服务器地址" prop="serverIp">
          <el-input v-model="form.serverIp" placeholder="请输入服务器地址" />
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="服务器端口" prop="serverPort">
              <el-input
                v-model="form.serverPort"
                placeholder="请输入服务器端口"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务器用户" prop="serverUser">
              <el-input
                v-model="form.serverUser"
                placeholder="请输入服务器用户"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务器密码" prop="serverPass">
              <el-input
                v-model="form.serverPass"
                placeholder="请输入服务器密码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户端端口" prop="agentPort">
              <el-input
                v-model="form.agentPort"
                placeholder="请输入客户端端口"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="客户端路径" prop="agentAdd">
          <el-input
            v-model="form.agentAdd"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务器类型" prop="serverType">
              <el-select
                v-model="form.serverType"
                placeholder="请选择服务器类型"
              >
                <el-option
                  v-for="dict in serverTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="并发线程数" prop="threadNum">
              <el-input-number
                v-model="form.threadNum"
                :min="0"
                :max="100"
                label="请输入并发线程数"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 100%;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.card {
  margin: 15px;
}
</style>
<script>
import {
  listAgent,
  getAgent,
  delAgent,
  addAgent,
  updateAgent,
  exportAgent,
  getStatus
} from "@/api/basic/agent";
export default {
  name: "Agent",
  components: {},
  data() {
    return {
      agents: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 代理机管理表格数据
      agentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 服务器类型字典
      serverTypeOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
        agentCode: null,
        agentName: null,
        serverType: null,
        serverIp: null,
        serverPort: null,
        serverUser: null,
        serverPass: null,
        agentAdd: null,
        agentPort: null,
        threadNum: null
      },
      // 表单参数
      form: {
        agentId: null,
        agentCode: null,
        agentName: null,
        serverType: null,
        serverIp: null,
        serverPort: null,
        serverUser: null,
        serverPass: null,
        agentAdd: null,
        agentPort: null,
        threadNum: 0
      },
      // 表单校验
      rules: {
        agentCode: [
          { required: true, message: "代理记编码不能为空", trigger: "blur" }
        ],
        agentName: [
          { required: true, message: "代理机名称不能为空", trigger: "blur" }
        ],
        serverType: [
          { required: true, message: "服务器类型不能为空", trigger: "change" }
        ],
        serverIp: [
          { required: true, message: "服务器地址不能为空", trigger: "blur" }
        ],
        serverPort: [
          { required: true, message: "服务器端口不能为空", trigger: "blur" }
        ],
        agentAdd: [
          { required: true, message: "客户端路径不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_server_type").then(response => {
      this.serverTypeOptions = response.data;
    });
  },
  mounted() {},
  methods: {
    /** 查询应用系统列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      listAgent(this.queryParams).then(response => {
        this.agentList = response.rows;
        this.total = response.total;
        this.loading = false;
        for (var i = 0; i < this.agentList.length; i++) {
          getStatus(this.agentList[i].agentId).then(res => {
            for (var j = 0; j < this.agentList.length; j++) {
              if (this.agentList[j].agentId == res.data.agentId) {
                this.agentList[j].status = res.data.status;
              }
            }
          });
        }
      });
    },
    // 服务器类型字典翻译
    serverTypeFormat(row, column) {
      return this.selectDictLabel(this.serverTypeOptions, row.serverType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        agentId: null,
        agentCode: null,
        agentName: null,
        serverType: null,
        serverIp: null,
        serverPort: null,
        serverUser: null,
        serverPass: null,
        agentAdd: null,
        agentPort: null,
        threadNum: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.agentId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加代理机管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const agentId = row.agentId || this.ids;
      getAgent(agentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改代理机管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.agentId != null) {
            updateAgent(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAgent(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const agentIds = row.agentId || this.ids;
      this.$confirm(
        '是否确认删除代理机管理编号为"' + agentIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function() {
          return delAgent(agentIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有应用系统数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return exportSystem(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        });
    }
  }
};
</script>
