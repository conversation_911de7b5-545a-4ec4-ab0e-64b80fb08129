<template>
  <div class="app-container" style="padding:20px;">
    <el-card shadow="always" style="height:550px;">
      <div slot="header" class="clearfix">
        <el-button
          class="runBtn"
          type="primary"
          icon="el-icon-caret-right"
          size="mini"
          @click="handleRestart(true)"
          >运行</el-button
        >
        <el-link type="primary" style="float: right"
          >API调用地址：http://localhost:8080/api</el-link
        >
      </div>
      <el-col :span="12">
        <div id="sqlQueryAPICol" style="height:460px;">
          <textarea
            ref="myCm"
            class="sqlTextarea"
            id="sqlQueryAPIText"
            name="sqlQueryAPIText"
          ></textarea>
        </div>
      </el-col>
      <el-col :span="12">
        <el-table v-loading="loading" :data="apiDefineParamList" height="480">
          <el-table-column align="left" label="输入参数">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="参数名称"
              align="center"
              prop="defineParamName"
            >
              <template slot-scope="scope">
                <el-input v-model="scope.row.defineParamName" />
              </template>
            </el-table-column>
            <el-table-column label="参数说明" align="center" prop="remark">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark" />
              </template>
            </el-table-column>
            <el-table-column label="是否必填" align="center" prop="isMust">
              <template slot-scope="scope">
                <el-select v-model="scope.row.isMust" size="small">
                  <el-option
                    v-for="dict in isMustOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              label="参数类型"
              align="center"
              prop="defineParamType"
            >
              <template slot-scope="scope">
                <el-select v-model="scope.row.defineParamType" size="small">
                  <el-option
                    v-for="dict in defineParamTypeOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="参数值" align="center" prop="defualtValue">
              <template slot-scope="scope">
                <el-input v-model="scope.row.defualtValue" />
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
    </el-card>
    <el-tabs type="border-card" ref="tabs" v-model="activeName">
      <el-tab-pane label="结果" name="first">
        <div
          style="overflow-y: auto;height:100%;white-space: pre-wrap;background:#000; color:#FFF;border: 1px solid #8d8c8c;padding: 5px;white-space: pre-wrap;word-wrap: break-word;"
          v-html="data"
        ></div>
      </el-tab-pane>
      <el-tab-pane label="脚本" name="second">
        <div
          style="overflow-y: auto;height:100%;white-space: pre-wrap;background:#000; color:#FFF"
          v-html="script"
        ></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import CodeMirror from "codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/merge/merge.js";
import "codemirror/addon/merge/merge.css";
import "codemirror/addon/hint/show-hint.css";
import "codemirror/addon/hint/sql-hint.js";
import { codemirror } from "vue-codemirror";
import "codemirror/theme/monokai.css";
import "codemirror/theme/idea.css";
import "codemirror/mode/sql/sql.js";
import "codemirror/addon/hint/show-hint.js";
import "codemirror/addon/display/autorefresh.js";
import DiffMatchPatch from "diff-match-patch";
import sqlFormatter from "sql-formatter";
window.diff_match_patch = DiffMatchPatch;
window.DIFF_DELETE = -1;
window.DIFF_INSERT = 1;
window.DIFF_EQUAL = 0;

import {
  listApiDefine,
  getApiDefine,
  delApiDefine,
  addApiDefine,
  updateApiDefine,
  exportApiDefine,
  changeStatus,
  updateApiDefineSystem,
  updateParam,
  getData
} from "@/api/api/apiDefine";
import { listApiDefineParam } from "@/api/api/apiDefineParam";
import { getToken } from "@/utils/auth";
import { listSystemAll } from "@/api/basic/system";
import { listDatasourceAll } from "@/api/basic/datasource";
import request from "@/utils/request";
import * as echarts from "echarts";
export default {
  name: "apiDebug",
  components: {},
  data() {
    return {
      //SQL编辑器
      table: {}, // 用于自定义列表宽度
      info: [], // 用于自定义列表宽度s
      idbLexicon: {},
      isRealTimeTip: false, // 是否是实时的提示
      activeName: "first",
      sqlInfo: [],
      codeEditor: null,
      defaultProps: {
        icon: "iconOne",
        label: "label",
        isLeaf: "leaf"
      },
      tableData: [],
      infoData: [],
      // 遮罩层
      loading: false,
      defineId: null,
      apiDefineParamList: [],
      form: {},
      // 是否必填字典
      isMustOptions: [],
      // 参数类型字典
      defineParamTypeOptions: [],
      // 输出类型字典
      defineColumnTypeOptions: [],
      // 是否脱敏字典
      isDesensitizationOptions: [],
      script: "无",
      data: "无"
    };
  },
  created() {
    this.defineId = this.$route.params && this.$route.params.defineId;
    this.getSystem();
    this.getDatasource();

    this.getDicts("sys_yes_no").then(response => {
      this.isMustOptions = response.data;
    });
    this.getDicts("dsc_desensitization_type").then(response => {
      this.isDesensitizationOptions = response.data;
    });
    this.getDicts("api_define_data_type").then(response => {
      this.defineParamTypeOptions = response.data;
      this.defineColumnTypeOptions = response.data;
    });
  },
  mounted() {
    this.getApi();
  },
  methods: {
    /** 获取应用系统 */
    getSystem() {
      listSystemAll().then(response => {
        this.systemOptions = response.data;
      });
    },
    getDatasource() {
      listDatasourceAll().then(response => {
        this.datasourceOptions = response.data;
      });
    },
    getApi() {
      getApiDefine(this.defineId).then(response => {
        this.form = response.data;
        this.init();
      });
      let queryParams = { defineId: this.defineId };
      listApiDefineParam(queryParams).then(response => {
        this.apiDefineParamList = response.rows;
      });
    },
    handleRestart() {
      let sqlScript =
        this.getSelection() != "" ? this.getSelection() : this.getValue();
      if (sqlScript == null || sqlScript == "") {
        this.$message.warning("请输入脚本后再点击运行");
        return;
      }
      this.form.defineSql = sqlScript;
      this.form.apiDefineParams = [];
      for (var i = 0; i < this.apiDefineParamList.length; i++) {
        this.form.apiDefineParams.push({
          defineParamName: this.apiDefineParamList[i].defineParamName,
          remark: this.apiDefineParamList[i].remark,
          isMust: this.apiDefineParamList[i].isMust,
          defineParamType: this.apiDefineParamList[i].defineParamType,
          defualtValue: this.apiDefineParamList[i].defualtValue
        });
      }
      getData(this.form).then(response => {
        console.log(response);
        this.script = response.data.script;
        this.data = response.data.data;
      });
    },
    init() {
      // 实例初始化
      const targetF = document.getElementById("sqlQueryAPICol");
      targetF.innerHTML = '<textarea id="sqlQueryAPIText" name="sqlQueryAPIText" />';
      const target = document.getElementById("sqlQueryAPIText");
      this.codeEditor = CodeMirror.fromTextArea(target, {
        lineNumbers: true, //显示行号
        styleActiveLine: true,
        matchBrackets: true,
        mode: "text/x-sql",
        connect: "align",
        theme: "monokai",
        autoCloseBrackets: true,
        autoRefresh: true,
        readOnly: false,
        hintOptions: {
          completeSingle: false,
          tables: this.idbLexicon
        },
        extraKeys: {
          "Ctrl-Space": editor => {
            editor.showHint();
          }
        }
      });
      this.codeEditor.setValue(this.form.defineSql);
      this.codeEditor.on("keypress", editor => {
        const __Cursor = editor.getDoc().getCursor();
        const __Token = editor.getTokenAt(__Cursor);
        if (
          __Token.type &&
          __Token.type !== "string" &&
          __Token.type !== "punctuation" &&
          __Token.string.indexOf(".") === -1
        ) {
          // 把输入的关键字统一变成大写字母
          editor.replaceRange(
            __Token.string.toUpperCase(),
            {
              line: __Cursor.line,
              ch: __Token.start
            },
            {
              line: __Cursor.line,
              ch: __Token.end
            },
            __Token.string
          );
        }
        editor.showHint();
      });
    },
    // 设置value
    setValue(val) {
      this.codeEditor.setValue(val);
    },
    // 获取value
    getValue() {
      return this.codeEditor.getValue();
    },
    // 获取选中内容
    getSelection() {
      return this.codeEditor.getSelection();
    }
  },
  destroyed() {}
};
</script>
<style scoped>
>>> .CodeMirror pre.CodeMirror-line,
>>> .CodeMirror pre.CodeMirror-line-like {
  line-height: 22px;
}
>>> .CodeMirror {
  height: 480px !important;
}
>>> .CodeMirror-merge,
.CodeMirror-merge .CodeMirror {
  height: 480px !important;
}
>>> .CodeMirror-merge-r-chunk {
  background: #38380d !important;
}

>>> .infinite-list-wrapper {
  height: calc(100vh - 84px);
}
>>> .el-tag--medium {
  margin-left: 5px;
}
>>> .loadingStatus {
  text-align: center;
  color: #303133;
  font-size: 14px;
}
.sqlTextarea {
  resize: none;
  width: 100%;
  height: calc(100vh - 432px);
  min-height: 400px;
  background: #f5f7fa;
}
>>> .head-container:last-child {
  overflow-y: scroll;
  height: calc(100vh - 180px);
  -ms-overflow-style: none;
  scrollbar-width: none;
}
>>> .head-container2::-webkit-scrollbar {
  height: 0 !important;
  width: 0px !important;
}
>>> .sqlBtn {
  margin-bottom: 1px;
}
>>> .spanStyle {
  white-space: nowrap;
  width: 90%;
  word-break: keep-all;
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
}
>>> .el-table th > .cell {
  padding: 0;
  text-align: center;
}
>>> .getTextWidth {
  font-size: 14px;
}
</style>
