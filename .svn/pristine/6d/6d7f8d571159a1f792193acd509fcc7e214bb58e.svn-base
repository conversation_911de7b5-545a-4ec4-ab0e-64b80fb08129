package com.dqms.mdm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.mdm.domain.MdmDataEntityShipAnalysis;
import com.dqms.mdm.service.IMdmDataEntityShipAnalysisService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 血缘解析历史Controller
 *
 * <AUTHOR>
 * @date 2021-09-07
 */
@RestController
@RequestMapping("/mdm/mdmDataEntityShipAnalysis")
public class MdmDataEntityShipAnalysisController extends BaseController
{
    @Autowired
    private IMdmDataEntityShipAnalysisService mdmDataEntityShipAnalysisService;

    /**
     * 查询血缘解析历史列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShipAnalysis:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmDataEntityShipAnalysis mdmDataEntityShipAnalysis)
    {
        startPage();
        List<MdmDataEntityShipAnalysis> list = mdmDataEntityShipAnalysisService.selectMdmDataEntityShipAnalysisList(mdmDataEntityShipAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出血缘解析历史列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShipAnalysis:export')")
    @Log(title = "血缘解析历史", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmDataEntityShipAnalysis mdmDataEntityShipAnalysis)
    {
        List<MdmDataEntityShipAnalysis> list = mdmDataEntityShipAnalysisService.selectMdmDataEntityShipAnalysisList(mdmDataEntityShipAnalysis);
        ExcelUtil<MdmDataEntityShipAnalysis> util = new ExcelUtil<MdmDataEntityShipAnalysis>(MdmDataEntityShipAnalysis.class);
        return util.exportExcel(list, "mdmDataEntityShipAnalysis");
    }

    /**
     * 获取血缘解析历史详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShipAnalysis:query')")
    @GetMapping(value = "/{shipAnalysisId}")
    public AjaxResult getInfo(@PathVariable("shipAnalysisId") Long shipAnalysisId)
    {
        return AjaxResult.success(mdmDataEntityShipAnalysisService.selectMdmDataEntityShipAnalysisById(shipAnalysisId));
    }

    /**
     * 新增血缘解析历史
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShipAnalysis:add')")
    @Log(title = "血缘解析历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmDataEntityShipAnalysis mdmDataEntityShipAnalysis)
    {
        return toAjax(mdmDataEntityShipAnalysisService.insertMdmDataEntityShipAnalysis(mdmDataEntityShipAnalysis));
    }

    /**
     * 修改血缘解析历史
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShipAnalysis:edit')")
    @Log(title = "血缘解析历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmDataEntityShipAnalysis mdmDataEntityShipAnalysis)
    {
        return toAjax(mdmDataEntityShipAnalysisService.updateMdmDataEntityShipAnalysis(mdmDataEntityShipAnalysis));
    }

    /**
     * 删除血缘解析历史
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmDataEntityShipAnalysis:remove')")
    @Log(title = "血缘解析历史", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shipAnalysisIds}")
    public AjaxResult remove(@PathVariable Long[] shipAnalysisIds)
    {
        return toAjax(mdmDataEntityShipAnalysisService.deleteMdmDataEntityShipAnalysisByIds(shipAnalysisIds));
    }
}
