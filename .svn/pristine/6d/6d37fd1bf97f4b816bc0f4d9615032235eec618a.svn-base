package com.dqms.needs.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.dqms.needs.domain.NesNeedsHandle;

/**
 * 需求处理Service接口
 * 
 * <AUTHOR>
 * @date 2021-05-19
 */
public interface INesNeedsHandleService 
{
    /**
     * 查询需求处理
     * 
     * @param needsHandleId 需求处理ID
     * @return 需求处理
     */
    public NesNeedsHandle selectNesNeedsHandleById(Long needsHandleId);

    /**
     * 查询需求处理列表
     * 
     * @param nesNeedsHandle 需求处理
     * @return 需求处理集合
     */
    public List<NesNeedsHandle> selectNesNeedsHandleList(NesNeedsHandle nesNeedsHandle);

    /**
     * 新增需求处理
     * 
     * @param nesNeedsHandle 需求处理
     * @return 结果
     */
    public int insertNesNeedsHandle(NesNeedsHandle nesNeedsHandle);

    /**
     * 修改需求处理
     * 
     * @param nesNeedsHandle 需求处理
     * @return 结果
     */
    public int updateNesNeedsHandle(NesNeedsHandle nesNeedsHandle);

    /**
     * 批量删除需求处理
     * 
     * @param needsHandleIds 需要删除的需求处理ID
     * @return 结果
     */
    public int deleteNesNeedsHandleByIds(Long[] needsHandleIds);

    /**
     * 删除需求处理信息
     * 
     * @param needsHandleId 需求处理ID
     * @return 结果
     */
    public int deleteNesNeedsHandleById(Long needsHandleId);
    
    
    void singleDown(Long needsHandleId, HttpServletResponse response, HttpServletRequest request);
}
