package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dqms.common.annotation.Excel;

/**
 * 落标主对象 dsm_discern_main
 *
 * <AUTHOR>
 * @date 2021-06-01
 */
public class DsmDiscernMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 落标主ID */
    private Long discernMainId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 标准总数 */
    @Excel(name = "标准总数")
    private Long dsmNum;
    private Long runNum;

    /** 创建人ID */
    private Long createId;
    
    private String discernTypes;
    private String systems;
    private String discernTypeLabels;
    private String systemLabels;

    public void setDiscernMainId(Long discernMainId)
    {
        this.discernMainId = discernMainId;
    }

    public Long getDiscernMainId()
    {
        return discernMainId;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setDsmNum(Long dsmNum)
    {
        this.dsmNum = dsmNum;
    }

    public Long getDsmNum()
    {
        return dsmNum;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }

    public Long getRunNum() {
		return runNum;
	}

	public void setRunNum(Long runNum) {
		this.runNum = runNum;
	}

	public String getDiscernTypes() {
		return discernTypes;
	}

	public void setDiscernTypes(String discernTypes) {
		this.discernTypes = discernTypes;
	}

	public String getSystems() {
		return systems;
	}

	public void setSystems(String systems) {
		this.systems = systems;
	}

	public String getDiscernTypeLabels() {
		return discernTypeLabels;
	}

	public void setDiscernTypeLabels(String discernTypeLabels) {
		this.discernTypeLabels = discernTypeLabels;
	}

	public String getSystemLabels() {
		return systemLabels;
	}

	public void setSystemLabels(String systemLabels) {
		this.systemLabels = systemLabels;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("discernMainId", getDiscernMainId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("status", getStatus())
            .append("dsmNum", getDsmNum())
            .append("createBy", getCreateBy())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
