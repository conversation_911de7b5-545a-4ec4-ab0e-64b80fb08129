package com.dqms.common.utils;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.time.DateFormatUtils;

public class DateTimeUtils {

    public final static String FORMAT_yyyy_MM_dd = "yyyy-MM-dd";
    public final static String FORMAT_yyyyMMdd = "yyyyMMdd";
    public final static String FORMAT_yyyyMM = "yyyyMM";

    public final static String FORMAT_yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";

    public final static String FORMAT_yyyy_nian_MM_yue_mm_ri = "yyyy年MM月dd日";

    public final static String FORMAT_yyyy_nian_M_yue_m_ri = "yyyy年M月d日";

    private final static char[][] CHINESE_CHAR = {
        {'0', '〇'},
        {'1', '一'},
        {'2', '二'},
        {'3', '三'},
        {'4', '四'},
        {'5', '五'},
        {'6', '六'},
        {'7', '七'},
        {'8', '八'},
        {'9', '九'}
    };
    private static Map<Integer,String> numberZhMap = new HashMap<Integer, String>();
    static{
    	numberZhMap.put(0, "〇");
        numberZhMap.put(1,"一");
        numberZhMap.put(2,"二");
        numberZhMap.put(3,"三");
        numberZhMap.put(4,"四");
        numberZhMap.put(5,"五");
        numberZhMap.put(6,"六");
        numberZhMap.put(7,"七");
        numberZhMap.put(8,"八");
        numberZhMap.put(9,"九");
    }
    public static int nowYear() {
        return Calendar.getInstance().get(Calendar.YEAR);
    }

    public static int nowMonth() {
        return Calendar.getInstance().get(Calendar.MONTH) + 1;
    }

    public static int nowDayOfMonth() {
        return Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
    }

    public static String convertChar2ChineseChar(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return "";
        }
        StringBuffer buf = new StringBuffer(dateTimeStr.length());
        for (int i = 0; i < dateTimeStr.length(); i++) {
            buf.append(convertChar2ChineseChar(dateTimeStr.charAt(i)));
        }
        return buf.toString();
    }

    public static char convertChar2ChineseChar(char dateTimeChar) {
        for (int i = 0; i < CHINESE_CHAR.length; i++) {
            if (CHINESE_CHAR[i][0] == dateTimeChar) {
                return CHINESE_CHAR[i][1];
            }
        }
        return dateTimeChar;
    }

    /**
     * Date转换到Calendar
     * @param date 要转换的Date
     * @return Calendar
     */
    public static Calendar date2Calendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * 设置指定的Calendar“时、分、妙”为零
     * @param calendar Calendar
     */
    public static void setTimeZero(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
    }

    /**
     * 得到当前时间的字符串yyyy-MM-dd
     * @return String
     */
    public static String now2StrDate() {
        return now2Str(FORMAT_yyyy_MM_dd);
    }

    public static String now2StrDateYYYYMMDD() {
        return now2Str(FORMAT_yyyyMMdd);
    }

    public static String now2StrDateYYYYMM() {
        return now2Str(FORMAT_yyyyMM);
    }

    /**
     * 得到当前时间的字符串yyyy-MM-dd HH:mm:ss
     * @return String
     */
    public static String now2StrDateTime() {
        return now2Str(FORMAT_yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 得到当前时间的字符串
     * @param format 字符串格式
     * @return String
     * @see org.apache.commons.lang.time.DateFormatUtils.DateFormatUtils#format(Date, String)
     */
    public static String now2Str(String format) {
        return DateFormatUtils.format(new Date(), format);
    }

    /**
     * Date转换到字符串yyyy-MM-dd
     * @param date Date
     * @return String yyyy-MM-dd
     * @see org.apache.commons.lang.time.DateFormatUtils.DateFormatUtils#format(Date, String)
     */
    public static String date2StrDate(Date date) {
        return DateFormatUtils.format(date, FORMAT_yyyy_MM_dd);
    }

    /**
     * Date转换到字符串
     * @param date
     * @param format
     * @return
     */
    public static String date2StrDate(Date date, String format) {
        return DateFormatUtils.format(date, format);
    }

    /**
     * Date转换到字符串yyyy-MM-dd HH:mm:ss
     * @param date Date
     * @return String yyyy-MM-dd HH:mm:ss
     * @see org.apache.commons.lang.time.DateFormatUtils.DateFormatUtils#format(Date, String)
     */
    public static String date2StrDateTime(Date date) {
        return DateFormatUtils.format(date, FORMAT_yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * Calendar转换到字符串yyyy-MM-dd
     * @param calendar Calendar
     * @return String yyyy-MM-dd
     * @see org.apache.commons.lang.time.DateFormatUtils.DateFormatUtils#format(Date, String)
     */
    public static String calendar2StrDate(Calendar calendar) {
        return date2StrDate(calendar.getTime());
    }

    /**
     * Calendar转换到字符串yyyy-MM-dd HH:mm:ss
     * @param calendar Calendar
     * @return String yyyy-MM-dd HH:mm:ss
     * @see org.apache.commons.lang.time.DateFormatUtils.DateFormatUtils#format(Date, String)
     */
    public static String calendar2StrDateTime(Calendar calendar) {
        return date2StrDateTime(calendar.getTime());
    }

    /**
     * 字符串yyyy-MM-dd转换到Calendar类型
     * @param dateStr yyyy-MM-dd
     * @return Calendar
     */
    public static Calendar strDate2Calendar(String dateStr) {
        return str2Calendar(dateStr, FORMAT_yyyy_MM_dd);
    }

    /**
     * 字符串yyyy-MM-dd转换到Date类型
     * @param dateStr yyyy-MM-dd
     * @return Date
     */
    public static Date strDate2Date(String dateStr) {
        return str2Date(dateStr, FORMAT_yyyy_MM_dd);
    }

    /**
     * 字符串yyyy-MM-dd HH:mm:ss转换到Calendar类型
     * @param dateStr yyyy-MM-dd HH:mm:ss
     * @return Calendar
     */
    public static Calendar strDateTime2Calendar(String dateStr) {
        return str2Calendar(dateStr, FORMAT_yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 字符串yyyy-MM-dd HH:mm:ss转换到Date类型
     * @param dateStr yyyy-MM-dd HH:mm:ss
     * @return Date
     */
    public static Date strDateTime2Date(String dateStr) {
        return str2Date(dateStr, FORMAT_yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 字符串转换到Date类型
     * @param dateStr 需要转换的字符串
     * @param format 转换格式
     * @return Date
     */
    public static Date str2Date(String dateStr, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setLenient(false);
        Date date = dateFormat.parse(dateStr, new ParsePosition(0));
        return date;
    }

    /**
     * 字符串转换到Calendar类型
     * @param dateStr 需要转换的字符串
     * @param format 转换格式
     * @return Calendar
     */
    public static Calendar str2Calendar(String dateStr, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(str2Date(dateStr, format));
        return calendar;
    }

    /**
     *  得到当前日期的Calendar类型
     * @return Calendar;
     */
    public static Calendar now2Calendar() {
        return Calendar.getInstance();
    }

	/**
	 * 与当前日期比较 ，修改月份<br/>
	 * 临界日期因为只传日期，所以当天也也是小于
	 * @param dateString
	 * @param addYear
	 * @param addMonth
	 * @param addDate
	 * @return true 大于 false 小于
	 * @throws ParseException
	 * @throws
	 * @created 2012-7-26 下午02:43:15
	 */
	public static boolean compareToNowDateWithMonth(String dateString,int addMonth) throws ParseException  {
		return compareToNowDate(dateString, 0, addMonth, 0) == DATE_COMPARED_GREATER;
	}

	public final static int DATE_COMPARED_GREATER = 1;
	public final static int DATE_COMPARED_LESSER = -1;
	public final static int DATE_COMPARED_EQUAL = 0;


	/**
	 * 与当前日期比较 ，修改年月日，格式为:yyyy-MM-dd
	 * @param dateString
	 * @param addYear
	 * @param addMonth
	 * @param addDate
	 * @return -1 小于，0 等于 ,1 大于
	 * @throws ParseException
	 * @throws
	 * @created 2012-7-26 下午02:43:52
	 */
	public static int compareToNowDate(String dateString,int addYear,int addMonth,int addDate) throws ParseException  {
		return compareTo(dateString,new Date(), addYear, addMonth, addDate, FORMAT_yyyy_MM_dd, "-");
	}
	/**
	 * 日期比较
	 * @param dateString  日期字符串
	 * @param date  待比较的日期
	 * @param addYear  增加的年 可以是负数，针对 dateString
	 * @param addMonth 增加的月 可以是负数，针对 dateString
	 * @param addDate  增加的日 可以是负数，针对 dateString
	 * @param formatEx 日期格式默认是  yyyy-MM-dd
	 * @param subfix 日期分隔符 默认 -
	 * @return -1 小于，0 等于 ,1 大于
	 * @throws ParseException
	 * @throws
	 * @created 2012-7-26 下午02:36:37
	 */
	public static int compareTo(String dateString,Date date,int addYear,int addMonth,int addDate,String formatEx,String subfix) throws ParseException  {
			formatEx =  StringUtils.isNotBlank(formatEx)?formatEx:FORMAT_yyyy_MM_dd;
			subfix =  StringUtils.isNotBlank(subfix)?subfix:"-";
			dateString = addDate(dateString,addYear,addMonth,addDate);
			SimpleDateFormat sdf = new SimpleDateFormat(formatEx);
			Date d = sdf.parse(dateString);
			return d.compareTo(date);
	}

	/**
     * 日期更改
     * @param dateString 日期   如2012-12-12
     * @param addYear 年数字
     * @param addMonth 月
     * @param addDate 日
     * @param subfix 分隔符 如"-"
     * @return
     * @throws
     * @created 2012-7-26 下午02:27:44
     */
	public static String addDate(String dateString,int addYear, int addMonth,int addDate) {
		return addDate(dateString, DateTimeUtils.FORMAT_yyyy_MM_dd, addYear, addMonth, addDate);
	}
    public static String addDate(String dateString,String format,int addYear, int addMonth,int addDate) {
		Calendar cal=DateTimeUtils.str2Calendar(dateString,format);
		cal.add(Calendar.YEAR, addYear);
		cal.add(Calendar.MONTH, addMonth);
		cal.add(Calendar.DATE, addDate);
		return DateTimeUtils.date2StrDate(cal.getTime(), format);
	}

    /**
     * 获取指定年、月等之后的时间，可指定年、月、日、时、分、秒。
     * @param date	当前时间
     * @param y		年
     * @param m		月
     * @param d		日
     * @param h		时
     * @param mi	分
     * @param s		秒
     * @return
     */
	public static Date nextDateTime(Date date,int y,int m,int d,int h,int mi,int s) {
		Date nextDate = null;
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		if(y!=0)
			cal.set(Calendar.YEAR, cal.get(Calendar.YEAR)+y);
		if(m!=0)
			cal.set(Calendar.MONTH, cal.get(Calendar.MONTH)+m);
		if(d!=0)
			cal.set(Calendar.DATE, cal.get(Calendar.DATE)+d);
		if(h!=0)
			cal.set(Calendar.HOUR, cal.get(Calendar.HOUR)+h);
		if(mi!=0)
			cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE)+mi);
		if(s!=0)
			cal.set(Calendar.SECOND, cal.get(Calendar.SECOND)+s);
		nextDate = cal.getTime();
		return nextDate;
	}

	public static Date nextHour(Date d,int hour) {
			return nextTime(d,hour,0,0);
	}

	public static Date nextTime(Date d,int h,int m,int s) {
		return nextDateTime(d,0,0,0,h,m,s);
	}

	public static Date nextDate(Date date,int y,int m,int d) {
		return nextDateTime(date,y,m,d,0,0,0);
	}

	public static int getQuarter(Date yearMonth) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(yearMonth);
		return 1+(cal.get(Calendar.MONTH)/3);
	}
	public static int getQuarter(String yearMonth, String format) {
		return getQuarter(str2Date(yearMonth, format));
	}
	public static String getQuarterZH(String yearMonth, String format) {
		return numberZhMap.get(getQuarter(str2Date(yearMonth, format)));
	}
	public static String getToday_YYYYMMDD() {
		return now2Str(FORMAT_yyyyMMdd);
	}
	public static String getYesterday_YYYYMMDD() {
		return DateTimeUtils.addDate(now2StrDate(), 0, 0, -1).replaceAll("-", "");
	}
}
