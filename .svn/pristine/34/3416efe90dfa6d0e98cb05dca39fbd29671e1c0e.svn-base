<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.mdm.mapper.MdmDataEntityShipAnalysisRelMapper">
    
    <resultMap type="MdmDataEntityShipAnalysisRel" id="MdmDataEntityShipAnalysisRelResult">
        <result property="shipId"    column="ship_id"    />
        <result property="entityId"    column="entity_id"    />
    </resultMap>

    <sql id="selectMdmDataEntityShipAnalysisRelVo">
        select ship_id, entity_id from mdm_data_entity_ship_analysis_rel
    </sql>

    <select id="selectMdmDataEntityShipAnalysisRelList" parameterType="MdmDataEntityShipAnalysisRel" resultMap="MdmDataEntityShipAnalysisRelResult">
        <include refid="selectMdmDataEntityShipAnalysisRelVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectMdmDataEntityShipAnalysisRelById" parameterType="Long" resultMap="MdmDataEntityShipAnalysisRelResult">
        <include refid="selectMdmDataEntityShipAnalysisRelVo"/>
        where ship_id = #{shipId}
    </select>
        
    <insert id="insertMdmDataEntityShipAnalysisRel" parameterType="MdmDataEntityShipAnalysisRel">
        insert into mdm_data_entity_ship_analysis_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shipId != null">ship_id,</if>
            <if test="entityId != null">entity_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shipId != null">#{shipId},</if>
            <if test="entityId != null">#{entityId},</if>
         </trim>
    </insert>

    <update id="updateMdmDataEntityShipAnalysisRel" parameterType="MdmDataEntityShipAnalysisRel">
        update mdm_data_entity_ship_analysis_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityId != null">entity_id = #{entityId},</if>
        </trim>
        where ship_id = #{shipId}
    </update>

    <delete id="deleteMdmDataEntityShipAnalysisRelById" parameterType="Long">
        delete from mdm_data_entity_ship_analysis_rel where entity_id = #{entityId}
    </delete>

    <delete id="deleteMdmDataEntityShipAnalysisRelByIds" parameterType="String">
        delete from mdm_data_entity_ship_analysis_rel where ship_id in 
        <foreach item="shipId" collection="array" open="(" separator="," close=")">
            #{shipId}
        </foreach>
    </delete>
    
    <select id="selectMdmDataEntityShipAnalysisRelListByEntityId" parameterType="Long"  resultMap="MdmDataEntityShipAnalysisRelResult">
        SELECT s.ship_id,COUNT(r.entity_id) AS entity_id FROM mdm_data_entity_ship s LEFT JOIN mdm_data_entity_ship_analysis_rel r ON s.ship_id =r.ship_id 
		
		WHERE s.trigger_type=2 AND r.entity_id=#{entityId}
		
		GROUP BY s.ship_id
    </select>
</mapper>