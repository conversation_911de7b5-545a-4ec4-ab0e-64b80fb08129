import request from '@/utils/request'
import axios from 'axios'
import { getToken } from '@/utils/auth'
// 查询需求处理列表
export function listNeedsHandle(query) {
  return request({
    url: '/needs/needsHandle/list',
    method: 'get',
    params: query
  })
}

// 查询需求处理详细
export function getNeedsHandle(needsHandleId) {
  return request({
    url: '/needs/needsHandle/' + needsHandleId,
    method: 'get'
  })
}

// 新增需求处理
export function addNeedsHandle(data) {
  return request({
    url: '/needs/needsHandle',
    method: 'post',
    data: data
  })
}

// 修改需求处理
export function updateNeedsHandle(data) {
  return request({
    url: '/needs/needsHandle',
    method: 'put',
    data: data
  })
}

// 删除需求处理
export function delNeedsHandle(needsHandleId) {
  return request({
    url: '/needs/needsHandle/' + needsHandleId,
    method: 'delete'
  })
}

// 导出需求处理
export function exportNeedsHandle(query) {
  return request({
    url: '/needs/needsHandle/export',
    method: 'get',
    params: query
  })
}

const baseURL = process.env.VUE_APP_BASE_API
export function singleHandleDown(needsHandleId) {
  return axios.get(baseURL + '/needs/needsHandle/singleDown?needsHandleId=' + encodeURI(needsHandleId), {
    responseType: 'blob', // 或者responseType: 'blob'
    xsrfHeaderName: 'Authorization',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken()
    }
  })
}