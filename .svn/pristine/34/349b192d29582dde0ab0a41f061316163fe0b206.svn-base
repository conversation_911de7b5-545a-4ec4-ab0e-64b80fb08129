package com.dqms.basic.service;

import java.util.List;

import com.dqms.basic.domain.SysDatasourceType;
import com.dqms.common.core.domain.entity.SysSystem;

/**
 * 应用系统Service接口
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
public interface ISysSystemService
{
    /**
     * 查询应用系统
     *
     * @param systemId 应用系统ID
     * @return 应用系统
     */
    public SysSystem selectSysSystemById(Long systemId);

    /**
     * 查询应用系统列表
     *
     * @param sysSystem 应用系统
     * @return 应用系统集合
     */
    public List<SysSystem> selectSysSystemList(SysSystem sysSystem);

    /**
     * 查询所有应用系统
     *
     * @param sysSystem 应用系统
     * @return 应用系统集合
     */
    public List<SysSystem> selectSysSystemAll(SysSystem sysSystem);

    /**
     * 新增应用系统
     *
     * @param sysSystem 应用系统
     * @return 结果
     */
    public int insertSysSystem(SysSystem sysSystem);

    /**
     * 修改应用系统
     *
     * @param sysSystem 应用系统
     * @return 结果
     */
    public int updateSysSystem(SysSystem sysSystem);

    /**
     * 批量删除应用系统
     *
     * @param systemIds 需要删除的应用系统ID
     * @return 结果
     */
    public int deleteSysSystemByIds(Long[] systemIds);

    /**
     * 删除应用系统信息
     *
     * @param systemId 应用系统ID
     * @return 结果
     */
    public int deleteSysSystemById(Long systemId);
    /**
     * 根据用户ID获取应用系统选择框列表
     *
     * @param userId 用户ID
     * @return 选中应用系统ID列表
     */
    public List<Integer> selectSystemListByUserId(Long userId);
    /**
     * 根据系统ID获取负责人系统选择框列表
     *
     * @param systemId 用户ID
     * @return 选中负责人ID列表
     */
    public List<Integer> selectPersonIdsBySystemId(Long systemId);
}
