package com.dqms.dqm.mapper;

import java.util.List;
import com.dqms.dqm.domain.DqmProblemHandling;

/**
 * 检查规则任务问题处理进度Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-07-28
 */
public interface DqmProblemHandlingMapper 
{
    /**
     * 查询检查规则任务问题处理进度
     * 
     * @param dqmProblemHandlingId 检查规则任务问题处理进度ID
     * @return 检查规则任务问题处理进度
     */
    public DqmProblemHandling selectDqmProblemHandlingById(Long dqmProblemHandlingId);

    /**
     * 查询检查规则任务问题处理进度列表
     * 
     * @param dqmProblemHandling 检查规则任务问题处理进度
     * @return 检查规则任务问题处理进度集合
     */
    public List<DqmProblemHandling> selectDqmProblemHandlingList(DqmProblemHandling dqmProblemHandling);

    /**
     * 新增检查规则任务问题处理进度
     * 
     * @param dqmProblemHandling 检查规则任务问题处理进度
     * @return 结果
     */
    public int insertDqmProblemHandling(DqmProblemHandling dqmProblemHandling);

    /**
     * 修改检查规则任务问题处理进度
     * 
     * @param dqmProblemHandling 检查规则任务问题处理进度
     * @return 结果
     */
    public int updateDqmProblemHandling(DqmProblemHandling dqmProblemHandling);

    /**
     * 删除检查规则任务问题处理进度
     * 
     * @param dqmProblemHandlingId 检查规则任务问题处理进度ID
     * @return 结果
     */
    public int deleteDqmProblemHandlingById(Long dqmProblemHandlingId);

    /**
     * 批量删除检查规则任务问题处理进度
     * 
     * @param dqmProblemHandlingIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDqmProblemHandlingByIds(Long[] dqmProblemHandlingIds);
}
