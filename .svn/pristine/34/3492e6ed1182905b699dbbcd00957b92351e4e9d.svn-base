<template>
  <div v-if="ueditor">
    <!-- 添加或修改指标管理对话框 -->

      <el-form ref="form" v-if="iFDisable != 'read'" :model="form" :rules="rules" label-width="80px">
        <el-row type="flex" justify="start" align="top">
          <el-col :span="23">
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-form-item
                  label="业务条线"
                  prop="indexClassId"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <treeselect
                    v-model="form.indexClassId"
                    :options="indexClassIdOptions"
                    :disable-branch-nodes="true"
                    :show-count="true"
                    :disabled="iFDisable == 'read'"
                    placeholder="请选择业务条线"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="指标类型"
                  prop="indexType"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-select-dispatcher
                    v-model="form.indexType"
                    placeholder="请选择指标类型 "
                    style="width:100%"
                  >
                    <el-option
                      v-for="dict in indexTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    ></el-option>
                  </el-select-dispatcher>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-form-item
                  label="指标编码"
                  prop="indexCode"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-input-dispatcher
                    v-model="form.indexCode"
                    placeholder="请输入指标编码"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="指标名称"
                  prop="indexName"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-input-dispatcher
                    v-model="form.indexName"
                    placeholder="请输入指标名称"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
             <el-row
              type="flex"
              justify="start"
              align="top"
              v-if="handleType != 'ADD'"
            >
              <el-col :span="24">
                <el-form-item
                  label="指标状态"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-radio-group-dispatcher v-model="form.status">
                    <el-radio-dispatcher
                      v-for="dict in statusOptions"
                      :key="dict.dictValue"
                      :label="dict.dictValue"
                      >{{ dict.dictLabel }}</el-radio-dispatcher
                    >
                  </el-radio-group-dispatcher>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row
              type="flex"
              justify="start"
              align="top"
            >
              <el-col :span="24">
                <el-form-item label="所属应用" prop="systemId">
                  <el-select v-model="form.systemId" placeholder="请选择所属应用" filterable clearable >
                    <el-option
                      v-for="item in systemOptions"
                      :key="item.systemId"
                      :label="item.name"
                      :value="item.systemId"
                    ></el-option>
                  </el-select>
                </el-form-item>
             </el-col>
            </el-row>
            </el-col>
          <el-col :span="1" style="margin-bottom: 22px;">
            <div
              class="transition-box"
              :class="{ title: iFDisable == 'read' }"
              style="background-color: #409EFF;font-size:36px;height:100%;text-align:center;margin:auto;padding-bottom:10px;border-radius: 4px"
            >
              <span style="font-size:22px;color:#FFF;">基本<br />信息</span>
            </div>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="23">
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-col :span="24">
                  <el-form-item
                    label="业务口径"
                    prop="definition"
                    :class="{ disabled: iFDisable == 'read' }"
                  >
                    <el-input-dispatcher
                      v-model="form.definition"
                      type="textarea"
                      placeholder="请输入内容"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="规则SQL"
                    prop="execSql"
                    :class="{ disabled: iFDisable == 'read' }"
                  >
                    <el-input-dispatcher
                      v-model="form.execSql"
                      type="textarea"
                      placeholder="请输入内容"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="指标单位"
                    prop="unit"
                    :class="{ disabled: iFDisable == 'read' }"
                  >
                    <el-select-dispatcher
                      v-model="form.unit"
                      placeholder="请选择指标单位 "
                      style="width:100%"
                    >
                      <el-option
                        v-for="dict in unitOptions"
                        :key="dict.dictValue"
                        :label="dict.dictLabel"
                        :value="dict.dictValue"
                      ></el-option>
                    </el-select-dispatcher>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="统计周期"
                    prop="cycle"
                    :class="{ disabled: iFDisable == 'read' }"
                  >
                    <el-select-dispatcher
                      v-model="form.cycle"
                      placeholder="请选择统计周期"
                      style="width:100%"
                    >
                      <el-option
                        v-for="dict in cycleOptions"
                        :key="dict.dictValue"
                        :label="dict.dictLabel"
                        :value="dict.dictValue"
                      ></el-option>
                    </el-select-dispatcher>
                  </el-form-item>
                </el-col>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="计算公式"
                  prop="formula"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <vue-ueditor-wrap
                    v-model="form.formula"
                    @ready="ready"
                    :config="myConfig"
                    :contenteditable="false"
                  ></vue-ueditor-wrap>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="1" style="margin-bottom: 22px;">
            <div
              class="transition-box"
              :class="{ title: iFDisable == 'read' }"
              style="line-height: 59px;background-color: #67C23A;font-size:36px;height:100%;text-align:center;margin:auto;padding-top:0px;border-radius: 4px"
            >
              <span style="font-size:22px;color:#FFF;"
                >业<br />务<br />属<br />性</span
              >
            </div>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="23">
            <el-row type="flex" justify="start" align="top">
              <el-col :span="24">
                <el-form-item
                  label="版本"
                  prop="version"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-input-dispatcher
                    v-model="form.version"
                    placeholder="系统自动生成"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="24">
                <el-form-item
                  label="关联指标"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-select-dispatcher
                    v-model="form.dsmIndexIds"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    style="width:100%"
                    placeholder="请输入关键词"
                    :remote-method="remoteIndexMethod"
                    @change="$forceUpdate()"
                    :loading="loading"
                  >
                    <el-option
                      v-for="item in dsmIndexOptions"
                      :key="item.indexId"
                      :label="item.indexName"
                      :value="item.indexId"
                    >
                    </el-option>
                  </el-select-dispatcher>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="24">
                <el-form-item
                  label="关联维度"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-select-dispatcher
                    v-model="form.dsmDimensionIds"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    style="width:100%"
                    placeholder="请输入关键词"
                    @change="$forceUpdate()"
                    :remote-method="remoteDimensionMethod"
                    :loading="loading"
                  >
                    <el-option
                      v-for="item in dsmDimensionOptions"
                      :key="item.dimensionId"
                      :label="item.dimensionName"
                      :value="item.dimensionId"
                    >
                    </el-option>
                  </el-select-dispatcher>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="24">
                <el-form-item
                  label-width="0px"
                  :error="uploaderr"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <div class="upload">
                    <el-upload
                      ref="upload"
                      class="upload-demo"
                      :action="uploadUrl"
                      accept=".jpg,.jpeg,.txt,.zip,.xls,.xlsx,.doc,.docx,.pdf,.PDF"
                      :auto-upload="true"
                      :file-list="fileList"
                      :on-success="handleSuccess"
                      :on-remove="handleRemove"
                      :disabled="iFDisable == 'read'"
                      :headers="myHeaders"
                      :limit="1"
                    >
                      <el-button
                        size="small"
                        icon="el-icon-upload2"
                        type="danger"
                        :disabled="iFDisable == 'read'"
                        >上传附件</el-button
                      >
                    </el-upload>
                    <div class="uptxt">
                      （支持pdf、word、excel、zip、jpg，文件限制500M以内）
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="文档名称" prop="attachment" v-show="false">
                  <el-input-dispatcher
                    v-model="form.attachment"
                    maxLength="20"
                    placeholder="请输入文档名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="1" style="margin-bottom: 22px;">
            <div
              :class="{ title: iFDisable == 'read' }"
              class="transition-box"
              style="background-color: #E6A23C;font-size:36px;height:100%;text-align:center;margin:auto;padding-top:0px;border-radius: 4px"
            >
              <span style="font-size:22px;color:#FFF;"
                >管<br />理<br />属<br />性</span
              >
            </div>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="23">
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-form-item
                  label="数据类型"
                  prop="dataType"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-select-dispatcher
                    v-model="form.dataType"
                    placeholder="请选择数据类型" clearable
                    style="width:100%"
                  >
                    <el-option
                      v-for="dict in dataTypeOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    ></el-option>
                  </el-select-dispatcher>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="数据长度"
                  prop="columnSize"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-input-dispatcher
                    v-model="form.columnSize"
                    placeholder="请输入数据长度"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-form-item
                  label="数据精度"
                  prop="decimalDigits"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-input-dispatcher
                    v-model="form.decimalDigits"
                    placeholder="请输入数据精度"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="缺省值"
                  prop="defaultValue"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-input-dispatcher
                    v-model="form.defaultValue"
                    placeholder="请输入缺省值"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-form-item
                  label="是否主键"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-radio-group-dispatcher v-model="form.isPriKey">
                    <el-radio-dispatcher
                      v-for="dict in isPriKeyOptions"
                      :key="dict.dictValue"
                      :label="dict.dictValue"
                      >{{ dict.dictLabel }}</el-radio-dispatcher
                    >
                  </el-radio-group-dispatcher>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="是否为空"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-radio-group-dispatcher v-model="form.nullable">
                    <el-radio-dispatcher
                      v-for="dict in nullableOptions"
                      :key="dict.dictValue"
                      :label="dict.dictValue"
                      >{{ dict.dictLabel }}</el-radio-dispatcher
                    >
                  </el-radio-group-dispatcher>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top">
              <el-col :span="12">
                <el-form-item
                  label="是否落地"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <el-radio-group-dispatcher v-model="form.ifDown">
                    <el-radio-dispatcher
                      v-for="dict in ifDownOptions"
                      :key="dict.dictValue"
                      :label="dict.dictValue"
                      >{{ dict.dictLabel }}</el-radio-dispatcher
                    >
                  </el-radio-group-dispatcher>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>


          <el-col :span="1" style="margin-bottom: 22px;">
            <div
              class="transition-box"
              :class="{ title: iFDisable == 'read' }"
              style="background-color: #F56C6C;font-size:36px;height:100%;text-align:center;margin:auto;padding-bottom:10px;border-radius: 4px"
            >
              <span style="font-size:22px;color:#FFF;"
                >技<br />术<br />属<br />性</span
              >
            </div>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
           <!-- <quote :loading="loading"
                  :dsmMdmRelList="dsmMdmRelList"
                  @getDsmMdmRelUnForm="getDsmMdmRelUnForm"
                  @delDsmMdmRel="delDsmMdmRel"
           ></quote> -->
          </el-col>
        </el-row>
        <br />
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-table v-loading="loading" :data="dsmIndexHisList">
              <el-table-column label="历史版本">
                <el-table-column
                  label="指标名称"
                  align="center"
                  prop="indexName"
                />
                <el-table-column
                  label="业务条线"
                  align="center"
                  prop="indexClassNameFull"
                  width="255"
                />
                <el-table-column
                  label="指标类型"
                  align="center"
                  prop="indexType"
                  :formatter="indexTypeFormat"
                />
                <el-table-column
                  label="指标状态"
                  align="center"
                  prop="status"
                  :formatter="statusFormat"
                />
                <el-table-column label="版本" align="center" prop="version" />
                <el-table-column
                  label="操作"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-finished"
                      @click="balanceShow(scope.row)"
                      v-if="scope.row.status == 'INVALID'"
                      >对比</el-button
                    >
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>

      <el-form ref="form" v-else :model="form">
        <el-tabs type="border-card">
          <el-tab-pane>
            <span slot="label"><i class="el-icon-s-custom"></i> 基本信息 </span>
            <el-table
              :data="tableData4"
              style="width: 100%"
              :show-header="false"
              stripe
              border>
                <el-table-column prop="date" min-width="30%">
                  <template slot-scope="scope">
                    <span v-if="scope.$index==0">业务条线</span>
                    <span v-if="scope.$index==1">指标类型</span>
                    <span v-if="scope.$index==2">指标编码</span>
                    <span v-if="scope.$index==3">指标名称</span>
                     <span v-if="scope.$index==4">所属应用</span>
                   <!-- <span v-if="scope.$index==4">指标状态</span>-->
                  </template>
                </el-table-column>
                <el-table-column prop="date">
                  <template slot-scope="scope">
                    <span v-if="scope.$index==0">
                      <el-form-item
                        prop="indexClassId"
                        :class="{ disabled: iFDisable == 'read' }"
                      >
                        <treeselect
                          v-model="form.indexClassId"
                          :options="indexClassIdOptions"
                          :disable-branch-nodes="true"
                          :show-count="true"
                          :disabled="iFDisable == 'read'"
                          placeholder="请选择业务条线"
                        />
                      </el-form-item>
                    </span>
                    <span v-if="scope.$index==1">
                      <el-form-item
                        prop="indexType"
                        :class="{ disabled: iFDisable == 'read' }"
                      >
                        <el-select-dispatcher
                          v-model="form.indexType"
                          placeholder="请选择指标类型 "
                          style="width:100%"
                        >
                          <el-option
                            v-for="dict in indexTypeOptions"
                            :key="dict.dictValue"
                            :label="dict.dictLabel"
                            :value="dict.dictValue"
                          ></el-option>
                        </el-select-dispatcher>
                      </el-form-item>
                    </span>
                    <span v-if="scope.$index==2">
                      <el-form-item
                        prop="indexCode"
                        :class="{ disabled: iFDisable == 'read' }"
                      >
                        <el-input-dispatcher
                          v-model="form.indexCode"
                          placeholder="请输入指标编码"
                          clearable
                        />
                      </el-form-item>
                    </span>
                    <span v-if="scope.$index==3">
                      <el-form-item
                        prop="indexName"
                        :class="{ disabled: iFDisable == 'read' }"
                      >
                        <el-input-dispatcher
                          v-model="form.indexName"
                          placeholder="请输入指标名称"
                          clearable
                        />
                      </el-form-item>
                    </span>
                    <span v-if="scope.$index==4">
                      <el-form-item
                        prop="systemId"
                        :class="{ disabled: iFDisable == 'read' }"
                      >
                        <el-input-dispatcher
                          v-model="form.systemName"
                          placeholder="请输入指标名称"
                          clearable
                        />
                      </el-form-item>
                    </span>
                    <!--<span v-if="scope.$index==4">
                      <el-form-item
                        :class="{ disabled: iFDisable == 'read' }"
                      >
                        <el-radio-group-dispatcher v-model="form.status">
                          <el-radio-dispatcher
                            v-for="dict in statusOptions"
                            :key="dict.dictValue"
                            :label="dict.dictValue"
                            >{{ dict.dictLabel }}</el-radio-dispatcher
                          >
                        </el-radio-group-dispatcher>
                      </el-form-item>
                    </span>-->
                  </template>
                 </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane>
            <span slot="label"><i class="el-icon-s-marketing"></i> 业务属性 </span>
            <el-table
                :data="tableData4"
                style="width: 100%"
                :show-header="false"
                stripe
                border>
                  <el-table-column prop="date" min-width="30%">
                    <template slot-scope="scope">
                      <span v-if="scope.$index==0">业务口径</span>
                      <span v-if="scope.$index==1">规则SQL</span>
                      <!--<span v-if="scope.$index==2">指标单位</span>-->
                      <span v-if="scope.$index==2">统计周期</span>
                      <span v-if="scope.$index==3">计算公式</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name">
                    <template slot-scope="scope">
                      <span v-if="scope.$index==0">
                        <el-form-item
                          prop="definition"
                          :class="{ disabled: iFDisable == 'read' }"
                        >
                          <el-input-dispatcher
                            v-model="form.definition"
                            type="textarea"
                            placeholder="请输入内容"
                          />
                        </el-form-item>
                      </span>
                      <span v-if="scope.$index==1">
                        <el-form-item
                          prop="execSql"
                          :class="{ disabled: iFDisable == 'read' }"
                        >
                          <el-input-dispatcher
                            v-model="form.execSql"
                            type="textarea"
                            placeholder="请输入内容"
                          />
                        </el-form-item>
                      </span>
                     <!-- <span v-if="scope.$index==2">
                        <el-form-item
                          prop="unit"
                          :class="{ disabled: iFDisable == 'read' }"
                        >
                          <el-select-dispatcher
                            v-model="form.unit"
                            placeholder="请选择指标单位 "
                            style="width:100%"
                          >
                            <el-option
                              v-for="dict in unitOptions"
                              :key="dict.dictValue"
                              :label="dict.dictLabel"
                              :value="dict.dictValue"
                            ></el-option>
                          </el-select-dispatcher>
                        </el-form-item>
                      </span>-->
                      <span v-if="scope.$index==2">
                        <el-form-item
                          prop="cycle"
                          :class="{ disabled: iFDisable == 'read' }"
                        >
                          <el-select-dispatcher
                            v-model="form.cycle"
                            placeholder="请选择统计周期 "
                            style="width:100%"
                          >
                            <el-option
                              v-for="dict in cycleOptions"
                              :key="dict.dictValue"
                              :label="dict.dictLabel"
                              :value="dict.dictValue"
                            ></el-option>assetsView
                          </el-select-dispatcher>
                        </el-form-item>
                      </span>
                      <span v-if="scope.$index==3">
                        <el-form-item
                          prop="formula"
                          :class="{ disabled: iFDisable == 'read' }"
                        >
                          <vue-ueditor-wrap
                            v-model="form.formula"
                            @ready="ready"
                            :config="myConfig"
                            :contenteditable="false"
                          ></vue-ueditor-wrap>
                        </el-form-item>
                      </span>
                    </template>
                  </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane>
            <span slot="label"><i class="el-icon-s-data"></i> 技术属性 </span>
            <el-table
                :data="tableData2"
                style="width: 100%"
                :show-header="false"
                stripe
                border>
              <el-table-column prop="date" min-width="30%">
                <template slot-scope="scope">
                  <span v-if="scope.$index==0">数据类型</span>
                  <span v-if="scope.$index==1">数据长度</span>
                  <span v-if="scope.$index==2">数据精度</span>
                  <span v-if="scope.$index==3">缺省值</span>
                  <span v-if="scope.$index==4">是否主键</span>
                  <span v-if="scope.$index==5">是否为空</span>
                  <span v-if="scope.$index==6">是否落地</span>
                </template>
              </el-table-column>
              <el-table-column prop="date">
                <template slot-scope="scope">
                  <span v-if="scope.$index==0">
                    <el-form-item
                      prop="dataType"
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-select-dispatcher
                        v-model="form.dataType"
                        placeholder="请选择数据类型" clearable
                        style="width:100%"
                      >
                        <el-option
                          v-for="dict in dataTypeOptions"
                          :key="dict.dictValue"
                          :label="dict.dictLabel"
                          :value="dict.dictValue"
                        ></el-option>
                      </el-select-dispatcher>
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==1">
                    <el-form-item
                      prop="columnSize"
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-input-dispatcher
                        v-model="form.columnSize"
                        placeholder="请输入数据长度"
                        clearable
                      />
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==2">
                    <el-form-item
                      prop="decimalDigits"
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-input-dispatcher
                        v-model="form.decimalDigits"
                        placeholder="请输入数据精度"
                        clearable
                      />
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==3">
                    <el-form-item
                      prop="defaultValue"
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-input-dispatcher
                        v-model="form.defaultValue"
                        placeholder="请输入缺省值"
                        clearable
                      />
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==4">
                   <el-form-item
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-radio-group-dispatcher v-model="form.isPriKey">
                        <el-radio-dispatcher
                          v-for="dict in isPriKeyOptions"
                          :key="dict.dictValue"
                          :label="dict.dictValue"
                          >{{ dict.dictLabel }}</el-radio-dispatcher
                        >
                      </el-radio-group-dispatcher>
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==5">
                    <el-form-item
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-radio-group-dispatcher v-model="form.nullable">
                        <el-radio-dispatcher
                          v-for="dict in nullableOptions"
                          :key="dict.dictValue"
                          :label="dict.dictValue"
                          >{{ dict.dictLabel }}</el-radio-dispatcher
                        >
                      </el-radio-group-dispatcher>
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==6">
                     <el-form-item
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-radio-group-dispatcher v-model="form.ifDown">
                        <el-radio-dispatcher label="Y"
                          >是</el-radio-dispatcher
                        >
                         <el-radio-dispatcher label="N"
                          >否</el-radio-dispatcher
                        >
                      </el-radio-group-dispatcher>
                    </el-form-item>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane>
            <span slot="label"><i class="el-icon-menu"></i> 管理属性 </span>
            <el-table
                :data="tableData3"
                style="width: 100%"
                :show-header="false"
                stripe
                border>
              <el-table-column prop="date" min-width="30%">
                <template slot-scope="scope">
                  <span v-if="scope.$index==0">版本</span>
                  <span v-if="scope.$index==1">关联指标</span>
                  <span v-if="scope.$index==2">关联维度</span>
                </template>
              </el-table-column>
              <el-table-column prop="date">
                <template slot-scope="scope">
                  <span v-if="scope.$index==0">
                    <el-form-item
                      prop="version"
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-input-dispatcher
                        v-model="form.version"
                        placeholder="系统自动生成"
                        disabled
                      />
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==1">
                   <el-form-item
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-select-dispatcher
                        v-model="form.dsmIndexIds"
                        multiple
                        filterable
                        remote
                        reserve-keyword
                        style="width:100%"
                        placeholder="请输入关键词"
                        :remote-method="remoteIndexMethod"
                        @change="$forceUpdate()"
                        :loading="loading"
                      >
                        <el-option
                          v-for="item in dsmIndexOptions"
                          :key="item.indexId"
                          :label="item.indexName"
                          :value="item.indexId"
                        >
                        </el-option>
                      </el-select-dispatcher>
                    </el-form-item>
                  </span>
                  <span v-if="scope.$index==2">
                    <el-form-item
                      label=""
                      :class="{ disabled: iFDisable == 'read' }"
                    >
                      <el-select-dispatcher
                        v-model="form.dsmDimensionIds"
                        multiple
                        filterable
                        remote
                        reserve-keyword
                        style="width:100%"
                        placeholder="请输入关键词"
                        @change="$forceUpdate()"
                        :remote-method="remoteDimensionMethod"
                        :loading="loading"
                      >
                        <el-option
                          v-for="item in dsmDimensionOptions"
                          :key="item.dimensionId"
                          :label="item.dimensionName"
                          :value="item.dimensionId"
                        >
                        </el-option>
                      </el-select-dispatcher>
                    </el-form-item>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-form-item
                  label-width="0px"
                  :error="uploaderr"
                  :class="{ disabled: iFDisable == 'read' }"
                >
                  <div class="upload">
                    <el-upload
                      ref="upload"
                      class="upload-demo"
                      :action="uploadUrl"
                      accept=".jpg,.jpeg,.txt,.zip,.xls,.xlsx,.doc,.docx,.pdf,.PDF"
                      :auto-upload="true"
                      :file-list="fileList"
                      :on-success="handleSuccess"
                      :on-remove="handleRemove"
                      :disabled="iFDisable == 'read'"
                      :headers="myHeaders"
                      :limit="1"
                    >
                      <el-button
                        size="small"
                        icon="el-icon-upload2"
                        type="danger"
                        :disabled="iFDisable == 'read'"
                        >上传附件</el-button
                      >
                    </el-upload>
                    <div class="uptxt">
                      （支持pdf、word、excel、zip、jpg，文件限制500M以内）
                    </div>
                  </div>
                </el-form-item>
            <el-form-item label="文档名称" prop="attachment" v-show="false">
              <el-input-dispatcher
                v-model="form.attachment"
                maxLength="20"
                placeholder="请输入文档名称"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
         <br />
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-table
              v-loading="loading"
              :data="dsmMdmRelList"
              highlight-current-row
              max-height="400"
          >
              <el-table-column label="引用情况">
              <el-table-column
                  label="系统"
                  align="center"
                  prop="systemName"
              />
              <el-table-column
                  label="数据源"
                  align="center"
                  prop="datasourceName"
              />
              <el-table-column label="表" align="center" prop="tableName" />
              <el-table-column label="字段" align="center" prop="propName" />
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <br />
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-table v-loading="loading" :data="dsmIndexHisList">
              <el-table-column label="历史版本">
                <el-table-column
                  label="指标名称"
                  align="center"
                  prop="indexName"
                />
                <el-table-column
                  label="业务条线"
                  align="center"
                  prop="indexClassNameFull"
                  width="255"
                />
                <el-table-column
                  label="指标类型"
                  align="center"
                  prop="indexType"
                  :formatter="indexTypeFormat"
                />
                <el-table-column
                  label="指标状态"
                  align="center"
                  prop="status"
                  :formatter="statusFormat"
                />
                <el-table-column label="版本" align="center" prop="version" />
                <el-table-column
                  label="操作"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-finished"
                      @click="balanceShow(scope.row)"
                      v-if="scope.row.status == 'INVALID'"
                      >对比</el-button
                    >
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          v-if="iFDisable == 'write'"
          @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
        <!-- <el-button
          icon="el-icon-edit"
          v-hasPermi="['dsm:dsmStandard:edit']"
          v-if="iFDisable == 'read'"
          @click="handleModify"
          >修改</el-button
        > -->
      </div>


    <el-dialog
      :title="title"
      :visible.sync="dsmMdmRelOpen"
      width="950px"
      append-to-body
    >
      <add
        :loading="loading"
        :handleType="handleType"
        :indexId="indexId"
        v-if="dsmMdmRelOpen"
      ></add>
    </el-dialog>

    <el-dialog
      title="对比"
      :visible.sync="balanceOpen"
      width="1200px"
      append-to-body
    >
      <contrast
        :loading="loading"
        :form="form"
        :formOld="formOld"
        :indexTypeOptions="indexTypeOptions"
        :indexClassIdOptions="indexClassIdOptions"
        :statusOptions="statusOptions"
        :unitOptions="unitOptions"
        :dsmIndexOptions="dsmIndexOptions"
        :dsmDimensionOptions="dsmDimensionOptions"
        :dataTypeOptions="dataTypeOptions"
        :isPriKeyOptions="isPriKeyOptions"
        :nullableOptions="nullableOptions"
        :ifDownOptions="ifDownOptions"
        :cycleOptions="cycleOptions"
        :systemOptions="systemOptions"
      ></contrast>
      <div slot="footer" class="dialog-footer">
        <el-button @click="balanceOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import quote from './quote'
import add from './add'
import {
  delDsmIndex,
  addDsmIndex,
  updateDsmIndex,
  getDsmIndex,
  listDsmIndex,
  listDsmIndexByNo
} from "@/api/dsm/dsmIndex";
import { listDsmDimension } from "@/api/dsm/dsmDimension";
import {
  listDsmMdmRel,
  unlistDsmMdmRel,
  updateDsmMdmRel,
  updateDsmMdmRelIndexUn
} from "@/api/dsm/dsmMdmRel";
import contrast from "./contrast";
import { listSystem } from "@/api/basic/datasource";

import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import VueUeditorWrap from "vue-ueditor-wrap";

export default {
  components: {
    Treeselect,
    VueUeditorWrap,
    contrast,
    add,
    quote
  },
  provide() {
    return {
      rwDispatcherProvider: this
    };
  },
  computed: {
    rwDispatcherState() {
      return this.iFDisable;
    }
  },
  props: [
    "indexClassIdOptions",
    "indexTypeOptions",
    "statusOptions",
    "unitOptions",
    "cycleOptions",
    "loading",
    "iFDisable"
  ],
  data() {
    return {
      total: 20,
      pageNum: 1,
      tableData: [{
          date: ''
        }, {
          date: ''
        },
        {
          date: ''
        },
        {
          date: ''
        },{
          date: ''
        }],
      tableData2: [{
          date: ''
        }, {
          date: ''
        },
        {
          date: ''
        },
        {
          date: ''
        },{
          date: ''
        },{
          date: ''
        },
        {
          date: ''
        }],
         tableData3: [{
          date: ''
        }, {
          date: ''
        },
        {
          date: ''
        }],
      tableData4: [{
          date: ''
        }, {
          date: ''
        },
        {
          date: ''
        },{
          date: ''
        },{
           date: ''
         }],
tableData5: [{
          date: ''
        }, {
          date: ''
        },
        {
          date: ''
        },{
          date: ''
        }],
      dsmIndexHisList: [],
      dsmDimensionOptions: [],
      dsmMdmRelOpen: false,
      dsmMdmRelUnList: [],
      dsmMdmRelList: [],
      dsmIndexOptions: [],
      systemOptions:[],
      balanceOpen: false,
      handleType: "ADD",
      // 弹出层标题
      title: "",
      // 表单参数
      form: {},
      formOld: {},
      // 是否为空字典
      nullableOptions: [],
      ifDownOptions:[],
      // 是否主键字典
      isPriKeyOptions: [],
      // 数据类型字典
      dataTypeOptions: [],
      fileList: [],
      myHeaders: {
        Authorization: "Bearer " + getToken()
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/attachment/upload",
      uploaderr: "",
      myConfig: {
        // 如果需要上传功能,找后端小伙伴要服务器接口地址
        serverUrl: process.env.VUE_APP_BASE_API + "/common/attachment/upload",
        // 你的UEditor资源存放的路径,相对于打包后的index.html
        UEDITOR_HOME_URL: "/UEditor/",
        // 编辑器不自动被内容撑高
        autoHeightEnabled: false,
        // 初始容器高度
        initialFrameHeight: 180,
        // 初始容器宽度
        initialFrameWidth: "100%",
        // 关闭自动保存
        enableAutoSave: false,
        zIndex: 9999,
        // 自定义工具栏，需要额外选项可以参考ueditor.config.js
        toolbars: [["kityformula"]]
      },
      // 表单校验
      rules: {
        indexCode: [
          { required: true, message: "指标编码不能为空", trigger: "blur" }
        ],
        indexName: [
          { required: true, message: "指标名称不能为空", trigger: "blur" }
        ],
        indexType: [
          { required: true, message: "指标类型不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "指标状态不能为空", trigger: "blur" }
        ],
        indexClassId: [
          { required: true, message: "业务条线不能为空", trigger: "change" }
        ]
      },
      bjqid:'',
      editor:'',
      ueditor:false
    };
  },
  created() {
    this.getDicts("sys_data_type").then(response => {
      this.dataTypeOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isPriKeyOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.nullableOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
          this.ifDownOptions = response.data;
        });
    this.getSystem();

  },
  methods: {
    closeDialog(done){
      this.editor = UE.getEditor(this.bjqid,this.myConfig)
      this.editor.destroy()
      done()
    },
    ready(editorInstance){
      this.bjqid = editorInstance.key
    },
    getDsmMdmRelUnForm() {
      this.dsmMdmRelOpen = true;
    },
    getSystem() {
      listSystem().then(response => {
        this.systemOptions = response.data;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.form.propIds = this.getRelMdm();
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.indexId != null) {
            updateDsmIndex(this.form).then(response => {
              this.msgSuccess("修改成功");
              if(this.$listeners.openChange){
                this.$emit('openChange',false)
              }
              this.$parent.$parent.getList();
            });
          } else {
            addDsmIndex(this.form).then(response => {
              this.msgSuccess("新增成功");
              if(this.$listeners.openChange){
                this.$emit('openChange',false)
              }
              this.$parent.$parent.getList();
            });
          }
        }
      });
    },
    // 模糊搜索
    remoteDimensionMethod(query) {
      if (query !== "") {
        setTimeout(() => {
          let params = { dimensionName: query };
          listDsmDimension(params).then(response => {
            this.dsmDimensionOptions = response.rows;
          });
        }, 200);
      } else {
        this.dsmDimensionOptions = [];
      }
    },
    delDsmMdmRel(row, index) {
      if (this.handleType == "UPDATA") {
        const dsmMdmForm = { indexId: this.indexId, propId: row.propId };
        updateDsmMdmRelIndexUn(dsmMdmForm).then(response => {
          this.msgSuccess("修改成功");
          this.dsmMdmRelList.splice(index, 1);
        });
      } else {
        this.dsmMdmRelList.splice(index, 1);
      }
    },
    getRelMdm() {
      const propIds = [];
      for (var i = 0; i < this.dsmMdmRelList.length; i++) {
        propIds.push(this.dsmMdmRelList[i].propId);
      }
      return propIds;
    },
    // addDsmMdmRel(row, index) {
    //   if (this.handleType == "UPDATA") {
    //     const dsmMdmForm = { indexId: this.indexId, propId: row.propId };
    //     updateDsmMdmRel(dsmMdmForm).then(response => {
    //       this.msgSuccess("修改成功");
    //       this.getDsmMdmRelList();
    //       this.dsmMdmRelUnList.splice(index, 1);
    //     });
    //   } else {
    //     this.dsmMdmRelList.push(row);
    //     this.dsmMdmRelUnList.splice(index, 1);
    //   }
    // },
    getDsmMdmRelList() {
      const relParams = { indexId: this.indexId, pageNum: 1, pageSize: 1000 };
      listDsmMdmRel(relParams).then(response => {
        this.dsmMdmRelList = response.rows;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        indexNo: null,
        indexId: null,
        indexCode: null,
        indexName: null,
        indexType: null,
        status: "RUNNING",
        attachment: null,
        definition: null,
        execSql: null,
        unit: null,
        cycle: null,
        version: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null,
        indexClassId: null,
        formula: null,
        dataType: null,
        columnSize: null,
        decimalDigits: null,
        isPriKey: "N",
        nullable: "Y",
        defaultValue: null,
        ifDown:"y",
        systemId:null
      };
      this.resetForm("form");
      this.indexId = "";
      this.dsmMdmRelList = [];
      this.dsmMdmRelUnList = [];
      this.handleType = "ADD";
      this.dsmMdmRelOpen = false;
      this.dsmIndexHisList = [];
    },
    // 模糊搜索
    remoteIndexMethod(query) {
      if (query !== "") {
        setTimeout(() => {
          let params = { indexName: query };
          listDsmIndex(params).then(response => {
            this.dsmIndexOptions = response.rows;
          });
        }, 200);
      } else {
        this.dsmIndexOptions = [];
      }
    },
    handle(row) {
      this.reset();
      const indexId = row.indexId || this.$parent.$parent.ids[0];
      this.getIndex(indexId)
    },
    getIndex(indexId){
      this.indexId = indexId;
      getDsmIndex(indexId).then(response => {
        this.form = response.data;
        if(!response.data){
          this.ueditor = false
          return
        }else {
          this.ueditor = true // 解决编辑器渲染问题
        }
        if(response.data.attachment!=null && response.data.attachment!=""){
          this.fileList = [
            {
              name: response.data.attachment,
              url: response.data.attachment
            }
          ];
        }
        this.dsmDimensionOptions = response.data.dsmDimensions;
        this.form.dsmDimensionIds = response.data.dsmDimensionIdsByList;
        this.dsmIndexOptions = response.data.dsmIndexs;
        this.form.dsmIndexIds = response.data.dsmIndexIdsByList;
        const hisQueryParams = {
          indexNo: response.data.indexNo,
          pageNum: 1,
          pageSize: 100
        };
        listDsmIndexByNo(hisQueryParams).then(response => {
          this.dsmIndexHisList = response.rows;
        });
      });
      this.getDsmMdmRelList();
    },
    handleSuccess(res, file, fileList) {
      // 文件上传成功处理
      this.form.attachment = res.msg;
      //成功后的业务逻辑处理
    },
    handleRemove(res, file, fileList){
      //文件移除处理
      this.form.attachment = "";
    },
    // 指标类型字典翻译
    indexTypeFormat(row, column) {
      return this.selectDictLabel(this.indexTypeOptions, row.indexType);
    },
     // 应用系统翻译
    systemFormat(row, column) {
      return this.selectLabel(this.systemOptions, row.systemId,'systemId','name');
    },
    // 指标状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
 // 指标状态字典翻译
   ifDownFormat(row, column) {
      return this.selectDictLabel(this.ifDownOptions, row.ifDown);
    },
    // 取消按钮
    cancel() {
      if(this.$listeners.openChange){
          this.$emit('openChange',false)
        }
      this.reset();
    },
    handleModify() {
      if(this.$listeners.changeiFDisable){
        this.$emit('changeiFDisable','write')
      }
    },
    balanceShow(row) {
      getDsmIndex(row.indexId).then(response => {
        this.formOld = response.data;
        this.formOld.dsmDimensionIds = response.data.dsmDimensionIdsByList;
        this.formOld.dsmIndexIds = response.data.dsmIndexIdsByList;
        this.balanceOpen = true;
      });
    }
  }
};
</script>

<style scoped>
>>> .vue-treeselect--disabled .vue-treeselect__control {
  background: #fff;
  border: 0;
}
>>> .vue-treeselect--disabled
  .vue-treeselect__control
  .vue-treeselect__control-arrow-container {
  display: none;
}
.disabled {
  margin-bottom: 0;
}
.disabled >>> .edui-editor-iframeholder {
  height: 93px !important;
}
.title {
  line-height: 22px;
}
>>>.dialog-footer{
  text-align:right;
  margin-top:20px;
}
</style>
