package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmDiscernMain;
import com.dqms.dsm.service.IDsmDiscernMainService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 落标主Controller
 *
 * <AUTHOR>
 * @date 2021-06-01
 */
@RestController
@RequestMapping("/dsm/dsmDiscernMain")
public class DsmDiscernMainController extends BaseController
{
    @Autowired
    private IDsmDiscernMainService dsmDiscernMainService;

    /**
     * 查询落标主列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DsmDiscernMain dsmDiscernMain)
    {
        startPage();
        List<DsmDiscernMain> list = dsmDiscernMainService.selectDsmDiscernMainList(dsmDiscernMain);
        return getDataTable(list);
    }

    /**
     * 导出落标主列表
     */
    @Log(title = "落标主", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmDiscernMain dsmDiscernMain)
    {
        List<DsmDiscernMain> list = dsmDiscernMainService.selectDsmDiscernMainList(dsmDiscernMain);
        ExcelUtil<DsmDiscernMain> util = new ExcelUtil<DsmDiscernMain>(DsmDiscernMain.class);
        return util.exportExcel(list, "dsmDiscernMain");
    }

    /**
     * 获取落标主详细信息
     */
    @GetMapping(value = "/{discernMainId}")
    public AjaxResult getInfo(@PathVariable("discernMainId") Long discernMainId)
    {
        return AjaxResult.success(dsmDiscernMainService.selectDsmDiscernMainById(discernMainId));
    }

    /**
     * 新增落标主
     */
    @Log(title = "落标主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmDiscernMain dsmDiscernMain)
    {
        return toAjax(dsmDiscernMainService.insertDsmDiscernMain(dsmDiscernMain));
    }

    /**
     * 修改落标主
     */
    @Log(title = "落标主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmDiscernMain dsmDiscernMain)
    {
        return toAjax(dsmDiscernMainService.updateDsmDiscernMain(dsmDiscernMain));
    }

    /**
     * 删除落标主
     */
    @Log(title = "落标主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{discernMainIds}")
    public AjaxResult remove(@PathVariable Long[] discernMainIds)
    {
        return toAjax(dsmDiscernMainService.deleteDsmDiscernMainByIds(discernMainIds));
    }
}
