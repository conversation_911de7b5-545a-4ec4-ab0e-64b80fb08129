<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="数据名称" prop="manualDataName">
        <el-input
          v-model="queryParams.manualDataName"
          placeholder="请输入数据名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="目标表" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入目标表"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计周期" prop="cycle">
        <el-select v-model="queryParams.cycle" placeholder="请选择统计周期" clearable size="small">
          <el-option
            v-for="dict in cycleOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="dicManualDataDefineList" @selection-change="handleSelectionChange" @sort-change="getList" :default-sort = "{prop: 'applyCount', order: 'descending'}">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="数据名称" align="center" prop="manualDataName"   width="220">
	      <template slot-scope="scope">
			  <router-link size="mini" :to="'/dic/dicManualDataManager/' + scope.row.manualDataId" class="link-type" >{{ scope.row.manualDataName}} </router-link>
		  </template>
      </el-table-column>
      <el-table-column label="目标表" align="center" prop="tableName" width="280" />
      <el-table-column label="权限控制" align="center" prop="isAuth" :formatter="isAuthFormat" />
      <el-table-column label="统计周期" align="center" prop="cycle" :formatter="cycleFormat" />
      <el-table-column label="待审核" align="center" prop="applyCount" sortable>
        <template slot-scope="scope">
            <span v-if="scope.row.applyCount>0" style="color:#E6A23C;font-color:#E6A23C">{{scope.row.applyCount}}</span>
            <span v-if="scope.row.applyCount==0">{{scope.row.applyCount}}</span>
        </template>
      </el-table-column>
      <el-table-column label="最新导入时间" align="center" prop="upTime"  width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.upTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最新审核时间" align="center" prop="upTime"  width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.apTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>      
      <el-table-column label="说明" align="center" prop="remark" >
        <template slot-scope="scope">
         <el-popover trigger="hover" placement="top">
            <p>{{scope.row.remark}}</p>
            <div slot="reference" >
              <i class="el-icon-warning" style="color:#E6A23C;font-color:#E6A23C"></i>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
		<template slot-scope="scope">
		<el-button
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            >
			  <router-link size="mini" :to="'/dic/dicManualDataManager/' + scope.row.manualDataId" class="link-type" >补录</router-link>
			</el-button>
		</template>
	  </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listDicManualDataDefine,listDicManualDataDefineByUse, getDicManualDataDefine, delDicManualDataDefine, addDicManualDataDefine, updateDicManualDataDefine, exportDicManualDataDefine ,changeStatus} from "@/api/dic/dicManualDataDefine";
import { listDatasourceAll,findTablesSelect } from "@/api/basic/datasource";
import { listTask} from "@/api/task/task";
import { getToken } from "@/utils/auth";
export default {
  name: "DicManualDataDefine",
  components: {
  },
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() }
      },
      uploading: false,
      uploaderr: "",
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/attachment/upload",
      myHeaders: {
        Authorization: "Bearer " + getToken()
      },
      isfile: true,
      fileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 手工数据配置表格数据
      dicManualDataDefineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否审批字典
      isApplyOptions: [],
      // 读写分离字典
      isSeparationOptions: [],
      // 权限控制字典
      isAuthOptions: [],
      // 统计周期字典
      cycleOptions: [],
      // 状态字典
      statusOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 修改时间时间范围
      daterangeUpdateTime: [],
      //数据源选项
      dataSourceOptions: [],
      //表选项
      tableOptions: [],
      // 关联任务
      taskOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        manualDataName: null,
        datasourceId: null,
        tableName: null,
        preSql: null,
        posTaskId: null,
        isApply: null,
        isSeparation: null,
        isAuth: null,
        cycle: null,
        maxLine: null,
        minLine: null,
        status: "1",
        prop:"applyCount",
        order:"descending"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        manualDataName: [
          { required: true, message: "数据名称不能为空", trigger: "blur" }
        ],
        datasourceId: [
          { required: true, message: "数据源不能为空", trigger: "blur" }
        ],
        tableName: [
          { required: true, message: "目标表不能为空", trigger: "blur" }
        ],
        isApply: [
          { required: true, message: "是否审批不能为空", trigger: "change" }
        ],
        isSeparation: [
          { required: true, message: "读写分离不能为空", trigger: "change" }
        ],
        isAuth: [
          { required: true, message: "权限控制不能为空", trigger: "change" }
        ],
        cycle: [
          { required: true, message: "统计周期不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response => {
      this.isApplyOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isSeparationOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isAuthOptions = response.data;
    });
    this.getDicts("nes_needs_rate").then(response => {
      this.cycleOptions = response.data;
    });
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
    this.getDataSource();
  },
  methods: {
    /** 查询手工数据配置列表 */
    getList(column) {
      this.loading = true;
      if(column!=undefined&&column.prop){
    	  this.queryParams.prop=column.prop;  
      }else{
    	  this.queryParams.prop="applyCount";
      }
      
      if(column!=undefined&&column.order){
    	  this.queryParams.order=column.order;  
      }else{
    	  this.queryParams.order="descending";
      }
      
      if(this.queryParams.order=="descending"){
    	  this.queryParams.order="desc";
      }else{
    	  this.queryParams.order="asc";
      }
      
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listDicManualDataDefineByUse(this.queryParams).then(response => {
        this.dicManualDataDefineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getDataSource() {
      listDatasourceAll().then(response => {
        this.dataSourceOptions = response.data;
      });
    },
    changed(value) {
      this.form.tableName=null
      if (value != null&&value != "") {
        findTablesSelect(value).then(response => {
          this.tableOptions = response.data;
        });
      }else{
          this.tableOptions = []
      }
    },
    // 是否审批字典翻译
    isApplyFormat(row, column) {
      return this.selectDictLabel(this.isApplyOptions, row.isApply);
    },
    // 读写分离字典翻译
    isSeparationFormat(row, column) {
      return this.selectDictLabel(this.isSeparationOptions, row.isSeparation);
    },
    // 权限控制字典翻译
    isAuthFormat(row, column) {
      return this.selectDictLabel(this.isAuthOptions, row.isAuth);
    },
    // 统计周期字典翻译
    cycleFormat(row, column) {
      return this.selectDictLabel(this.cycleOptions, row.cycle);
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        manualDataId: null,
        manualDataName: null,
        datasourceId: null,
        tableName: null,
        preSql: null,
        posTaskId: null,
        isApply: 'N',
        isSeparation: 'N',
        isAuth: 'N',
        cycle: 'REAL',
        maxLine: 0,
        minLine: 0,
        status: '0',
        remark: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.manualDataId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加手工数据配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const manualDataId = row.manualDataId || this.ids
      getDicManualDataDefine(manualDataId).then(response => {
   	  	this.taskOptions =[{
             taskName: response.data.posTaskName,
             taskId: response.data.posTaskId
           }]
   	 	if(response.data.attachment!=null && response.data.attachment!=""){
         this.fileList = [
           {
             name: response.data.attachment,
             url: response.data.attachment
           }
         ];
       }
        this.form = response.data;
        this.open = true;
        this.title = "修改手工数据配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.manualDataId != null) {
            updateDicManualDataDefine(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDicManualDataDefine(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const manualDataIds = row.manualDataId || this.ids;
      this.$confirm('是否确认删除手工数据配置编号为"' + manualDataIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDicManualDataDefine(manualDataIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有手工数据配置数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDicManualDataDefine(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    // 模糊搜索
    remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
            let taskParams = {taskName:query}
            listTask(taskParams).then(response => {
	          this.taskOptions = response.rows;
	        });
          }, 200);
        } else {
          this.taskOptions = [];
        }
    },
    // 状态修改
    handleStatusChange(row) {
    	return changeStatus(row.manualDataId,row.status);
    },
    handleSuccess(res, file, fileList) {
      // 文件上传成功处理
      this.form.attachment = res.msg;
      //成功后的业务逻辑处理
    },
    handleRemove(res, file, fileList){
      //文件移除处理
      this.form.attachment = "";
    }
  }
};
</script>
