package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DictUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dam.domain.DamAssets;
import com.dqms.dam.enums.DamConstants;
import com.dqms.dam.service.IDamAssetsService;
import com.dqms.dsm.domain.*;
import com.dqms.dsm.mapper.DsmIndexClassMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.dsm.domain.vo.DsmIndexRelVo;
import com.dqms.dsm.domain.vo.DsmIndexVo;
import com.dqms.dsm.enums.DsmConstants;
import com.dqms.dsm.mapper.DsmIndexMapper;
import com.dqms.dsm.mapper.DsmMdmRelMapper;
import com.dqms.dsm.service.IDsmIndexService;
import com.dqms.framework.web.service.TokenService;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.system.service.ISysConfigService;

/**
 * 指标管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Service
public class DsmIndexServiceImpl implements IDsmIndexService
{
    @Autowired
    private DsmIndexMapper dsmIndexMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private DsmMdmRelMapper dsmMdmRelMapper;

	@Autowired
	private DsmIndexClassMapper dsmIndexClassMapper;

    @Autowired
    private IDamAssetsService damAssetsService;

    @Autowired
    private ISysConfigService sysConfigService;
	@Autowired
	SysSystemMapper systemMapper;
    /**
     * 查询指标管理
     *
     * @param indexId 指标管理ID
     * @return 指标管理
     */
    @Override
    public DsmIndex selectDsmIndexById(Long indexId)
    {
        return dsmIndexMapper.selectDsmIndexById(indexId);
    }
    @Override
    public DsmIndexVo selectDsmIndexVoById(Long indexId)
    {
    	DsmIndexVo  vo = dsmIndexMapper.selectDsmIndexVoById(indexId);
    	if(vo!=null) {
    		StringBuffer s = new StringBuffer();
    		s.append("指标名称：").append(vo.getIndexName()).append("  ")
    		 .append("指标编码：").append(vo.getIndexCode()).append("  ")
    		 .append("指标分类：").append(vo.getIndexClassNameFull()).append("  ")
    		 .append("业务定义：").append(vo.getDefinition()).append("  ");
    		vo.setMemo(s.toString());
    	}
        return vo;
    }

    /**
     * 查询指标管理列表
     *
     * @param dsmIndex 指标管理
     * @return 指标管理
     */
    @Override
    public List<DsmIndex> selectDsmIndexList(DsmIndex dsmIndex)
    {
        return dsmIndexMapper.selectDsmIndexList(dsmIndex);
    }

    @Override
    public List<DsmIndex> selectDsmIndexListByPage(DsmIndex dsmIndex)
    {
        return dsmIndexMapper.selectDsmIndexListByPage(dsmIndex);
    }
	@Override
	public List<DsmIndex> selectDsmIndexListForShow(DsmIndex dsmIndex)
	{
		return dsmIndexMapper.selectDsmIndexListForShow(dsmIndex);
	}

	@Override
	public List<DsmIndexVo> selectDsmIndexVoList(DsmIndex dsmIndex) {
		return dsmIndexMapper.selectDsmIndexVoList(dsmIndex);
	}

	@Override
    public List<DsmIndex> selectDsmIndexListByNo(DsmIndex dsmIndex)
    {
        return dsmIndexMapper.selectDsmIndexListByNo(dsmIndex);
    }
    @Override
    public List<DsmIndex> selectDsmIndexListMap(DsmIndex dsmIndex)
    {
    	Long[] ids=new Long[] {dsmIndex.getIndexId()};
    	List<DsmIndex> list =  new ArrayList<DsmIndex>();
    	HashMap<Long, Long> map = new HashMap<Long, Long>();
    	selectDsmIndexByList(ids,list,map);
        return list;
    }

    public void selectDsmIndexByList(Long[] ids,List<DsmIndex> list,HashMap<Long, Long> map)
    {
    	List<DsmIndex> listr =  dsmIndexMapper.selectDsmIndexListMap(ids);
    	for(DsmIndex index : listr) {
    		if(map.get(index.getIndexId())==null) {
    			list.add(index);
    			map.put(index.getIndexId(), index.getIndexId());
        		if(index.getDsmIndexs()!=null&&index.getDsmIndexs().size()>0) {
        			selectDsmIndexByList(index.getDsmIndexIdsByList(),list,map);
        		}
    		}

    	}
    }

    @Override
    public List<DsmIndexRelVo> selectDsmIndexListRel(DsmIndex dsmIndex)
    {
    	Long[] ids=new Long[] {dsmIndex.getIndexId()};
    	HashMap<Long, Long> map = new HashMap<Long, Long>();
    	List<DsmIndexRelVo> list = selectDsmIndexRelByList(ids,map);
    	for(DsmIndexRelVo vo : list ) {
    		if(vo.getChildren()!=null&&vo.getChildren().size()>0) {
    			vo.getChildren().addAll(selectDsmIndexRelBySRCList(ids, map));
    		}else {
    			vo.setChildren(selectDsmIndexRelBySRCList(ids, map));
    		}
    	}
        return list;
    }
    public List<DsmIndexRelVo> selectDsmIndexRelByList(Long[] ids,HashMap<Long, Long> map)
    {
    	List<DsmIndexRelVo> list =  new ArrayList<DsmIndexRelVo>();
    	List<DsmIndex> listr =  dsmIndexMapper.selectDsmIndexListMap(ids);
    	for(DsmIndex index : listr) {
    		if(map.get(index.getIndexId())==null) {
    			DsmIndexRelVo vo = new DsmIndexRelVo();
    			vo.setId(index.getIndexId().toString());
    			vo.setLabel(index.getIndexName());
    			vo.setType("S");
    			list.add(vo);
    			map.put(index.getIndexId(), index.getIndexId());
        		if(index.getDsmIndexs()!=null&&index.getDsmIndexs().size()>0) {
        			vo.setChildren(selectDsmIndexRelByList(index.getDsmIndexIdsByList(),map));
        		}
    		}

    	}
    	return list;
    }

    public List<DsmIndexRelVo> selectDsmIndexRelBySRCList(Long[] ids,HashMap<Long, Long> map)
    {
    	List<DsmIndex> listr =  dsmIndexMapper.selectDsmIndexRelBySRCList(ids);
    	List<DsmIndexRelVo> list =  new ArrayList<DsmIndexRelVo>();
    	for(DsmIndex index : listr) {
    		if(map.get(index.getIndexId())==null) {
    			DsmIndexRelVo vo = new DsmIndexRelVo();
    			vo.setId(index.getIndexId().toString());
    			vo.setLabel(index.getIndexName());
    			vo.setType("T");
    			list.add(vo);
    			map.put(index.getIndexId(), index.getIndexId());
        		if(index.getDsmIndexs()!=null&&index.getDsmIndexs().size()>0) {
        			vo.setChildren(selectDsmIndexRelBySRCList(index.getDsmIndexIdsByList(),map));
        		}
    		}

    	}
    	return list;
    }

    /**
     * 新增指标管理
     *
     * @param dsmIndex 指标管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDsmIndex(DsmIndex dsmIndex,String oldId)
    {
    	DamAssets damAssets = new DamAssets();
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmIndex.setCreateTime(DateUtils.getNowDate());
    	dsmIndex.setCreateId(loginUser.getUser().getUserId());
    	dsmIndex.setCreateBy(loginUser.getUser().getNickName());
    	dsmIndex.setUpdateTime(DateUtils.getNowDate());
    	dsmIndex.setUpdateId(loginUser.getUser().getUserId());
    	dsmIndex.setUpdateBy(loginUser.getUser().getNickName());
    	dsmIndex.setStatus(DsmConstants.DSM_STATUS_RUNNING);
		List<DsmIndex> tName=dsmIndexMapper.selectDsmIndexByName(dsmIndex);
		if(tName!=null&&tName.size()!=0){
			throw new RuntimeException("指标标准名称已经存在！");
		}
		List<DsmIndex> tCode=dsmIndexMapper.selectDsmIndexByCode(dsmIndex);
		if(tCode!=null&&tCode.size()!=0){
			throw new RuntimeException("指标标准编码已经存在！");
		}
    	int i = dsmIndexMapper.insertDsmIndex(dsmIndex);
    	insertDsmIndexIndex(dsmIndex);
    	insertDimension(dsmIndex);
    	if(dsmIndex.getPropIds()!=null&&dsmIndex.getPropIds().length>0) {
    		for(Long propId :dsmIndex.getPropIds()) {
    			DsmMdmRel dsmMdmRel = new DsmMdmRel();
        		dsmMdmRel.setPropId(propId);
        		dsmMdmRel.setIndexId(dsmIndex.getIndexId());
        		dsmMdmRelMapper.updateDsmMdmRel(dsmMdmRel);
    		}

    	}
    	if(StringUtils.isNotEmpty(oldId)) {
	    	dsmIndexMapper.updateDsmIndexIndexByTar(dsmIndex.getIndexId(), Long.parseLong(oldId));
    	}

        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	if(StringUtils.isNotEmpty(oldId)) {
        		damAssets = damAssetsService.selectDamAssetsByRel(oldId, DamConstants.DSM_TYPE_INDEX);
            	if(damAssets!=null) {
                	damAssets.setAssetsName(dsmIndex.getIndexName());
                    damAssets.setAssetsType(DamConstants.DSM_TYPE_INDEX);
                    damAssets.setAssetsCode(DamConstants.DSM_TYPE_INDEX+dsmIndex.getIndexCode());
                    damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
                    damAssets.setRelId(dsmIndex.getIndexId()+"");
                    StringBuffer s = new StringBuffer();
            		s.append("指标名称：").append(dsmIndex.getIndexName()).append("  ")
            		 .append("指标编码：").append(dsmIndex.getIndexCode()).append("  ")
            		 .append("业务定义：").append(dsmIndex.getDefinition()).append("  ");
            		damAssets.setRemark(s.toString());
                    damAssetsService.updateDamAssets(damAssets);
            	}
        	}else {
            	damAssets.setAssetsName(dsmIndex.getIndexName());
                damAssets.setAssetsType(DamConstants.DSM_TYPE_INDEX);
                damAssets.setAssetsCode(DamConstants.DSM_TYPE_INDEX+dsmIndex.getIndexCode());
                damAssets.setStatus(DamConstants.DSM_STATUS_RUNNING);
                damAssets.setRelId(dsmIndex.getIndexId()+"");
                StringBuffer s = new StringBuffer();
        		s.append("指标名称：").append(dsmIndex.getIndexName()).append("  ")
        		 .append("指标编码：").append(dsmIndex.getIndexCode()).append("  ")
        		 .append("业务定义：").append(dsmIndex.getDefinition()).append("  ");
        		damAssets.setRemark(s.toString());
                damAssetsService.insertDamAssets(damAssets);
        	}

        }
        return i;
    }

    @Transactional
    public void insertDsmIndexIndex(DsmIndex dsmIndex)
    {
    	if(dsmIndex.getDsmIndexIds()==null||dsmIndex.getDsmIndexIds().length==0) {return;}
    	Long[] sysIndexIds = dsmIndex.getDsmIndexIds();
        Long sysIndexId = dsmIndex.getIndexId();
            List<DsmIndexIndex> list = new ArrayList<DsmIndexIndex>();
            for (Long indexId : sysIndexIds)
            {
            	DsmIndexIndex item = new DsmIndexIndex();
            	item.setSouIndexId(sysIndexId);
            	item.setTarIndexId(indexId);
                list.add(item);
            }
            if (list.size() > 0)
            {
            	dsmIndexMapper.insertDsmIndexIndex(list);
            }
    }

    @Transactional
    public void insertDimension(DsmIndex dsmIndex)
    {
    	if(dsmIndex.getDsmDimensionIds()==null||dsmIndex.getDsmDimensionIds().length==0) {return;}
    	Long[] sysDimensionIds = dsmIndex.getDsmDimensionIds();
        Long sysDimensionId = dsmIndex.getIndexId();
            List<DsmIndexDimension> list = new ArrayList<DsmIndexDimension>();
            for (Long dimensionId : sysDimensionIds)
            {
            	DsmIndexDimension item = new DsmIndexDimension();
            	item.setIndexId(sysDimensionId);
            	item.setDimensionId(dimensionId);
                list.add(item);
            }
            if (list.size() > 0)
            {
            	dsmIndexMapper.insertDsmIndexDimension(list);
            }
    }

    /**
     * 修改指标管理
     *
     * @param dsmIndex 指标管理
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDsmIndex(DsmIndex dsmIndex)
    {
    	DsmIndex dsmIndexOld=dsmIndexMapper.selectDsmIndexById(dsmIndex.getIndexId());
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmIndexOld.setUpdateTime(DateUtils.getNowDate());
    	dsmIndexOld.setUpdateId(loginUser.getUser().getUserId());
    	dsmIndexOld.setUpdateBy(loginUser.getUser().getNickName());
    	dsmIndexOld.setStatus(DsmConstants.DSM_STATUS_INVALID);
		List<DsmIndex> tName=dsmIndexMapper.selectDsmIndexByName(dsmIndex);
		if(tName.size()>1){
			throw new RuntimeException("指标标准名称已经存在！");
		}else if(tName.size()==1){
			if(!tName.get(0).getIndexId().equals(dsmIndex.getIndexId())){
				throw new RuntimeException("指标标准名称已经存在！");
			}
		}
		List<DsmIndex> tCode=dsmIndexMapper.selectDsmIndexByCode(dsmIndex);
		if(tCode.size()>1){
			throw new RuntimeException("指标标准编码已经存在！");
		}else if(tCode.size()==1){
			if(!tCode.get(0).getIndexId().equals(dsmIndex.getIndexId())){
				throw new RuntimeException("指标标准编码已经存在！");
			}
		}
		if(dsmIndex.getStatus().equals(DsmConstants.DSM_STATUS_INVALID)){
			String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
			DamAssets damAssets = new DamAssets();
			if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
				damAssets = damAssetsService.selectDamAssetsByRel(dsmIndex.getIndexId() + "", DamConstants.DSM_TYPE_INDEX);
				if (damAssets != null) {
					damAssets.setAssetsName(dsmIndex.getIndexName());
					damAssets.setAssetsType(DamConstants.DSM_TYPE_INDEX);
					damAssets.setAssetsCode(DamConstants.DSM_TYPE_INDEX + dsmIndex.getIndexCode());
					damAssets.setStatus(dsmIndex.getStatus());
					damAssets.setRelId(dsmIndex.getIndexId() + "");
					StringBuffer s = new StringBuffer();
					s.append("指标名称：").append(dsmIndex.getIndexName()).append("  ")
							.append("指标编码：").append(dsmIndex.getIndexCode()).append("  ")
							.append("业务定义：").append(dsmIndex.getDefinition()).append("  ");
					damAssets.setRemark(s.toString());
					damAssetsService.updateDamAssets(damAssets);
				}
			}
			return dsmIndexMapper.updateDsmIndex(dsmIndexOld);
		}
		else {
			dsmIndexMapper.updateDsmIndex(dsmIndexOld);
			dsmMdmRelMapper.updateDsmMdmRelIndexUnAll(dsmIndex.getIndexId());
			dsmIndex.setVersion(dsmIndexOld.getVersion() + 1);
			dsmIndex.setIndexNo(dsmIndexOld.getIndexNo());
			dsmIndex.setStatus(dsmIndexOld.getStatus());
			return this.insertDsmIndex(dsmIndex, dsmIndexOld.getIndexId() + "");
		}
    }

	@Override
	@Transactional
	public int updateDsmIndexStatus(DsmIndex dsmIndex)
	{

		return dsmIndexMapper.updateDsmIndexStatus(dsmIndex);
	}

    /**
     * 批量删除指标管理
     *
     * @param indexIds 需要删除的指标管理ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDsmIndexByIds(Long[] indexIds)
    {
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
        	for(Long indexId : indexIds) {
        		DamAssets d = damAssetsService.selectDamAssetsByRel(indexId+"", DamConstants.DSM_TYPE_INDEX);
            	if(d!=null) {
            		damAssetsService.deleteDamAssetsById(d.getDamAssetsId());
            	}
        	}
        }
        int x=0;
		for(int i=0;i<indexIds.length;i++){
			DsmIndex dsmIndex =dsmIndexMapper.selectDsmIndexById(indexIds[i]);
			x+=dsmIndexMapper.deleteDsmIndexByNos(dsmIndex.getIndexNo());
		}
        return x;
    }

    /**
     * 删除指标管理信息
     *
     * @param indexId 指标管理ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDsmIndexById(Long indexId)
    {
        String flag = sysConfigService.selectConfigByKey("dam.synchro.rel");
        if(StringUtils.isNotEmpty(flag)&&flag.equals("TRUE")) {
    		DamAssets d = damAssetsService.selectDamAssetsByRel(indexId+"", DamConstants.DSM_TYPE_INDEX);
        	if(d!=null) {
        		damAssetsService.deleteDamAssetsById(d.getDamAssetsId());
        	}
        }
		DsmIndex dsmIndex =dsmIndexMapper.selectDsmIndexById(indexId);
		return dsmIndexMapper.deleteDsmIndexByNos(dsmIndex.getIndexNo());

    }

	@Override
	@Transactional
	public String importDsmIndex(List<DsmIndexVo> dsmIndexList, Boolean isUpdateSupport) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		if (StringUtils.isNull(dsmIndexList) || dsmIndexList.size() == 0)
		{
			throw new CustomException("导入任务数据不能为空！");
		}
		int successNum = 0;
		int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		for (DsmIndexVo vo : dsmIndexList)
		{
			try {
				DsmIndex t = new DsmIndex();
				BeanUtils.copyBeanProp(t, vo);
				DsmIndex dsmIndex = dsmIndexMapper.selectDsmIndexByCodeOrName(vo.getIndexCode(),vo.getIndexName());
				if (StringUtils.isNull(dsmIndex) || (StringUtils.isNotNull(dsmIndex) && isUpdateSupport)) {
					DsmIndexClass dsmIndexClass = dsmIndexClassMapper.selectDsmIndexClassByName(vo.getIndexClassName());
					if (StringUtils.isNull(dsmIndexClass)) {
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、指标管理 " + vo.getIndexClassName() + " 分类名称未定义");
						continue;
					}
					t.setIndexClassId(dsmIndexClass.getIndexClassId());
					if(StringUtils.isEmpty(vo.getIndexType())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、指标管理 " + vo.getIndexType() + " 指标类型为空");
						continue;
					}
					if(StringUtils.isEmpty(vo.getIndexCode())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、指标管理 " + vo.getIndexType() + " 指标编码为空");
						continue;
					}
					if(StringUtils.isEmpty(vo.getStatus())){
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、指标管理 " + vo.getStatus() + " 状态名称为空");
						continue;
					}
					if(!StringUtils.isEmpty(vo.getSystemName())){
						SysSystem system=systemMapper.getSysSystemByName(vo.getSystemName());
						if(!system.equals(null)){
							t.setSystemId(system.getSystemId());
						}
					}
				}
				if (StringUtils.isNull(dsmIndex)) {
					t.setCreateTime(DateUtils.getNowDate());
					t.setCreateId(loginUser.getUser().getUserId());
					t.setCreateBy(loginUser.getUser().getNickName());
					t.setUpdateTime(DateUtils.getNowDate());
					t.setUpdateId(loginUser.getUser().getUserId());
					t.setUpdateBy(loginUser.getUser().getNickName());
			    	t.setIndexNo(UUID.randomUUID().toString());
			    	t.setVersion(1);
					this.insertDsmIndex(t,null);
					successNum++;
					successMsg.append("<br/>" + successNum + "、指标管理 " + vo.getIndexName() + " 导入成功");
				} else if (isUpdateSupport) {
					t.setIndexId(dsmIndex.getIndexId());
					t.setUpdateTime(DateUtils.getNowDate());
					t.setUpdateId(loginUser.getUser().getUserId());
					t.setUpdateBy(loginUser.getUser().getNickName());
					this.updateDsmIndex(t);
					successNum++;
					successMsg.append("<br/>" + successNum + "、指标管理 " + vo.getIndexName() + " 更新成功");
				} else {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、指标管理 "+vo.getIndexCode()+"/" + vo.getIndexName() + " 已存在");
				}
			}
			catch(Exception e)
			{
				failureNum++;
				String msg = "<br/>" + failureNum + "、指标管理 " + vo.getIndexName() + " 导入失败：";
				failureMsg.append(msg + e.getMessage());
			}

		}
		if (failureNum > 0)
		{
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
			throw new CustomException(failureMsg.toString());
		}
		else
		{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
}
