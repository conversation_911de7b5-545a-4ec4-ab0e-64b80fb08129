package com.dqms.dsc.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.dqms.basic.mapper.SysSystemMapper;
import com.dqms.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsc.mapper.DscEntityPropSafeClassMapper;
import com.dqms.dsc.domain.DscEntityPropSafeClass;
import com.dqms.dsc.service.IDscEntityPropSafeClassService;

/**
 * 安全分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-10
 */
@Service
public class DscEntityPropSafeClassServiceImpl implements IDscEntityPropSafeClassService
{
    @Autowired
    private DscEntityPropSafeClassMapper dscEntityPropSafeClassMapper;

    private SysSystemMapper systemMapper;

    /**
     * 查询安全分类
     *
     * @param safeClassId 安全分类ID
     * @return 安全分类
     */
    @Override
    public DscEntityPropSafeClass selectDscEntityPropSafeClassById(Long safeClassId)
    {
        return dscEntityPropSafeClassMapper.selectDscEntityPropSafeClassById(safeClassId);
    }

    /**
     * 查询安全分类列表
     *
     * @param dscEntityPropSafeClass 安全分类
     * @return 安全分类
     */
    @Override
    public List<DscEntityPropSafeClass> selectDscEntityPropSafeClassList(DscEntityPropSafeClass dscEntityPropSafeClass)
    {
        List<DscEntityPropSafeClass> list=new ArrayList<DscEntityPropSafeClass>();
        list =dscEntityPropSafeClassMapper.selectDscEntityPropSafeClassList(dscEntityPropSafeClass);
        return list;
    }
    @Override
    public List<DscEntityPropSafeClass> showClassLevel(DscEntityPropSafeClass dscEntityPropSafeClass)
    {
        List<DscEntityPropSafeClass> list=new ArrayList<DscEntityPropSafeClass>();
        list =dscEntityPropSafeClassMapper.showClassLevel(dscEntityPropSafeClass);
        return list;
    }


    /**
     * 新增安全分类n
     *
     * @param dscEntityPropSafeClass 安全分类
     * @return 结果
     */
    @Override
    public int insertDscEntityPropSafeClass(DscEntityPropSafeClass dscEntityPropSafeClass)
    {
        dscEntityPropSafeClass.setCreateTime(DateUtils.getNowDate());
        return dscEntityPropSafeClassMapper.insertDscEntityPropSafeClass(dscEntityPropSafeClass);
    }

    /**
     * 修改安全分类
     *
     * @param dscEntityPropSafeClass 安全分类
     * @return 结果
     */
    @Override
    public int updateDscEntityPropSafeClass(DscEntityPropSafeClass dscEntityPropSafeClass)
    {
        dscEntityPropSafeClass.setUpdateTime(DateUtils.getNowDate());
        return dscEntityPropSafeClassMapper.updateDscEntityPropSafeClass(dscEntityPropSafeClass);
    }

    public List<DscEntityPropSafeClass> getDscEntityPropSafeClassList(DscEntityPropSafeClass dscEntityPropSafeClass) {
        List<DscEntityPropSafeClass> list=new ArrayList<DscEntityPropSafeClass>();
        list =dscEntityPropSafeClassMapper.getDscEntityPropSafeClassList(dscEntityPropSafeClass);
        return list;
    }

    public DscEntityPropSafeClass getDscEntityPropSafeClassById(Long safeClassId) {
        return dscEntityPropSafeClassMapper.getDscEntityPropSafeClassById(safeClassId);
    }



    /**
     * 批量删除安全分类
     *
     * @param safeClassIds 需要删除的安全分类ID
     * @return 结果
     */
    @Override
    public int deleteDscEntityPropSafeClassByIds(Long[] safeClassIds)
    {
        return dscEntityPropSafeClassMapper.deleteDscEntityPropSafeClassByIds(safeClassIds);
    }

    /**
     * 删除安全分类信息
     *
     * @param safeClassId 安全分类ID
     * @return 结果
     */
    @Override
    public int deleteDscEntityPropSafeClassById(Long safeClassId)
    {
        return dscEntityPropSafeClassMapper.deleteDscEntityPropSafeClassById(safeClassId);
    }
}
