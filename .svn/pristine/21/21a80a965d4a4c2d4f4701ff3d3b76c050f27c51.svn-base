package com.dqms.dsm.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 标准应用部门对象 dsm_standard_tar_dept
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public class DsmStandardTarDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据标准 */
    private Long standardId;

    /** 应用部门 */
    private Long deptId;

    public void setStandardId(Long standardId)
    {
        this.standardId = standardId;
    }

    public Long getStandardId()
    {
        return standardId;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("standardId", getStandardId())
            .append("deptId", getDeptId())
            .toString();
    }
}
