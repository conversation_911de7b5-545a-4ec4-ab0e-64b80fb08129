package com.dqms.dam.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 我的脑图对象 dam_mind
 *
 * <AUTHOR>
 * @date 2021-07-01
 */
public class DamMind extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 脑图Id */
    private Long mindId;

    /** 脑图名称 */
    @Excel(name = "脑图名称")
    private String mindName;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    public void setMindId(Long mindId)
    {
        this.mindId = mindId;
    }

    public Long getMindId()
    {
        return mindId;
    }
    public void setMindName(String mindName)
    {
        this.mindName = mindName;
    }

    public String getMindName()
    {
        return mindName;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("mindId", getMindId())
            .append("mindName", getMindName())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
