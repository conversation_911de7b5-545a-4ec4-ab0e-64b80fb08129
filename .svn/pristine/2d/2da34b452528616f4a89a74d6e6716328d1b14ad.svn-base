package com.dqms.dic.mapper;

import java.util.List;
import com.dqms.dic.domain.DicManualDataInstall;

/**
 * 模板配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-06-28
 */
public interface DicManualDataInstallMapper 
{
    /**
     * 查询模板配置
     * 
     * @param manualDataInstallId 模板配置ID
     * @return 模板配置
     */
    public DicManualDataInstall selectDicManualDataInstallById(Long manualDataInstallId);

    /**
     * 查询模板配置列表
     * 
     * @param dicManualDataInstall 模板配置
     * @return 模板配置集合
     */
    public List<DicManualDataInstall> selectDicManualDataInstallList(DicManualDataInstall dicManualDataInstall);

    /**
     * 新增模板配置
     * 
     * @param dicManualDataInstall 模板配置
     * @return 结果
     */
    public int insertDicManualDataInstall(DicManualDataInstall dicManualDataInstall);

    /**
     * 修改模板配置
     * 
     * @param dicManualDataInstall 模板配置
     * @return 结果
     */
    public int updateDicManualDataInstall(DicManualDataInstall dicManualDataInstall);

    /**
     * 删除模板配置
     * 
     * @param manualDataInstallId 模板配置ID
     * @return 结果
     */
    public int deleteDicManualDataInstallById(Long manualDataInstallId);
    public int deleteDicManualDataInstallByDefineId(Long manualDataId);
    /**
     * 批量删除模板配置
     * 
     * @param manualDataInstallIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDicManualDataInstallByIds(Long[] manualDataInstallIds);
}
