package com.wisdom.sms.net.http;

public class SmsServiceProxy implements SmsService {
  private String _endpoint = null;
  private SmsService smsService = null;
  
  public SmsServiceProxy() {
    _initSmsServiceProxy();
  }
  
  public SmsServiceProxy(String endpoint) {
    _endpoint = endpoint;
    _initSmsServiceProxy();
  }
  
  private void _initSmsServiceProxy() {
    try {
      smsService = (new SmsServiceServiceLocator()).getSmsService();
      if (smsService != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)smsService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)smsService)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (smsService != null)
      ((javax.xml.rpc.Stub)smsService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public SmsService getSmsService() {
    if (smsService == null)
      _initSmsServiceProxy();
    return smsService;
  }
  
  public Response sendSMS(String userName, String password, com.wisdom.sms.net.messages.SmsMessages msgs) throws java.rmi.RemoteException{
    if (smsService == null)
      _initSmsServiceProxy();
    return smsService.sendSMS(userName, password, msgs);
  }
  
  
}