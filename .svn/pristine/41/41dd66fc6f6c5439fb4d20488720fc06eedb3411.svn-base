<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.basic.mapper.SysAgentMapper">
    
    <resultMap type="SysAgent" id="SysAgentResult">
        <result property="agentId"    column="agent_id"    />
        <result property="agentCode"    column="agent_code"    />
        <result property="agentName"    column="agent_name"    />
        <result property="serverType"    column="server_type"    />
        <result property="serverIp"    column="server_ip"    />
        <result property="serverPort"    column="server_port"    />
        <result property="serverUser"    column="server_user"    />
        <result property="serverPass"    column="server_pass"    />
        <result property="agentAdd"    column="agent_add"    />
        <result property="agentPort"    column="agent_port"    />
        <result property="threadNum"    column="thread_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectSysAgentVo">
        select agent_id, agent_code, agent_name, server_type, server_ip, server_port, server_user, server_pass, agent_add, agent_port, thread_num, create_by, update_by, create_id, update_id, create_time, update_time,'9' as status from sys_agent
    </sql>

    <select id="selectSysAgentList" parameterType="SysAgent" resultMap="SysAgentResult">
        <include refid="selectSysAgentVo"/>
        <where>  
            <if test="agentCode != null  and agentCode != ''"> and agent_code like concat('%', #{agentCode}, '%')</if>
            <if test="agentName != null  and agentName != ''"> and agent_name like concat('%', #{agentName}, '%')</if>
            <if test="serverType != null  and serverType != ''"> and server_type = #{serverType}</if>
            <if test="serverIp != null  and serverIp != ''"> and server_ip = #{serverIp}</if>
            <if test="serverPort != null  and serverPort != ''"> and server_port = #{serverPort}</if>
            <if test="serverUser != null  and serverUser != ''"> and server_user = #{serverUser}</if>
            <if test="serverPass != null  and serverPass != ''"> and server_pass = #{serverPass}</if>
            <if test="agentAdd != null  and agentAdd != ''"> and agent_add = #{agentAdd}</if>
            <if test="agentPort != null  and agentPort != ''"> and agent_port = #{agentPort}</if>
            <if test="threadNum != null "> and thread_num = #{threadNum}</if>
        </where>
    </select>
    
    <select id="selectSysAgentById" parameterType="Long" resultMap="SysAgentResult">
        <include refid="selectSysAgentVo"/>
        where agent_id = #{agentId}
    </select>
    
    <select id="selectSysAgentByName" parameterType="String" resultMap="SysAgentResult">
        <include refid="selectSysAgentVo"/>
        where agent_name = #{agentName}
    </select>
        
    <insert id="insertSysAgent" parameterType="SysAgent" useGeneratedKeys="true" keyProperty="agentId">
        insert into sys_agent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentCode != null and agentCode != ''">agent_code,</if>
            <if test="agentName != null and agentName != ''">agent_name,</if>
            <if test="serverType != null and serverType != ''">server_type,</if>
            <if test="serverIp != null and serverIp != ''">server_ip,</if>
            <if test="serverPort != null and serverPort != ''">server_port,</if>
            <if test="serverUser != null">server_user,</if>
            <if test="serverPass != null">server_pass,</if>
            <if test="agentAdd != null and agentAdd != ''">agent_add,</if>
            <if test="agentPort != null">agent_port,</if>
            <if test="threadNum != null">thread_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentCode != null and agentCode != ''">#{agentCode},</if>
            <if test="agentName != null and agentName != ''">#{agentName},</if>
            <if test="serverType != null and serverType != ''">#{serverType},</if>
            <if test="serverIp != null and serverIp != ''">#{serverIp},</if>
            <if test="serverPort != null and serverPort != ''">#{serverPort},</if>
            <if test="serverUser != null">#{serverUser},</if>
            <if test="serverPass != null">#{serverPass},</if>
            <if test="agentAdd != null and agentAdd != ''">#{agentAdd},</if>
            <if test="agentPort != null">#{agentPort},</if>
            <if test="threadNum != null">#{threadNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysAgent" parameterType="SysAgent">
        update sys_agent
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentCode != null and agentCode != ''">agent_code = #{agentCode},</if>
            <if test="agentName != null and agentName != ''">agent_name = #{agentName},</if>
            <if test="serverType != null and serverType != ''">server_type = #{serverType},</if>
            <if test="serverIp != null and serverIp != ''">server_ip = #{serverIp},</if>
            <if test="serverPort != null and serverPort != ''">server_port = #{serverPort},</if>
            <if test="serverUser != null">server_user = #{serverUser},</if>
            <if test="serverPass != null">server_pass = #{serverPass},</if>
            <if test="agentAdd != null and agentAdd != ''">agent_add = #{agentAdd},</if>
            <if test="agentPort != null">agent_port = #{agentPort},</if>
            <if test="threadNum != null">thread_num = #{threadNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where agent_id = #{agentId}
    </update>

    <delete id="deleteSysAgentById" parameterType="Long">
        delete from sys_agent where agent_id = #{agentId}
    </delete>

    <delete id="deleteSysAgentByIds" parameterType="String">
        delete from sys_agent where agent_id in 
        <foreach item="agentId" collection="array" open="(" separator="," close=")">
            #{agentId}
        </foreach>
    </delete>

    <select id="selectSysAgentByNameOrCode" parameterType="SysAgent" resultMap="SysAgentResult">
       select agent_id, agent_code, agent_name, server_type, server_ip, server_port, server_user, server_pass, agent_add, agent_port, thread_num, create_by, update_by, create_id, update_id, create_time, update_time,'9' as status from sys_agent
        where agent_name = #{agentName} or agent_code = #{agentCode}
    </select>

    <select id="listSysAgentByName" parameterType="SysAgent" resultMap="SysAgentResult">
       select agent_id, agent_code, agent_name, server_type, server_ip, server_port, server_user, server_pass, agent_add, agent_port, thread_num, create_by, update_by, create_id, update_id, create_time, update_time,'9' as status from sys_agent
        where agent_name = #{agentName}
    </select>
    <select id="selectSysAgentByCode" parameterType="SysAgent" resultMap="SysAgentResult">
       select agent_id, agent_code, agent_name, server_type, server_ip, server_port, server_user, server_pass, agent_add, agent_port, thread_num, create_by, update_by, create_id, update_id, create_time, update_time,'9' as status from sys_agent
        where agent_code = #{agentCode}
    </select>

</mapper>