package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmDimension;
import com.dqms.dsm.domain.vo.DsmDimensionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 维度字典Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface DsmDimensionMapper 
{
    /**
     * 查询维度字典
     * 
     * @param dimensionId 维度字典ID
     * @return 维度字典
     */
    public DsmDimension selectDsmDimensionById(Long dimensionId);
    public List<DsmDimension> selectDsmDimensionByIds(Long[] dimensionIds);




    /**
     * 查询维度字典列表
     * 
     * @param dsmDimension 维度字典
     * @return 维度字典集合
     */
    public List<DsmDimension> selectDsmDimensionList(DsmDimension dsmDimension);
    public List<DsmDimension> forDetail(DsmDimension dsmDimension);


    public List<DsmDimensionVo> selectDsmDimensionVoList(DsmDimensionVo dsmDimensionVo);

    /**
     * 新增维度字典
     * 
     * @param dsmDimension 维度字典
     * @return 结果
     */
    public int insertDsmDimension(DsmDimension dsmDimension);

    /**
     * 修改维度字典
     * 
     * @param dsmDimension 维度字典
     * @return 结果
     */
    public int updateDsmDimension(DsmDimension dsmDimension);
    public int updateDsmDimensionStatus(DsmDimension dsmDimension);




    /**
     * 删除维度字典
     * 
     * @param dimensionId 维度字典ID
     * @return 结果
     */
    public int deleteDsmDimensionById(Long dimensionId);

    /**
     * 批量删除维度字典
     * 
     * @param dimensionIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmDimensionByIds(Long[] dimensionIds);

    public List<DsmDimension> selectDsmDimensionByCodeOrName(DsmDimension dsmDimension);
}
