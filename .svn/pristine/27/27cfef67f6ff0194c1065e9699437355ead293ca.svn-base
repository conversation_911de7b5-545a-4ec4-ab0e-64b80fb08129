package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmQuotaDeptRel;

/**
 * 指标与机构Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-03
 */
public interface DsmQuotaDeptRelMapper 
{
    /**
     * 查询指标与机构
     * 
     * @param quotaId 指标与机构ID
     * @return 指标与机构
     */
    public DsmQuotaDeptRel selectDsmQuotaDeptRelById(Long quotaId);

    /**
     * 查询指标与机构列表
     * 
     * @param dsmQuotaDeptRel 指标与机构
     * @return 指标与机构集合
     */
    public List<DsmQuotaDeptRel> selectDsmQuotaDeptRelList(DsmQuotaDeptRel dsmQuotaDeptRel);

    /**
     * 新增指标与机构
     * 
     * @param dsmQuotaDeptRel 指标与机构
     * @return 结果
     */
    public int insertDsmQuotaDeptRel(DsmQuotaDeptRel dsmQuotaDeptRel);

    /**
     * 修改指标与机构
     * 
     * @param dsmQuotaDeptRel 指标与机构
     * @return 结果
     */
    public int updateDsmQuotaDeptRel(DsmQuotaDeptRel dsmQuotaDeptRel);

    /**
     * 删除指标与机构
     * 
     * @param quotaId 指标与机构ID
     * @return 结果
     */
    public int deleteDsmQuotaDeptRelById(Long quotaId);

    /**
     * 批量删除指标与机构
     * 
     * @param quotaIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmQuotaDeptRelByIds(Long[] quotaIds);
}
