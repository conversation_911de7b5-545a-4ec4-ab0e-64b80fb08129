package com.dqms.dsm.domain;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

/**
 * 指标分类对象 dsm_index_class
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
public class DsmIndexClass extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指标分类ID */
    private Long indexClassId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String indexClassName;

    /** 全路径 */
    @Excel(name = "全路径")
    private String indexClassNameFull;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    /** 父菜单名称 */
    private String parentName;

    /** 父菜单ID */
    private Long parentId;

    /** 显示顺序 */
    private Integer orderNum;

    /** 祖级列表 */
    private String ancestors;

    /** 子部门 */
    private List<DsmIndexClass> children = new ArrayList<DsmIndexClass>();

    public void setIndexClassId(Long indexClassId)
    {
        this.indexClassId = indexClassId;
    }

    public Long getIndexClassId()
    {
        return indexClassId;
    }
    public void setIndexClassName(String indexClassName)
    {
        this.indexClassName = indexClassName;
    }

    public String getIndexClassName()
    {
        return indexClassName;
    }
    public void setIndexClassNameFull(String indexClassNameFull)
    {
        this.indexClassNameFull = indexClassNameFull;
    }

    public String getIndexClassNameFull()
    {
        return indexClassNameFull;
    }

    public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public String getAncestors() {
		return ancestors;
	}

	public void setAncestors(String ancestors) {
		this.ancestors = ancestors;
	}

	public List<DsmIndexClass> getChildren() {
		return children;
	}

	public void setChildren(List<DsmIndexClass> children) {
		this.children = children;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("indexClassId", getIndexClassId())
            .append("parentId", getParentId())
            .append("ancestors", getAncestors())
            .append("indexClassName", getIndexClassName())
            .append("indexClassNameFull", getIndexClassNameFull())
            .append("orderNum", getOrderNum())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
