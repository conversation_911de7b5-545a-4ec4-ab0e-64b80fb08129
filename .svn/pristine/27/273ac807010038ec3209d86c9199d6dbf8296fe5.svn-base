package com.dqms.common.enums;

public enum TableInfo {

    PROCEDURE_NAME("procedureName","procedureName"),
    PROCEDURE_CAT("procedureCat","procedureCat"),
    PROCEDURE_SCHEMA("procedureSchem","procedureSchem"),
    PROCEDURE_REMARKS("procedureRemarks","procedureRemarks"),
    PROCEDURE_TYPE("procedureType","procedureType"),

    TABLE_NAME("tableName", "tableName"),
    TABLE_SCHEMA("schema", "schema"),
    TABLE_REMARK("remark", "remark"),
    TABLE_TYPE("tableType", "tableType"),

    COLUMN_CODE("code","code"),
    COLUMN_NAME("name","name"),
    COLUMN_REMARK("remark","remark"),
    COLUMN_DATA_TYPE("dataType","dataType"),
    COLUMN_DATA_TYPE_DB("dataTypeDB","dataTypeDB"),
    COLUMN_SIZE("columnSize","columnSize"),
    DECIMAL_DIGITS("decimalDigits","decimalDigits"),
    COLUMN_NULLABLE("nullable","nullable"),
    COLUMN_IS_PRIMARY_KEY("isPrimaryKey","isPrimaryKey"),
    COLUMN_DEFAULT_VALUE("defaultValue","defaultValue");


    private final String code;
    private final String info;

    TableInfo(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
