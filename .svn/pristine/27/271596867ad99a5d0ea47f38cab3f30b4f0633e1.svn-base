package com.dqms.api.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 接口输出对象 api_define_column
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
public class ApiDefineColumn extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 输出ID */
    private Long defineColumnId;

    /** 接口ID */
    @Excel(name = "接口ID")
    private Long defineId;

    /** 输出名称 */
    @Excel(name = "输出名称")
    private String defineColumnName;

    /** 是否必填 */
    @Excel(name = "是否必填")
    private String isMust;

    /** 输出类型 */
    @Excel(name = "输出类型")
    private String defineColumnType;

    /** 是否脱敏 */
    @Excel(name = "是否脱敏")
    private String isDesensitization;

    /** 上级输出 */
    @Excel(name = "上级输出")
    private Long parentId;

    public void setDefineColumnId(Long defineColumnId)
    {
        this.defineColumnId = defineColumnId;
    }

    public Long getDefineColumnId()
    {
        return defineColumnId;
    }
    public void setDefineId(Long defineId)
    {
        this.defineId = defineId;
    }

    public Long getDefineId()
    {
        return defineId;
    }
    public void setDefineColumnName(String defineColumnName)
    {
        this.defineColumnName = defineColumnName;
    }

    public String getDefineColumnName()
    {
        return defineColumnName;
    }
    public void setIsMust(String isMust)
    {
        this.isMust = isMust;
    }

    public String getIsMust()
    {
        return isMust;
    }
    public void setDefineColumnType(String defineColumnType)
    {
        this.defineColumnType = defineColumnType;
    }

    public String getDefineColumnType()
    {
        return defineColumnType;
    }
    public void setIsDesensitization(String isDesensitization)
    {
        this.isDesensitization = isDesensitization;
    }

    public String getIsDesensitization()
    {
        return isDesensitization;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("defineColumnId", getDefineColumnId())
            .append("defineId", getDefineId())
            .append("defineColumnName", getDefineColumnName())
            .append("remark", getRemark())
            .append("isMust", getIsMust())
            .append("defineColumnType", getDefineColumnType())
            .append("isDesensitization", getIsDesensitization())
            .append("parentId", getParentId())
            .toString();
    }
}
