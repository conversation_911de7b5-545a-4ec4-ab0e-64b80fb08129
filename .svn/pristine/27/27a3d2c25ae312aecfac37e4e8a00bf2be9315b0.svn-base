package com.dqms.needs.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.dqms.needs.domain.NesNeeds;

/**
 * 需求反馈Service接口
 * 
 * <AUTHOR>
 * @date 2021-04-17
 */
public interface INesNeedsService 
{
    /**
     * 查询需求反馈
     * 
     * @param needsId 需求反馈ID
     * @return 需求反馈
     */
    public NesNeeds selectNesNeedsById(Long needsId);

    /**
     * 查询需求反馈列表
     * 
     * @param nesNeeds 需求反馈
     * @return 需求反馈集合
     */
    public List<NesNeeds> selectNesNeedsList(NesNeeds nesNeeds);

    /**
     * 新增需求反馈
     * 
     * @param nesNeeds 需求反馈
     * @return 结果
     */
    public int insertNesNeeds(NesNeeds nesNeeds);

    /**
     * 修改需求反馈
     * 
     * @param nesNeeds 需求反馈
     * @return 结果
     */
    public int updateNesNeeds(NesNeeds nesNeeds);

    /**
     * 批量删除需求反馈
     * 
     * @param needsIds 需要删除的需求反馈ID
     * @return 结果
     */
    public int deleteNesNeedsByIds(Long[] needsIds);

    /**
     * 删除需求反馈信息
     * 
     * @param needsId 需求反馈ID
     * @return 结果
     */
    public int deleteNesNeedsById(Long needsId);
    
    /**
     * 根据需求ID查询部门树信息
     *
     * @param needId 需求ID
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByNeedId(Long needsId);
    
    void singleDown(Long needsId, HttpServletResponse response, HttpServletRequest request);
}
