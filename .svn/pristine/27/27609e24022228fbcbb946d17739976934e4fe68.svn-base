package com.dqms.task.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.management.RuntimeErrorException;

import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;
import com.dqms.dsm.domain.DsmIndex;
import com.dqms.dsm.domain.DsmIndexClass;
import com.dqms.dsm.domain.vo.DsmIndexVo;
import com.dqms.quartz.mapper.SysJobMapper;
import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskCalendarClass;
import com.dqms.task.domain.vo.EtlTaskScheduleVo;
import com.dqms.task.mapper.EtlTaskCalendarClassMapper;
import com.dqms.task.mapper.EtlTaskMapper;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.constant.ScheduleConstants;
import com.dqms.common.core.domain.entity.SysRole;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.exception.job.TaskException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.framework.web.service.TokenService;
import com.dqms.quartz.domain.SysJob;
import com.dqms.quartz.service.ISysJobService;
import com.dqms.system.domain.SysUserTaskGroup;
import com.dqms.system.mapper.SysUserTaskGroupMapper;
import com.dqms.task.domain.EtlTaskSchedule;
import com.dqms.task.enums.EtlConstants;
import com.dqms.task.mapper.EtlTaskScheduleMapper;
import com.dqms.task.service.IEtlTaskScheduleService;

/**
 * 任务调度计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@Service
public class EtlTaskScheduleServiceImpl implements IEtlTaskScheduleService
{
    @Autowired
    private EtlTaskScheduleMapper etlTaskScheduleMapper;
    
    @Autowired
    private ISysJobService jobService;
    
    @Autowired
    private TokenService tokenService;

    @Autowired
    EtlTaskMapper etlTaskMapper;
    
    @Autowired
    EtlTaskCalendarClassMapper etlTaskCalendarClassMapper;
    
    @Autowired
    SysJobMapper sysJobMapper;
    
    @Autowired
    private SysUserTaskGroupMapper userTaskGroupMapper;
    /**
     * 查询任务调度计划
     *
     * @param taskScheduleId 任务调度计划ID
     * @return 任务调度计划
     */
    @Override
    public EtlTaskSchedule selectEtlTaskScheduleById(Long taskScheduleId)
    {
        return etlTaskScheduleMapper.selectEtlTaskScheduleById(taskScheduleId);
    }

    /**
     * 查询任务调度计划列表
     *
     * @param etlTaskSchedule 任务调度计划
     * @return 任务调度计划
     */
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTaskSchedule> selectEtlTaskScheduleList(EtlTaskSchedule etlTaskSchedule)
    {
        return etlTaskScheduleMapper.selectEtlTaskScheduleList(etlTaskSchedule);
    }
    @Override
    @DataScope(systemAlias = "t")
    public List<EtlTaskSchedule> selectEtlTaskScheduleListByPage(EtlTaskSchedule etlTaskSchedule)
    {
    	etlTaskSchedule.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
        return etlTaskScheduleMapper.selectEtlTaskScheduleListByPage(etlTaskSchedule);
    }
    public List<EtlTaskScheduleVo> selectEtlTaskScheduleVoList(EtlTaskSchedule etlTaskSchedule)
    {
        return etlTaskScheduleMapper.selectEtlTaskScheduleVoList(etlTaskSchedule);
    }
    /**
     * 新增任务调度计划
     *
     * @param etlTaskSchedule 任务调度计划
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEtlTaskSchedule(EtlTaskSchedule etlTaskSchedule)
    {

    	//验证用户是否有分组的管理权限
    	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
    	boolean flag=false;
    	for(SysRole role : roles) {
    		if(role.getDataScope().equals("6")) {
    			flag=true;
    		}
    	}
    	if(flag) {
    		EtlTask etlTask = etlTaskMapper.selectEtlTaskById(etlTaskSchedule.getTaskId());
    		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
    		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
    		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
    		if(groups==null||groups.size()==0) {
    			throw new CustomException("用户没有该分组管理权限！");
    		}
    	}
    	
        List<EtlTaskSchedule> ifExist=new ArrayList<>();
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	etlTaskSchedule.setCreateTime(DateUtils.getNowDate());
    	etlTaskSchedule.setCreateId(loginUser.getUser().getUserId());
    	etlTaskSchedule.setCreateBy(loginUser.getUser().getUserName());
    	etlTaskSchedule.setUpdateTime(DateUtils.getNowDate());
    	etlTaskSchedule.setUpdateId(loginUser.getUser().getUserId());
    	etlTaskSchedule.setUpdateBy(loginUser.getUser().getUserName());
        EtlTaskSchedule es=new EtlTaskSchedule();
        es.setScheduleName(etlTaskSchedule.getScheduleName());
        ifExist=etlTaskScheduleMapper.selectEtlTaskScheduleList(es);
    	if(ifExist.size()==0) {
            etlTaskScheduleMapper.insertEtlTaskSchedule(etlTaskSchedule);

        }
        else{
            throw new RuntimeErrorException(null, "调度名称已存在");
        }

        SysJob job = new SysJob();
        job.setJobName(etlTaskSchedule.getScheduleName());
        job.setJobGroup("DEFAULT");
        job.setInvokeTarget("taskJob.run("+etlTaskSchedule.getTaskScheduleId()+"L)");
        job.setCronExpression(etlTaskSchedule.getExpression());
        job.setConcurrent(ScheduleConstants.MISFIRE_DEFAULT);
        job.setStatus(etlTaskSchedule.getStatus());
        try {
			jobService.insertJobBySchedule(job);
			etlTaskSchedule.setJobId(job.getJobId());
		} catch (SchedulerException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeErrorException(null, "调度操作失败："+e.getMessage());
		} catch (TaskException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeErrorException(null, "调度操作失败："+e.getMessage());
		}
        
        etlTaskSchedule.setJobId(job.getJobId());
        etlTaskScheduleMapper.updateEtlTaskSchedule(etlTaskSchedule);
        return 1;
    }

    /**
     * 修改任务调度计划
     *
     * @param etlTaskSchedule 任务调度计划
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEtlTaskSchedule(EtlTaskSchedule etlTaskSchedule)
    {
    	//验证用户是否有分组的管理权限
    	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
    	boolean flag=false;
    	for(SysRole role : roles) {
    		if(role.getDataScope().equals("6")) {
    			flag=true;
    		}
    	}
    	if(flag) {
    		EtlTask etlTask = etlTaskMapper.selectEtlTaskById(etlTaskSchedule.getTaskId());
    		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
    		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
    		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
    		if(groups==null||groups.size()==0) {
    			throw new CustomException("用户没有该分组管理权限！");
    		}
    	}
    	
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	etlTaskSchedule.setUpdateTime(DateUtils.getNowDate());
    	etlTaskSchedule.setUpdateId(loginUser.getUser().getUserId());
    	etlTaskSchedule.setUpdateBy(loginUser.getUser().getUserName());
        SysJob job = new SysJob();
        job.setJobName(etlTaskSchedule.getScheduleName());
        job.setCronExpression(etlTaskSchedule.getExpression());
        job.setConcurrent(ScheduleConstants.MISFIRE_DEFAULT);
        job.setStatus(etlTaskSchedule.getStatus());
        job.setJobId(etlTaskSchedule.getJobId());
        job.setJobGroup("DEFAULT");
        job.setInvokeTarget("taskJob.run("+etlTaskSchedule.getTaskScheduleId()+"L)");
        try {
			jobService.updateJob(job);
		} catch (SchedulerException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeErrorException(null, "调度操作失败："+e.getMessage());
		} catch (TaskException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeErrorException(null, "调度操作失败："+e.getMessage());
		}
        etlTaskScheduleMapper.updateEtlTaskSchedule(etlTaskSchedule);
        return 1;
    }
    
    /**
     * 修改任务调度计划状态
     *
     * @param etlTaskSchedule 任务调度计划
     * @return 结果
     */
    @Override
    public int updateEtlTaskScheduleStutas(EtlTaskSchedule etlTaskSchedule)
    {
        etlTaskSchedule.setUpdateTime(DateUtils.getNowDate());
        return etlTaskScheduleMapper.updateEtlTaskSchedule(etlTaskSchedule);
    }

    /**
     * 批量删除任务调度计划
     *
     * @param taskScheduleIds 需要删除的任务调度计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEtlTaskScheduleByIds(Long[] taskScheduleIds)
    {
    	
    	int i=0;
    	List<Long> list = new ArrayList<Long>();
    	for(Long taskScheduleId : taskScheduleIds) {
    		EtlTaskSchedule taskSchedule = etlTaskScheduleMapper.selectEtlTaskScheduleById(taskScheduleId);
        	//验证用户是否有分组的管理权限
        	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        	boolean flag=false;
        	for(SysRole role : roles) {
        		if(role.getDataScope().equals("6")) {
        			flag=true;
        		}
        	}
        	if(flag) {
        		EtlTask etlTask = etlTaskMapper.selectEtlTaskById(taskSchedule.getTaskId());
        		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
        		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
        		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
        		if(groups==null||groups.size()==0) {
        			throw new CustomException("用户没有该分组管理权限！");
        		}
        	}
    		if(taskSchedule!=null&&taskSchedule.getJobId()!=null) {
    			list.add(taskSchedule.getJobId());
    		}
    	}
    	
    	if(list.size()>0) {
    		Long[] jobIds= new Long[list.size()];
    		for(Long jobId : list) {
    			jobIds[i++]=jobId;
    		}
    		try {
    			jobService.deleteJobByIds(jobIds);
    		} catch (SchedulerException e) {
    			// TODO Auto-generated catch block
    			e.printStackTrace();
    			throw new RuntimeErrorException(null, "调度操作失败："+e.getMessage());
    		}
    	}
    	
        return etlTaskScheduleMapper.deleteEtlTaskScheduleByIds(taskScheduleIds);
    }

    /**
     * 删除任务调度计划信息
     *
     * @param taskScheduleId 任务调度计划ID
     * @return 结果
     */
    @Override
    public int deleteEtlTaskScheduleById(Long taskScheduleId)
    {

        EtlTaskSchedule taskSchedule = etlTaskScheduleMapper.selectEtlTaskScheduleById(taskScheduleId);
    	//验证用户是否有分组的管理权限
    	List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
    	boolean flag=false;
    	for(SysRole role : roles) {
    		if(role.getDataScope().equals("6")) {
    			flag=true;
    		}
    	}
    	if(flag) {
    		EtlTask etlTask = etlTaskMapper.selectEtlTaskById(taskSchedule.getTaskId());
    		SysUserTaskGroup sysUserTaskGroup = new SysUserTaskGroup();
    		sysUserTaskGroup.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    		sysUserTaskGroup.setTaskGroupId(etlTask.getTaskGroupId());
    		List<SysUserTaskGroup> groups = userTaskGroupMapper.selectSysUserTaskGroupList(sysUserTaskGroup);
    		if(groups==null||groups.size()==0) {
    			throw new CustomException("用户没有该分组管理权限！");
    		}
    	}
        
        if(taskSchedule!=null&&taskSchedule.getJobId()!=null) {
            sysJobMapper.deleteJobById(taskSchedule.getJobId());
        }

        return etlTaskScheduleMapper.deleteEtlTaskScheduleById(taskScheduleId);
    }
    
    /**
     * 执行任务调度计划
     * 
     * @param etlTaskSchedule 任务调度计划
     * @return 结果
     */
    @Override
    @Transactional
    public int run(EtlTaskSchedule etlTaskSchedule)
    {
        SysJob job = new SysJob();
        job.setJobGroup("DEFAULT");
        job.setJobId(etlTaskSchedule.getJobId());
		try {
			jobService.run(job);
		} catch (SchedulerException e) {
			e.printStackTrace();
		};
        return 1;
    }

    @Override
    @Transactional
    public String importetlTaskSchedule(List<EtlTaskScheduleVo> etlTaskScheduleVoList, boolean updateSupport) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (StringUtils.isNull(etlTaskScheduleVoList) || etlTaskScheduleVoList.size() == 0){
            throw new CustomException("导入任务数据不能为空！");
            }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (EtlTaskScheduleVo vo : etlTaskScheduleVoList){
        try {
            EtlTaskSchedule t = new EtlTaskSchedule();
            BeanUtils.copyBeanProp(t, vo);
            if(StringUtils.isNull(vo.getTaskName())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getTaskName() + " 关联任务为空");
                continue;
            }
            if(StringUtils.isNull(vo.getScheduleName())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划" + vo.getScheduleName() + " 调度名称为空");
                continue;
            }
            if(StringUtils.isNull(vo.getExpression())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getExpression() + " 表达式为空");
                continue;
            }
            if(StringUtils.isNull(vo.getStatus())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getStatus() + " 状态为空");
                continue;
            }
            if(StringUtils.isNull(vo.getIsLine())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getIsLine() + " 后置任务链为空");
                continue;
            }
            if(StringUtils.isNull(vo.getIsGroup())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getIsGroup() + " 后置任务组为空");
                continue;
            }
            if(StringUtils.isNull(vo.getIsConcurrent())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getIsConcurrent() + " 是否并发为空");
                continue;
            }
            if(StringUtils.isNull(vo.getExecType())){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getExecType() + " 执行方式为空");
                continue;
            }
            if(!StringUtils.isNull(vo.getPreScheduleName())&&vo.getPreScheduleName()!=""){
                EtlTaskSchedule es=new EtlTaskSchedule();
                es.setScheduleName(vo.getPreScheduleName());
                List<EtlTaskSchedule> sc=etlTaskScheduleMapper.selectEtlTaskScheduleList(es);
                if(sc!=null&&sc.size()!=0) {
                    t.setPreScheduleId(sc.get(0).getTaskScheduleId());
                }
            }
            if(StringUtils.isNull(vo.getTaskCalendarClassName())&&vo.getTaskCalendarClassName()!=""){
                EtlTaskCalendarClass ecc=new EtlTaskCalendarClass();
                ecc=etlTaskCalendarClassMapper.selectEtlTaskCalendarClassByName(vo.getTaskCalendarClassName());
                if(ecc!=null&&ecc.getTaskCalendarClassName()!=null){
                    t.setTaskCalendarClassId(ecc.getTaskCalendarClassId());
                }
            }
            t.setEtlTask(etlTaskMapper.selectEtlTaskByTaskName(vo.getTaskName()));
            EtlTask et=new EtlTask();
            et=etlTaskMapper.selectEtlTaskByTaskName(vo.getTaskName());
            t.setTaskId(et.getTaskId());
            EtlTaskSchedule forup=new EtlTaskSchedule();
            forup.setScheduleName(vo.getScheduleName());
            List<EtlTaskSchedule> list=new ArrayList<>();
            list=etlTaskScheduleMapper.selectEtlTaskScheduleList(forup);
            if(list.size()==0){
                successNum++;
                this.insertEtlTaskSchedule(t);
            }else{
                if(updateSupport){
                    if(list.size()!=0){
                        t.setTaskScheduleId(list.get(0).getTaskScheduleId());
                        t.setJobId(list.get(0).getJobId());
                        successNum++;
                        this.updateEtlTaskSchedule(t);
                    }else{
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getScheduleName() + " 不存在");
                    }

                }else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、任务调度计划 " + vo.getScheduleName() + " 已存在");
                    continue;
                }
            }
        }catch(Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、任务调度计划 " + vo.getScheduleName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0){
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        }else{
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();

    }
}
