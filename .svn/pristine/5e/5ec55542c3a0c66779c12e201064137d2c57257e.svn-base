package com.dqms.dam.service.impl;

import java.util.List;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dam.mapper.DamAssetsSubscribeMapper;
import com.dqms.dam.domain.DamAssetsSubscribe;
import com.dqms.dam.service.IDamAssetsSubscribeService;
import com.dqms.framework.web.service.TokenService;

/**
 * 资产订阅Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
@Service
public class DamAssetsSubscribeServiceImpl implements IDamAssetsSubscribeService
{
    @Autowired
    private DamAssetsSubscribeMapper damAssetsSubscribeMapper;

    @Autowired
    private TokenService tokenService;
    /**
     * 查询资产订阅
     *
     * @param assetsSubscribeId 资产订阅ID
     * @return 资产订阅
     */
    @Override
    public DamAssetsSubscribe selectDamAssetsSubscribeById(Long assetsSubscribeId)
    {
        return damAssetsSubscribeMapper.selectDamAssetsSubscribeById(assetsSubscribeId);
    }

    /**
     * 查询资产订阅列表
     *
     * @param damAssetsSubscribe 资产订阅
     * @return 资产订阅
     */
    @Override
    public List<DamAssetsSubscribe> selectDamAssetsSubscribeList(DamAssetsSubscribe damAssetsSubscribe)
    {
        return damAssetsSubscribeMapper.selectDamAssetsSubscribeList(damAssetsSubscribe);
    }

    /**
     * 新增资产订阅
     *
     * @param damAssetsSubscribe 资产订阅
     * @return 结果
     */
    @Override
    public int insertDamAssetsSubscribe(DamAssetsSubscribe damAssetsSubscribe)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	damAssetsSubscribe.setCreateTime(DateUtils.getNowDate());
    	damAssetsSubscribe.setCreateId(loginUser.getUser().getUserId());
    	damAssetsSubscribe.setCreateBy(loginUser.getUser().getNickName());
        return damAssetsSubscribeMapper.insertDamAssetsSubscribe(damAssetsSubscribe);
    }

    /**
     * 修改资产订阅
     *
     * @param damAssetsSubscribe 资产订阅
     * @return 结果
     */
    @Override
    public int updateDamAssetsSubscribe(DamAssetsSubscribe damAssetsSubscribe)
    {
        return damAssetsSubscribeMapper.updateDamAssetsSubscribe(damAssetsSubscribe);
    }

    /**
     * 批量删除资产订阅
     *
     * @param assetsSubscribeIds 需要删除的资产订阅ID
     * @return 结果
     */
    @Override
    public int deleteDamAssetsSubscribeByIds(Long[] assetsSubscribeIds)
    {
        return damAssetsSubscribeMapper.deleteDamAssetsSubscribeByIds(assetsSubscribeIds);
    }

    /**
     * 删除资产订阅信息
     *
     * @param assetsSubscribeId 资产订阅ID
     * @return 结果
     */
    @Override
    public int deleteDamAssetsSubscribeById(Long assetsSubscribeId)
    {
        return damAssetsSubscribeMapper.deleteDamAssetsSubscribeById(assetsSubscribeId);
    }
}
