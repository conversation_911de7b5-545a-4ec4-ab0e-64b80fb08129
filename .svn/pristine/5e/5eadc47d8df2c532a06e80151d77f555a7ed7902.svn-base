<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dam.mapper.DamAssetsClassRelMapper">
    
    <resultMap type="DamAssetsClassRel" id="DamAssetsClassRelResult">
        <result property="assetsId"    column="assets_id"    />
        <result property="assetsClassId"    column="assets_class_id"    />
    </resultMap>

    <sql id="selectDamAssetsClassRelVo">
        select assets_id, assets_class_id from dam_assets_class_rel
    </sql>

    <select id="selectDamAssetsClassRelList" parameterType="DamAssetsClassRel" resultMap="DamAssetsClassRelResult">
        <include refid="selectDamAssetsClassRelVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectDamAssetsClassRelById" parameterType="Long" resultMap="DamAssetsClassRelResult">
        <include refid="selectDamAssetsClassRelVo"/>
        where assets_id = #{assetsId}
    </select>
        
    <insert id="insertDamAssetsClassRel" parameterType="DamAssetsClassRel">
        insert into dam_assets_class_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetsId != null">assets_id,</if>
            <if test="assetsClassId != null">assets_class_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetsId != null">#{assetsId},</if>
            <if test="assetsClassId != null">#{assetsClassId},</if>
         </trim>
    </insert>

    <update id="updateDamAssetsClassRel" parameterType="DamAssetsClassRel">
        update dam_assets_class_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetsClassId != null">assets_class_id = #{assetsClassId},</if>
        </trim>
        where assets_id = #{assetsId}
    </update>

    <delete id="deleteDamAssetsClassRelById" parameterType="Long">
        delete from dam_assets_class_rel where assets_id = #{assetsId}
    </delete>

    <delete id="deleteDamAssetsClassRelByIds" parameterType="String">
        delete from dam_assets_class_rel where assets_id in 
        <foreach item="assetsId" collection="array" open="(" separator="," close=")">
            #{assetsId}
        </foreach>
    </delete>
    
    <insert id="insertDamAssetsClassRelByDamAssets"  parameterType="DamAssets">
        insert into dam_assets_class_rel (assets_id,assets_class_id) values 
      <foreach collection="assetsClassIds" item="assetsClassId" separator=",">
      	(#{assetsId},#{assetsClassId})
      </foreach>
    </insert>
</mapper>