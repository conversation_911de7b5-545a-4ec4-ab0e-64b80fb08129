<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="接口ID" prop="defineId">
        <el-input
          v-model="queryParams.defineId"
          placeholder="请输入接口ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="输出名称" prop="defineColumnName">
        <el-input
          v-model="queryParams.defineColumnName"
          placeholder="请输入输出名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="是否必填" prop="isMust">
        <el-select v-model="queryParams.isMust" placeholder="请选择是否必填" clearable size="small">
          <el-option
            v-for="dict in isMustOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="输出类型" prop="defineColumnType">
        <el-select v-model="queryParams.defineColumnType" placeholder="请选择输出类型" clearable size="small">
          <el-option
            v-for="dict in defineColumnTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否脱敏" prop="isDesensitization">
        <el-select v-model="queryParams.isDesensitization" placeholder="请选择是否脱敏" clearable size="small">
          <el-option
            v-for="dict in isDesensitizationOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上级输出" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入上级输出"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['api:apiDefineColumn:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['api:apiDefineColumn:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['api:apiDefineColumn:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['api:apiDefineColumn:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :refreshShow="true" :searchShow="true"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="apiDefineColumnList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="输出ID" align="center" prop="defineColumnId" />
      <el-table-column label="接口ID" align="center" prop="defineId" />
      <el-table-column label="输出名称" align="center" prop="defineColumnName" />
      <el-table-column label="输出说明" align="center" prop="remark" />
      <el-table-column label="是否必填" align="center" prop="isMust" :formatter="isMustFormat" />
      <el-table-column label="输出类型" align="center" prop="defineColumnType" :formatter="defineColumnTypeFormat" />
      <el-table-column label="是否脱敏" align="center" prop="isDesensitization" :formatter="isDesensitizationFormat" />
      <el-table-column label="上级输出" align="center" prop="parentId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['api:apiDefineColumn:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['api:apiDefineColumn:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改接口输出对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="接口ID" prop="defineId">
          <el-input v-model="form.defineId" placeholder="请输入接口ID" clearable/>
        </el-form-item>
        <el-form-item label="输出名称" prop="defineColumnName">
          <el-input v-model="form.defineColumnName" placeholder="请输入输出名称" clearable/>
        </el-form-item>
        <el-form-item label="输出说明" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入输出说明" clearable/>
        </el-form-item>
        <el-form-item label="是否必填" prop="isMust">
          <el-select v-model="form.isMust" placeholder="请选择是否必填" clearable>
            <el-option
              v-for="dict in isMustOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="输出类型" prop="defineColumnType">
          <el-select v-model="form.defineColumnType" placeholder="请选择输出类型" clearable>
            <el-option
              v-for="dict in defineColumnTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否脱敏" prop="isDesensitization">
          <el-select v-model="form.isDesensitization" placeholder="请选择是否脱敏" clearable>
            <el-option
              v-for="dict in isDesensitizationOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上级输出" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入上级输出" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiDefineColumn, getApiDefineColumn, delApiDefineColumn, addApiDefineColumn, updateApiDefineColumn, exportApiDefineColumn } from "@/api/api/apiDefineColumn";

export default {
  name: "ApiDefineColumn",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 接口输出表格数据
      apiDefineColumnList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否必填字典
      isMustOptions: [],
      // 输出类型字典
      defineColumnTypeOptions: [],
      // 是否脱敏字典
      isDesensitizationOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        defineId: null,
        defineColumnName: null,
        isMust: null,
        defineColumnType: null,
        isDesensitization: null,
        parentId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        defineId: [
          { required: true, message: "接口ID不能为空", trigger: "blur" }
        ],
        defineColumnName: [
          { required: true, message: "输出名称不能为空", trigger: "blur" }
        ],
        isMust: [
          { required: true, message: "是否必填不能为空", trigger: "change" }
        ],
        defineColumnType: [
          { required: true, message: "输出类型不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response => {
      this.isMustOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.defineColumnTypeOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isDesensitizationOptions = response.data;
    });
  },
  methods: {
    /** 查询接口输出列表 */
    getList() {
      this.loading = true;
      listApiDefineColumn(this.queryParams).then(response => {
        this.apiDefineColumnList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 是否必填字典翻译
    isMustFormat(row, column) {
      return this.selectDictLabel(this.isMustOptions, row.isMust);
    },
    // 输出类型字典翻译
    defineColumnTypeFormat(row, column) {
      return this.selectDictLabel(this.defineColumnTypeOptions, row.defineColumnType);
    },
    // 是否脱敏字典翻译
    isDesensitizationFormat(row, column) {
      return this.selectDictLabel(this.isDesensitizationOptions, row.isDesensitization);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        defineColumnId: null,
        defineId: null,
        defineColumnName: null,
        remark: null,
        isMust: null,
        defineColumnType: null,
        isDesensitization: null,
        parentId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.defineColumnId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加接口输出";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const defineColumnId = row.defineColumnId || this.ids
      getApiDefineColumn(defineColumnId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改接口输出";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.defineColumnId != null) {
            updateApiDefineColumn(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApiDefineColumn(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const defineColumnIds = row.defineColumnId || this.ids;
      this.$confirm('是否确认删除接口输出编号为"' + defineColumnIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delApiDefineColumn(defineColumnIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有接口输出数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportApiDefineColumn(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
