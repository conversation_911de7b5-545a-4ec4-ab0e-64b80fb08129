package com.dqms.api.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.api.domain.ApiTableManager;
import com.dqms.api.service.IApiTableManagerService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 数据管控Controller
 *
 * <AUTHOR>
 * @date 2022-07-15
 */
@RestController
@RequestMapping("/api/apiTableManager")
public class ApiTableManagerController extends BaseController
{
    @Autowired
    private IApiTableManagerService apiTableManagerService;

    /**
     * 查询数据管控列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApiTableManager apiTableManager)
    {
        startPage();
        List<ApiTableManager> list = apiTableManagerService.selectApiTableManagerList(apiTableManager);
        return getDataTable(list);
    }

    /**
     * 导出数据管控列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:export')")
    @Log(title = "数据管控", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ApiTableManager apiTableManager)
    {
        List<ApiTableManager> list = apiTableManagerService.selectApiTableManagerList(apiTableManager);
        ExcelUtil<ApiTableManager> util = new ExcelUtil<ApiTableManager>(ApiTableManager.class);
        return util.exportExcel(list, "apiTableManager");
    }

    /**
     * 获取数据管控详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:query')")
    @GetMapping(value = "/{entityId}")
    public AjaxResult getInfo(@PathVariable("entityId") Long entityId)
    {
        return AjaxResult.success(apiTableManagerService.selectApiTableManagerById(entityId));
    }

    /**
     * 新增数据管控
     */
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:add')")
    @Log(title = "数据管控", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApiTableManager apiTableManager)
    {
        return toAjax(apiTableManagerService.insertApiTableManager(apiTableManager));
    }

    /**
     * 修改数据管控
     */
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:edit')")
    @Log(title = "数据管控", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApiTableManager apiTableManager)
    {
        return toAjax(apiTableManagerService.updateApiTableManager(apiTableManager));
    }

    /**
     * 删除数据管控
     */
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:remove')")
    @Log(title = "数据管控", businessType = BusinessType.DELETE)
	@DeleteMapping("/{entityIds}")
    public AjaxResult remove(@PathVariable Long[] entityIds)
    {
        return toAjax(apiTableManagerService.deleteApiTableManagerByIds(entityIds));
    }
    
    @GetMapping("/listSystem/{entityId}")
    public TableDataInfo listSystem(@PathVariable Long entityId)
    {
    	List<ApiTableManager> list = apiTableManagerService.listSystem(entityId);
        return getDataTable(list);
    }
    
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:edit')")
    @Log(title = "数据管控", businessType = BusinessType.UPDATE)
    @PostMapping("/addApiTableSystem")
    public AjaxResult addApiTableSystem(@RequestBody ApiTableManager apiTableManager)
    {
        return toAjax(apiTableManagerService.insertApiTableManager(apiTableManager));
    }
    
    
    @PreAuthorize("@ss.hasPermi('api:apiTableManager:edit')")
    @Log(title = "数据管控", businessType = BusinessType.UPDATE)
    @PostMapping("/delApiTableSystem")
    public AjaxResult delApiTableSystem(@RequestBody ApiTableManager apiTableManager)
    {
        return toAjax(apiTableManagerService.deleteApiTableManager(apiTableManager));
    }
}
