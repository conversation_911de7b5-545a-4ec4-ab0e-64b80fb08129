package com.dqms.utils;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.common.utils.spring.SpringUtils;
import com.dqms.system.service.ISysConfigService;
import com.dqms.task.domain.EtlTaskGroup;
import com.dqms.task.service.IEtlTaskGroupService;


/**
 * ClassName:ThreadPool
 * @version  
 * @since    Ver 1.1
 * @Date	 2021-02-16		下午12:25:32
 * <AUTHOR>
 */
public class ThreadPoolUtils {
	private static final Logger log = LoggerFactory.getLogger(ThreadPoolUtils.class);
	
	private static volatile Map<Long,ThreadPoolExecutor> group = new ConcurrentHashMap<Long, ThreadPoolExecutor>();
	private static volatile Map<String,Thread> threads = new ConcurrentHashMap<String, Thread>();
	
	private ThreadPoolUtils(){}
	
	public static void addTask(Runnable task){
		Long groupId = -1L;
		addTask(groupId,0, task);
	}
	
	public static void addTask(Long groupId,int poolSize,Runnable task){
		if(groupId.equals(-1L)) {
			poolSize = poolSize <= 0 ? Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num")):poolSize;
		}else if(groupId.equals(-2L)) {
			poolSize = poolSize <= 0 ? Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num.api")):poolSize;
		}else if(groupId.equals(-3L)) {
			poolSize = poolSize <= 0 ? Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num.mdm")):poolSize;
		}else if(groupId.equals(-4L)) {
			poolSize = poolSize <= 0 ? Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num.dsm")):poolSize;
		}else if(groupId.equals(-5L)){
			poolSize = poolSize <= 0 ? Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num")):poolSize;
		}
		
		createGroup(groupId,poolSize);
		group.get(groupId).submit(task);
		log.info("groupId:"+task.toString());
	}
	
	public static Future addTaskCallable(Long groupId,int poolSize,Callable task){
		poolSize = poolSize <= 0 ? Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num.api")):poolSize;
		createGroup(groupId,poolSize);
		Future submit = group.get(groupId).submit(task);
		log.info("groupId:"+task.toString());
		return submit;
	}
	
	public static void addGroupTask(Long groupId,Runnable task){
		EtlTaskGroup etlTaskGroup = SpringUtils.getBean(IEtlTaskGroupService.class).selectEtlTaskGroupById(groupId);
		if(etlTaskGroup==null||etlTaskGroup.getThreadNum()<=0) {
			groupId = -1L;
			int poolSize=Integer.parseInt(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("etl.threadpool.init.num"));
			createGroup(groupId,poolSize);
		}else {
			createGroup(groupId,etlTaskGroup.getThreadNum().intValue());
		}
		group.get(groupId).submit(task);
		log.info("groupId:"+task.toString()+",执行线程数："+getActiveCount(groupId));
	}

	private static void createGroup(Long groupId, int poolSize) {
		System.out.println("线程池agentId:"+groupId+" poolSize:"+poolSize);
		if(null == group.get(groupId)){
			group.put(groupId, initExecutor(poolSize));
		}else{
			if(group.get(groupId).getCorePoolSize()!=poolSize){
				resetGroup(groupId,poolSize);
			}
		}
	}
	
	public static void resetGroup(Long groupId,int poolSize){
		log.info("resetGroup groupId:"+groupId);
		if(group.get(groupId)!=null){
			log.info("resetGroup BEFORE ActiveCount:"+group.get(groupId).getCorePoolSize());
			group.remove(groupId);
		}
		if(poolSize>0){
			createGroup(groupId,poolSize);	
			log.info("resetGroup AFTET ActiveCount:"+group.get(groupId).getCorePoolSize());
		}
		
	}
	
	public static Map<Long,ThreadPoolExecutor> getGroup(){
		return group;
	}
	public static long getActiveCount(Long groupId){
		return group.get(groupId).getActiveCount();
	}	
	public static void addThread(String batchId ,Long taskInstanceId,Thread thread){
		threads.put(batchId+"-"+taskInstanceId, thread);
		log.info("threads:"+threads.size());
	}
	public static Thread getThread(String batchId ,Long taskInstanceId){
		return threads.get(batchId+"-"+taskInstanceId);
	}
	public static void delThread(String batchId ,Long taskInstanceId){
		threads.remove(batchId+"-"+taskInstanceId);
		log.info("threads:"+threads.size());
	}
	
	public static ThreadPoolExecutor initExecutor(int corePoolSizeFile){
		ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
			corePoolSizeFile, Integer.MAX_VALUE, 0, TimeUnit.SECONDS,
			new LinkedBlockingDeque<Runnable>(Integer.MAX_VALUE),
			new ThreadPoolExecutor.AbortPolicy());
		return threadPoolExecutor;
	}
}

