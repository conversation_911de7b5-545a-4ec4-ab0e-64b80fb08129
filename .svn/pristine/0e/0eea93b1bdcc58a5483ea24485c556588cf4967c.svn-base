package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmQuota;

/**
 * 指标信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
public interface DsmQuotaMapper 
{
    /**
     * 查询指标信息
     * 
     * @param quotaId 指标信息ID
     * @return 指标信息
     */
    public DsmQuota selectDsmQuotaById(Long quotaId);
    public DsmQuota selectDsmQuotaByCode(DsmQuota dsmQuota);
    public DsmQuota selectDsmQuotaByName(DsmQuota dsmQuota);

    /**
     * 查询指标信息列表
     * 
     * @param dsmQuota 指标信息
     * @return 指标信息集合
     */
    public List<DsmQuota> selectDsmQuotaList(DsmQuota dsmQuota);

    /**
     * 新增指标信息
     * 
     * @param dsmQuota 指标信息
     * @return 结果
     */
    public int insertDsmQuota(DsmQuota dsmQuota);

    /**
     * 修改指标信息
     * 
     * @param dsmQuota 指标信息
     * @return 结果
     */
    public int updateDsmQuota(DsmQuota dsmQuota);

    /**
     * 删除指标信息
     * 
     * @param quotaId 指标信息ID
     * @return 结果
     */
    public int deleteDsmQuotaById(Long quotaId);

    /**
     * 批量删除指标信息
     * 
     * @param quotaIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmQuotaByIds(Long[] quotaIds);
}
