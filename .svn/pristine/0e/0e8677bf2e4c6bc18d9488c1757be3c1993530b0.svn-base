import request from '@/utils/request'

// 查询工作流节点列表
export function listModelNode(query) {
  return request({
    url: '/wfs/modelNode/list',
    method: 'get',
    params: query
  })
}

// 查询工作流节点详细
export function getModelNode(modelNodeId) {
  return request({
    url: '/wfs/modelNode/' + modelNodeId,
    method: 'get'
  })
}

// 新增工作流节点
export function addModelNode(data) {
  return request({
    url: '/wfs/modelNode',
    method: 'post',
    data: data
  })
}

// 修改工作流节点
export function updateModelNode(data) {
  return request({
    url: '/wfs/modelNode',
    method: 'put',
    data: data
  })
}

// 删除工作流节点
export function delModelNode(modelNodeId) {
  return request({
    url: '/wfs/modelNode/' + modelNodeId,
    method: 'delete'
  })
}

// 导出工作流节点
export function exportModelNode(query) {
  return request({
    url: '/wfs/modelNode/export',
    method: 'get',
    params: query
  })
}