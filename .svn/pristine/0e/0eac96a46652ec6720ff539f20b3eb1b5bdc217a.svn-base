package com.dqms.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.api.domain.ApiDefineHis;
import com.dqms.api.domain.vo.ApiDefineHisVo;
import com.dqms.api.service.IApiDefineHisService;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.page.TableDataInfo;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;

/**
 * 接口日志Controller
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@RestController
@RequestMapping("/api/apiDefineHis")
public class ApiDefineHisController extends BaseController
{
    @Autowired
    private IApiDefineHisService apiDefineHisService;

    /**
     * 查询接口日志列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineHis:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApiDefineHis apiDefineHis)
    {
        startPage();
        List<ApiDefineHis> list = apiDefineHisService.selectApiDefineHisList(apiDefineHis);
        return getDataTable(list);
    }

    /**
     * 导出接口日志列表
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineHis:export')")
    @Log(title = "接口日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ApiDefineHis apiDefineHis)
    {
        List<ApiDefineHis> list = apiDefineHisService.selectApiDefineHisList(apiDefineHis);
        ExcelUtil<ApiDefineHis> util = new ExcelUtil<ApiDefineHis>(ApiDefineHis.class);
        return util.exportExcel(list, "apiDefineHis");
    }

    /**
     * 获取接口日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineHis:query')")
    @GetMapping(value = "/{defineHisId}")
    public AjaxResult getInfo(@PathVariable("defineHisId") Long defineHisId)
    {
        return AjaxResult.success(apiDefineHisService.selectApiDefineHisById(defineHisId));
    }

    /**
     * 新增接口日志
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineHis:add')")
    @Log(title = "接口日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApiDefineHis apiDefineHis)
    {
        return toAjax(apiDefineHisService.insertApiDefineHis(apiDefineHis));
    }

    /**
     * 修改接口日志
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineHis:edit')")
    @Log(title = "接口日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApiDefineHis apiDefineHis)
    {
        return toAjax(apiDefineHisService.updateApiDefineHis(apiDefineHis));
    }

    /**
     * 删除接口日志
     */
    @PreAuthorize("@ss.hasPermi('api:apiDefineHis:remove')")
    @Log(title = "接口日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{defineHisIds}")
    public AjaxResult remove(@PathVariable Long[] defineHisIds)
    {
        return toAjax(apiDefineHisService.deleteApiDefineHisByIds(defineHisIds));
    }
    
    @GetMapping("/getRunFb")
    public AjaxResult getRunFb()
    {
        List<ApiDefineHisVo> list = apiDefineHisService.getRunFb();
        return AjaxResult.success(list);
    }
    
    @GetMapping("/getRunQs")
    public AjaxResult getRunQs()
    {
        List<ApiDefineHisVo> list = apiDefineHisService.getRunQs();
        return AjaxResult.success(list);
    }
    
    @GetMapping("/getRunZl")
    public AjaxResult getRunZl()
    {
        List<ApiDefineHisVo> list = apiDefineHisService.getRunZl();
        return AjaxResult.success(list);
    }
    
    @GetMapping("/getRunHs")
    public AjaxResult getRunHs()
    {
        List<ApiDefineHisVo> list = apiDefineHisService.getRunHs();
        return AjaxResult.success(list);
    }
    
    @GetMapping("/getRunYc")
    public TableDataInfo getRunYc(ApiDefineHis apiDefineHis)
    {
    	startPage();
        List<ApiDefineHis> list = apiDefineHisService.getRunYc();
        return getDataTable(list);
    }
}
