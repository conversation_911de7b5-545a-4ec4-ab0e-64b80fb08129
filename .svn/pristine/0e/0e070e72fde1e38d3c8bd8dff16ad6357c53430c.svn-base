package com.dqms.dsm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dsm.domain.DsmStandardClass;
import com.dqms.dsm.service.IDsmStandardClassService;

/**
 * 数据标准分类Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmStandardClass")
public class DsmStandardClassController extends BaseController
{
    @Autowired
    private IDsmStandardClassService dsmStandardClassService;

    /**
     * 查询数据标准分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardClass:list')")
    @GetMapping("/list")
    public AjaxResult list(DsmStandardClass dsmStandardClass)
    {
        List<DsmStandardClass> list = dsmStandardClassService.selectDsmStandardClassList(dsmStandardClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出数据标准分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardClass:export')")
    @Log(title = "数据标准分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmStandardClass dsmStandardClass)
    {
        List<DsmStandardClass> list = dsmStandardClassService.selectDsmStandardClassList(dsmStandardClass);
        ExcelUtil<DsmStandardClass> util = new ExcelUtil<DsmStandardClass>(DsmStandardClass.class);
        return util.exportExcel(list, "dsmStandardClass");
    }

    /**
     * 获取数据标准分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardClass:query')")
    @GetMapping(value = "/{standardClassId}")
    public AjaxResult getInfo(@PathVariable("standardClassId") Long standardClassId)
    {
        return AjaxResult.success(dsmStandardClassService.selectDsmStandardClassById(standardClassId));
    }

    /**
     * 新增数据标准分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardClass:add')")
    @Log(title = "数据标准分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmStandardClass dsmStandardClass)
    {
        return toAjax(dsmStandardClassService.insertDsmStandardClass(dsmStandardClass));
    }

    /**
     * 修改数据标准分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardClass:edit')")
    @Log(title = "数据标准分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmStandardClass dsmStandardClass)
    {
        return toAjax(dsmStandardClassService.updateDsmStandardClass(dsmStandardClass));
    }

    /**
     * 删除数据标准分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardClass:remove')")
    @Log(title = "数据标准分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{standardClassIds}")
    public AjaxResult remove(@PathVariable Long[] standardClassIds)
    {
        return toAjax(dsmStandardClassService.deleteDsmStandardClassByIds(standardClassIds));
    }
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DsmStandardClass dsmStandardClass)
    {
        List<DsmStandardClass> dsmStandardClasss = dsmStandardClassService.selectDsmStandardClassList(dsmStandardClass);
        return AjaxResult.success(dsmStandardClassService.buildDsmStandardClassTreeSelect(dsmStandardClasss));
    }
}
