package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmStandardSrcDept;
import com.dqms.dsm.service.IDsmStandardSrcDeptService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 标准来源部门Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/dsm/dsmStandardSrcDept")
public class DsmStandardSrcDeptController extends BaseController
{
    @Autowired
    private IDsmStandardSrcDeptService dsmStandardSrcDeptService;

    /**
     * 查询标准来源部门列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcDept:list')")
    @GetMapping("/list")
    public TableDataInfo list(DsmStandardSrcDept dsmStandardSrcDept)
    {
        startPage();
        List<DsmStandardSrcDept> list = dsmStandardSrcDeptService.selectDsmStandardSrcDeptList(dsmStandardSrcDept);
        return getDataTable(list);
    }

    /**
     * 导出标准来源部门列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcDept:export')")
    @Log(title = "标准来源部门", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmStandardSrcDept dsmStandardSrcDept)
    {
        List<DsmStandardSrcDept> list = dsmStandardSrcDeptService.selectDsmStandardSrcDeptList(dsmStandardSrcDept);
        ExcelUtil<DsmStandardSrcDept> util = new ExcelUtil<DsmStandardSrcDept>(DsmStandardSrcDept.class);
        return util.exportExcel(list, "dsmStandardSrcDept");
    }

    /**
     * 获取标准来源部门详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcDept:query')")
    @GetMapping(value = "/{standardId}")
    public AjaxResult getInfo(@PathVariable("standardId") Long standardId)
    {
        return AjaxResult.success(dsmStandardSrcDeptService.selectDsmStandardSrcDeptById(standardId));
    }

    /**
     * 新增标准来源部门
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcDept:add')")
    @Log(title = "标准来源部门", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmStandardSrcDept dsmStandardSrcDept)
    {
        return toAjax(dsmStandardSrcDeptService.insertDsmStandardSrcDept(dsmStandardSrcDept));
    }

    /**
     * 修改标准来源部门
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcDept:edit')")
    @Log(title = "标准来源部门", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmStandardSrcDept dsmStandardSrcDept)
    {
        return toAjax(dsmStandardSrcDeptService.updateDsmStandardSrcDept(dsmStandardSrcDept));
    }

    /**
     * 删除标准来源部门
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmStandardSrcDept:remove')")
    @Log(title = "标准来源部门", businessType = BusinessType.DELETE)
	@DeleteMapping("/{standardIds}")
    public AjaxResult remove(@PathVariable Long[] standardIds)
    {
        return toAjax(dsmStandardSrcDeptService.deleteDsmStandardSrcDeptByIds(standardIds));
    }
}
