package com.dqms.dic.domain;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.dsm.domain.DsmDimensionDetail;
import com.dqms.dsm.domain.DsmMasterDataRule;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 模板配置对象 dic_manual_data_install
 *
 * <AUTHOR>
 * @date 2022-06-28
 */
public class DicManualDataInstall extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long manualDataInstallId;

    /** 手工数据 */
    @Excel(name = "手工数据")
    private Long manualDataId;

    /** 字段 */
    @Excel(name = "字段")
    private String propName;

    /** 注释 */
    @Excel(name = "注释")
    private String propComment;

    /** 字段类型 */
    @Excel(name = "字段类型")
    private String propType;

    /** 组件类型 */
    @Excel(name = "组件类型")
    private String type;

    /** 占位提示 */
    @Excel(name = "占位提示")
    private String placeholder;

    /** 组件宽度 */
    @Excel(name = "组件宽度")
    private Integer width;

    /** 默认值 */
    @Excel(name = "默认值")
    private String defaultValue;

    /** 字段长度 */
    @Excel(name = "字段长度")
    private Integer columnSize;

    /** 标签宽度 */
    @Excel(name = "标签宽度")
    private Long labelWidth;

    /** 前缀 */
    @Excel(name = "前缀")
    private String prefix;

    /** 后缀 */
    @Excel(name = "后缀")
    private String postfix;

    /** 可否清空 */
    @Excel(name = "可否清空")
    private String clearable;

    /** 可否为空 */
    @Excel(name = "可否为空")
    private String nullable;

    /** 是否主键 */
    @Excel(name = "是否主键")
    private String isKey;

    /** 是否查询 */
    @Excel(name = "是否查询")
    private String isSearch;

    /** 是否列表 */
    @Excel(name = "是否列表")
    private String isTable;

    /** 模板位置 */
    @Excel(name = "模板位置")
    private String place;

    /** 关联维度 */
    @Excel(name = "关联维度")
    private Long relId;
    private String relName;

    /** 关联表 */
    @Excel(name = "关联表")
    private String tableName;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    private List<DsmMasterDataRule> dsmMasterDataRules;
    private List<DsmDimensionDetail> options;   
    private Long[] rules;

    public void setManualDataInstallId(Long manualDataInstallId)
    {
        this.manualDataInstallId = manualDataInstallId;
    }

    public Long getManualDataInstallId()
    {
        return manualDataInstallId;
    }
    public void setManualDataId(Long manualDataId)
    {
        this.manualDataId = manualDataId;
    }

    public Long getManualDataId()
    {
        return manualDataId;
    }
    public void setPropName(String propName)
    {
        this.propName = propName;
    }

    public String getPropName()
    {
        return propName;
    }
    public void setPropComment(String propComment)
    {
        this.propComment = propComment;
    }

    public String getPropComment()
    {
        return propComment;
    }
    public void setPropType(String propType)
    {
        this.propType = propType;
    }

    public String getPropType()
    {
        return propType;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setPlaceholder(String placeholder)
    {
        this.placeholder = placeholder;
    }

    public String getPlaceholder()
    {
        return placeholder;
    }
    public void setWidth(Integer width)
    {
        this.width = width;
    }

    public Integer getWidth()
    {
        return width;
    }
    public void setDefaultValue(String defaultValue)
    {
        this.defaultValue = defaultValue;
    }

    public String getDefaultValue()
    {
        return defaultValue;
    }
    public void setColumnSize(Integer columnSize)
    {
        this.columnSize = columnSize;
    }

    public Integer getColumnSize()
    {
        return columnSize;
    }
    public void setLabelWidth(Long labelWidth)
    {
        this.labelWidth = labelWidth;
    }

    public Long getLabelWidth()
    {
        return labelWidth;
    }
    public void setPrefix(String prefix)
    {
        this.prefix = prefix;
    }

    public String getPrefix()
    {
        return prefix;
    }
    public void setPostfix(String postfix)
    {
        this.postfix = postfix;
    }

    public String getPostfix()
    {
        return postfix;
    }
    public void setClearable(String clearable)
    {
        this.clearable = clearable;
    }

    public String getClearable()
    {
        return clearable;
    }
    public void setNullable(String nullable)
    {
        this.nullable = nullable;
    }

    public String getNullable()
    {
        return nullable;
    }
    public void setIsKey(String isKey)
    {
        this.isKey = isKey;
    }

    public String getIsKey()
    {
        return isKey;
    }
    public void setIsSearch(String isSearch)
    {
        this.isSearch = isSearch;
    }

    public String getIsSearch()
    {
        return isSearch;
    }
    public void setIsTable(String isTable)
    {
        this.isTable = isTable;
    }

    public String getIsTable()
    {
        return isTable;
    }
    public void setPlace(String place)
    {
        this.place = place;
    }

    public String getPlace()
    {
        return place;
    }
    public void setRelId(Long relId)
    {
        this.relId = relId;
    }

    public Long getRelId()
    {
        return relId;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    public List<DsmDimensionDetail> getOptions() {
		return options;
	}

	public void setOptions(List<DsmDimensionDetail> options) {
		this.options = options;
	}

	public Long[] getRules() {
		return rules;
	}

	public void setRules(Long[] rules) {
		this.rules = rules;
	}

	public List<DsmMasterDataRule> getDsmMasterDataRules() {
		return dsmMasterDataRules;
	}

	public void setDsmMasterDataRules(List<DsmMasterDataRule> dsmMasterDataRules) {
		this.dsmMasterDataRules = dsmMasterDataRules;
	}

	public String getRelName() {
		return relName;
	}

	public void setRelName(String relName) {
		this.relName = relName;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("manualDataInstallId", getManualDataInstallId())
            .append("manualDataId", getManualDataId())
            .append("propName", getPropName())
            .append("propComment", getPropComment())
            .append("propType", getPropType())
            .append("type", getType())
            .append("placeholder", getPlaceholder())
            .append("width", getWidth())
            .append("defaultValue", getDefaultValue())
            .append("columnSize", getColumnSize())
            .append("labelWidth", getLabelWidth())
            .append("prefix", getPrefix())
            .append("postfix", getPostfix())
            .append("clearable", getClearable())
            .append("nullable", getNullable())
            .append("isKey", getIsKey())
            .append("isSearch", getIsSearch())
            .append("isTable", getIsTable())
            .append("place", getPlace())
            .append("relId", getRelId())
            .append("tableName", getTableName())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
