<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="KEY" prop="modelKey">
        <el-input
          v-model="queryParams.modelKey"
          placeholder="请输入KEY"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="名称" prop="modelName">
        <el-input
          v-model="queryParams.modelName"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="SERVICE" prop="businessService">
        <el-input
          v-model="queryParams.businessService"
          placeholder="请输入SERVICE"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="POJO" prop="businessPojo">
        <el-input
          v-model="queryParams.businessPojo"
          placeholder="请输入POJO"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="ID" prop="businessKey">
        <el-input
          v-model="queryParams.businessKey"
          placeholder="请输入ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="LABEL" prop="businessLabel">
        <el-input
          v-model="queryParams.businessLabel"
          placeholder="请输入LABEL"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="LABEL类型" prop="businessLabelType">
        <el-input
          v-model="queryParams.businessLabelType"
          placeholder="请输入LABEL类型"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="审批路径" prop="applyPath">
        <el-input
          v-model="queryParams.applyPath"
          placeholder="请输入审批路径"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="详情路径" prop="detailPath">
        <el-input
          v-model="queryParams.detailPath"
          placeholder="请输入详情路径"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本" prop="version">
        <el-input
          v-model="queryParams.version"
          placeholder="请输入版本"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['wfs:model:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['wfs:model:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['wfs:model:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['wfs:model:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模型ID" align="center" prop="modelId" />
      <el-table-column label="KEY" align="center" prop="modelKey" />
      <el-table-column label="名称" align="center" prop="modelName" />
      <el-table-column label="SERVICE" align="center" prop="businessService" />
      <el-table-column label="POJO" align="center" prop="businessPojo" />
      <el-table-column label="ID" align="center" prop="businessKey" />
      <el-table-column label="LABEL" align="center" prop="businessLabel" />
      <el-table-column label="LABEL类型" align="center" prop="businessLabelType" />
      <el-table-column label="审批路径" align="center" prop="applyPath" />
      <el-table-column label="详情路径" align="center" prop="detailPath" />
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column label="版本" align="center" prop="version" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['wfs:model:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['wfs:model:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工作流程模型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="KEY" prop="modelKey">
          <el-input v-model="form.modelKey" placeholder="请输入KEY" clearable/>
        </el-form-item>
        <el-form-item label="名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入名称" clearable/>
        </el-form-item>
        <el-form-item label="SERVICE" prop="businessService">
          <el-input v-model="form.businessService" placeholder="请输入SERVICE" clearable/>
        </el-form-item>
        <el-form-item label="POJO" prop="businessPojo">
          <el-input v-model="form.businessPojo" placeholder="请输入POJO" clearable/>
        </el-form-item>
        <el-form-item label="ID" prop="businessKey">
          <el-input v-model="form.businessKey" placeholder="请输入ID" clearable/>
        </el-form-item>
        <el-form-item label="LABEL" prop="businessLabel">
          <el-input v-model="form.businessLabel" placeholder="请输入LABEL" clearable/>
        </el-form-item>
        <el-form-item label="LABEL类型" prop="businessLabelType">
          <el-input v-model="form.businessLabelType" placeholder="请输入LABEL类型" clearable/>
        </el-form-item>
        <el-form-item label="审批路径" prop="applyPath">
          <el-input v-model="form.applyPath" placeholder="请输入审批路径" clearable/>
        </el-form-item>
        <el-form-item label="详情路径" prop="detailPath">
          <el-input v-model="form.detailPath" placeholder="请输入详情路径" clearable/>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
            >{{dict.dictLabel}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本" clearable/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listModel, getModel, delModel, addModel, updateModel, exportModel } from "@/api/activiti/model";

export default {
  name: "Model",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作流程模型表格数据
      modelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态字典
      statusOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        modelKey: null,
        modelName: null,
        businessService: null,
        businessPojo: null,
        businessKey: null,
        businessLabel: null,
        businessLabelType: null,
        applyPath: null,
        detailPath: null,
        status: null,
        version: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        modelKey: [
          { required: true, message: "KEY不能为空", trigger: "blur" }
        ],
        modelName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        businessService: [
          { required: true, message: "SERVICE不能为空", trigger: "blur" }
        ],
        businessPojo: [
          { required: true, message: "POJO不能为空", trigger: "blur" }
        ],
        businessKey: [
          { required: true, message: "ID不能为空", trigger: "blur" }
        ],
        businessLabel: [
          { required: true, message: "LABEL不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    /** 查询工作流程模型列表 */
    getList() {
      this.loading = true;
      listModel(this.queryParams).then(response => {
        this.modelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        modelId: null,
        modelKey: null,
        modelName: null,
        businessService: null,
        businessPojo: null,
        businessKey: null,
        businessLabel: null,
        businessLabelType: null,
        applyPath: null,
        detailPath: null,
        status: "0",
        version: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.modelId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工作流程模型";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const modelId = row.modelId || this.ids
      getModel(modelId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工作流程模型";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.modelId != null) {
            updateModel(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addModel(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const modelIds = row.modelId || this.ids;
      this.$confirm('是否确认删除工作流程模型编号为"' + modelIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delModel(modelIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有工作流程模型数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportModel(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
