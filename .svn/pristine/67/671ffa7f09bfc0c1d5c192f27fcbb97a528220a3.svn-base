package com.dqms.mdm.domain.vo;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 数据实体关系对象 mdm_data_entity_ship
 *
 * <AUTHOR>
 * @date 2021-06-10
 */
public class MdmDataEntityShipAnalyse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据实体关系编号 */
    private Long shipId;

    /** 源实体编号 */
    @Excel(name = "源实体编号")
    private String srcEntityId;

    /** 源实体属性编号 */
    @Excel(name = "源实体属性编号")
    private String srcEntityPropId;

    /** 目标实体编号 */
    @Excel(name = "目标实体编号")
    private String tarEntityId;

    /** 目标实体属性编号 */
    @Excel(name = "目标实体属性编号")
    private String tarEntityPropId;

    /** 关系类型 */
    @Excel(name = "关系类型")
    private String shipType;

    /** 转换规则表达式 */
    @Excel(name = "转换规则表达式")
    private String transferRule;

    /** 关系名称 */
    @Excel(name = "关系名称")
    private String shipName;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNo;

    /** 所属源系统 */
    private Long systemId;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;

    private String srcTableName;
    private String tarTableName;
    private String srcPropName;
    private String tarPropName;
    private String srcSystemName;
    private String tarSystemName;
    private Long srcSystemId;
    private Long tarSystemId;

    public Long getSrcSystemId() {
        return srcSystemId;
    }

    public void setSrcSystemId(Long srcSystemId) {
        this.srcSystemId = srcSystemId;
    }

    public Long getTarSystemId() {
        return tarSystemId;
    }

    public void setTarSystemId(Long tarSystemId) {
        this.tarSystemId = tarSystemId;
    }

    public String getSrcTableName() {
        return srcTableName;
    }

    public void setSrcTableName(String srcTableName) {
        this.srcTableName = srcTableName;
    }

    public String getTarTableName() {
        return tarTableName;
    }

    public void setTarTableName(String tarTableName) {
        this.tarTableName = tarTableName;
    }

    public String getSrcPropName() {
        return srcPropName;
    }

    public void setSrcPropName(String srcPropName) {
        this.srcPropName = srcPropName;
    }

    public String getTarPropName() {
        return tarPropName;
    }

    public void setTarPropName(String tarPropName) {
        this.tarPropName = tarPropName;
    }

    public String getSrcSystemName() {
        return srcSystemName;
    }

    public void setSrcSystemName(String srcSystemName) {
        this.srcSystemName = srcSystemName;
    }

    public String getTarSystemName() {
        return tarSystemName;
    }

    public void setTarSystemName(String tarSystemName) {
        this.tarSystemName = tarSystemName;
    }

    public void setShipId(Long shipId)
    {
        this.shipId = shipId;
    }

    public Long getShipId()
    {
        return shipId;
    }
    public void setSrcEntityId(String srcEntityId)
    {
        this.srcEntityId = srcEntityId;
    }

    public String getSrcEntityId()
    {
        return srcEntityId;
    }
    public void setSrcEntityPropId(String srcEntityPropId)
    {
        this.srcEntityPropId = srcEntityPropId;
    }

    public String getSrcEntityPropId()
    {
        return srcEntityPropId;
    }
    public void setTarEntityId(String tarEntityId)
    {
        this.tarEntityId = tarEntityId;
    }

    public String getTarEntityId()
    {
        return tarEntityId;
    }
    public void setTarEntityPropId(String tarEntityPropId)
    {
        this.tarEntityPropId = tarEntityPropId;
    }

    public String getTarEntityPropId()
    {
        return tarEntityPropId;
    }
    public void setShipType(String shipType)
    {
        this.shipType = shipType;
    }

    public String getShipType()
    {
        return shipType;
    }
    public void setTransferRule(String transferRule)
    {
        this.transferRule = transferRule;
    }

    public String getTransferRule()
    {
        return transferRule;
    }
    public void setShipName(String shipName)
    {
        this.shipName = shipName;
    }

    public String getShipName()
    {
        return shipName;
    }
    public void setVersionNo(String versionNo)
    {
        this.versionNo = versionNo;
    }

    public String getVersionNo()
    {
        return versionNo;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("shipId", getShipId())
            .append("srcEntityId", getSrcEntityId())
            .append("srcEntityPropId", getSrcEntityPropId())
            .append("tarEntityId", getTarEntityId())
            .append("tarEntityPropId", getTarEntityPropId())
            .append("shipType", getShipType())
            .append("transferRule", getTransferRule())
            .append("shipName", getShipName())
            .append("versionNo", getVersionNo())
            .append("systemId", getSystemId())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
