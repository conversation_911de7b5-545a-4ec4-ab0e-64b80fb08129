package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmDimensionClass;
import com.dqms.dsm.domain.DsmIndexClass;
import com.dqms.dsm.service.IDsmDimensionClassService;
import com.dqms.common.utils.poi.ExcelUtil;

/**
 * 字典分类Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RestController
@RequestMapping("/dsm/DsmDimensionClass")
public class DsmDimensionClassController extends BaseController
{
    @Autowired
    private IDsmDimensionClassService dsmDimensionClassService;

    /**
     * 查询字典分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:DsmDimensionClass:list')")
    @GetMapping("/list")
    public AjaxResult list(DsmDimensionClass dsmDimensionClass)
    {
        List<DsmDimensionClass> list = dsmDimensionClassService.selectDsmDimensionClassList(dsmDimensionClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出字典分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:DsmDimensionClass:export')")
    @Log(title = "字典分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmDimensionClass dsmDimensionClass)
    {
        List<DsmDimensionClass> list = dsmDimensionClassService.selectDsmDimensionClassList(dsmDimensionClass);
        ExcelUtil<DsmDimensionClass> util = new ExcelUtil<DsmDimensionClass>(DsmDimensionClass.class);
        return util.exportExcel(list, "DsmDimensionClass");
    }

    /**
     * 获取字典分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:DsmDimensionClass:query')")
    @GetMapping(value = "/{dimensionClassId}")
    public AjaxResult getInfo(@PathVariable("dimensionClassId") Long dimensionClassId)
    {
        return AjaxResult.success(dsmDimensionClassService.selectDsmDimensionClassById(dimensionClassId));
    }

    /**
     * 新增字典分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:DsmDimensionClass:add')")
    @Log(title = "字典分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmDimensionClass dsmDimensionClass)
    {
        return toAjax(dsmDimensionClassService.insertDsmDimensionClass(dsmDimensionClass));
    }

    /**
     * 修改字典分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:DsmDimensionClass:edit')")
    @Log(title = "字典分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmDimensionClass dsmDimensionClass)
    {
        return toAjax(dsmDimensionClassService.updateDsmDimensionClass(dsmDimensionClass));
    }

    /**
     * 删除字典分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:DsmDimensionClass:remove')")
    @Log(title = "字典分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dimensionClassIds}")
    public AjaxResult remove(@PathVariable Long[] dimensionClassIds)
    {
        return toAjax(dsmDimensionClassService.deleteDsmDimensionClassByIds(dimensionClassIds));
    }
    
    /**
     * 获取下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DsmDimensionClass dsmDimensionClass)
    {
        List<DsmDimensionClass> dsmDimensionClasss = dsmDimensionClassService.selectDsmDimensionClassList(dsmDimensionClass);
        return AjaxResult.success(dsmDimensionClassService.buildDsmDimensionClassTreeSelect(dsmDimensionClasss));
    }
}
