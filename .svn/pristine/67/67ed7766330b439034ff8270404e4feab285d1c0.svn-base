package com.dqms.basic.service;

import java.util.List;
import com.dqms.basic.domain.SysDatasourceType;
import com.dqms.system.domain.SysPost;

/**
 * 数据库分组Service接口
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
public interface ISysDatasourceTypeService
{
    /**
     * 查询数据库分组
     *
     * @param datasourceTypeId 数据库分组ID
     * @return 数据库分组
     */
    public SysDatasourceType selectSysDatasourceTypeById(Long datasourceTypeId);

    /**
     * 查询所有数据库类型
     *
     * @return 数据库分组集合
     */
    public List<SysDatasourceType> selectSysDatasourceTypeAll();

    /**
     * 查询数据库分组列表
     *
     * @param sysDatasourceType 数据库分组
     * @return 数据库分组集合
     */
    public List<SysDatasourceType> selectSysDatasourceTypeList(SysDatasourceType sysDatasourceType);

    /**
     * 新增数据库分组
     *
     * @param sysDatasourceType 数据库分组
     * @return 结果
     */
    public int insertSysDatasourceType(SysDatasourceType sysDatasourceType);

    /**
     * 修改数据库分组
     *
     * @param sysDatasourceType 数据库分组
     * @return 结果
     */
    public int updateSysDatasourceType(SysDatasourceType sysDatasourceType);

    /**
     * 批量删除数据库分组
     *
     * @param datasourceTypeIds 需要删除的数据库分组ID
     * @return 结果
     */
    public int deleteSysDatasourceTypeByIds(Long[] datasourceTypeIds);

    /**
     * 删除数据库分组信息
     *
     * @param datasourceTypeId 数据库分组ID
     * @return 结果
     */
    public int deleteSysDatasourceTypeById(Long datasourceTypeId);
}
