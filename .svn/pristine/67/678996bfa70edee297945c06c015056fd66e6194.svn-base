import request from '@/utils/request'

// 查询指标管理列表
export function listDsmIndex(query) {
  return request({
    url: '/dsm/dsmIndex/list',
    method: 'get',
    params: query
  })
}

export function selectDsmIndexListForShow(query) {
  return request({
    url: '/dsm/dsmIndex/selectDsmIndexListForShow',
    method: 'get',
    params: query
  })
}
export function listDsmIndexByNo(query) {
  return request({
    url: '/dsm/dsmIndex/listByNo',
    method: 'get',
    params: query
  })
}

// 查询指标管理详细
export function getDsmIndex(indexId) {
  return request({
    url: '/dsm/dsmIndex/' + indexId,
    method: 'get'
  })
}

// 新增指标管理
export function addDsmIndex(data) {
  return request({
    url: '/dsm/dsmIndex',
    method: 'post',
    data: data
  })
}

// 修改指标管理
export function updateDsmIndex(data) {
  return request({
    url: '/dsm/dsmIndex',
    method: 'put',
    data: data
  })
}
export function updateDsmIndexStatus(data) {
  return request({
    url: '/dsm/dsmIndex/updateStatus',
    method: 'post',
    data: data
  })
}
// 删除指标管理
export function delDsmIndex(indexId) {
  return request({
    url: '/dsm/dsmIndex/' + indexId,
    method: 'delete'
  })
}

// 导出指标管理
export function exportDsmIndex(query) {
  return request({
    url: '/dsm/dsmIndex/export',
    method: 'get',
    params: query
  })
}

export function getDsmIndexVo(indexId) {
  return request({
    url: '/dsm/dsmIndex/getVoById/' + indexId,
    method: 'get'
  })
}

// 查询指标管理列表
export function listDsmIndexRel(query) {
  return request({
    url: '/dsm/dsmIndex/listRel',
    method: 'get',
    params: query
  })
}
// 下载指标管理导入模板
export function importTemplate() {
  return request({
    url: '/dsm/dsmIndex/importTemplate',
    method: 'get'
  })
}

// 下载指标标准元数据映射导入模板
export function exportDsmIndexMdmRel() {
  return request({
    url: '/dsm/dsmMdmRel/exportimportDsmIndexMdmRelDimensionIdUn',
    method: 'get'
  })
}
