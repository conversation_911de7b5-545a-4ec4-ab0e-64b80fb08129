package com.dqms.task.job.executor;

import java.rmi.RemoteException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dqms.basic.domain.SysAgent;
import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskInstance;
import com.dqms.task.service.ITaskExecutor;
import com.dqms.web.core.client.DqmsClient;
import com.dqms.web.core.client.bo.CmdResultsParser;
import com.dqms.web.core.client.bo.IDqmsService;
import com.dqms.web.core.client.bo.ParamMap;

public class ETLTaskExecutor implements ITaskExecutor {
	private static final Logger log = LoggerFactory.getLogger(ETLTaskExecutor.class);
	
	public String execute(EtlTaskInstance ti) {
		String errMsg = null;
		boolean success = false;
		EtlTask t = ti.getEtlTask();
		if (t.getTaskName().indexOf("&") != -1) {
			t.setTaskName(t.getTaskName().substring(0, t.getTaskName().indexOf("&")));
		}
		SysAgent a = t.getSysAgent();
		IDqmsService s = DqmsClient.getDqmsServer(a.getServerIp(), a.getAgentPort());
		ParamMap param = new ParamMap();
	    param.put("type",t.getType());
	    param.put("taskName",t.getTaskName());
	    param.put("loadDate",ti.getLoadDate().replaceAll("-", ""));
	    param.put("instanceId",ti.getTaskInstanceId());
		if(t.getScriptPath()!=null) {
			String[] scripts=t.getScriptPath().split(" ");
			param.put("scriptPath",scripts[0]);
			if(scripts.length>1) {
				for(int ii=1;ii<scripts.length;ii++) {
					param.put("param"+ii,scripts[ii]);
				}
			}
		}
		ParamMap res;
		Long failedRedoPeriod = t.getErrorTime();
		boolean notEnd = false;
		
		int times = 0;
		
		do{
			try {
				res = s.execute(param);
			} catch (RemoteException e) {
				throw new RuntimeException(e);
			}		
			
			success = (Boolean) res.get(CmdResultsParser.STATUS_NAME);
			if(!success){
				errMsg = (String) res.get(CmdResultsParser.MSG_NAME);
				log.info("任务【"+t.getTaskName()+"】执行失败，失败重做间隔时间（小于1s不重做）："+failedRedoPeriod+"s，失败次数："+(++times));
				if(failedRedoPeriod > 1){
					notEnd = true;	//如果设置了失败重做间隔时间(s)，则如果未成功，则待指定时间(秒)后继续执行
					int maxTimes = t.getErrorNum();
					if(times >= maxTimes ) break;//超过最大次数，不再重做
					try {
						Thread.sleep(failedRedoPeriod*1000);
					} catch (InterruptedException e) {
						e.printStackTrace();
						break;
					}
				}
			}else{
				notEnd = false; 
				errMsg = null;
			}
		}while(notEnd);		
		return 	errMsg;
	}
}
