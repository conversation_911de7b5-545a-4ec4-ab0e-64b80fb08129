<template>
  <div class="container-fluid" style="margin-top:0px;width:100%;">
    <div
      id="scalingToolBar2"
      :class="{right:right}"
      style="position:absolute;padding-top:10px;cursor:pointer;z-index: 99;width:791px;"
    >
      <div class="logo" v-show="!right">
        <img src="/favicon.ico">
        <span style="font-size:20px;font-weight:bold;margin-left:20px;">血缘引擎</span>
      </div>
      <el-select
        v-model="queryParams.tarEntityId"
        @change="srcChanged"
        placeholder="请选择源表"
        clearable
        filterable
        remote
        reserve-keyword
        size="small"
        style="width:350px;"
        :loading="loading"
        :remote-method="getMdmEntity"
      >
        <el-option
          v-for="item in srcEntityData"
          :key="item.entityId"
          :label="item.tableName"
          :value="item.entityId"
        >
        <span style="float: left">{{ item.tableSchema}}.{{ item.tableName }}</span>
      	<span style="float: right; color: #8492a6; font-size: 13px">{{ item.sysDatasourceName}}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="queryParams.tarEntityPropId"
        placeholder="请选择源表字段"
        clearable
        size="small"
        style="width:350px;margin-left:10px;margin-right:10px;"
        filterable
      >
        <el-option
          v-for="item in srcEntityPropData"
          :key="item.propId"
          :label="item.propName"
          :value="item.propId"
        >
          <span style="float: left">{{ item.propName }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.propComment}}</span>
        </el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getList2()"
        ><i class="el-icon-search"></i>查询</el-button
      >
    </div>
    <div id="containerDiv">
      <div id="container" style="position: relative;"></div>
    </div>
  </div>
</template>

<script>
import {   findAllEntity,findAllEntityProp,findShipAllList,showShipMap} from "@/api/mdm/mdmDataEntityShip";
import { listSystem } from "@/api/basic/datasource";
import G6 from "@antv/g6";
export default {
  name: "mdmDataMap",
  components: {},
  data() {
    return {
      right:false,
      graphA: undefined,
      mdmDataEntityShipList: [],
      srcg6graph: undefined,
      srcnodes: [],
      srcedges: [],
      queryParams: {
        tarEntityIds: [],
        tarEntityPropIds: [],
        tarEntityPropId:'',
        entityId: ""
      },
      srcEntityData: [],
      tarEntityData: [],
      srcEntityPropData: [],
      tarEntityPropData: []
    };
  },
  created() {
    this.getMdmEntity();
  },
  methods: {
    getMdmEntity(query) {
        if (query !== "") {
            this.loading = true;
            setTimeout(() => {
              this.loading = false;
              let mdmDataEntity = { pageNum: 1,pageSize: 20,tableName: query };
              findAllEntity(mdmDataEntity).then(response => {
            	  this.srcEntityData = response.rows;
      	        this.tarEntityData = response.rows;
              });
            }, 200);
         } else {
       	  this.srcEntityData = [];
       	  this.tarEntityData = [];
         }
    },
    srcChanged(value) {
    	this.queryParams.tarEntityPropId=null;
    	if (value != null&&value != "") {
    		let mdmDataEntityProp = { pageNum: 1,pageSize: 500,entityId:value};
	      findAllEntityProp(mdmDataEntityProp).then(response => {
	        this.srcEntityPropData = response.rows;
	      });
    	}else{
    		this.srcEntityPropData =[];
    	}
    },
    tarChanged(value) {
      findAllEntityProp(value).then(response => {
        this.tarEntityPropData = response.data;
      });
    },
    getList2(){
      this.right = true
      this.getList()
    },
    getList() {
      if (this.srcg6graph) {
        this.srcg6graph.destroy();
      }
      if (
        !this.queryParams.tarEntityPropId &&
        !this.queryParams.tarEntityId
      ) {
        return;
      }
      this.srcnodes = [];
      this.srcedges = [];
      this.queryParams.tarEntityPropIds = [];
      if (!!this.queryParams.tarEntityPropId) {
        this.queryParams.tarEntityPropIds.push(
          this.queryParams.tarEntityPropId
        );
      }
      this.queryParams.tarEntityIds = [];
      if (this.queryParams.tarEntityId != null) {
        this.queryParams.tarEntityIds.push(this.queryParams.tarEntityId);
      }
	    findShipAllList(this.queryParams).then(response => {
	          for(var i=0;i<response.data.length;i++){
	        	  if(response.data[i].entryType=='API-TABLE'){
	        		    this.srcnodes.push({id:(response.data[i].entityId).toString(),label:response.data[i].tableName+'('+(response.data[i].tableComment?response.data[i].tableComment:'无')+')',attrs:response.data[i].mdmDataEntityProps,collapsed:"collapsed",xtype:"API-TABLE"});
	          		}else{
	          			this.srcnodes.push({id:(response.data[i].entityId).toString(),label:response.data[i].tableName+'('+(response.data[i].tableComment?response.data[i].tableComment:'无')+')',attrs:response.data[i].mdmDataEntityProps});
	          		}
	    			console.log(response.data[i].mdmDataEntityProps);
	    			if(response.data[i].relations!=null){
	    				for(var j=0;j<response.data[i].relations.length;j++){
	    					//this.edges.push({"source":(response.data[i].relations[j].srcEntityId).toString(),"target":(response.data[i].relations[j].tarEntityId).toString()});
	    					if(response.data[i].relations[j].shipType=='3'&&response.data[i].relations[j].srcEntityId!=null&&response.data[i].relations[j].tarEntityId!=null){
	    						this.srcedges.push({source: (response.data[i].relations[j].srcEntityId).toString(),target: (response.data[i].relations[j].tarEntityId).toString(),sourceKey: response.data[i].relations[j].srcPropName,targetKey: response.data[i].relations[j].tarPropName,label: "交换"});
	    					}else if(response.data[i].relations[j].shipType!='API-TABLE'&&response.data[i].relations[j].srcEntityId!=null&&response.data[i].relations[j].tarEntityId!=null){
	    						this.srcedges.push({source: (response.data[i].relations[j].srcEntityId).toString(),target: (response.data[i].relations[j].tarEntityId).toString(),sourceKey: response.data[i].relations[j].srcPropName,targetKey: response.data[i].relations[j].tarPropName,label: "计算"});
	    					}else if(response.data[i].relations[j].shipType=='API-TABLE'&&response.data[i].relations[j].srcTableName!=null&&response.data[i].relations[j].tarTableName!=null){
	    						this.srcedges.push({source: response.data[i].relations[j].srcTableName,target: response.data[i].relations[j].tarTableName,sourceKey: response.data[i].relations[j].srcPropName,targetKey: response.data[i].relations[j].tarPropName,label: "管控"});
	    					}
	    					
	    				}
	    			}
	    		}
	          this.srcg6graph=showShipMap("container",this.srcnodes,this.srcedges);
	    });
    }

  },
  destroy() {
    //注意，VUE此处必须清理，否则切换界面会越来越卡
    this.srcg6graph.clear();
    this.srcg6graph.destroy();
  },
  mounted() {
    this.getList();
  }
};
</script>

<style>
.entity-container.fact {
  border: 1px solid #ced4de;
  height: 248px;
  width: 214px;
}
.entity-container {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border-radius: 2px;
  background-color: #fff;
}
.entity-container .content.fact {
  background-color: #ced4de;
}
.entity-container .content {
  margin: 1px;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.entity-container .content .head {
  width: calc(100% - 12px);
  height: 38px;
  margin-left: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.entity-container .content .head .type {
  padding-right: 8px;
}
.anticon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.anticon svg {
  display: inline-block;
}
svg:not(:root) {
  overflow: hidden;
}
.entity-container .content .head .more {
  cursor: pointer;
}
.entity-container .content .body {
  width: calc(100% - 12px);
  height: calc(100% - 42px);
  margin-left: 6px;
  margin-bottom: 6px;
  background-color: #fff;
  overflow: auto;
  cursor: pointer;
}
.entity-container .content .body .body-item {
  width: 100%;
  height: 28px;
  font-size: 12px;
  color: #595959;
  border-bottom: 1px solid rgba(206, 212, 222, 0.2);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.entity-container .content .body .body-item .name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 6px;
}
.entity-container .content .body .body-item .name .fk,
.entity-container .content .body .body-item .name .pk {
  width: 12px;
  font-family: "HelveticaNeue-CondensedBold";
  color: #ffd666;
  margin-right: 6px;
}
.entity-container .content .body .body-item .type {
  color: #bfbfbf;
  font-size: 8px;
  margin-right: 8px;
}
#scalingToolBar2{
  transition:all 2s ease;
  right:25%;
  top:10px;
}
#scalingToolBar2.right{
  right:0;
  top:0;
}
.logo{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom:15px;
  width:706px;
}
.container-fluid{
  position:relative;
  height:calc(100vh - 84px);
}
</style>
