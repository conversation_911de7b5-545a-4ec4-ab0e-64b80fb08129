package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.dqms.basic.domain.SysAgent;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.common.core.domain.entity.SysSystem;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.exception.CustomException;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.DictUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.bean.BeanUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.dsm.mapper.DsmModelSqlCheckMapper;
import com.dqms.dsm.domain.DsmModelSqlCheck;
import com.dqms.dsm.service.IDsmModelSqlCheckService;
import com.dqms.framework.web.service.TokenService;
import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskClass;
import com.dqms.task.domain.EtlTaskGroup;
import com.dqms.task.domain.vo.EtlTaskVo;
import com.dqms.utils.SqlCheckUtils;

/**
 * 脚本检核Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
@Service
public class DsmModelSqlCheckServiceImpl implements IDsmModelSqlCheckService
{
    @Autowired
    private DsmModelSqlCheckMapper dsmModelSqlCheckMapper;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 查询脚本检核
     *
     * @param modelSqlCheckId 脚本检核ID
     * @return 脚本检核
     */
    @Override
    public DsmModelSqlCheck selectDsmModelSqlCheckById(Long modelSqlCheckId)
    {
        return dsmModelSqlCheckMapper.selectDsmModelSqlCheckById(modelSqlCheckId);
    }

    /**
     * 查询脚本检核列表
     *
     * @param dsmModelSqlCheck 脚本检核
     * @return 脚本检核
     */
    @Override
    public List<DsmModelSqlCheck> selectDsmModelSqlCheckList(DsmModelSqlCheck dsmModelSqlCheck)
    {
        return dsmModelSqlCheckMapper.selectDsmModelSqlCheckList(dsmModelSqlCheck);
    }

    /**
     * 新增脚本检核
     *
     * @param dsmModelSqlCheck 脚本检核
     * @return 结果
     */
    @Override
    public int insertDsmModelSqlCheck(DsmModelSqlCheck dsmModelSqlCheck)
    {
        dsmModelSqlCheck.setCreateTime(DateUtils.getNowDate());
        return dsmModelSqlCheckMapper.insertDsmModelSqlCheck(dsmModelSqlCheck);
    }

    /**
     * 修改脚本检核
     *
     * @param dsmModelSqlCheck 脚本检核
     * @return 结果
     */
    @Override
    public int updateDsmModelSqlCheck(DsmModelSqlCheck dsmModelSqlCheck)
    {
        return dsmModelSqlCheckMapper.updateDsmModelSqlCheck(dsmModelSqlCheck);
    }

    /**
     * 批量删除脚本检核
     *
     * @param modelSqlCheckIds 需要删除的脚本检核ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelSqlCheckByIds(Long[] modelSqlCheckIds)
    {
        return dsmModelSqlCheckMapper.deleteDsmModelSqlCheckByIds(modelSqlCheckIds);
    }

    /**
     * 删除脚本检核信息
     *
     * @param modelSqlCheckId 脚本检核ID
     * @return 结果
     */
    @Override
    public int deleteDsmModelSqlCheckById(Long modelSqlCheckId)
    {
        return dsmModelSqlCheckMapper.deleteDsmModelSqlCheckById(modelSqlCheckId);
    }
    
    /**
     * 导入任务数据
     * 
     * @param etlTaskRelationList 任务数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    @Transactional
    public String importDsmModelSqlCheck(List<DsmModelSqlCheck> list, Boolean isUpdateSupport)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new CustomException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DsmModelSqlCheck vo : list)
        {
            try
            {
	        	if(StringUtils.isNull(vo.getSqlName())){
	        		failureNum++;
	                failureMsg.append("<br/>" + failureNum + "、脚本名称必填");
	                continue;
	        	}
	        	if(StringUtils.isNull(vo.getSqlScript())){
	        		failureNum++;
	                failureMsg.append("<br/>" + failureNum + "、 " + vo.getSqlName() + "脚本信息必填");
	                continue;
	        	}
	            vo.setCreateTime(DateUtils.getNowDate());
	            List<Map<String, Object>>  infoList = SqlCheckUtils.exec(vo.getSqlScript());
	            String s ="";
	            for(Map<String ,Object> map : infoList) {
	        		s+=map.get("规则")+":"+map.get("信息")+"\r\n";
	        	}
	            vo.setResult(s.trim());
	            this.insertDsmModelSqlCheck(vo);
	            successNum++;
	            successMsg.append("<br/>" + successNum + "、 " + vo.getSqlName() + " 导入成功");
	            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、 " + vo.getSqlName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
