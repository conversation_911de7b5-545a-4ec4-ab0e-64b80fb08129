package com.dqms.dic.mapper;

import java.util.List;
import com.dqms.dic.domain.DicFileTask;

/**
 * 文件数据补录Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-04-14
 */
public interface DicFileTaskMapper 
{
    /**
     * 查询文件数据补录
     * 
     * @param fileTaskId 文件数据补录ID
     * @return 文件数据补录
     */
    public DicFileTask selectDicFileTaskById(Long fileTaskId);

    /**
     * 查询文件数据补录列表
     * 
     * @param dicFileTask 文件数据补录
     * @return 文件数据补录集合
     */
    public List<DicFileTask> selectDicFileTaskList(DicFileTask dicFileTask);

    /**
     * 新增文件数据补录
     * 
     * @param dicFileTask 文件数据补录
     * @return 结果
     */
    public int insertDicFileTask(DicFileTask dicFileTask);

    /**
     * 修改文件数据补录
     * 
     * @param dicFileTask 文件数据补录
     * @return 结果
     */
    public int updateDicFileTask(DicFileTask dicFileTask);

    /**
     * 删除文件数据补录
     * 
     * @param fileTaskId 文件数据补录ID
     * @return 结果
     */
    public int deleteDicFileTaskById(Long fileTaskId);

    /**
     * 批量删除文件数据补录
     * 
     * @param fileTaskIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDicFileTaskByIds(Long[] fileTaskIds);
}
