<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmStandardMdmRelMapper">
    
    <resultMap type="DsmStandardMdmRel" id="DsmStandardMdmRelResult">
        <result property="standardId"    column="standard_id"    />
        <result property="propId"    column="prop_id"    />
        <result property="propComment"    column="prop_comment"    />
        <result property="standardCode"    column="standard_code"    />
        <result property="standardName"    column="standard_name"    />
        <result property="propName"    column="prop_name"    />
        <result property="tableName"    column="table_name"    />
        <result property="tableSchema"    column="table_schema"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemName"    column="system_name"    />
    </resultMap>

    <sql id="selectDsmStandardMdmRelVo">
        SELECT t.standard_id,d.standard_code,d.standard_name,
		 t.prop_id,m.prop_name  ,m.prop_comment,e.table_name,ds.name AS datasource_name,ss.name AS system_name,e.table_schema
		FROM dsm_standard_mdm_rel t
		LEFT JOIN dsm_standard d ON t.standard_id=d.standard_id
		LEFT JOIN mdm_data_entity_prop m ON t.prop_id=m.prop_id
		LEFT JOIN mdm_data_entity e ON m.entity_id=e.entity_id
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
		LEFT JOIN sys_system ss ON r.system_id=ss.system_id
    </sql>

    <select id="selectDsmStandardMdmRelList" parameterType="DsmStandardMdmRel" resultMap="DsmStandardMdmRelResult">
        <include refid="selectDsmStandardMdmRelVo"/>
        <where>  
        	<if test="standardId != null  and standardId != ''"> and t.standard_id = #{standardId}</if>
            and r.del_flag != '2'
        </where>
    </select>
    
    <select id="selectUnDsmStandardMdmRelList" parameterType="DsmStandardMdmRel" resultMap="DsmStandardMdmRelResult">
        SELECT m.prop_id,m.prop_name ,m.prop_comment,e.table_name,ds.name AS datasource_name,ss.name AS system_name,e.table_schema
		FROM mdm_data_entity_prop m 
		LEFT JOIN mdm_data_entity e ON m.entity_id=e.entity_id
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
		LEFT JOIN sys_system ss ON r.system_id=ss.system_id
		WHERE m.prop_id NOT IN (SELECT prop_id FROM dsm_standard_mdm_rel WHERE standard_id=#{standardId})
		<if test="propName != null  and propName != ''"> and (m.prop_name like concat('%', #{propName}, '%') or e.table_name like concat('%', #{propName}, '%')
		<if test="propComment != null  and propComment != ''"> or m.prop_comment like concat('%', #{propComment}, '%') </if>
		)</if>
		<if test="tableName != null  and tableName != ''"> and e.table_name like concat('%', #{tableName}, '%')</if>
        and r.del_flag != '2'
    </select>
    
    <select id="selectDsmStandardMdmRelById" parameterType="Long" resultMap="DsmStandardMdmRelResult">
        <include refid="selectDsmStandardMdmRelVo"/>
        where standard_id = #{standardId}
    </select>
        
    <insert id="insertDsmStandardMdmRel" parameterType="DsmStandardMdmRel">
        insert into dsm_standard_mdm_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="standardId != null">standard_id,</if>
            <if test="propId != null">prop_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="standardId != null">#{standardId},</if>
            <if test="propId != null">#{propId},</if>
         </trim>
    </insert>

    <update id="updateDsmStandardMdmRel" parameterType="DsmStandardMdmRel">
        update dsm_standard_mdm_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="propId != null">prop_id = #{propId},</if>
        </trim>
        where standard_id = #{standardId}
    </update>

    <delete id="deleteDsmStandardMdmRelById" parameterType="Long">
        delete from dsm_standard_mdm_rel where standard_id = #{standardId}
    </delete>

    <delete id="deleteDsmStandardMdmRelByIds" parameterType="String">
        delete from dsm_standard_mdm_rel where standard_id in 
        <foreach item="standardId" collection="array" open="(" separator="," close=")">
            #{standardId}
        </foreach>
    </delete>
    
    <delete id="deleteDsmStandardMdmRelByPk" parameterType="DsmStandardMdmRel">
        delete from dsm_standard_mdm_rel where standard_id = #{standardId} and prop_id = #{propId}
    </delete>


    <delete id="deleteDsmStandardMdmRelALL" parameterType="java.util.List">
        delete from dsm_standard_mdm_rel where standard_id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
             #{item.standardId}
        </foreach>
    </delete>

    <insert id="importInsertDsmStandardMdmRel" parameterType="java.util.List">
        insert into dsm_standard_mdm_rel
        (
            standard_id
            ,prop_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.standardId},
                #{item.propId}
            )
        </foreach>
    </insert>

    <select id="selectDsmStandardMdmRelListSingle" parameterType="DsmStandardMdmRel" resultMap="DsmStandardMdmRelResult">
        SELECT t.standard_id, t.prop_id
        FROM dsm_standard_mdm_rel t
        <where>
            <if test="standardId != null  and standardId != ''"> and t.standard_id = #{standardId}</if>
            <if test="propId != null  and propId != ''"> and t.prop_id = #{propId}</if>
        </where>
    </select>
</mapper>