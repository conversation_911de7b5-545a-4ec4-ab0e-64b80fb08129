package com.dqms.dqm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.dqm.domain.DqmValidationClass;
import com.dqms.dqm.service.IDqmValidationClassService;

/**
 * 检查分类Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RestController
@RequestMapping("/dqm/dqmValidationClass")
public class DqmValidationClassController extends BaseController
{
    @Autowired
    private IDqmValidationClassService dqmValidationClassService;

    /**
     * 查询检查分类列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationClass:list')")
    @GetMapping("/list")
    public AjaxResult list(DqmValidationClass dqmValidationClass)
    {
        List<DqmValidationClass> list = dqmValidationClassService.selectDqmValidationClassList(dqmValidationClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出检查分类列表
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationClass:export')")
    @Log(title = "检查分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DqmValidationClass dqmValidationClass)
    {
        List<DqmValidationClass> list = dqmValidationClassService.selectDqmValidationClassList(dqmValidationClass);
        ExcelUtil<DqmValidationClass> util = new ExcelUtil<DqmValidationClass>(DqmValidationClass.class);
        return util.exportExcel(list, "dqmValidationClass");
    }

    /**
     * 获取检查分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationClass:query')")
    @GetMapping(value = "/{validationClassId}")
    public AjaxResult getInfo(@PathVariable("validationClassId") Long validationClassId)
    {
        return AjaxResult.success(dqmValidationClassService.selectDqmValidationClassById(validationClassId));
    }

    /**
     * 新增检查分类
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationClass:add')")
    @Log(title = "检查分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DqmValidationClass dqmValidationClass)
    {
        return toAjax(dqmValidationClassService.insertDqmValidationClass(dqmValidationClass));
    }

    /**
     * 修改检查分类
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationClass:edit')")
    @Log(title = "检查分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DqmValidationClass dqmValidationClass)
    {
        return toAjax(dqmValidationClassService.updateDqmValidationClass(dqmValidationClass));
    }

    /**
     * 删除检查分类
     */
    @PreAuthorize("@ss.hasPermi('dqm:dqmValidationClass:remove')")
    @Log(title = "检查分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{validationClassIds}")
    public AjaxResult remove(@PathVariable Long[] validationClassIds)
    {
        return toAjax(dqmValidationClassService.deleteDqmValidationClassByIds(validationClassIds));
    }
    
    /**
     * 获取下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DqmValidationClass dqmValidationClass)
    {
        List<DqmValidationClass> dqmValidationClasss = dqmValidationClassService.selectDqmValidationClassList(dqmValidationClass);
        return AjaxResult.success(dqmValidationClassService.buildDqmValidationClassTreeSelect(dqmValidationClasss));
    }
}
