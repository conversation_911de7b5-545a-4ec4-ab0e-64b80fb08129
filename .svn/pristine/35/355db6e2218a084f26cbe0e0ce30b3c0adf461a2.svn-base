package com.dqms.task.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dqms.task.domain.EtlTaskCalendarDetail;
import com.dqms.task.domain.vo.EtlTaskCalendarDetailVo;

/**
 * 调度日历明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
public interface EtlTaskCalendarDetailMapper 
{
    /**
     * 查询调度日历明细
     * 
     * @param taskCalendarDetailId 调度日历明细ID
     * @return 调度日历明细
     */
    public EtlTaskCalendarDetail selectEtlTaskCalendarDetailById(Long taskCalendarDetailId);

    /**
     * 查询调度日历明细列表
     * 
     * @param etlTaskCalendarDetail 调度日历明细
     * @return 调度日历明细集合
     */
    public List<EtlTaskCalendarDetail> selectEtlTaskCalendarDetailList(EtlTaskCalendarDetail etlTaskCalendarDetail);
    public List<EtlTaskCalendarDetailVo> selectEtlTaskCalendarDetailVoList(EtlTaskCalendarDetail etlTaskCalendarDetail);

    /**
     * 新增调度日历明细
     * 
     * @param etlTaskCalendarDetail 调度日历明细
     * @return 结果
     */
    public int insertEtlTaskCalendarDetail(EtlTaskCalendarDetail etlTaskCalendarDetail);

    /**
     * 修改调度日历明细
     * 
     * @param etlTaskCalendarDetail 调度日历明细
     * @return 结果
     */
    public int updateEtlTaskCalendarDetail(EtlTaskCalendarDetail etlTaskCalendarDetail);

    /**
     * 删除调度日历明细
     * 
     * @param taskCalendarDetailId 调度日历明细ID
     * @return 结果
     */
    public int deleteEtlTaskCalendarDetailById(Long taskCalendarDetailId);

    /**
     * 批量删除调度日历明细
     * 
     * @param taskCalendarDetailIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskCalendarDetailByIds(Long[] taskCalendarDetailIds);
    
    /**
     * 查询调度日历明细
     * 
     * @param classId 和 loadDate
     * @return 调度日历明细
     */
    public EtlTaskCalendarDetail selectEtlTaskCalendarDetailByLoadDate(@Param("taskCalendarClassId")Long taskCalendarClassId,@Param("loadDate")String loadDate);
    
    /**
     * 查询当前可执行日期
     * 
     * @param TaskCalendarClassId 调度日历
     * @return LoadDate
     */
    public EtlTaskCalendarDetail selectMinLoadDateByTaskCalendarClassId(@Param("taskCalendarClassId")Long taskCalendarClassId);
}
