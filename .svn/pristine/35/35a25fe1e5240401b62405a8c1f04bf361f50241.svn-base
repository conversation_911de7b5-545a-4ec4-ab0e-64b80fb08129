package com.dqms.mdm.util.StrategyType;

import com.dqms.DqmsApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DqmsApplication.class)
@WebAppConfiguration
public class DB2StrategyTest {

    @Test
    public void getTableInfo() {
    }
}
