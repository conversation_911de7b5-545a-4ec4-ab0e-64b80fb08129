import request from '@/utils/request'
import * as echarts from 'echarts';

// 查询标准落标列表
export function listDsmDiscern(query) {
  return request({
    url: '/dsm/dsmDiscern/list',
    method: 'get',
    params: query
  })
}

// 查询标准落标详细
export function getDsmDiscern(discernId) {
  return request({
    url: '/dsm/dsmDiscern/' + discernId,
    method: 'get'
  })
}

// 新增标准落标
export function addDsmDiscern(data) {
  return request({
    url: '/dsm/dsmDiscern',
    method: 'post',
    data: data
  })
}

// 修改标准落标
export function updateDsmDiscern(data) {
  return request({
    url: '/dsm/dsmDiscern',
    method: 'put',
    data: data
  })
}

// 删除标准落标
export function delDsmDiscern(discernId) {
  return request({
    url: '/dsm/dsmDiscern/' + discernId,
    method: 'delete'
  })
}

// 忽略标准落标
export function ignoreDsmDiscern(discernId) {
  return request({
    url: '/dsm/dsmDiscern/ignore/' + discernId,
    method: 'put'
  })
}

// 导出标准落标
export function exportDsmDiscern(query) {
  return request({
    url: '/dsm/dsmDiscern/export',
    method: 'get',
    params: query
  })
}

export function getRunWcl(value_) {
var chartDom = document.getElementById('runWcl');
    	  var myChart = echarts.init(chartDom);
    	  var option;

		option = {
		    series: [{
		        type: 'gauge',
		        radius: '100%',
		        axisLine: {
		            lineStyle: {
		                width: 5
		            }
		        },
		        pointer: {
		            itemStyle: {
		                color: 'auto'
		            }
		        },
		        axisTick: {
		            distance: -30,
		            length: 8,
		            lineStyle: {
		                color: '#fff',
		                width: 2
		            }
		        },
		        splitLine: {
		            distance: -30,
		            length: 30,
		            lineStyle: {
		                color: '#fff',
		                width: 4
		            }
		        },
		        axisLabel: {
		            color: 'auto',
		            distance: 0,
		            fontSize: 5
		        },
		        detail: {
		            valueAnimation: true,
		            formatter: '{value} %',
		            color: 'auto'
		        },
		        data: [{
		            value: value_
		        }]
		    }]
		};

    	  option && myChart.setOption(option);
}