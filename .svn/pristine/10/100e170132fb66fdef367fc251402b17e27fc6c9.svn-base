package com.dqms.dsc.service;

import java.util.ArrayList;
import java.util.List;
import com.dqms.dsc.domain.DscEntityPropSafeClass;

/**
 * 安全分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-11-10
 */
public interface IDscEntityPropSafeClassService 
{
    /**
     * 查询安全分类
     * 
     * @param safeClassId 安全分类ID
     * @return 安全分类
     */
    public DscEntityPropSafeClass selectDscEntityPropSafeClassById(Long safeClassId);

    /**
     * 查询安全分类列表
     * 
     * @param dscEntityPropSafeClass 安全分类
     * @return 安全分类集合
     */
    public List<DscEntityPropSafeClass> selectDscEntityPropSafeClassList(DscEntityPropSafeClass dscEntityPropSafeClass);

    public List<DscEntityPropSafeClass> showClassLevel(DscEntityPropSafeClass dscEntityPropSafeClass);



    /**
     * 新增安全分类
     * 
     * @param dscEntityPropSafeClass 安全分类
     * @return 结果
     */
    public int insertDscEntityPropSafeClass(DscEntityPropSafeClass dscEntityPropSafeClass);

    /**
     * 修改安全分类
     * 
     * @param dscEntityPropSafeClass 安全分类
     * @return 结果
     */
    public int updateDscEntityPropSafeClass(DscEntityPropSafeClass dscEntityPropSafeClass);

    /**
     * 批量删除安全分类
     * 
     * @param safeClassIds 需要删除的安全分类ID
     * @return 结果
     */
    public int deleteDscEntityPropSafeClassByIds(Long[] safeClassIds);

    /**
     * 删除安全分类信息
     * 
     * @param safeClassId 安全分类ID
     * @return 结果
     */
    public int deleteDscEntityPropSafeClassById(Long safeClassId);

    public List<DscEntityPropSafeClass> getDscEntityPropSafeClassList(DscEntityPropSafeClass dscEntityPropSafeClass) ;

    public DscEntityPropSafeClass getDscEntityPropSafeClassById(Long safeClassId) ;

}
