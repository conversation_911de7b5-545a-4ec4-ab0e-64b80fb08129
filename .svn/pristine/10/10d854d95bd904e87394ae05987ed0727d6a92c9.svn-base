import request from '@/utils/request'

// 查询数据交换配置列表
export function listDicDataExchange(query) {
  return request({
    url: '/dic/dicDataExchange/list',
    method: 'get',
    params: query
  })
}

// 查询数据交换配置详细
export function getDicDataExchange(exchangeId) {
  return request({
    url: '/dic/dicDataExchange/' + exchangeId,
    method: 'get'
  })
}

// 新增数据交换配置
export function addDicDataExchange(data) {
  return request({
    url: '/dic/dicDataExchange',
    method: 'post',
    data: data
  })
}

// 修改数据交换配置
export function updateDicDataExchange(data) {
  return request({
    url: '/dic/dicDataExchange',
    method: 'put',
    data: data
  })
}

// 删除数据交换配置
export function delDicDataExchange(exchangeId) {
  return request({
    url: '/dic/dicDataExchange/' + exchangeId,
    method: 'delete'
  })
}

export function syncJson(query) {
  return request({
    url: '/dic/dicDataExchange/syncJson',
    method: 'get',
    params: query
  })
}

// 导出数据交换配置
export function exportDicDataExchange(query) {
  return request({
    url: '/dic/dicDataExchange/export',
    method: 'get',
    params: query
  })
}

// 获取目标表字段信息
export function findProp(data) {
  return request({
    url: '/dic/dicDataExchange/getPropByEx',
    method: 'post',
    data: data
  })
}

// sql生成
export function sqlGeneration(data) {
  return request({
    url: '/dic/dicDataExchange/sqlGeneration',
    method: 'post',
    data: data
  })
}

export function jsonGeneration(data) {
  return request({
    url: '/dic/dicDataExchange/jsonGeneration',
    method: 'post',
    data: data
  })
}

// 下载元系统注册导入模板
export function importTemplate() {
  return request({
    url: '/dic/dicDataExchange/importTemplate',
    method: 'get'
  })
}

export function syncMdm(exchangeId) {
  return request({
    url: '/dic/dicDataExchange/syncMdm/'+exchangeId,
    method: 'post'
  })
}