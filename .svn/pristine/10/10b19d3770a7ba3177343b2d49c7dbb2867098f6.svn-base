package com.dqms.web.controller.common;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.dqms.common.config.DqmsConfig;
import com.dqms.common.constant.Constants;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.common.utils.file.FileUploadUtils;
import com.dqms.common.utils.file.FileUtils;
import com.dqms.framework.config.ServerConfig;
import com.dqms.needs.domain.NesNeeds;
import com.dqms.system.service.ISysConfigService;

/**
 * 通用请求处理
 *
*/
@RestController
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    public ISysConfigService configService;
    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName =  fileName.substring(fileName.indexOf("_") + 1);
            if(realFileName.lastIndexOf(".")!=-1) {
        		realFileName=realFileName.substring(0, realFileName.lastIndexOf("."))+System.currentTimeMillis()+realFileName.substring(realFileName.lastIndexOf("."));
        	}
            String filePath = DqmsConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = DqmsConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = DqmsConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
    
    @RequestMapping(value = "common/attachment/upload" ,method = RequestMethod.POST)
    public Object upload(MultipartFile file, HttpServletRequest request){
    	HashMap<String, Object> returnMap = new HashMap<String, Object>();
    	if (file != null){
	    	boolean isTag = true;
	        InputStream in = null;
	        BufferedOutputStream out = null;
	        BigDecimal divide = new BigDecimal(0);
	        divide = new BigDecimal(file.getSize()).divide(new BigDecimal(1024 * 1024), 4, RoundingMode.HALF_UP);
	        String Urlpath = configService.selectConfigByKey("file.needs.attachment.path");
	        if (!Urlpath.endsWith("/")) {
	            Urlpath = Urlpath + "/";
	        }
	        Urlpath += System.currentTimeMillis();
	        File isFilePath = new File(Urlpath);
	        if (!isFilePath.exists()) {
	            isFilePath.mkdirs();
	        }
	        String fileSourceName = file.getOriginalFilename();
	        String filePath = Urlpath + "/" + fileSourceName;//
	        System.out.println(filePath);
	        returnMap.put("msg",filePath);
	        try {
	            in = file.getInputStream();
	            out = new BufferedOutputStream(new FileOutputStream(filePath));
	            int i;
	            while ((i = in.read()) != -1) {
	                out.write(i);
	            }
	            out.flush();
	        } catch (Exception e) {
	        	returnMap.put("code","00");
	            e.printStackTrace();
	            isTag = false;
	            throw new RuntimeException(e.getMessage());
	        } finally {
	            try {
	                if (out != null) {
	                    out.close();
	                }
	            } catch (Exception e1) {
	            }
	            try {
	                if (in != null) {
	                    in.close();
	                }
	            } catch (Exception e1) {
	            }
	            // TODO Auto-generated catch block
	        }
    	}
    	returnMap.put("code","99");
        
       return returnMap;
  }
    /**
     * 本地资源通用下载
     */
    @GetMapping("common/attachment")
    public void attachmentDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.lastIndexOf("/")+1);

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(fileName, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(fileName);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
}
