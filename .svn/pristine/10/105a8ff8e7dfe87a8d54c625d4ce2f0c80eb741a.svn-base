package com.dqms.dsc.domain;
import com.dqms.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

/**
 * 加密明细对象 dsc_encryption_detail
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public class DscEncryptionDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private Long encryptionDetailId;

    /** 加密ID */
    @Excel(name = "加密ID")
    private Long encryptionId;

    /** 实体ID */
    @Excel(name = "实体ID")
    private Long entityId;

    /** 字段ID */
    @Excel(name = "字段ID")
    private Long propId;

    /** 加密类型 */
    @Excel(name = "加密类型")
    private String runType;
    private String propName;
    private String propComment;

    public void setEncryptionDetailId(Long encryptionDetailId)
    {
        this.encryptionDetailId = encryptionDetailId;
    }

    public Long getEncryptionDetailId()
    {
        return encryptionDetailId;
    }
    public void setEncryptionId(Long encryptionId)
    {
        this.encryptionId = encryptionId;
    }

    public Long getEncryptionId()
    {
        return encryptionId;
    }
    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setPropId(Long propId)
    {
        this.propId = propId;
    }

    public Long getPropId()
    {
        return propId;
    }
    public void setRunType(String runType)
    {
        this.runType = runType;
    }

    public String getRunType()
    {
        return runType;
    }

    public String getPropName() {
		return propName;
	}

	public void setPropName(String propName) {
		this.propName = propName;
	}

	public String getPropComment() {
		return propComment;
	}

	public void setPropComment(String propComment) {
		this.propComment = propComment;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("encryptionDetailId", getEncryptionDetailId())
            .append("encryptionId", getEncryptionId())
            .append("entityId", getEntityId())
            .append("propId", getPropId())
            .append("runType", getRunType())
            .toString();
    }
}
