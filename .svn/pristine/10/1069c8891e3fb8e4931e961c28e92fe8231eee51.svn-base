package com.dqms.dsc.service;

import java.util.List;


import com.dqms.dsc.domain.DscEntityProp;
import com.dqms.dsc.domain.vo.DscEntityPropVo;

/**
 * 分级分类Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IDscEntityPropService 
{
    /**
     * 查询分级分类
     * 
     * @param dscEntityPropId 分级分类ID
     * @return 分级分类
     */
    public DscEntityProp selectDscEntityPropById(Long dscEntityPropId);

    /**
     * 查询分级分类列表
     * 
     * @param dscEntityProp 分级分类
     * @return 分级分类集合
     */
    public List<DscEntityProp> selectDscEntityPropList(DscEntityProp dscEntityProp);

    /**
     * 导出分级分类列表
     *
     * @param dscEntityPropVo 分级分类
     * @return 分级分类集合
     */
    public List<DscEntityPropVo> getDscEntityPropListVo(DscEntityPropVo dscEntityPropVo);

    /**
     * 新增分级分类
     * 
     * @param dscEntityProp 分级分类
     * @return 结果
     */
    public int insertDscEntityProp(DscEntityProp dscEntityProp);

    /**
     * 修改分级分类
     * 
     * @param dscEntityProp 分级分类
     * @return 结果
     */
    public int updateDscEntityProp(DscEntityProp dscEntityProp);

    /**
     * 批量删除分级分类
     * 
     * @param dscEntityPropIds 需要删除的分级分类ID
     * @return 结果
     */
    public int deleteDscEntityPropByIds(Long[] dscEntityPropIds);

    /**
     * 删除分级分类信息
     * 
     * @param dscEntityPropId 分级分类ID
     * @return 结果
     */
    public int deleteDscEntityPropById(Long dscEntityPropId);

    public String importDscEntityProp(List<DscEntityPropVo> dscEntityPropVoList, Boolean isUpdateSupport);
}
