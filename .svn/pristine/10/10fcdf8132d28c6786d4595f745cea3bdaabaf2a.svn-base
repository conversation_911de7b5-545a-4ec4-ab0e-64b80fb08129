package com.dqms.api.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.alibaba.druid.util.JdbcConstants;
import com.dqms.api.domain.ApiDefine;
import com.dqms.api.domain.ApiDefineParam;
import com.dqms.api.enums.ApiConstants;
import com.dqms.api.mapper.ApiDefineMapper;
import com.dqms.api.mapper.ApiDefineParamMapper;
import com.dqms.api.service.IApiDefineParamService;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.common.constant.MdmConstants;
import com.dqms.mdm.domain.MdmRegistry;
import com.dqms.mdm.util.MetaDataContext;
import com.dqms.utils.sql.GbHiveSchemaStatVisitor;
import com.dqms.utils.sql.GbOracleSchemaStatVisitor;
import com.dqms.utils.sql.GbSQLUtils;
import com.dqms.utils.sql.GbSchemaStatVisitor;
import com.dqms.utils.sql.GbSqlSchemaStatVisitor;
import com.dqms.utils.sql.GbTableStat;
import com.dqms.utils.sql.GbTableStat.Column;
import com.dqms.utils.sql.GbTableStat.Name;

/**
 * 接口参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@Service
public class ApiDefineParamServiceImpl implements IApiDefineParamService
{
    @Autowired
    private ApiDefineParamMapper apiDefineParamMapper;
    
    @Autowired
    private ApiDefineMapper apiDefineMapper;

    @Autowired
    private MetaDataContext metaDataContext;
    
    @Autowired
    private SysDatasourceMapper sysDatasourceMapper;
    /**
     * 查询接口参数
     *
     * @param defineParamId 接口参数ID
     * @return 接口参数
     */
    @Override
    public ApiDefineParam selectApiDefineParamById(Long defineParamId)
    {
        return apiDefineParamMapper.selectApiDefineParamById(defineParamId);
    }

    /**
     * 查询接口参数列表
     *
     * @param apiDefineParam 接口参数
     * @return 接口参数
     */
    @Override
    public List<ApiDefineParam> selectApiDefineParamList(ApiDefineParam apiDefineParam)
    {
    	List<ApiDefineParam> list = apiDefineParamMapper.selectApiDefineParamList(apiDefineParam);
    	if(list==null||list.size()==0) {
    		ApiDefine apiDefine = apiDefineMapper.selectApiDefineById(apiDefineParam.getDefineId());
    		if(apiDefine.getDefineType().equals(ApiConstants.TASK_TASK)) {
    			ApiDefineParam param = new ApiDefineParam();
    			param.setDefineParamName("params");
    			param.setDefineParamType(ApiConstants.STRING);
    			param.setIsMust("Y");
    			param.setRemark("任务参数");
    			list.add(param);
    			return list;
    		}
    		if(!apiDefine.getDefineType().equals(ApiConstants.TASK_DATA)) {
    			return list;
    		}
    		if(apiDefine.getDefineSql().indexOf("${WHERE}")>=0) {
    			apiDefine.setDefineSql(apiDefine.getDefineSql().replace("${WHERE}", ""));
    		}
    		if(apiDefine!=null&&apiDefine.getDefineSql()!=null&&apiDefine.getDefineSql().toLowerCase().startsWith("select")) {

    			list = getTables(apiDefine);
				/*
				 * for(String table :li) { MdmRegistry mdmRegistry=new MdmRegistry();
				 * mdmRegistry.setDatasourceId(apiDefine.getDatasourceId());
				 * if(table.indexOf(".")>=0) { mdmRegistry.setRegDir(table.split("\\.")[0]);
				 * mdmRegistry.setRegName(table.split("\\.")[1]); }else {
				 * mdmRegistry.setRegName(table); } List<Map<String, Object>> columnsList =
				 * metaDataContext.getColumns(mdmRegistry);
				 * list.addAll(converMapToDataEntity(columnsList)); }
				 */
    		}else if(apiDefine!=null&&apiDefine.getDefineSql()!=null&&apiDefine.getDefineSql().indexOf(".")>0){
    			MdmRegistry mdmRegistry=new MdmRegistry();
    			mdmRegistry.setDatasourceId(apiDefine.getDatasourceId());
    			if(apiDefine.getDefineSql().indexOf(".")>=0) {
                    String defineSql = apiDefine.getDefineSql();
                    //defineSql presto将以cat.schem.table的形式出现
                    int i = defineSql.lastIndexOf(".");
                    String regDir = defineSql.substring(0,i);
                    if(regDir.indexOf(".")!=-1) {
                    	String[] regDirs= regDir.split("\\.");
                    	regDir=regDirs[regDirs.length-1];
                    }
                    String regName = defineSql.substring(i+1);
                    mdmRegistry.setRegDir(regDir);
        			mdmRegistry.setRegName(regName);
    			}else {
    				mdmRegistry.setRegName(apiDefine.getDefineSql());
    			}
    			List<Map<String, Object>> columnsList = metaDataContext.getColumns(mdmRegistry);
    			list = converMapToDataEntity(columnsList);
    		}
    	}
        return list;
    }
    private List<ApiDefineParam> converMapToDataEntity( List<Map<String, Object>> columnsList){
        List<ApiDefineParam> newMdmDataEntityPropList = new ArrayList<ApiDefineParam>();
        for (int i = 0; i < columnsList.size(); i++) {
        	ApiDefineParam param = new ApiDefineParam();
            Map<String, Object> propMap = columnsList.get(i);
            String propName = propMap.get("name").toString();
            String dataType = propMap.get("dataType").toString().toUpperCase();
            param.setDefineParamName(propName);
            param.setRemark(propMap.get("remark").toString());
            if(dataType.toUpperCase().indexOf("INT")>-1) {
            	param.setDefineParamType(ApiConstants.INT);
            }else if(dataType.toUpperCase().indexOf("FLOAT")>-1) {
            	param.setDefineParamType(ApiConstants.DOUBLE);
            }else if(dataType.toUpperCase().indexOf("DOUBLE")>-1||dataType.toUpperCase().indexOf("DECIMAL")>-1) {
            	param.setDefineParamType(ApiConstants.DOUBLE);
            }else if(dataType.toUpperCase().indexOf("LONG")>-1) {
            	param.setDefineParamType(ApiConstants.LONG);
            }else if(dataType.toUpperCase().indexOf("BOOLEAN")>-1) {
            	param.setDefineParamType(ApiConstants.BOOLEAN);
            }else {
            	param.setDefineParamType(ApiConstants.STRING);
            }
            param.setIsMust(ApiConstants.NO);
            newMdmDataEntityPropList.add(param);
        }
        return newMdmDataEntityPropList;
    }
    private List<ApiDefineParam> getTables( ApiDefine apiDefine){
    	List<ApiDefineParam> newMdmDataEntityPropList = new ArrayList<ApiDefineParam>();
		SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(apiDefine.getDatasourceId());
        if (sysDatasource == null) {
            throw new RuntimeException("数据源不存在！");
        }
        List<SQLStatement> sqlStatements = metaDataContext.parseStatements(sysDatasource, apiDefine.getDefineSql());
        for (SQLStatement sqlStatement : sqlStatements) {
        	GbSchemaStatVisitor schemaStatVisitor = null;
    		if(sysDatasource.getSysDatasourceType().getDatasourceTypeCode().equals(MdmConstants.HIVE)) {
    			schemaStatVisitor = (GbHiveSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(JdbcConstants.HIVE);
    		}else if(sysDatasource.getSysDatasourceType().getDatasourceTypeCode().equals(MdmConstants.ORACLE)) {
    			schemaStatVisitor = (GbOracleSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(JdbcConstants.ORACLE);
    		}else {
    			schemaStatVisitor = (GbSqlSchemaStatVisitor) GbSQLUtils.createSchemaStatVisitor(JdbcConstants.MYSQL);
    		}
            sqlStatement.accept(schemaStatVisitor);
            Map<Name, GbTableStat> tables = schemaStatVisitor.getTables();
            Collection<Column> columns = schemaStatVisitor.getColumns();
            if (Objects.nonNull(tables)) {
                tables.forEach(((name, tableStat) -> {
                    if (tableStat.getSelectCount() > 0) {
                        columns.stream().filter(column -> Objects.equals(column.getTable().toLowerCase(), name.getName().toLowerCase())).forEach(column -> {
                        	if(!column.getName().equals("*")) {
                        		ApiDefineParam param = new ApiDefineParam();
                            	param.setDefineParamName(column.getTable()+"."+column.getName());
                            	param.setDefineParamType(ApiConstants.STRING);
                            	param.setIsMust(ApiConstants.NO);
                                newMdmDataEntityPropList.add(param);
                        	}
                        });
                    }
                }));
            }
        }
       
        return newMdmDataEntityPropList;
    }
    /**
     * 新增接口参数
     *
     * @param apiDefineParam 接口参数
     * @return 结果
     */
    @Override
    public int insertApiDefineParam(ApiDefineParam apiDefineParam)
    {
        return apiDefineParamMapper.insertApiDefineParam(apiDefineParam);
    }

    /**
     * 修改接口参数
     *
     * @param apiDefineParam 接口参数
     * @return 结果
     */
    @Override
    public int updateApiDefineParam(ApiDefineParam apiDefineParam)
    {
        return apiDefineParamMapper.updateApiDefineParam(apiDefineParam);
    }

    /**
     * 批量删除接口参数
     *
     * @param defineParamIds 需要删除的接口参数ID
     * @return 结果
     */
    @Override
    public int deleteApiDefineParamByIds(Long[] defineParamIds)
    {
        return apiDefineParamMapper.deleteApiDefineParamByIds(defineParamIds);
    }

    /**
     * 删除接口参数信息
     *
     * @param defineParamId 接口参数ID
     * @return 结果
     */
    @Override
    public int deleteApiDefineParamById(Long defineParamId)
    {
        return apiDefineParamMapper.deleteApiDefineParamById(defineParamId);
    }
}
