package com.dqms.dsm.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.dqms.common.exception.CustomException;
import com.dqms.dsm.domain.DsmDimensionDetailRelForDownOrUp;
import com.dqms.dsm.domain.vo.DsmDimensionVo;
import com.dqms.dsm.mapper.DsmDimensionDetailRelForDownOrUpMapper;
import com.dqms.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmDimensionDetailRelMapper;
import com.dqms.dsm.domain.DsmDimensionDetailRel;
import com.dqms.dsm.service.IDsmDimensionDetailRelService;

/**
 * 维度字典关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Service
@Slf4j
public class DsmDimensionDetailRelServiceImpl implements IDsmDimensionDetailRelService
{
    @Autowired
    private DsmDimensionDetailRelMapper dsmDimensionDetailRelMapper;

    @Autowired
    private DsmDimensionDetailRelForDownOrUpMapper dsmDimensionDetailRelForDownOrUpMapper;

    /**
     * 查询维度字典关系
     *
     * @param souDimensionDetailId 维度字典关系ID
     * @return 维度字典关系
     */
    @Override
    public DsmDimensionDetailRel selectDsmDimensionDetailRelById(Long souDimensionDetailId)
    {
        return dsmDimensionDetailRelMapper.selectDsmDimensionDetailRelById(souDimensionDetailId);
    }

    /**
     * 查询维度字典关系列表
     *
     * @param dsmDimensionDetailRel 维度字典关系
     * @return 维度字典关系
     */
    @Override
    public List<DsmDimensionDetailRel> selectDsmDimensionDetailRelList(DsmDimensionDetailRel dsmDimensionDetailRel)
    {
        return dsmDimensionDetailRelMapper.selectDsmDimensionDetailRelList(dsmDimensionDetailRel);
    }
    @Override
    public List<DsmDimensionDetailRel> selectDsmDimensionDetailUnRelList(DsmDimensionDetailRel dsmDimensionDetailRel)
    {
        return dsmDimensionDetailRelMapper.selectDsmDimensionDetailUnRelList(dsmDimensionDetailRel);
    }

    @Override
    public List<DsmDimensionDetailRel> selectDsmDimensionDetailRelList_sou(DsmDimensionDetailRel dsmDimensionDetailRel) {
        return dsmDimensionDetailRelMapper.selectDsmDimensionDetailRelList_sou(dsmDimensionDetailRel);
    }

    @Override
    public List<DsmDimensionDetailRelForDownOrUp> selectDsmDimensionDetailRelForDownOrUpList(DsmDimensionVo dsmDimensionVo) {
        return dsmDimensionDetailRelForDownOrUpMapper.selectDsmDimensionDetailRelForDownOrUpList(dsmDimensionVo);
    }


    @Override
    public List<DsmDimensionDetailRelForDownOrUp> selectDsmDimensionDetailRelForDownOrUpList_sou(Long souDimensionId) {
        return dsmDimensionDetailRelForDownOrUpMapper.selectDsmDimensionDetailRelForDownOrUpList_sou(souDimensionId);
    }

    /**
     * 新增维度字典关系
     *
     * @param dsmDimensionDetailRel 维度字典关系
     * @return 结果
     */
    @Override
    public int insertDsmDimensionDetailRel(DsmDimensionDetailRel dsmDimensionDetailRel)
    {
        return dsmDimensionDetailRelMapper.insertDsmDimensionDetailRel(dsmDimensionDetailRel);
    }

    /**
     * 修改维度字典关系
     *
     * @param dsmDimensionDetailRel 维度字典关系
     * @return 结果
     */
    @Override
    public int updateDsmDimensionDetailRel(DsmDimensionDetailRel dsmDimensionDetailRel)
    {
        return dsmDimensionDetailRelMapper.updateDsmDimensionDetailRel(dsmDimensionDetailRel);
    }

    /**
     * 批量删除维度字典关系
     *
     * @param souDimensionDetailIds 需要删除的维度字典关系ID
     * @return 结果
     */
    @Override
    public int deleteDsmDimensionDetailRelByIds(Long[] souDimensionDetailIds)
    {
        return dsmDimensionDetailRelMapper.deleteDsmDimensionDetailRelByIds(souDimensionDetailIds);
    }
    

    /**
     * 删除维度字典关系信息
     *
     * @param souDimensionDetailId 维度字典关系ID
     * @return 结果
     */
    @Override
    public int deleteDsmDimensionDetailRelById(Long souDimensionDetailId)
    {
        return dsmDimensionDetailRelMapper.deleteDsmDimensionDetailRelById(souDimensionDetailId);
    }
    @Override
    public int deleteDsmDimensionDetailRelByPk(DsmDimensionDetailRel dsmDimensionDetailRel)
    {
        return dsmDimensionDetailRelMapper.deleteDsmDimensionDetailRelByPk(dsmDimensionDetailRel);
    }

    /**
     * 导入维度字典关系
     * @param dsmDimensionDetailRelList
     * @param isUpdateSupport
     * @param souDimensionId
     * @return
     */
    @Override
    public String importDsmDimensionDetailRel(List<DsmDimensionDetailRel> dsmDimensionDetailRelList, boolean isUpdateSupport, Long souDimensionId) {
        if(StringUtils.isNull(dsmDimensionDetailRelList) || dsmDimensionDetailRelList.size()==0){
            throw new CustomException("导入任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        Map<Long, String> sou_tar = new HashMap<>();//验证不会出现重复导入
        Map<Long, DsmDimensionDetailRel> tarMap = new HashMap<>();//验证目标字典数据存在
        Map<Long, DsmDimensionDetailRel> souMap = new HashMap<>();//验证原始字典数据存在

        DsmDimensionDetailRel dsmDimensionDetailRel = new DsmDimensionDetailRel();
        List<DsmDimensionDetailRel> list_tar = this.selectDsmDimensionDetailUnRelList(dsmDimensionDetailRel);
        tarMap = list_tar.stream().collect(Collectors.toMap(DsmDimensionDetailRel::getTarDimensionDetailId, Function.identity(), (key1, key2) -> key2));

        if(souDimensionId != null){
            dsmDimensionDetailRel.setSouDimensionId(souDimensionId);
        }
        List<DsmDimensionDetailRel> list_sou = this.selectDsmDimensionDetailRelList_sou(dsmDimensionDetailRel);
        souMap = list_sou.stream().collect(Collectors.toMap(DsmDimensionDetailRel::getSouDimensionDetailId, Function.identity(), (key1, key2) -> key2));

        //1.将excel的list整合到sou_tar中，校验出所有的错误信息
        for(DsmDimensionDetailRel vo : dsmDimensionDetailRelList){
            try{
                boolean checkFlag = true;
                Long souDimensionDetailId = vo.getSouDimensionDetailId();
                if(souDimensionDetailId==null){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、维度字典对象关系 " + souDimensionDetailId + " 原始字典id为空");
                    continue;
                }else if(souMap.get(souDimensionDetailId)==null){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、维度字典对象关系 " + souDimensionDetailId + " 原始字典id不存在");
                    continue;
                }

                Long tarDimensionDetailId = vo.getTarDimensionDetailId();
                if(tarDimensionDetailId==null){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、维度字典对象关系 " + tarDimensionDetailId + " 目标字典id为空");
                    continue;
                }else if(tarMap.get(tarDimensionDetailId)==null){
                    failureNum++;
                    checkFlag = false;
                    failureMsg.append("<br/>" + failureNum + "、维度字典对象关系 " + tarDimensionDetailId + " 目标字典id不存在");
                    continue;
                }

                if(checkFlag){
                    String s = sou_tar.get(souDimensionDetailId);
                    String value = "@"+tarDimensionDetailId.toString()+"@";//防止10与100当作重复处理
                    if(s==null||s.length()==0){
                        sou_tar.put(souDimensionDetailId,value);
                    }else{
                        if(s.contains(value)){
                            failureNum++;
                            checkFlag = false;
                            failureMsg.append("<br/>" + failureNum + "、维度字典对象关系 " + souDimensionDetailId +"+"+ tarDimensionDetailId + " 原始字典id+目标字典id重复出现");
                            continue;
                        }else {
                            sou_tar.put(souDimensionDetailId, s+","+value);
                        }
                    }
                }
            }catch (Exception e){
                failureNum++;
                String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 导入失败：",failureNum, vo.getSouDetailName());
                failureMsg.append(msg + e.getMessage());
                //log.error(msg, e);
            }
        }
        //2.将sou_tar数据添加到数据库 因为不是一对一的关系，更新=删除+新增
        Iterator<Map.Entry<Long, String>> iterator = sou_tar.entrySet().iterator();
        for(Map.Entry<Long, String> map : sou_tar.entrySet()){
            try {
                Long souId = map.getKey();
                DsmDimensionDetailRel dddrVo = new DsmDimensionDetailRel();
                dddrVo.setSouDimensionDetailId(souId);
                List<DsmDimensionDetailRel> dsmDimensionDetailRelsList = this.selectDsmDimensionDetailRelList(dddrVo);
                if(dsmDimensionDetailRelsList==null||dsmDimensionDetailRelsList.size()==0){
                    String[] split = map.getValue().split(",");
                    for (String s : split){
                        long tarId = Long.parseLong(s.trim().replaceAll("@", ""));
                        DsmDimensionDetailRel newVo = new DsmDimensionDetailRel();
                        newVo.setSouDimensionDetailId(souId);
                        newVo.setTarDimensionDetailId(tarId);
                        this.insertDsmDimensionDetailRel(newVo);

                        successNum++;
                        String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 导入成功",successNum, souId+"+"+tarId);
                        successMsg.append(msg);
                    }
                }else if(isUpdateSupport){
                    this.deleteDsmDimensionDetailRelById(souId);
                    String[] split = map.getValue().split(",");
                    for (String s : split){
                        long tarId = Long.parseLong(s.trim().replaceAll("@", ""));
                        DsmDimensionDetailRel newVo = new DsmDimensionDetailRel();
                        newVo.setSouDimensionDetailId(souId);
                        newVo.setTarDimensionDetailId(tarId);
                        this.insertDsmDimensionDetailRel(newVo);
                        successNum++;
                        String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 更新成功",successNum, map.getKey());
                        successMsg.append(msg);
                    }
                }else{
                    failureNum++;
                    String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 名称已存在",failureNum, map.getKey());
                    failureMsg.append(msg);
                    continue;
                }
            }catch (Exception e){
                failureNum++;
                String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 导入失败：",failureNum, map.getKey());
                failureMsg.append(msg + e.getMessage());
                //log.error(msg, e);
            }
        }

        if (failureNum > 0)
        {
            String msg = StringUtils.format("很抱歉，导入失败！共 {} 条数据不正确，错误如下：",failureNum);
            failureMsg.insert(0,msg);
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            String msg = StringUtils.format("恭喜您，数据已全部导入成功！共 {} 条，数据如下：",successNum);
            successMsg.insert(0,msg);
        }
        return successMsg.toString();
    }

    @Override
    public String importDsmDimensionDetailRelForDownOrUp(List<DsmDimensionDetailRelForDownOrUp> dsmDimensionDetailRelForDownOrUplist) {
        if(StringUtils.isNull(dsmDimensionDetailRelForDownOrUplist) || dsmDimensionDetailRelForDownOrUplist.size()==0){
            throw new CustomException("导入任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int n=0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        try {

                for (int i=0;i < dsmDimensionDetailRelForDownOrUplist.size();i++) {
                    n=1;
                    DsmDimensionDetailRelForDownOrUp dsmDimensionDetailRelForDownOrUp = new DsmDimensionDetailRelForDownOrUp();
                    dsmDimensionDetailRelForDownOrUp = dsmDimensionDetailRelForDownOrUplist.get(i);
                    long d = dsmDimensionDetailRelForDownOrUpMapper.getIfHaveRelId(dsmDimensionDetailRelForDownOrUp);
                    if (d < 1) {
                        DsmDimensionDetailRel newVo = new DsmDimensionDetailRel();
                        Long souId=dsmDimensionDetailRelForDownOrUpMapper.getSouId(dsmDimensionDetailRelForDownOrUp);
                        Long tarId=dsmDimensionDetailRelForDownOrUpMapper.getTarId(dsmDimensionDetailRelForDownOrUp);
                        if(souId==null){
                            failureNum++;
                            String errmsg=dsmDimensionDetailRelForDownOrUp.getSouSystemName()+" "+dsmDimensionDetailRelForDownOrUp.getSouSysDictTypeCode()+" "+dsmDimensionDetailRelForDownOrUp.getSouSysDictCode();
                            String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 源标准不存在",failureNum, errmsg);
                            failureMsg.append(msg);
                            continue;
                        }else if(tarId==null){
                            failureNum++;
                            String errmsg=dsmDimensionDetailRelForDownOrUp.getTarSystemName()+" "+dsmDimensionDetailRelForDownOrUp.getTarSysDictTypeCode()+" "+dsmDimensionDetailRelForDownOrUp.getTarSysDictCode();
                            String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 目标标准不存在",failureNum, errmsg);
                            failureMsg.append(msg);
                            continue;
                        }
                        else{
                            newVo.setSouDimensionDetailId(souId);
                            newVo.setTarDimensionDetailId(tarId);
                            this.insertDsmDimensionDetailRel(newVo);
                            successNum++;
                            String sucmsg=dsmDimensionDetailRelForDownOrUp.getSouSystemName()+" "+dsmDimensionDetailRelForDownOrUp.getSouSysDictTypeCode()+" "+dsmDimensionDetailRelForDownOrUp.getSouSysDictCode()+" "+dsmDimensionDetailRelForDownOrUp.getTarSystemName()+" "+dsmDimensionDetailRelForDownOrUp.getTarSysDictTypeCode()+" "+dsmDimensionDetailRelForDownOrUp.getTarSysDictCode();

                            String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 导入成功", successNum, sucmsg);
                            successMsg.append(msg);
                        }
                    }else{
                        failureNum++;
                        String errmsg=dsmDimensionDetailRelForDownOrUp.getSouSystemName()+" "+dsmDimensionDetailRelForDownOrUp.getSouSysDictTypeCode()+" "+dsmDimensionDetailRelForDownOrUp.getSouSysDictCode()+" "+dsmDimensionDetailRelForDownOrUp.getTarSystemName()+" "+dsmDimensionDetailRelForDownOrUp.getTarSysDictTypeCode()+" "+dsmDimensionDetailRelForDownOrUp.getTarSysDictCode();
                        String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 数据已存在",failureNum, errmsg);
                        failureMsg.append(msg);
                        continue;
                    }


                }
            }catch (Exception e){
                failureNum++;
                n+=1;
                String msg = StringUtils.format("<br/>{}、维度字典对象关系 {} 导入失败：",failureNum, "第"+n+"条数据异常");
                failureMsg.append(msg + e.getMessage());
                //log.error(msg, e);
            }

        if (failureNum > 0)
        {
            String msg = StringUtils.format("很抱歉，导入失败！共 {} 条数据不正确，错误如下：",failureNum);
            failureMsg.insert(0,msg);
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            String msg = StringUtils.format("恭喜您，数据已全部导入成功！共 {} 条，数据如下：",successNum);
            successMsg.insert(0,msg);
        }

        return successMsg.toString();
    }


}

