package com.dqms.dsm.service;

import java.util.List;
import com.dqms.dsm.domain.DsmDimensionDetail;
import com.dqms.dsm.domain.vo.DsmDimensionDetailVo;
import com.dqms.dsm.domain.vo.DsmDimensionDetailVo2;
import com.dqms.dsm.domain.vo.DsmDimensionVo;

/**
 * 维度明细Service接口
 * 
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface IDsmDimensionDetailService 
{
    /**
     * 查询维度明细
     * 
     * @param dimensionDetailId 维度明细ID
     * @return 维度明细
     */
    public DsmDimensionDetail selectDsmDimensionDetailById(Long dimensionDetailId);
    public DsmDimensionDetail getDsmDimensionDetailById(Long dimensionDetailId);
    /**
     * 查询维度明细列表
     * 
     * @param dsmDimensionDetail 维度明细
     * @return 维度明细集合
     */
    public List<DsmDimensionDetail> selectDsmDimensionDetailList(DsmDimensionDetail dsmDimensionDetail);
    public List<DsmDimensionDetailVo> selectDsmDimensionDetailVoList(DsmDimensionVo dsmDimensionVo);

    /**
     * 新增维度明细
     * 
     * @param dsmDimensionDetail 维度明细
     * @return 结果
     */
    public int insertDsmDimensionDetail(DsmDimensionDetail dsmDimensionDetail);

    /**
     * 修改维度明细
     * 
     * @param dsmDimensionDetail 维度明细
     * @return 结果
     */
    public int updateDsmDimensionDetail(DsmDimensionDetail dsmDimensionDetail);

    /**
     * 批量删除维度明细
     * 
     * @param dimensionDetailIds 需要删除的维度明细ID
     * @return 结果
     */
    public int deleteDsmDimensionDetailByIds(Long[] dimensionDetailIds);

    /**
     * 删除维度明细信息
     * 
     * @param dimensionDetailId 维度明细ID
     * @return 结果
     */
    public int deleteDsmDimensionDetailById(Long dimensionDetailId);

    /**
     * 删除维度明细信息
     *
     * @param dimensionId 维度ID
     * @return 结果
     */
    public int deleteDsmDimensionDetailByDimensionId(Long dimensionId);

    /**
     * 导入维度字典明细
     * @param dsmDimensionDetailVoList
     * @param isUpdateSupport
     * @return
     */
    public String importDsmDimensionDetail(List<DsmDimensionDetailVo2> dsmDimensionDetailVoList, boolean isUpdateSupport);
}
