package com.dqms.mdm.util.StrategyType;

import base.BaseJunit;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.common.constant.MdmConstants;
import com.dqms.mdm.domain.MdmRegistry;
import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
* SqlServerStrategy Tester.
*
* <AUTHOR> name>
* @since <pre>10/13/2021</pre>
* @version 1.0
*/
public class SqlServerStrategyTest extends BaseJunit {
    @Autowired
    SqlServerStrategy sqlServerStrategy;

    @Autowired
    SysDatasourceMapper sysDatasourceMapper;

    @Before
    public void before(){
    }

    @After
    public void after(){
    }

    /**
    *
    * Method: getTableInfo(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void getTableInfo(){//table 成功； view 成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("VW_Core_BondRecords");
        mdmRegistry.setRegDir("dbo");
        mdmRegistry.setDatasourceId(36L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        Map<String, Object> table = sqlServerStrategy.getTableInfo(mdmRegistry, sysDatasource);
        for(Map.Entry<String, Object> map : table.entrySet()){
            System.out.println("key: "+map.getKey()+"; value: "+map.getValue());
        }
        io.jsonwebtoken.lang.Assert.notEmpty(table,"不能为空");
    }

    /**
    *
    * Method: getColumns(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void getColumns(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("change_tables");
        mdmRegistry.setRegDir("cdc");
        mdmRegistry.setDatasourceId(36L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        List<Map<String, Object>> list = sqlServerStrategy.getColumns(mdmRegistry, sysDatasource);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey() + " : " + m.getValue());
            }
            System.out.println("--------------");
        }
        Assert.notEmpty(list,"不能为空");
    }

    /**
    *
    * Method: checkTableExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void checkTableExist(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("change_tables");
        mdmRegistry.setRegDir("cdc");
        mdmRegistry.setDatasourceId(36L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        Boolean aBoolean = sqlServerStrategy.checkTableExist(mdmRegistry, sysDatasource);

        System.out.println("checkTableExist: "+aBoolean);
    }

    /**
    *
    * Method: checkProcedureExist(MdmRegistry mdmRegistry, SysDatasource sysDatasource)
    *
    */
    @Test
    public void checkProcedureExist(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
//        mdmRegistry.setRegName("fn_cdc_get_all_changes_dbo_auth_departments");
//        mdmRegistry.setRegDir("cdc");
        mdmRegistry.setRegName("FN_Bond_GetAllBondBlackAndWhiteList");
        mdmRegistry.setRegDir("dbo");
        mdmRegistry.setDatasourceId(36L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        Boolean aBoolean = sqlServerStrategy.checkProcedureExist(mdmRegistry, sysDatasource);

        System.out.println("checkProcedureExist: "+aBoolean);
    }

    /**
    *
    * Method: getTablesAndViews(SysDatasource sysDatasource, String catalog, String [] types)
    *
    */
    @Test
    public void getTablesAndViews(){//table 成功； view 成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(36L);
//        List<Map<String, Object>> list = sqlServerStrategy.getTablesAndViews(sysDatasource, "dbo", new String[]{"TABLE"});
        List<Map<String, Object>> list = sqlServerStrategy.getTablesAndViews(sysDatasource, "dbo", new String[]{"VIEW"});
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey() + " : " + m.getValue());
            }
            System.out.println("--------------");
        }
        Assert.notEmpty(list,"不能为空");
    }

    /**
    *
    * Method: getProcedures(SysDatasource sysDatasource, String catalog)
    *
    */
    @Test
    public void getProcedures(){//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(36L);
        String catalog= "cdc";

        List<Map<String, Object>> list = sqlServerStrategy.getProcedures(sysDatasource, catalog);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey() + " : " + m.getValue());
            }
            System.out.println("--------------");
        }
//        Assert.notEmpty(list,"不能为空");
    }

    /**
    *
    * Method: getProcedureInfo(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getProcedureInfo(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("sp_dropdiagram");
        mdmRegistry.setRegDir("dbo");
        mdmRegistry.setDatasourceId(36L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        Map<String, Object> procedureInfo = sqlServerStrategy.getProcedureInfo(sysDatasource, mdmRegistry);
        for (Map.Entry m : procedureInfo.entrySet()) {
            System.out.println(m.getKey() + " : " + m.getValue());
        }
    }

    /**
    *
    * Method: excuteByLimit(SysDatasource sysDatasource, String sqlText, int pageSize)
    *
    */
    @Test
    public void excuteByLimit(){//table 成功；view 成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(36L);
        String sql1 = "select * from cdc.change_tables; ";
        String sql2 = "select * from dbo.VM_CORE_PORTFOLIOS_TMP; ";
        Map<Object, Object> map = sqlServerStrategy.excuteByLimit(sysDatasource, sql2, 30);
        for (Map.Entry m : map.entrySet()) {
            System.out.println(m.getKey() + " : " + m.getValue());

            List<Object> list = (List<Object>) m.getValue();
            for (Object o : list){
                System.out.println(o.toString());
            }
            System.out.println("---------------------");
        }
        System.out.println("测试excuteByLimit");
    }

    /**
    *
    * Method: quaryByPage(SysDatasource sysDatasource, String sqlText, int page, int size)
    * 必须有order by
    */
    @Test
    public void quaryByPage(){//table 成功；view 成功 必须有order by
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(36L);
        String sql1 = "select * from cdc.change_tables a order by a.object_id; ";
        String sql2 = "select * from dbo.VM_CORE_PORTFOLIOS_TMP a order by a.PortfolioNo; ";
        List<Map<String, Object>> list = sqlServerStrategy.quaryByPage(sysDatasource, sql1, 3, 4);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey() + " : " + m.getValue());
            }
            System.out.println("--------------");
        }
        System.out.println("测试quaryByPage");
    }

    /**
    *
    * Method: getSqlCount(SysDatasource sysDatasource, String sqlText)
    *
    */
    @Test
    public void getSqlCount(){//table 成功；view 成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(36L);
        String sql1 = "select * from cdc.change_tables; ";
        String sql2 = "select * from dbo.VM_CORE_PORTFOLIOS_TMP; ";
        int sqlCount = sqlServerStrategy.getSqlCount(sysDatasource, sql2);
        System.out.println("测试getSqlCount："+sqlCount);

    }

    /**
    *
    * Method: getTableCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getTableCreate(){
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("Auth_Applications");
        mdmRegistry.setRegDir("dbo");
        mdmRegistry.setDatasourceId(39L);//SIT
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        String procedureCreate = sqlServerStrategy.getTableCreate(sysDatasource, mdmRegistry);

        System.out.println("getTableCreate: "+procedureCreate);
    }

    /**
    *
    * Method: getProcedureCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getProcedureCreate(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("FN_Bond_GetAllBondBlackAndWhiteList");
        mdmRegistry.setRegDir("dbo");
        mdmRegistry.setDatasourceId(39L);//SIT
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        String procedureCreate = sqlServerStrategy.getProcedureCreate(sysDatasource, mdmRegistry);
        System.out.println("procedureCreate: "+procedureCreate);
    }

    /**
    *
    * Method: getViewCreate(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getViewCreate(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("allocation_units");
        mdmRegistry.setRegDir("sys");
        mdmRegistry.setDatasourceId(36L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        String procedureCreate = sqlServerStrategy.getViewCreate(sysDatasource, mdmRegistry);
        System.out.println("getViewCreate: "+procedureCreate);
    }

    /**
    *
    * Method: getDatabaseInfos(SysDatasource sysDatasource, MdmRegistry mdmRegistry)
    *
    */
    @Test
    public void getDatabaseInfos(){//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setDatasourceId(36L);

        mdmRegistry.setRegDir("dbo");
        mdmRegistry.setMetaTypes(new Integer[]{MdmConstants.TABLE,MdmConstants.VIEW,MdmConstants.PROCEDURE});
//        mdmRegistry.setMetaTypes(new Integer[]{MdmConstants.VIEW});
//        mdmRegistry.setMetaTypes(new Integer[]{MdmConstants.PROCEDURE});

        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        Map<String, List> mapList = sqlServerStrategy.getDatabaseInfos(sysDatasource, mdmRegistry);
        for(Map.Entry map : mapList.entrySet()){
            Object key = map.getKey();
            System.out.println("key: "+key);
            List<Map<String, Object>> valueList = (List<Map<String, Object>>) map.getValue();
            for (Map<String, Object> mm : valueList){
                for (Map.Entry<String, Object> m : mm.entrySet()){
                    System.out.println("key: "+m.getKey()+"; value: "+m.getValue());
                }
                System.out.println("------------------------------");
            }
        }
        io.jsonwebtoken.lang.Assert.notEmpty(mapList,"不能为空");

    }

    /**
    *
    * Method: parseStatements(SysDatasource sysDatasource, String sqlText)
    *
    */
    @Test
    public void parseStatements(){//table 成功, view 成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(36L);
        String sql1 = "select * from cdc.change_tables; ";
        String sql2 = "select * from dbo.VM_CORE_PORTFOLIOS_TMP; ";
        List<SQLStatement> sqlStatements = sqlServerStrategy.parseStatements(sysDatasource, sql1);
        for (SQLStatement s : sqlStatements){
            System.out.println(s.toString());
        }
    }

    /**
    *
    * Method: createSchemaStatVisitor(SysDatasource sysDatasource)
    *
    */
    @Test
    public void createSchemaStatVisitor(){
    //TODO: Test goes here...
    }

    @Test
    public void createUser(){//未完成
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(39L);
        Boolean testUser = sqlServerStrategy.createUser(sysDatasource, "testUser", "1qaz@WSX");
        System.out.println("createUser："+testUser);
    }

    @Test
    public void grantBytable(){//未完成
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(39L);
        Boolean testUser = sqlServerStrategy.grantBytable(sysDatasource, "testUser", "dbo.Auth_AccountFunctions;dbo.Cache_CounterPartyPaymentTimes", null);
        System.out.println("grantBytable："+testUser);
    }

    @Test
    public void dropUser(){//未完成
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(39L);
        Boolean testUser = sqlServerStrategy.dropUser(sysDatasource, "testUser");
        System.out.println("grantBytable："+testUser);
    }
}
