package com.dqms.dam.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dam.domain.DamMind;
import com.dqms.dam.service.IDamMindService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 我的脑图Controller
 *
 * <AUTHOR>
 * @date 2021-07-01
 */
@RestController
@RequestMapping("/dam/damMind")
public class DamMindController extends BaseController
{
    @Autowired
    private IDamMindService damMindService;

    /**
     * 查询我的脑图列表
     */
    @PreAuthorize("@ss.hasPermi('dam:damMind:list')")
    @GetMapping("/list")
    public TableDataInfo list(DamMind damMind)
    {
        startPage();
        List<DamMind> list = damMindService.selectDamMindList(damMind);
        return getDataTable(list);
    }

    /**
     * 导出我的脑图列表
     */
    @PreAuthorize("@ss.hasPermi('dam:damMind:export')")
    @Log(title = "我的脑图", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DamMind damMind)
    {
        List<DamMind> list = damMindService.selectDamMindList(damMind);
        ExcelUtil<DamMind> util = new ExcelUtil<DamMind>(DamMind.class);
        return util.exportExcel(list, "damMind");
    }

    /**
     * 获取我的脑图详细信息
     */
    @PreAuthorize("@ss.hasPermi('dam:damMind:query')")
    @GetMapping(value = "/{mindId}")
    public AjaxResult getInfo(@PathVariable("mindId") Long mindId)
    {
        return AjaxResult.success(damMindService.selectDamMindById(mindId));
    }

    /**
     * 新增我的脑图
     */
    @PreAuthorize("@ss.hasPermi('dam:damMind:add')")
    @Log(title = "我的脑图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DamMind damMind)
    {
        return toAjax(damMindService.insertDamMind(damMind));
    }

    /**
     * 修改我的脑图
     */
    @PreAuthorize("@ss.hasPermi('dam:damMind:edit')")
    @Log(title = "我的脑图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DamMind damMind)
    {
        return toAjax(damMindService.updateDamMind(damMind));
    }

    /**
     * 删除我的脑图
     */
    @PreAuthorize("@ss.hasPermi('dam:damMind:remove')")
    @Log(title = "我的脑图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{mindIds}")
    public AjaxResult remove(@PathVariable Long[] mindIds)
    {
        return toAjax(damMindService.deleteDamMindByIds(mindIds));
    }
}
