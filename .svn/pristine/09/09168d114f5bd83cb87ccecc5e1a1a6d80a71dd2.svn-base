<template>
  <div class="app-container" style="padding:0px;">
    <el-row :gutter="20">
      <el-col :span="20" :xs="24"
        ><!-- style="border-left: 1px solid #304156;" -->
        <el-row style="text-align:center;margin:0 auto;margin-top: 60px;">
          <div style="margin-top: 15px;margin-bottom: 15px;">
            <el-link type="primary" style="font-size: 30px;"
              >数据资产检索</el-link
            >
          </div>
          <div
            style="margin-top: 15px;width: 800px;text-align:center;margin:0 auto;"
          >
            <div style="display:inline;float:left;width: 590px;">
              <el-input
                placeholder="请输入内容"
                v-model="queryParams.remark"
                class="input-with-select"
                style="border: 1px solid #1890ff;border-radius:5px;"
                clearable
              >
                <el-select
                  v-model="queryParams.assetsType"
                  slot="prepend"
                  placeholder="全部"
                  style="background-color: #1890ff;color: #FFF;padding-right: 10px;width: 100px;"
                  value="0"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="自定义" value="DEFAULT"></el-option>
                  <el-option label="元数据" value="MDM"></el-option>
                  <el-option label="基础标准" value="STANDARD"></el-option>
                  <el-option label="指标标准" value="INDEX"></el-option>
                </el-select>
              </el-input>
            </div>
            <div style="display:inline;">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="handleQuery"
                round
                >搜索</el-button
              >
            </div>
            <div style="display:inline;">
              <el-button icon="el-icon-refresh" @click="quertyReset" round
                >重置</el-button
              >
            </div>
          </div>
          <div style="margin-top: 15px;" v-show="false">
            <el-link>搜索历史：</el-link>
            <el-link type="info" style="margin-left: 5px;">{{
              searchHis
            }}</el-link>
          </div>
        </el-row>
        <el-row style="text-align:left;margin:0 auto;margin-top: 10px;">
          <div
            style="margin-top: 15px;"
            v-for="(item, index) in damAssetsList"
            :key="'damAssets' + index"
          >
            <el-col :span="12" :xs="12">
              <div style="padding: 14px;">
                <!--		        <el-link @click="handleShow(item)"><span style="color: rgb(24, 144, 255);font-size:22px;">{{item.assetsName}}</span></el-link>-->
                <router-link
                  :to="'/assetsSearch/assets/detail/' + item.damAssetsId"
                  class="link-type"
                  v-html="item.assetsName"
                ></router-link>
                <span style="color: #6B6C6D;font-size:12px;font-style: italic;"
                  >({{ item.assetsCode }})</span
                >
                <span style="color: #6B6C6D;font-size:12px;font-style: italic;"
                  >[{{ assetsTypeFormat(item, "assetsType") }}]</span
                >
              </div>
            </el-col>
            <el-col :span="12" :xs="12" style="padding: 14px;">
              <div style="padding-right: 20px;text-align:right;">
                <el-button
                  size="mini"
                  icon="el-icon-star-on"
                  @click="handleAdd(item)"
                  round
                  >评价</el-button
                >
                <el-button
                  size="mini"
                  icon="el-icon-bell"
                  @click="handleSubscribe(item)"
                  v-if="item.assetsSubscribeId == null ? true : false"
                  round
                  >关注</el-button
                >
                <el-button
                  size="mini"
                  icon="el-icon-message-solid"
                  @click="delSubscribe(item)"
                  v-if="item.assetsSubscribeId == null ? false : true"
                  round
                  >取关</el-button
                >
              </div>
            </el-col>
            <el-col :span="24" :xs="24">
              <div
                style="padding-left: 4px;font-size:14px;max-height:42px;line-height: 21px;overflow:hidden;text-overflow:ellipsis;display: block;word-wrap:break-word;text-indent:20px"
              >
                <span
                  style="color: #6B6C6D;font-family:Hiragino Sans GB"
                  v-html="item.remark"
                ></span>
              </div>
              <el-divider
                ><el-rate
                  v-model="item.grade"
                  disabled
                  show-score
                  text-color="#ff9900"
                  >{{ item.grade }}</el-rate
                ></el-divider>
            </el-col>
          </div>
        </el-row>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
      <el-col
        :span="4"
        :xs="24"
        style="background-color: rgb(48, 65, 86);padding-left: 10px; padding-right: 0px;height: calc(100vh - 85px);border-left: 1px solid #1890ff;border-radius: 23px;"
      >
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="资产标签" name="1">
            <div class="head-container">
              <el-tree
                :data="assetsClassOptions"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree"
                default-expand-all
                @node-click="handleTreeNodeClick"
              />
            </div>
          </el-collapse-item>
          <el-collapse-item
            title="应用系统"
            name="2"
            style="text-align:center;"
          >
            <el-radio-group v-model="queryParams.systemLabels" size="mini">
              <el-radio
                v-for="item in systemOptions"
                :key="item.systemId"
                :label="item.name"
                :value="item.systemId"
                style="display:block;margin:5px 0px ;"
                border
              ></el-radio>
            </el-radio-group>
          </el-collapse-item>
          <el-collapse-item
            title="数据来源"
            name="3"
            style="text-align:center;"
          >
            <div class="assetsSearch_scrollbar">
               <el-radio-group v-model="queryParams.datasourceLabels" size="mini">
                <el-radio
                  v-for="item in datasourceOptions"
                  :key="item.datasourceId"
                  :label="item.name"
                  :value="item.datasourceId"
                  style="display:block;margin:5px 0px ;"
                  border
                ></el-radio>
              </el-radio-group>
            </div>
          </el-collapse-item>
          <el-collapse-item
            title="资产等级"
            name="4"
            style="text-align:center;"
          >
            <el-radio-group v-model="queryParams.level" size="mini">
              <el-radio
                v-for="dict in levelOptions"
                :key="dict.dictValue"
                :label="dict.dictValue"
                style="display:block;margin:5px 0px ;"
                border
                >{{ dict.dictLabel }}</el-radio
              >
            </el-radio-group>
          </el-collapse-item>
        </el-collapse>
      </el-col>
    </el-row>

    <!-- 添加或修改资产评价对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="资产ID" prop="assetsId" v-show="false">
          <el-input
            v-model="form.assetsId"
            placeholder="请输入资产ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="评分" prop="grade">
          <el-rate v-model="form.grade" style="margin:8px;"></el-rate>
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="showtitle"
      :visible.sync="showopen"
      width="900px"
      append-to-body
    >
      <el-form
        ref="showform"
        :model="showform"
        :rules="rules"
        label-width="80px"
      >
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="资产标签" prop="assetsClassId">
              <treeselect
                v-model="showform.assetsClassId"
                placeholder="请输入标准标签"
                :options="assetsClassOptions"
                :disable-branch-nodes="true"
                :show-count="true"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产类型" prop="assetsType">
              <el-select
                v-model="showform.assetsType"
                placeholder="请选择资产类型 "
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="dict in assetsTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="资产名称" prop="assetsName">
              <el-input
                v-model="showform.assetsName"
                placeholder="请输入资产名称"
                clearable
                :disabled="
                  form.assetsType == '' ||
                  form.assetsType == undefined ||
                  form.assetsType == 'DEFAULT'
                    ? false
                    : true
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产编码" prop="assetsCode">
              <el-input
                v-model="showform.assetsCode"
                placeholder="请输入资产编码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="资产说明" prop="remark">
              <el-input
                v-model="showform.remark"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="管理系统" prop="systemId">
              <el-select
                v-model="showform.systemId"
                placeholder="请选择管理系统 "
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="item in systemOptions"
                  :key="item.systemId"
                  :label="item.name"
                  :value="item.systemId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据来源" prop="datasourceId">
              <el-select
                v-model="showform.datasourceId"
                placeholder="请选择数据来源 "
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="item in datasourceOptions"
                  :key="item.datasourceId"
                  :label="item.name"
                  :value="item.datasourceId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="资产等级" prop="level">
              <el-select
                v-model="showform.level"
                placeholder="请选择资产等级 "
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="dict in levelOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="person">
              <el-input
                v-model="showform.person"
                placeholder="请输入负责人"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label="生效日期" prop="startTime">
              <el-date-picker
                clearable
                size="small"
                style="width:100%"
                v-model="showform.startTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择生效日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期" prop="endTime">
              <el-date-picker
                clearable
                size="small"
                style="width:100%"
                v-model="showform.endTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择失效日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="24">
            <el-form-item label="资产状态">
              <el-radio-group v-model="showform.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-show="false">
            <el-form-item label="关联属性" prop="relId">
              <el-input
                v-model="showform.relId"
                placeholder="请输入关联属性"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="start" align="top">
          <el-col :span="12">
            <el-form-item label-width="0px" :error="uploaderr">
              <div class="upload">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :action="uploadUrl"
                  accept=".jpg,.jpeg,.txt,.zip,.xls,.xlsx,.doc,.docx,.pdf,.PDF"
                  :auto-upload="true"
                  :file-list="fileList"
                  :on-success="handleSuccess"
                  :headers="myHeaders"
                  :limit="1"
                >
                  <el-button size="small" icon="el-icon-upload2" type="danger"
                    >上传附件</el-button
                  >
                </el-upload>
                <div class="uptxt">
                  （支持pdf、word、excel、zip、jpg，文件限制500M以内）
                </div>
              </div>
            </el-form-item>
            <el-form-item label="文档名称" prop="attachment" v-show="false">
              <el-input
                v-model="showform.attachment"
                maxLength="20"
                placeholder="请输入文档名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showcancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  listDamAssets,
  listMyDamAssets,
  getDamAssets,
  delDamAssets,
  addDamAssets,
  updateDamAssets,
  exportDamAssets
} from "@/api/dam/damAssets";
import { treeselect } from "@/api/dam/damAssetsClass";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getToken } from "@/utils/auth";
import { listSystemAll } from "@/api/basic/system";
import { listDatasourceAll } from "@/api/basic/datasource";
import { addDamAssetsEvaluate } from "@/api/dam/damAssetsEvaluate";
import {
  delDamAssetsSubscribe,
  addDamAssetsSubscribe
} from "@/api/dam/damAssetsSubscribe";

export default {
  name: "DamAssets",
  components: {
    Treeselect
  },
  data() {
    return {
      input1: "",
      input2: "",
      input3: "",
      select: "",
      activeName: "1",
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 数据资产表格数据
      damAssetsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      assetsClassOptions: [],
      assetsClassId: "",
      className: "",
      datasourceOptions: [],
      systemOptions: [],
      levelOptions: [],
      assetsTypeOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      searchHis: "无",
      searchHisOld: "无",
      form: { grade: 5 },
      showtitle: "",
      showform: {},
      showopen: false,
      uploading: false,
      uploaderr: "",
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/attachment/upload",
      myHeaders: {
        Authorization: "Bearer " + getToken()
      },
      isfile: true,
      fileList: [],
      statusOptions: [],
      // 表单校验
      rules: {
        assetsId: [
          { required: true, message: "资产ID不能为空", trigger: "blur" }
        ],
        grade: [{ required: true, message: "评分不能为空", trigger: "blur" }]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assetsClassId: null,
        assetsName: null,
        assetsCode: null,
        assetsType: null,
        systemId: null,
        datasourceId: null,
        person: null,
        startTime: null,
        endTime: null,
        level: null,
        relId: null,
        status: null,
        systemLabels: [],
        datasourceLabels: []
      }
    };
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getSystem();
    this.getDatasource();
    this.getDicts("assets_type").then(response => {
      this.assetsTypeOptions = response.data;
    });
    this.getDicts("sys_important_level").then(response => {
      this.levelOptions = response.data;
    });
    this.getDicts("dsm_standard_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("mdm_meta_type").then(response => {
      this.metaTypeOptions = response.data;
    });
  },
  watch: {
    className(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    /** 查询数据资产列表 */
    getList() {
      this.searchHis = this.searchHisOld;
      this.searchHisOld = this.queryParams.remark;
      let systemId = null;
      if(this.systemOptions){
    	  for (let i = 0; i < this.systemOptions.length; i++) {
    	        if (this.queryParams.systemLabels.indexOf(this.systemOptions[i].name) !=-1) {
    	          systemId = this.systemOptions[i].systemId;
    	        }
    	      } 
    	  this.queryParams.systemId = systemId;
      }
      if(this.systemOptions){
	      let datasourceId = null;
	      for (let i = 0; i < this.datasourceOptions.length; i++) {
	        if ( this.queryParams.datasourceLabels.indexOf(this.datasourceOptions[i].name ) != -1) {
	          datasourceId = this.datasourceOptions[i].datasourceId;
	        }
	      }
	      this.queryParams.datasourceId = datasourceId;
      }
      this.loading = true;
      this.queryParams.params = {};
      listMyDamAssets(this.queryParams).then(response => {
        this.damAssetsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 表单重置
    quertyReset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        assetsClassId: null,
        assetsName: null,
        assetsCode: null,
        assetsType: null,
        systemId: null,
        datasourceId: null,
        person: null,
        startTime: null,
        endTime: null,
        level: null,
        relId: null,
        status: null,
        systemLabels: [],
        datasourceLabels: []
      };
    },
    getTreeselect() {
      treeselect().then(response => {
        this.assetsClassOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleTreeNodeClick(data) {
      this.queryParams.assetsClassId = data.id;
      // this.getList();
    },
    handleSuccess(res, file, fileList) {
      // 文件上传成功处理
      this.form.attachment = res.msg;
      //成功后的业务逻辑处理
    },
    /** 获取应用系统 */
    getSystem() {
    	listSystemAll().then(response => {
        this.systemOptions = response.data;
      });
    },
    getDatasource() {
      listDatasourceAll().then(response => {
        this.datasourceOptions = response.data;
      });
    },
    // 应用系统翻译
    systemFormat(row, column) {
      return this.selectLabel(
        this.systemOptions,
        row.systemId,
        "systemId",
        "name"
      );
    },
    // 数据源翻译
    datasourceFormat(row, column) {
      return this.selectLabel(
        this.datasourceOptions,
        row.datasourceId,
        "datasourceId",
        "name"
      );
    },
    // 资产类型字典翻译
    assetsTypeFormat(row, column) {
      return this.selectDictLabel(this.assetsTypeOptions, row.assetsType);
    },
    // 资产等级字典翻译
    levelFormat(row, column) {
      return this.selectDictLabel(this.levelOptions, row.level);
    },
    // 资产状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 元类型字典翻译
    metaTypeFormat(row, column) {
      return this.selectDictLabel(this.metaTypeOptions, row.metaType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    handleAdd(row) {
      this.open = true;
      this.reset();
      this.form.assetsId = row.damAssetsId;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          addDamAssetsEvaluate(this.form).then(response => {
            this.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    handleSubscribe(row) {
      let param = { damAssetsId: row.damAssetsId };
      addDamAssetsSubscribe(param).then(response => {
        this.msgSuccess("关注成功");
        this.open = false;
        this.getList();
      });
    },
    delSubscribe(row) {
      delDamAssetsSubscribe(row.assetsSubscribeId);
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {
        assetsEvaluateId: null,
        assetsId: null,
        grade: 5,
        remark: null
      };
      this.resetForm("form");
    },
    handleShow(row) {
      const damAssetsId = row.damAssetsId || this.ids;
      getDamAssets(damAssetsId).then(response => {
        this.showform = response.data;
        this.showopen = true;
        this.showtitle = "资产详情";
      });
    },
    showcancel() {
      this.showopen = false;
    }
  }
};
</script>
<style>
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
.el-collapse-item__header {
  background-color: rgb(48, 65, 86);
  color: #fff;
  border-radius: 4px;
  padding-left: 10px;
}
.el-collapse-item {
  border: 1px;
}
.el-radio--mini {
  width: 100%;
}
.el-radio {
  margin-left: 0px;
}
.assetsSearch_scrollbar{
  height:400px;
  overflow:scroll;
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.assetsSearch_scrollbar::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
</style>
