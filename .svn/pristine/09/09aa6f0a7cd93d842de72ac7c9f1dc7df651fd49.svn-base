import request from '@/utils/request'

// 查询安全分类列表
export function listDscEntityPropSafeClass(query) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/list',
    method: 'get',
    params: query
  })
}

export function getListDscEntityPropSafeClass(query) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/getDscEntityPropSafeClassList',
    method: 'get',
    params: query
  })
}

export function showClassLevel(query) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/showClassLevel',
    method: 'get',
    params: query
  })
}


// 查询安全分类详细
export function getDscEntityPropSafeClass(safeClassId) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/' + safeClassId,
    method: 'get'
  })
}
// 查询安全分类详细
export function getDscEntityPropSafeClassById(safeClassId) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/getDscEntityPropSafeClassById',
    method: 'get'
  })
}

// 新增安全分类
export function addDscEntityPropSafeClass(data) {
  return request({
    url: '/dsc/DscEntityPropSafeClass',
    method: 'post',
    data: data
  })
}

// 修改安全分类
export function updateDscEntityPropSafeClass(data) {
  return request({
    url: '/dsc/DscEntityPropSafeClass',
    method: 'put',
    data: data
  })
}

// 删除安全分类
export function delDscEntityPropSafeClass(safeClassId) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/' + safeClassId,
    method: 'delete'
  })
}

// 导出安全分类
export function exportClass(query) {
  return request({
    url: '/dsc/DscEntityPropSafeClass/export',
    method: 'get',
    params: query
  })
}

//获取一级节点
export function getGrandList(query) {
  return request({
    url: '/dsc/dscEntityPropClass/getGrandList',
    method: 'get',
    params: query
  })
}
//获取二级节点
export function getParentList(query) {
  return request({
    url: '/dsc/dscEntityPropClass/getParentList',
    method: 'get',
    params: query
  })
}
//获取三级节点
export function getEntityPropClassList(query) {
  return request({
    url: '/dsc/dscEntityPropClass/getEntityPropClassList',
    method: 'get',
    params: query
  })
}
