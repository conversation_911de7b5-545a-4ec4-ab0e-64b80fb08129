package com.dqms.common.utils;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
//import org.apache.hadoop.conf.Configuration;
//import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

public class DataSourceUtils {
    private static final Logger log = LoggerFactory.getLogger(DataSourceUtils.class);
    private static volatile Map<String, DataSource> datasourceMap = new HashMap();

    /*** 创建连接池对象
     *
     * @param hikariConfig
     * @return
     */
    public static synchronized void add(HikariConfig hikariConfig) {
        DataSource ds = datasourceMap.get(hikariConfig.getDataSourceProperties().get("datasourceId").toString());
        if(null == ds){
        	HikariDataSource dataSource = new HikariDataSource(hikariConfig);
        	datasourceMap.put(hikariConfig.getDataSourceProperties().get("datasourceId").toString(),dataSource);
        }
    }
    public static void remove(String  datasourceId){
        log.info("移除数据源开始："+datasourceId);
        datasourceMap.remove(datasourceId);
        log.info("移除数据源结束："+datasourceId);
    }

    public static void removeAll(Long[] datasourceIds){
        log.info("移除数据源开始："+datasourceIds);
        for (int i = 0; i < datasourceIds.length; i++) {
            datasourceMap.remove(datasourceIds[i].toString());
        }
        log.info("移除数据源结束："+datasourceIds);
    }

    public static void update(HikariConfig hikariConfig){
        log.info("更新数据源开始");
        remove(hikariConfig.getDataSourceProperties().get("datasourceId").toString());
        add(hikariConfig);
        log.info("更新数据源结束");
    }
    public static DataSource getDataSource(HikariConfig hikariConfig){

        String datasourceId = hikariConfig.getDataSourceProperties().get("datasourceId").toString();
        DataSource ds = datasourceMap.get(datasourceId);
        if(null == ds){
            add(hikariConfig);
            ds = datasourceMap.get(datasourceId);
            log.info("数据源为空");
        }else {
            log.info("数据源不为空");
        }
        return ds;
    }

    public static void loginKerberos(String kerberosUser,String kerberosPath) {
//        Configuration conf  = new Configuration();
//        conf.set("hadoop.security.authentication", "Kerberos");
//        UserGroupInformation.setConfiguration(conf);
//        try {
//            UserGroupInformation.loginUserFromKeytab(kerberosUser, kerberosPath);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    // 定义得到连接对象的方法
    public static Connection getConnection(String queun) throws SQLException {
        return datasourceMap.get(queun).getConnection();
    }

    // 定义关闭资源的方法
    public static void close(Connection conn, Statement stmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
            }
        }
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
            }
        }
    }

    // 重载关闭方法
    public static void close(Connection conn, Statement stmt) {
        close(conn, stmt, null);
    }


}
