package com.dqms.mdm.controller;

import java.util.List;

import com.dqms.common.constant.UserConstants;
import com.dqms.common.core.domain.entity.SysUser;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.mdm.domain.MdmTheme;
import com.dqms.mdm.service.IMdmThemeService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 主题管理Controller
 *
 * <AUTHOR>
 * @date 2021-03-15
 */
@RestController
@RequestMapping("/mdm/mdmTheme")
public class MdmThemeController extends BaseController
{
    @Autowired
    private IMdmThemeService mdmThemeService;

    /**
     * 查询主题管理列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:list')")
    @GetMapping("/list")
    public TableDataInfo list(MdmTheme mdmTheme)
    {
        startPage();
        List<MdmTheme> list = mdmThemeService.selectMdmThemeList(mdmTheme);
        return getDataTable(list);
    }

    @GetMapping("/findAll")
    public AjaxResult findAll()
    {
        return AjaxResult.success(mdmThemeService.selectMdmThemeAll());
    }

    /**
     * 导出主题管理列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:export')")
    @Log(title = "主题管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(MdmTheme mdmTheme)
    {
        List<MdmTheme> list = mdmThemeService.selectMdmThemeList(mdmTheme);
        ExcelUtil<MdmTheme> util = new ExcelUtil<MdmTheme>(MdmTheme.class);
        return util.exportExcel(list, "主题数据");
    }

    @Log(title = "主题管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<MdmTheme> util = new ExcelUtil<MdmTheme>(MdmTheme.class);
        List<MdmTheme> themeList = util.importExcel(file.getInputStream());
        String message = mdmThemeService.importTheme(themeList, updateSupport);
        return AjaxResult.success(message);
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<MdmTheme> util = new ExcelUtil<MdmTheme>(MdmTheme.class);
        return util.importTemplateExcel("主题数据");
    }

    /**
     * 获取主题管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:query')")
    @GetMapping(value = "/{themeId}")
    public AjaxResult getInfo(@PathVariable("themeId") Long themeId)
    {
        AjaxResult ajax = AjaxResult.success();
        if (StringUtils.isNotNull(themeId)){
            ajax.put(AjaxResult.DATA_TAG, mdmThemeService.selectMdmThemeById(themeId));
            ajax.put("sysPersonIds",mdmThemeService.selectuserIdsByThemeId(themeId));
        }
        return ajax;
    }

    /**
     * 新增主题管理
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:add')")
    @Log(title = "主题管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MdmTheme mdmTheme)
    {
        if (UserConstants.NOT_UNIQUE.equals(mdmThemeService.checkThemeNameUnique(mdmTheme)))
        {
            return AjaxResult.error("新增主题'" + mdmTheme.getThemeName() + "'失败，主题名称已存在");
        }
        if (UserConstants.NOT_UNIQUE.equals(mdmThemeService.checkThemeCodeUnique(mdmTheme)))
        {
            return AjaxResult.error("新增主题'" + mdmTheme.getThemeName() + "'失败，主题编号已存在");
        }
        return toAjax(mdmThemeService.insertMdmTheme(mdmTheme));
    }

    /**
     * 修改主题管理
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:edit')")
    @Log(title = "主题管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MdmTheme mdmTheme)
    {
        if (UserConstants.NOT_UNIQUE.equals(mdmThemeService.checkThemeNameUnique(mdmTheme)))
        {
            return AjaxResult.error("修改主题'" + mdmTheme.getThemeName() + "'失败，主题名称已存在");
        }
        if (UserConstants.NOT_UNIQUE.equals(mdmThemeService.checkThemeCodeUnique(mdmTheme)))
        {
            return AjaxResult.error("新增主题'" + mdmTheme.getThemeName() + "'失败，主题编号已存在");
        }
        return toAjax(mdmThemeService.updateMdmTheme(mdmTheme));
    }

    /**
     * 删除主题管理
     */
    @PreAuthorize("@ss.hasPermi('mdm:mdmTheme:remove')")
    @Log(title = "主题管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{themeIds}")
    public AjaxResult remove(@PathVariable Long[] themeIds)
    {
        return toAjax(mdmThemeService.deleteMdmThemeByIds(themeIds));
    }
}
