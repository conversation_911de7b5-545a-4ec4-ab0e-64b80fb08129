<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.system.mapper.SysUserTaskGroupMapper">
    
    <resultMap type="SysUserTaskGroup" id="SysUserTaskGroupResult">
        <result property="userId"    column="user_id"    />
        <result property="taskGroupId"    column="task_group_id"    />
    </resultMap>

    <sql id="selectSysUserTaskGroupVo">
        select user_id, task_group_id from sys_user_task_group
    </sql>

    <select id="selectSysUserTaskGroupList" parameterType="SysUserTaskGroup" resultMap="SysUserTaskGroupResult">
        <include refid="selectSysUserTaskGroupVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="taskGroupId != null "> and task_group_id = #{taskGroupId}</if>
        </where>
    </select>
    
    <select id="selectSysUserTaskGroupById" parameterType="Long" resultMap="SysUserTaskGroupResult">
        <include refid="selectSysUserTaskGroupVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertSysUserTaskGroup" parameterType="SysUserTaskGroup">
        insert into sys_user_task_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="taskGroupId != null">task_group_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="taskGroupId != null">#{taskGroupId},</if>
         </trim>
    </insert>
    
	<insert id="batchUserTaskGroup">
		insert into sys_user_task_group(user_id, task_group_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.userId},#{item.taskGroupId})
		</foreach>
	</insert>
	
    <update id="updateSysUserTaskGroup" parameterType="SysUserTaskGroup">
        update sys_user_task_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskGroupId != null">task_group_id = #{taskGroupId},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteSysUserTaskGroupById" parameterType="Long">
        delete from sys_user_task_group where user_id = #{userId}
    </delete>

    <delete id="deleteSysUserTaskGroupByIds" parameterType="String">
        delete from sys_user_task_group where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>