package com.dqms.dsm.domain.vo;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;

public class DsmIndexMdmRelVo extends BaseEntity{


        /** 指标Id */

        private Long indexId;

        /** 指标编码 */
        @Excel(name = "指标编码")
        private String indexCode;

        /** 数据源 */
        @Excel(name = "元数据数据源")
        private String datasourceName;
        private Long datasourceId;

        /** 系统 */
        @Excel(name = "元数据表")
        private String tableName;

        /** 系统 */
        @Excel(name = "元数据字段")
        private String regName;




        public String getDatasourceName() {
            return datasourceName;
        }

        public void setDatasourceName(String datasourceName) {
            this.datasourceName = datasourceName;
        }

        public Long getDatasourceId() {
            return datasourceId;
        }

        public void setDatasourceId(Long datasourceId) {
            this.datasourceId = datasourceId;
        }

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getRegName() {
            return regName;
        }

        public void setRegName(String regName) {
            this.regName = regName;
        }

    public Long getIndexId() {
        return indexId;
    }

    public void setIndexId(Long indexId) {
        this.indexId = indexId;
    }

    public String getIndexCode() {
        return indexCode;
    }

    public void setIndexCode(String indexCode) {
        this.indexCode = indexCode;
    }
}




