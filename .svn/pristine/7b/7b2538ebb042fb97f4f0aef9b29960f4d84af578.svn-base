package com.dqms.mdm.util.StrategyType;

import base.BaseJunit;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.dqms.basic.domain.SysDatasource;
import com.dqms.basic.mapper.SysDatasourceMapper;
import com.dqms.mdm.domain.MdmRegistry;
import io.jsonwebtoken.lang.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.*;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class PrestoStrategyTest extends BaseJunit {
    @Autowired
    PrestoStrategy prestoStrategy;

    @Autowired
    SysDatasourceMapper sysDatasourceMapper;


    @Test
    public void getTableInfo() {//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("call_center");
        mdmRegistry.setRegDir("sf10");
        mdmRegistry.setDatasourceId(33L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        Map<String, Object> table = prestoStrategy.getTableInfo(mdmRegistry, sysDatasource);
        for(Map.Entry<String, Object> map : table.entrySet()){
            System.out.println("key: "+map.getKey()+"; value: "+map.getValue());
        }
        Assert.notEmpty(table,"不能为空");
    }

    @Test
    public void getColumns() {//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("call_center");
        mdmRegistry.setRegDir("sf10");
        mdmRegistry.setDatasourceId(33L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());

        List<Map<String, Object>> list =  prestoStrategy.getColumns(mdmRegistry,sysDatasource);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("\n");
        }
        Assert.notEmpty(list,"不能为空");
    }

    @Test
    public void checkTableExist() {//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("call_center");
        mdmRegistry.setRegDir("sf10");
        mdmRegistry.setDatasourceId(33L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        Boolean aBoolean = prestoStrategy.checkTableExist(mdmRegistry, sysDatasource);
        System.out.println("表是否存在："+aBoolean);
    }

    @Test
    public void checkProcedureExist() {
        System.out.println("没有存储过程");

        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("call_center");
        mdmRegistry.setRegDir("sf10");
        mdmRegistry.setDatasourceId(33L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        Boolean aBoolean = prestoStrategy.checkProcedureExist(mdmRegistry, sysDatasource);
        System.out.println("表是否存在："+aBoolean);
    }

    @Test
    public void getTablesAndViews() {//仅展示type+name
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(33L);
        List<Map<String, Object>> list = prestoStrategy.getTablesAndViews(sysDatasource, "sf10", null);
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("\n");
        }
    }

    @Test
    public void getProcedures() {
        System.out.println("没有存储过程");
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(33L);
        List<Map<String, Object>> list = prestoStrategy.getProcedures(sysDatasource, "sf10");
        for (Map<String, Object> stringObjectMap : list) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("\n");
        }
    }

    @Test
    public void getProcedureInfo() {
        System.out.println("没有存储过程");
    }

    @Test
    public void excuteByLimit() {//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(33L);
        String sqlText = "select a.d_date ,a.d_date_sk ,a.d_date_id from tpcds.sf10.date_dim a ";
        Map map = prestoStrategy.excuteByLimit(sysDatasource, sqlText, 300);
        List<Map<String, Object>> data = (List<Map<String, Object>>) map.get("data");
        List<Map<String, Object>> info = (List<Map<String, Object>>) map.get("info");
        for (Map<String, Object> stringObjectMap : data) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("\n");
        }
    }

    @Test
    public void quaryByPage() {//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(33L);
        String sqlText = "select a.d_date ,a.d_date_sk ,a.d_date_id from tpcds.sf10.date_dim a order by a.d_date ";
        List<Map<String, Object>> maps = prestoStrategy.quaryByPage(sysDatasource, sqlText, 1, 5);
        for (Map<String, Object> stringObjectMap : maps) {
            for (Map.Entry m : stringObjectMap.entrySet()) {
                System.out.println(m.getKey()+":"+m.getValue());
            }
            System.out.println("---------------------------\n");
        }
    }

    @Test
    public void getSqlCount() {//成功
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(33L);
        String sqlText = "SELECT cc_name ,cc_class FROM sf10.call_center";
        int sqlCount = prestoStrategy.getSqlCount(sysDatasource, sqlText);

        System.out.println("获取总数："+sqlCount);
    }

    @Test
    public void getTableCreate() {//成功
        MdmRegistry mdmRegistry = new MdmRegistry();
        mdmRegistry.setRegName("call_center");
        mdmRegistry.setRegDir("sf10");
        mdmRegistry.setDatasourceId(33L);
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(mdmRegistry.getDatasourceId());
        String tableCreate = prestoStrategy.getTableCreate(sysDatasource, mdmRegistry);
        System.out.println("获取表创建："+tableCreate);
    }

    @Test
    public void getProcedureCreate() {
        System.out.println("没有存储过程");
    }

    @Test
    public void getViewCreate() {
        System.out.println("没有视图");
    }

    @Test
    public void getDatabaseInfos() {

    }

    @Test
    public void parseStatements() {//成功？？？
        SysDatasource sysDatasource = sysDatasourceMapper.selectSysDatasourceById(33L);
        String sqlText = "SELECT cc_name ,cc_class FROM sf10.call_center";
        List<SQLStatement> sqlStatements = prestoStrategy.parseStatements(sysDatasource, sqlText);
        for (SQLStatement s : sqlStatements) {
            System.out.println("解析成功："+s.toString());
        }
    }

    @Test
    public void createSchemaStatVisitor() {
        System.out.println("没有测试");
    }

}
