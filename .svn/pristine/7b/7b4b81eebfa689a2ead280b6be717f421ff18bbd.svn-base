<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmDimensionDetaiTempMapper">
    
    <resultMap type="DsmDimensionDetaiTemp" id="DsmDimensionDetaiTempResult">
        <result property="dimensionDetaiTempId"    column="dimension_detai_temp_id"    />
        <result property="dimensionId"    column="dimension_id"    />
        <result property="detailName"    column="detail_name"    />
        <result property="detailCode"    column="detail_code"    />
    </resultMap>

    <sql id="selectDsmDimensionDetaiTempVo">
        select dimension_detai_temp_id, dimension_id, detail_name, detail_code from dsm_dimension_detai_temp
    </sql>

    <select id="selectDsmDimensionDetaiTempList" parameterType="DsmDimensionDetaiTemp" resultMap="DsmDimensionDetaiTempResult">
        <include refid="selectDsmDimensionDetaiTempVo"/>
        <where>  
            <if test="dimensionId != null "> and dimension_id = #{dimensionId}</if>
            <if test="detailName != null  and detailName != ''"> and detail_name like concat('%', #{detailName}, '%')</if>
            <if test="detailCode != null  and detailCode != ''"> and detail_code = #{detailCode}</if>
        </where>
    </select>
    
    <select id="selectDsmDimensionDetaiTempById" parameterType="Long" resultMap="DsmDimensionDetaiTempResult">
        <include refid="selectDsmDimensionDetaiTempVo"/>
        where dimension_detai_temp_id = #{dimensionDetaiTempId}
    </select>
        
    <insert id="insertDsmDimensionDetaiTemp" parameterType="DsmDimensionDetaiTemp" useGeneratedKeys="true" keyProperty="dimensionDetaiTempId">
        insert into dsm_dimension_detai_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dimensionId != null">dimension_id,</if>
            <if test="detailName != null and detailName != ''">detail_name,</if>
            <if test="detailCode != null and detailCode != ''">detail_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dimensionId != null">#{dimensionId},</if>
            <if test="detailName != null and detailName != ''">#{detailName},</if>
            <if test="detailCode != null and detailCode != ''">#{detailCode},</if>
         </trim>
    </insert>

    <update id="updateDsmDimensionDetaiTemp" parameterType="DsmDimensionDetaiTemp">
        update dsm_dimension_detai_temp
        <trim prefix="SET" suffixOverrides=",">
            <if test="dimensionId != null">dimension_id = #{dimensionId},</if>
            <if test="detailName != null and detailName != ''">detail_name = #{detailName},</if>
            <if test="detailCode != null and detailCode != ''">detail_code = #{detailCode},</if>
        </trim>
        where dimension_detai_temp_id = #{dimensionDetaiTempId}
    </update>

    <delete id="deleteDsmDimensionDetaiTempById" parameterType="Long">
        delete from dsm_dimension_detai_temp where dimension_detai_temp_id = #{dimensionDetaiTempId}
    </delete>

    <delete id="deleteDsmDimensionDetaiTempByIds" parameterType="String">
        delete from dsm_dimension_detai_temp where dimension_detai_temp_id in 
        <foreach item="dimensionDetaiTempId" collection="array" open="(" separator="," close=")">
            #{dimensionDetaiTempId}
        </foreach>
    </delete>
    
    <delete id="deleteDsmDimensionDetaiTempByDimensionId" parameterType="Long">
        delete from dsm_dimension_detai_temp where dimension_id = #{dimensionId}
    </delete>
</mapper>