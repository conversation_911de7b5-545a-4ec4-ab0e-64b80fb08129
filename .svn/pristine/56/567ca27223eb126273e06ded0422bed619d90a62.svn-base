import request from '@/utils/request'

// 查询任务结果订阅列表
export function listTaskSubscribe(query) {
  return request({
    url: '/task/taskSubscribe/list',
    method: 'get',
    params: query
  })
}

// 查询任务结果订阅详细
export function getTaskSubscribe(taskSubscribeId) {
  return request({
    url: '/task/taskSubscribe/' + taskSubscribeId,
    method: 'get'
  })
}

// 新增任务结果订阅
export function addTaskSubscribe(data) {
  return request({
    url: '/task/taskSubscribe',
    method: 'post',
    data: data
  })
}

// 修改任务结果订阅
export function updateTaskSubscribe(data) {
  return request({
    url: '/task/taskSubscribe',
    method: 'put',
    data: data
  })
}

// 删除任务结果订阅
export function delTaskSubscribe(taskSubscribeId) {
  return request({
    url: '/task/taskSubscribe/' + taskSubscribeId,
    method: 'delete'
  })
}

// 导出任务结果订阅
export function exportTaskSubscribe(query) {
  return request({
    url: '/task/taskSubscribe/export',
    method: 'get',
    params: query
  })
}

//用户状态修改
export function changeStatus(taskId,userId, type) {
  const data = {
	  taskId,
	  type,
	  userId
  }
  return request({
    url: '/task/taskSubscribe/changeStatus',
    method: 'put',
    data: data
  })
}