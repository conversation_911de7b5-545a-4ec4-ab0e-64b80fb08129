package com.dqms.dsc.service.impl;

import java.util.List;

import com.dqms.dsc.domain.vo.DscDesensitizationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.annotation.DataScope;
import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.dsc.domain.DscDesensitization;
import com.dqms.dsc.domain.DscDesensitizationDetail;
import com.dqms.dsc.mapper.DscDesensitizationDetailMapper;
import com.dqms.dsc.mapper.DscDesensitizationMapper;
import com.dqms.dsc.service.IDscDesensitizationService;
import com.dqms.framework.web.service.TokenService;

/**
 * 数据脱敏Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@Service
public class DscDesensitizationServiceImpl implements IDscDesensitizationService
{
    @Autowired
    private DscDesensitizationMapper dscDesensitizationMapper;

    @Autowired
    private DscDesensitizationDetailMapper dscDesensitizationDetailMapper;
    
    @Autowired
    private TokenService tokenService;
    /**
     * 查询数据脱敏
     *
     * @param desensitizationId 数据脱敏ID
     * @return 数据脱敏
     */
    @Override
    public DscDesensitization selectDscDesensitizationById(Long desensitizationId)
    { 
        return dscDesensitizationMapper.selectDscDesensitizationById(desensitizationId);
    }

    /**
     * 查询数据脱敏列表
     *
     * @param dscDesensitization 数据脱敏
     * @return 数据脱敏
     */
    @Override
    @DataScope(systemAlias = "r")
    public List<DscDesensitization> selectDscDesensitizationList(DscDesensitization dscDesensitization)
    {
        return dscDesensitizationMapper.selectDscDesensitizationList(dscDesensitization);
    }

    @Override
    @DataScope(systemAlias = "r")
    public List<DscDesensitizationVo> getDscDesensitizationVoList(DscDesensitizationVo dscDesensitizationVo)
    {
        return dscDesensitizationMapper.getDscDesensitizationVoList(dscDesensitizationVo);
    }

    /**
     * 新增数据脱敏
     *
     * @param dscDesensitization 数据脱敏
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDscDesensitization(DscDesensitization dscDesensitization)
    {
       	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       	dscDesensitization.setCreateTime(DateUtils.getNowDate());
       	dscDesensitization.setCreateId(loginUser.getUser().getUserId());
       	dscDesensitization.setCreateBy(loginUser.getUser().getNickName());
       	dscDesensitization.setUpdateTime(DateUtils.getNowDate());
       	dscDesensitization.setUpdateId(loginUser.getUser().getUserId());
       	dscDesensitization.setUpdateBy(loginUser.getUser().getNickName());
       	int i = dscDesensitizationMapper.insertDscDesensitization(dscDesensitization);
       	if(dscDesensitization.getDetails()!=null) {
       		for(DscDesensitizationDetail detail : dscDesensitization.getDetails()) {
       			detail.setEntityId(dscDesensitization.getEntityId());
       			detail.setDesensitizationId(dscDesensitization.getDesensitizationId());
       			dscDesensitizationDetailMapper.insertDscDesensitizationDetail(detail);
       		}
       	}
        return i;
    }

    /**
     * 修改数据脱敏
     *
     * @param dscDesensitization 数据脱敏
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDscDesensitization(DscDesensitization dscDesensitization)
    {
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       	dscDesensitization.setUpdateTime(DateUtils.getNowDate());
       	dscDesensitization.setUpdateId(loginUser.getUser().getUserId());
       	dscDesensitization.setUpdateBy(loginUser.getUser().getNickName());
       	int i=dscDesensitizationMapper.updateDscDesensitization(dscDesensitization);
       	dscDesensitizationDetailMapper.deleteDscDesensitizationDetailByDesensitizationId(dscDesensitization.getDesensitizationId());
       	if(dscDesensitization.getDetails()!=null) {
       		for(DscDesensitizationDetail detail : dscDesensitization.getDetails()) {
       			detail.setEntityId(dscDesensitization.getEntityId());
       			detail.setDesensitizationId(dscDesensitization.getDesensitizationId());
       			dscDesensitizationDetailMapper.insertDscDesensitizationDetail(detail);
       		}
       	}
        return i;
    }

    /**
     * 批量删除数据脱敏
     *
     * @param desensitizationIds 需要删除的数据脱敏ID
     * @return 结果
     */
    @Override
    public int deleteDscDesensitizationByIds(Long[] desensitizationIds)
    {
        return dscDesensitizationMapper.deleteDscDesensitizationByIds(desensitizationIds);
    }

    /**
     * 删除数据脱敏信息
     *
     * @param desensitizationId 数据脱敏ID
     * @return 结果
     */
    @Override
    public int deleteDscDesensitizationById(Long desensitizationId)
    {
        return dscDesensitizationMapper.deleteDscDesensitizationById(desensitizationId);
    }
}
