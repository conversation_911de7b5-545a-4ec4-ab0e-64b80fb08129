package com.dqms.dsc.mapper;

import java.util.List;
import java.util.Map;

import com.dqms.dsc.domain.DscEncryption;
import com.dqms.dsc.domain.vo.DscEncryptionVo;

/**
 * 数据加密Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface DscEncryptionMapper 
{
    /**
     * 查询数据加密
     * 
     * @param encryptionId 数据加密ID
     * @return 数据加密
     */
    public DscEncryption selectDscEncryptionById(Long encryptionId);

    /**
     * 查询数据加密列表
     * 
     * @param dscEncryption 数据加密
     * @return 数据加密集合
     */
    public List<DscEncryption> selectDscEncryptionList(DscEncryption dscEncryption);

    public List<DscEncryptionVo> getDscEncryptionListVo(DscEncryptionVo dscEncryptionVo);

    /**
     * 新增数据加密
     * 
     * @param dscEncryption 数据加密
     * @return 结果
     */
    public int insertDscEncryption(DscEncryption dscEncryption);

    /**
     * 修改数据加密
     * 
     * @param dscEncryption 数据加密
     * @return 结果
     */
    public int updateDscEncryption(DscEncryption dscEncryption);

    /**
     * 删除数据加密
     * 
     * @param encryptionId 数据加密ID
     * @return 结果
     */
    public int deleteDscEncryptionById(Long encryptionId);

    /**
     * 批量删除数据加密
     * 
     * @param encryptionIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDscEncryptionByIds(Long[] encryptionIds);

    /**
     * 执行数据加密
     *
     * @param dscEncryption 数据加密
     * @return 结果
     */
    public void callProcedure(DscEncryption dscEncryption);
}
