<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="接口类型" prop="defineType">
        <el-select v-model="queryParams.defineType" placeholder="请选择接口类型" filterable clearable size="small">
          <el-option
            v-for="dict in defineTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="接口名称" prop="defineName">
        <el-input
          v-model="queryParams.defineName"
          placeholder="请输入接口名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="接口标识" prop="defineCode">
        <el-input
          v-model="queryParams.defineCode"
          placeholder="请输入接口标识"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"

        />
      </el-form-item>
      <el-form-item label="数据源" prop="datasourceId">
        <el-select v-model="queryParams.datasourceId" placeholder="请选择数据源" filterable clearable size="small">
          <el-option
            v-for="item in dataSourceOptions"
            :key="item.datasourceId"
            :label="item.name"
            :value="item.datasourceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

	<el-row class="el-row-inline">
	  <el-col :span="4" v-for="(item, index) in apiDefineList" :key="'apiDefine'+index" >
	    <el-card :body-style="{ padding: '0px' }" class="card" shadow="always">
	    <div slot="header" class="clearfix">
	     <el-switch v-model="item.status" active-value="0" inactive-value="1" @change="handleStatusChange(item)" ></el-switch>
	     <el-link type="primary" style="float: right">{{item.defineCode}}</el-link>
	    </div>
	      <img src="@/assets/images/api.png" class="image">
	      <div style="padding: 14px;">
	        <span style="color: #228B22;">{{item.defineName}}</span>

	        <div class="bottom clearfix">
		          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleParam(item)" v-if="item.status!='2' && item.defineType ==='N'"  v-hasPermi="['api:apiDefine:edit']" >参数设置</el-button>
		          <el-button size="mini" type="text"  icon="el-icon-edit" @click="handleAuth(item)" v-if="item.status!='2'" v-hasPermi="['api:apiDefine:edit']" >应用权限</el-button>
		          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(item)" v-if="item.status!='2'" v-hasPermi="['api:apiDefine:edit']">修改</el-button>
		          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(item)" v-hasPermi="['api:apiDefine:remove']" >删除</el-button>
		          <el-button size="mini" type="text" v-if="item.status!='2'" icon="el-icon-cpu" >
		          <router-link :to="'/apiDefine/api/debug/' + item.defineId" class="link-type" >联调测试</router-link>
		          </el-button>

	        </div>
	      </div>
	    </el-card>
	  </el-col>
	  <el-col :span="4" :key="'taskGroup'">
		  <el-card :body-style="{ padding: '0px' }" class="card" shadow="hover">
		  	<div slot="header" class="clearfix">
		     <el-link type="primary" style="float: right">API</el-link>
		    </div>
		      <img src="@/assets/images/add.png" class="image" @click="handleAdd">
		      <div style="padding: 14px;">
		        <span @click="handleAdd" style="color: #A9A9A9;">点击扩展</span>
		        <div class="bottom clearfix" style="height:58px;">
		          <time class="time"> 操作</time>
		          <el-tooltip class="item" effect="dark" content="添加API" placement="bottom">
		          <el-button type="text" class="button" @click="handleAdd"><i class="el-icon-circle-plus-outline"></i></el-button>
		          </el-tooltip>
		        </div>
		      </div>
		    </el-card>
	   </el-col>
	</el-row>


    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改接口管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row type="flex" justify="start" align="top"  >
        	<el-col :span="12">
		        <el-form-item label="接口类型" prop="defineType">
		          <el-select v-model="form.defineType" placeholder="请选择接口类型 " style="width:100%" filterable clearable>
		            <el-option
		              v-for="dict in defineTypeOptions"
		              :key="dict.dictValue"
		              :label="dict.dictLabel"
		              :value="dict.dictValue"
		            ></el-option>
		          </el-select>
		        </el-form-item>
	        </el-col>
	        <el-col :span="12">
		        <el-form-item label="数据源" prop="datasourceId" v-if="form.defineType==='N' || form.defineType==='J'">
		          <el-select v-model="form.datasourceId" placeholder="请选择数据源 " style="width:100%" filterable clearable>
			          <el-option
			            v-for="item in dataSourceOptions"
			            :key="item.datasourceId"
			            :label="item.name"
			            :value="item.datasourceId"
			          ></el-option>
		          </el-select>
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top"  >
	        <el-col :span="12">
		        <el-form-item label="接口名称" prop="defineName">
		          <el-input v-model="form.defineName" placeholder="请输入接口名称" clearable/>
		        </el-form-item>
	        </el-col>
	        <el-col :span="12">
		        <el-form-item label="接口标识" prop="defineCode">
		          <el-input v-model="form.defineCode" placeholder="请输入接口标识" clearable/>
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top"  >
	        <el-col :span="12">
		        <el-form-item label="成功信息" prop="successMsg">
		          <el-input v-model="form.successMsg" type="textarea" placeholder="请输入内容" />
		        </el-form-item>
	        </el-col>
	        <el-col :span="12">
		        <el-form-item label="错误信息" prop="errorMsg">
		          <el-input v-model="form.errorMsg" type="textarea" placeholder="请输入内容" />
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top"  >
	        <el-col :span="24">
		        <el-form-item label="接口说明" prop="remark">
		          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top"  >
	        <el-col :span="24">
		        <el-form-item label="接口信息" prop="defineSql">
		          <el-input v-model="form.defineSql" type="textarea" placeholder="数据接口:单个用户.表名或者具体的执行SQL;kafka接口:填写topic值;TCP代理:监听端口PORT:被代理IP:被代理PORT;JDBC代理：用户.表名(多个以;分割);任务接口：调度日历名称" />
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top"  >
	        <el-col :span="12">
				<el-form-item label="有效时间" prop="validTime">
		          <el-time-picker v-model="form.validTime" is-range format="HH:mm:ss" value-format="HH:mm:ss"
		            :style="{width: '100%'}" start-placeholder="开始时间" end-placeholder="结束时间" range-separator="至"
		            clearable></el-time-picker>
		        </el-form-item>
        	</el-col>
        	<el-col :span="12">
		        <el-form-item label="监听端口" prop="listen" v-if="form.defineType==='T' || form.defineType==='J'">
		          <el-input v-model="form.listen" placeholder="请输入监听端口,不填则系统自动分配" clearable/>
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top"  v-if="form.defineType==='N'" >
	        <el-col :span="12">
		        <el-form-item label="输出类型" prop="outType">
		          <el-select v-model="form.outType" placeholder="请选择输出类型" style="width:100%"  clearable>
		            <el-option
		              v-for="dict in outTypeOptions"
		              :key="dict.dictValue"
		              :label="dict.dictLabel"
		              :value="dict.dictValue"
		            ></el-option>
		          </el-select>
		        </el-form-item>
        	</el-col>
	        <el-col :span="12">
		        <el-form-item label="缓存时间" prop="cacheTime" v-if="form.defineType==='N'">
		          <el-input v-model="form.cacheTime" placeholder="请输入缓存时间" clearable/>
		        </el-form-item>
        	</el-col>
	      </el-row>
          <el-row type="flex" justify="start" align="top" >
  	        <el-col :span="12">
		        <el-form-item label="接口用户" prop="usernm" v-if="form.defineType==='J'">
		          <el-input v-model="form.usernm" placeholder="如不填系统会默认自动创建临时用户" clearable/>
		        </el-form-item>
        	</el-col>
	        <el-col :span="12">
		        <el-form-item label="接口密码" prop="passwd" v-if="form.defineType==='J'">
		          <el-input v-model="form.passwd" placeholder="请输入接口密码" clearable/>
		        </el-form-item>
        	</el-col>
	      </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改接口管理对话框 -->
    <el-dialog :title="authtitle" :visible.sync="authopen" width="800px" append-to-body>
    <el-table v-loading="loading" :data="apiDefineSystemList" @selection-change="handleSelectionChange" stripe border size="mini" height="400">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="应用系统" align="center" prop="systemName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.flag"
              active-value="Y"
              inactive-value="N"
              active-color="#13ce66"
              @change="authsubmitForm(scope.row)"
            ></el-switch>
          </template>
      </el-table-column>
    </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="authcancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改接口管理对话框 -->
    <el-dialog :title="paramtitle" :visible.sync="paramopen" width="1000px" append-to-body>
	    <el-table v-loading="loading" :data="apiDefineParamList" @selection-change="handleSelectionChange" height="300">
	    <el-table-column align="left" label="输入参数">
	      <el-table-column type="selection" width="55" align="center" />
	      <el-table-column label="参数名称" align="center" prop="defineParamName" >
	      	 <template slot-scope="scope">
               <el-input   v-model="scope.row.defineParamName" />
             </template>
          </el-table-column>
	      <el-table-column label="参数说明" align="center" prop="remark" >
	      	 <template slot-scope="scope">
               <el-input   v-model="scope.row.remark" />
             </template>
          </el-table-column>
	      <el-table-column label="是否必填" align="center" prop="isMust" >
            <template slot-scope="scope">
              <el-select v-model="scope.row.isMust"  size="small">
                <el-option v-for="dict in isMustOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
              </el-select>
            </template>
          </el-table-column>
	      <el-table-column label="参数类型" align="center" prop="defineParamType" >
            <template slot-scope="scope">
              <el-select v-model="scope.row.defineParamType"  size="small">
                <el-option v-for="dict in defineParamTypeOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
              </el-select>
            </template>
          </el-table-column>
	      <el-table-column label="默认值" align="center" prop="defualtValue" >
	      	 <template slot-scope="scope">
               <el-input   v-model="scope.row.defualtValue" />
             </template>
          </el-table-column>
	      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot="header" slot-scope="scope">
              <el-button
                slot="append"
                icon="el-icon-share"
                @click="addParam"
                size="mini"
                type="text"
                >添加</el-button
              >
            </template>
	        <template slot-scope="scope">
	          <el-button
	            size="mini"
	            type="text"
	            icon="el-icon-delete"
	            @click="delParam(scope.row, scope.$index)"
	            v-hasPermi="['api:apiDefineParam:remove']"
	          >删除</el-button>
	        </template>
	      </el-table-column>
	    </el-table-column>
	    </el-table>

	    <el-table v-loading="loading" :data="apiDefineColumnList" @selection-change="handleSelectionChange" height="300">
	    <el-table-column align="left" label="输出参数">
	      <el-table-column type="selection" width="55" align="center" />
	      <el-table-column label="输出名称" align="center" prop="defineColumnName" >
	      	 <template slot-scope="scope">
               <el-input   v-model="scope.row.defineColumnName" />
             </template>
          </el-table-column>
	      <el-table-column label="输出说明" align="center" prop="remark" >
	      	 <template slot-scope="scope">
               <el-input   v-model="scope.row.remark" />
             </template>
          </el-table-column>
	      <el-table-column label="是否必填" align="center" prop="isMust" >
            <template slot-scope="scope">
              <el-select v-model="scope.row.isMust"  size="small">
                <el-option v-for="dict in isMustOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
              </el-select>
            </template>
          </el-table-column>
	      <el-table-column label="输出类型" align="center" prop="defineColumnType" >
            <template slot-scope="scope">
              <el-select v-model="scope.row.defineColumnType"  size="small">
                <el-option v-for="dict in defineColumnTypeOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" ></el-option>
              </el-select>
            </template>
          </el-table-column>
	      <el-table-column label="是否脱敏" align="center" prop="isDesensitization" >
            <template slot-scope="scope">
              <el-select v-model="scope.row.isDesensitization"  size="small">
                <el-option v-for="dict in isDesensitizationOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" clearable></el-option>
              </el-select>
            </template>
          </el-table-column>
	      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot="header" slot-scope="scope">
              <el-button
                slot="append"
                icon="el-icon-share"
                @click="addColumn"
                size="mini"
                type="text"
                >添加</el-button
              >
            </template>
	        <template slot-scope="scope">
	          <el-button
	            size="mini"
	            type="text"
	            icon="el-icon-delete"
	            @click="delColumn(scope.row, scope.$index)"
	            v-hasPermi="['api:apiDefineColumn:remove']"
	          >删除</el-button>
	        </template>
	      </el-table-column>
	    </el-table-column>
	    </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="paramsubmitForm">确 定</el-button>
        <el-button @click="paramcancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiDefine, getApiDefine, delApiDefine, addApiDefine, updateApiDefine, exportApiDefine ,changeStatus,updateApiDefineSystem,updateParam} from "@/api/api/apiDefine";
import { listApiDefineParam, getApiDefineParam, delApiDefineParam, addApiDefineParam, updateApiDefineParam, exportApiDefineParam } from "@/api/api/apiDefineParam";
import { listApiDefineColumn, getApiDefineColumn, delApiDefineColumn, addApiDefineColumn, updateApiDefineColumn, exportApiDefineColumn } from "@/api/api/apiDefineColumn";
import { listApiDefineSystemAll, addApiDefineSystem,delApiDefineSystem, exportApiDefineSystem } from "@/api/api/apiDefineSystem";
import { listDatasourceAll } from "@/api/basic/datasource";
import { listSystemAll } from "@/api/basic/system";
export default {
  name: "ApiDefine",
  components: {
  },
  data() {
    return {
      defineId:null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 接口管理表格数据
      apiDefineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      authtitle: "",
      // 是否显示弹出层
      authopen: false,
      // 弹出层标题
      paramtitle: "参数设置",
      // 是否显示弹出层
      paramopen: false,
      // 接口类型字典
      defineTypeOptions: [],
      // 数据源字典
      datasourceIdOptions: [],
      // 输出类型字典
      outTypeOptions: [],
      //数据源
      dataSourceOptions: [],
      //状态
      statusOptions: [],

      systemOptions: [],

      apiDefineParamList: [],

      apiDefineColumnList:[],

      apiDefineSystemList:[],

      // 是否必填字典
      isMustOptions: [],
      // 参数类型字典
      defineParamTypeOptions: [],
      // 输出类型字典
      defineColumnTypeOptions: [],
      // 是否脱敏字典
      isDesensitizationOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        defineType: null,
        defineName: null,
        defineCode: null,
        datasourceId: null,
        version: null,
        outType: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        defineType: [
          { required: true, message: "接口类型不能为空", trigger: "change" }
        ],
        defineName: [
          { required: true, message: "接口名称不能为空", trigger: "blur" }
        ],
        defineCode: [
          { required: true, message: "接口标识不能为空", trigger: "blur" }
        ],
        version: [
          { required: true, message: "版本号不能为空", trigger: "blur" }
        ],
        cacheTime: [
          { required: true, message: "缓存时间不能为空", trigger: "blur" }
        ],
        datasourceId:[
          { required: true, message: "数据源不能为空", trigger: "blur" }
        ],
        defineSql:[
          { required: true, message: "接口信息不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("api_define_type").then(response => {
      this.defineTypeOptions = response.data;
    });
    this.getDicts("api_define_out_type").then(response => {
      this.outTypeOptions = response.data;
    });
    this.getDicts("api_define_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response => {
      this.isMustOptions = response.data;
    });
    this.getDicts("dsc_desensitization_type").then(response => {
      this.isDesensitizationOptions = response.data;
    });
    this.getDicts("api_define_data_type").then(response => {
      this.defineParamTypeOptions = response.data;
      this.defineColumnTypeOptions = response.data;
    });
    this.getDataSource();
    this.getSystem();
  },
  methods: {
    /** 查询接口管理列表 */
    getList() {
      this.loading = true;
      listApiDefine(this.queryParams).then(response => {
        this.apiDefineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 接口类型字典翻译
    defineTypeFormat(row, column) {
      return this.selectDictLabel(this.defineTypeOptions, row.defineType);
    },
    // 输出类型字典翻译
    outTypeFormat(row, column) {
      return this.selectDictLabel(this.outTypeOptions, row.outType);
    },
 	// 输出类型字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
   // 输出类型字典翻译
    dataSourceFormat(row, column) {
    	for(let i=0;i<=this.dataSourceOptions.length;i++){
    		if(this.dataSourceOptions[i]&&this.dataSourceOptions[i].datasourceId==row.datasourceId){
    			return this.dataSourceOptions[i].name;
    		}
    	}
      return "";
    },
    // 是否必填字典翻译
    isMustFormat(row, column) {
      return this.selectDictLabel(this.isMustOptions, row.isMust);
    },
    // 输出类型字典翻译
    defineColumnTypeFormat(row, column) {
      return this.selectDictLabel(this.defineColumnTypeOptions, row.defineColumnType);
    },
    // 是否脱敏字典翻译
    isDesensitizationFormat(row, column) {
      return this.selectDictLabel(this.isDesensitizationOptions, row.isDesensitization);
    },
    // 参数类型字典翻译
    defineParamTypeFormat(row, column) {
      return this.selectDictLabel(this.defineParamTypeOptions, row.defineParamType);
    },
    /** 获取应用系统 */
    getSystem() {
      let systemParams = {};
      listSystemAll(systemParams).then(response => {
        this.systemOptions = response.data;
      });
    },
    getDataSource() {
        listDatasourceAll().then(response => {
          this.dataSourceOptions = response.data;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    authcancel() {
      this.authopen = false;
      this.reset();
    },
    // 取消按钮
    paramcancel() {
      this.paramopen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        defineId: null,
        defineType: null,
        defineName: null,
        defineCode: null,
        datasourceId: null,
        version: null,
        cacheTime: null,
        outType: null,
        successMsg: null,
        errorMsg: null,
        remark: null,
        defineSql: null,
        createBy: null,
        updateBy: null,
        createId: null,
        updateId: null,
        createTime: null,
        updateTime: null,
        status:null,
        validTime:null,
        systemIds:[],
        apiDefineParams:[],
        apiDefineColumns:[]
      };
      this.resetForm("form");
      this.apiDefineParamList=[];
      this.apiDefineColumnList=[];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.defineId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加接口管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const defineId = row.defineId || this.ids
      getApiDefine(defineId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改接口管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
    	if(this.form.validTime!=null&&this.form.validTime.length>0){
    		this.form.startTime=this.form.validTime[0];
    		this.form.endTime=this.form.validTime[1];
    	}
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.defineId != null) {
            updateApiDefine(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApiDefine(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const defineIds = row.defineId || this.ids;
      this.$confirm('是否确认删除接口管理编号为"' + defineIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delApiDefine(defineIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有接口管理数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportApiDefine(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    // 状态修改
    handleStatusChange(row) {
    	return changeStatus(row.defineId,row.status).catch(()=>{
        row.status=row.status=='0'?'1':'0'
      });
    } ,
    /** 修改按钮操作 */
    handleAuth(row) {
    	this.reset();
    	this.defineId =row.defineId || this.ids;
	   	 let systemParams={};
	   	systemParams.defineId = row.defineId || this.ids;
		 listApiDefineSystemAll(systemParams).then(response => {
		       this.apiDefineSystemList = response.rows;
	     });
		 this.authopen = true;
         this.authtitle = "应用权限";
     },
     /** 提交按钮 */
     authsubmitForm(row) {
    	 let systemForm={};
    	 systemForm.defineId = this.defineId;
    	 systemForm.systemId = row.systemId;
		if(row.flag=='N'){
			delApiDefineSystem(systemForm).then(response => {
                this.msgSuccess("取消成功");
                this.open = false;
                this.handleAuth();
              });
		}else{
			addApiDefineSystem(systemForm).then(response => {
                this.msgSuccess("赋权成功");
                this.open = false;
                this.handleAuth();
              });
		}
     } ,
     handleParam(row) {
         this.reset();
         this.paramopen=true;
         const defineId = row.defineId || this.ids
         let queryParams={defineId:defineId};
         listApiDefineParam(queryParams).then(response => {
           this.apiDefineParamList = response.rows;
         });
         listApiDefineColumn(queryParams).then(response => {
           this.apiDefineColumnList = response.rows;
         });
         this.form.defineId=defineId;
     },
     /** 提交按钮 */
     paramsubmitForm() {
   	   this.form.apiDefineParams = [];
        for (var i = 0; i < this.apiDefineParamList.length; i++) {
          this.form.apiDefineParams.push({
        	 defineParamName: this.apiDefineParamList[i].defineParamName,
        	 remark: this.apiDefineParamList[i].remark,
        	 isMust: this.apiDefineParamList[i].isMust,
        	 defineParamType: this.apiDefineParamList[i].defineParamType,
        	 defualtValue: this.apiDefineParamList[i].defualtValue
          });
       }
   	   this.form.apiDefineColumns = [];
       for (var i = 0; i < this.apiDefineColumnList.length; i++) {
         this.form.apiDefineColumns.push({
        	 defineColumnName: this.apiDefineColumnList[i].defineColumnName,
        	 remark: this.apiDefineColumnList[i].remark,
        	 isMust: this.apiDefineColumnList[i].isMust,
        	 defineColumnType: this.apiDefineColumnList[i].defineColumnType,
        	 isDesensitization: this.apiDefineColumnList[i].isDesensitization
         });
       }
       updateParam(this.form).then(response => {
         this.msgSuccess("修改成功");
         this.paramopen = false;
       });
     },
     addParam() {
    	 let j = { "defineParamName": "","remark": "", "isMust": "N", "defineParamType": "STRING", "defualtValue": ""};
    	 this.apiDefineParamList.push(j);
     },
     addColumn() {
    	 let j = { "defineColumnName": "","remark": "", "isMust": "N", "defineColumnType": "STRING", "isDesensitization": ""};
    	 this.apiDefineColumnList.push(j);
     },
     delParam(row, index) {
    	 this.apiDefineParamList.splice(index, 1);
     },
     delColumn(row, index) {
    	 this.apiDefineColumnList.splice(index, 1);
     }
  }
};
</script>
<style>
  .time {
    font-size: 13px;
    color: #999;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }

  .card{
  	margin:15px;
    min-width: 90%;
    height: 90%;
  }

  .el-row-inline {
    display: flex;
    flex-wrap: wrap;
  }

</style>
<style scoped>
 .bottom {
    margin-top: 13px;
    line-height: 12px;
    min-height: 90px;
  }
</style>
