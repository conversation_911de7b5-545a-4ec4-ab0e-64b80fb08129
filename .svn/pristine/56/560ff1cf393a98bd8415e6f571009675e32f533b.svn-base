import request from '@/utils/request'

// 查询维度字典列表
export function listDsmDimension(query) {
  return request({
    url: '/dsm/dsmDimension/list',
    method: 'get',
    params: query
  })
}
// 查询维度字典列表
export function listDsmDimensionForDetail(query) {
  return request({
    url: '/dsm/dsmDimension/forDetail',
    method: 'get',
    params: query
  })
}
// 查询维度字典详细
export function getDsmDimension(dimensionId) {
  return request({
    url: '/dsm/dsmDimension/' + dimensionId,
    method: 'get'
  })
}

// 新增维度字典
export function addDsmDimension(data) {
  return request({
    url: '/dsm/dsmDimension',
    method: 'post',
    data: data
  })
}

// 修改维度字典
export function updateDsmDimension(data) {
  return request({
    url: '/dsm/dsmDimension',
    method: 'put',
    data: data
  })
}

// 修改维度字典
export function updateDsmDimensionStatus(data) {
  return request({
    url: '/dsm/dsmDimension/updateStatus',
    method: 'post',
    data: data
  })
}
// 删除维度字典
export function delDsmDimension(dimensionId) {
  return request({
    url: '/dsm/dsmDimension/' + dimensionId,
    method: 'delete'
  })
}

// 导出维度字典
export function exportDsmDimension(query) {
  return request({
    url: '/dsm/dsmDimension/export',
    method: 'get',
    params: query
  })
}

//同步明细
export function loadDsmDimension(dimensionId) {
  return request({
    url: '/dsm/dsmDimension/loadDsmDimension/' + dimensionId,
    method: 'put'
  })
}

//批量同步明细
export function batchLoadDsmDimension(dimensionIds) {
  return request({
    url: '/dsm/dsmDimension/batchLoadDsmDimension/' + dimensionIds,
    method: 'put'
  })
}

//审批明细
export function applyDsmDimension(dimensionId) {
return request({
  url: '/dsm/dsmDimension/applyDsmDimension/' + dimensionId,
  method: 'put'
})
}
//审批明细
export function rejectDsmDimension(dimensionId) {
return request({
  url: '/dsm/dsmDimension/rejectDsmDimension/' + dimensionId,
  method: 'put'
})
}

//下载模板
export function exportTemplate(){
  return request({
    url:'/dsm/dsmDimension/exportTemplate',
    method:'get'
  })
}
