package com.dqms.dsm.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.dsm.mapper.DsmIndexIndexMapper;
import com.dqms.dsm.domain.DsmIndexIndex;
import com.dqms.dsm.service.IDsmIndexIndexService;

/**
 * 指标关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Service
public class DsmIndexIndexServiceImpl implements IDsmIndexIndexService
{
    @Autowired
    private DsmIndexIndexMapper dsmIndexIndexMapper;

    /**
     * 查询指标关系
     *
     * @param indexIndexId 指标关系ID
     * @return 指标关系
     */
    @Override
    public DsmIndexIndex selectDsmIndexIndexById(Long indexIndexId)
    {
        return dsmIndexIndexMapper.selectDsmIndexIndexById(indexIndexId);
    }

    /**
     * 查询指标关系列表
     *
     * @param dsmIndexIndex 指标关系
     * @return 指标关系
     */
    @Override
    public List<DsmIndexIndex> selectDsmIndexIndexList(DsmIndexIndex dsmIndexIndex)
    {
        return dsmIndexIndexMapper.selectDsmIndexIndexList(dsmIndexIndex);
    }

    /**
     * 新增指标关系
     *
     * @param dsmIndexIndex 指标关系
     * @return 结果
     */
    @Override
    public int insertDsmIndexIndex(DsmIndexIndex dsmIndexIndex)
    {
        return dsmIndexIndexMapper.insertDsmIndexIndex(dsmIndexIndex);
    }

    /**
     * 修改指标关系
     *
     * @param dsmIndexIndex 指标关系
     * @return 结果
     */
    @Override
    public int updateDsmIndexIndex(DsmIndexIndex dsmIndexIndex)
    {
        return dsmIndexIndexMapper.updateDsmIndexIndex(dsmIndexIndex);
    }

    /**
     * 批量删除指标关系
     *
     * @param indexIndexIds 需要删除的指标关系ID
     * @return 结果
     */
    @Override
    public int deleteDsmIndexIndexByIds(Long[] indexIndexIds)
    {
        return dsmIndexIndexMapper.deleteDsmIndexIndexByIds(indexIndexIds);
    }

    /**
     * 删除指标关系信息
     *
     * @param indexIndexId 指标关系ID
     * @return 结果
     */
    @Override
    public int deleteDsmIndexIndexById(Long indexIndexId)
    {
        return dsmIndexIndexMapper.deleteDsmIndexIndexById(indexIndexId);
    }
}
