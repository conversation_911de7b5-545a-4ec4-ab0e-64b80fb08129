<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dqm.mapper.DqmValidationTaskMapper">

    <resultMap type="DqmValidationTask" id="DqmValidationTaskResult">
        <result property="dqmValidationTaskId"    column="dqm_validation_task_ID"    />
        <result property="validationRuleCateId"    column="VALIDATION_RULE_CATE_ID"    />
        <result property="validationTaskName"    column="dqm_validation_task_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="topic"    column="topic"    />
        <result property="executionStyle"    column="execution_style"    />
        <result property="executeState"    column="execute_state"    />
        <result property="executeResult"    column="execute_result"    />
        <result property="totalResult"    column="total_result"    />
        <result property="errorResult"    column="error_result"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="errorMsg"    column="ERROR_MESSAGE"    />
        <result property="validationName"    column="validation_name"    />
        <result property="threshold"    column="threshold"    />
        <result property="level"    column="level"  />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemName"    column="system_name"    />
        <result property="threshold"    column="threshold"    />
        <result property="loadDate"    column="load_date"    />
    </resultMap>

    <sql id="selectDqmValidationTaskVo">
        select
	t.dqm_validation_task_id,
	t.validation_rule_cate_id,
	t.start_time,
	t.end_time,
	t.topic,
	t.execution_style,
	t.execute_state,
	t.execute_result,
	t.total_result,
	t.error_result,
	t.create_id,
	t.create_time,
	t.update_id,
	t.update_time,
	t.error_message,
	t.dqm_validation_task_name,
	t.create_by,
	t.update_by,
	dvrc.validation_name validation_name,
	t.threshold,
	dvrc.level level,
	sd.name datasource_name,
	ss.name system_name,
	t.load_date
from
	dqm_validation_task t
	left join dqm_validation_rule_cate dvrc on t.validation_rule_cate_id =dvrc.validation_rule_cate_id
	left join dqm_validation_class dvc on dvrc.validation_class_id=dvc.validation_class_id
	left join sys_datasource sd on dvrc.datasource_id =sd.datasource_id
	left join sys_system ss on dvrc.system_id =ss.system_id
    </sql>

    <select id="selectDqmValidationTaskList" parameterType="DqmValidationTask" resultMap="DqmValidationTaskResult">
        <include refid="selectDqmValidationTaskVo"/>
        <where>
            <if test="validationRuleCateId != null "> and t.VALIDATION_RULE_CATE_ID = #{validationRuleCateId}</if>
            <if test="validationTaskName != null  and validationTaskName != ''"> and t.dqm_validation_task_name like concat('%', #{validationTaskName}, '%')</if>
            <if test="startTime != null "> and t.start_time LIKE CONCAT(STR_TO_DATE(#{startTime}, '%Y-%m-%d') ,'%')</if>
            <if test="endTime != null "> and t.end_time LIKE CONCAT(STR_TO_DATE(#{endTime}, '%Y-%m-%d') ,'%')</if>
            <if test="topic != null and topic != ''"> and t.topic = #{topic}</if>
            <if test="executionStyle != null "> and t.execution_style = #{executionStyle}</if>
            <if test="executeState != null "> and t.execute_state = #{executeState}</if>
            <if test="executeResult != null "> and t.execute_result = #{executeResult}</if>
            <if test="totalResult != null "> and t.total_result = #{totalResult}</if>
            <if test="errorResult != null "> and t.error_result = #{errorResult}</if>
            <if test="createId != null "> and t.create_id = #{createId}</if>
            <if test="updateId != null "> and t.update_id = #{updateId}</if>
            <if test="datasourceId !=null"> and sd.datasource_id = #{datasourceId}</if>
            <if test="systemId != null">and ss.system_id=#{systemId}</if>
			<if test="validationClassId != null and validationClassId != '0' "> and (dvc.ancestors like concat('%,', #{validationClassId}, '%') or dvrc.validation_class_id = #{validationClassId}) </if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.start_time desc
    </select>

    <select id="selectDqmValidationTaskById" parameterType="Integer" resultMap="DqmValidationTaskResult">
        <include refid="selectDqmValidationTaskVo"/>
        where dqm_validation_task_ID = #{dqmValidationTaskId}
        ${params.dataScope}<!-- 数据范围过滤 -->
    </select>

    <insert id="insertDqmValidationTask" parameterType="DqmValidationTask" useGeneratedKeys="true" keyProperty="dqmValidationTaskId">
        insert into dqm_validation_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="validationRuleCateId != null">VALIDATION_RULE_CATE_ID,</if>
            <if test="validationTaskName != null">dqm_validation_task_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="topic != null">topic,</if>
            <if test="executionStyle != null">execution_style,</if>
            <if test="executeState != null">execute_state,</if>
            <if test="executeResult != null">execute_result,</if>
            <if test="totalResult != null">total_result,</if>
            <if test="errorResult != null">error_result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="errorMsg != null">ERROR_MESSAGE,</if>
            <if test="threshold != null">threshold,</if>
            <if test="loadDate != null">load_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="validationRuleCateId != null">#{validationRuleCateId},</if>
            <if test="validationTaskName != null">#{validationTaskName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="topic != null">#{topic},</if>
            <if test="executionStyle != null">#{executionStyle},</if>
            <if test="executeState != null">#{executeState},</if>
            <if test="executeResult != null">#{executeResult},</if>
            <if test="totalResult != null">#{totalResult},</if>
            <if test="errorResult != null">#{errorResult},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="threshold != null">#{threshold},</if>
            <if test="loadDate != null">#{loadDate},</if>
        </trim>
    </insert>

    <update id="updateDqmValidationTask" parameterType="DqmValidationTask">
        update dqm_validation_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="validationRuleCateId != null">VALIDATION_RULE_CATE_ID = #{validationRuleCateId},</if>
            <if test="validationTaskName != null">dqm_validation_task_name = #{validationTaskName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="executionStyle != null">execution_style = #{executionStyle},</if>
            <if test="executeState != null">execute_state = #{executeState},</if>
            <if test="executeResult != null">execute_result = #{executeResult},</if>
            <if test="totalResult != null">total_result = #{totalResult},</if>
            <if test="errorResult != null">error_result = #{errorResult},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="errorMsg != null">error_message = #{errorMsg},</if>
            <if test="threshold != null">threshold = #{threshold},</if>
            <if test="loadDate != null">load_date = #{loadDate},</if>
        </trim>
        where dqm_validation_task_ID = #{dqmValidationTaskId}
    </update>

    <delete id="deleteDqmValidationTaskById" parameterType="Integer">
        delete from dqm_validation_task where dqm_validation_task_ID = #{dqmValidationTaskId}
    </delete>

    <delete id="deleteDqmValidationTaskByIds" parameterType="String">
        delete from dqm_validation_task where dqm_validation_task_ID in
        <foreach item="dqmValidationTaskId" collection="array" open="(" separator="," close=")">
            #{dqmValidationTaskId}
        </foreach>
    </delete>
    <select id="getExeCuteState30" parameterType="DqmValidationTask" resultType="Map">
        SELECT t.execute_state executeState ,count(1) sum FROM (select execute_state from dqm_validation_task d
        left join dqm_validation_rule_cate dvrc on d.validation_rule_cate_id =dvrc.validation_rule_cate_id
        where d.validation_rule_cate_id=#{params.validationRuleCateId}  ${params.dataScope}<!-- 数据范围过滤 --> LIMIT 30) t group by t.execute_state
    </select>
    <select id="getErrorRestule30" parameterType="DqmValidationTask" resultType="Map">
        select DATE_FORMAT(start_time,'%Y-%m-%d %H:%i:%s') startTime,error_result errorResult from dqm_validation_task t
        left join dqm_validation_rule_cate dvrc on t.validation_rule_cate_id =dvrc.validation_rule_cate_id
        where t.validation_rule_cate_id=#{params.validationRuleCateId}  ${params.dataScope}<!-- 数据范围过滤 --> order by  t.start_time asc LIMIT 30
    </select>

    <!--规则任务每日运行统计-->
    <select id="getTaskRun" parameterType="DqmValidationTask" resultType="map">
        select DATE_FORMAT(start_time,'%Y-%m-%d') name,count(1) value from dqm_validation_task t
        left join dqm_validation_rule_cate dvrc on t.validation_rule_cate_id =dvrc.validation_rule_cate_id
        where 1=1  ${params.dataScope}<!-- 数据范围过滤 --> group by DATE_FORMAT(t.start_time,'%Y-%m-%d') order by DATE_FORMAT(t.start_time,'%Y-%m-%d') asc
    </select>
<!--任务平均耗时排行-->
    <select id="getTaskRunTime" parameterType="DqmValidationTask" resultType="map">
        select
            t.dqm_validation_task_id,
            t.validation_rule_cate_id,
            t.start_time,
            t.end_time,
            t.topic,
            t.execution_style,
            t.execute_state,
            t.execute_result,
            t.total_result,
            t.error_result,
            t.create_id,
            t.create_time,
            t.update_id,
            t.update_time,
            t.error_message,
            t.dqm_validation_task_name,
            t.create_by,
            t.update_by,
            dvrc.validation_name validation_name,
            t.threshold threshold,
            dvrc.level level,
            sd.name datasource_name,
            ss.name system_name,
            TIMESTAMPDIFF(SECOND,t.start_time,t.end_time) value
        from
            dqm_validation_task t
            left join dqm_validation_rule_cate dvrc on t.validation_rule_cate_id =dvrc.validation_rule_cate_id
            left join sys_datasource sd on dvrc.datasource_id =sd.datasource_id
            left join sys_system ss on dvrc.system_id =ss.system_id
             where start_time is not null and end_time is not null
             and DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt;= start_time
            ${params.dataScope}
             order by value desc limit 20
    </select>
</mapper>
