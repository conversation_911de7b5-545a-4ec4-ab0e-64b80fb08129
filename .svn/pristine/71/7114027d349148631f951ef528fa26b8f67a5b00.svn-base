import request from '@/utils/request'

// 查询指标引用列表
export function listDsmMdmRel(query) {
  return request({
    url: '/dsm/dsmMdmRel/list',
    method: 'get',
    params: query
  })
}

// 查询指标引用列表
export function unlistDsmMdmRel(query) {
  return request({
    url: '/dsm/dsmMdmRel/unlist',
    method: 'get',
    params: query
  })
}

// 修改指标引用
export function updateDsmMdmRel(data) {
  return request({
    url: '/dsm/dsmMdmRel',
    method: 'put',
    data: data
  })
}

export function updateDsmMdmRelDimensionIdUn(data) {
  return request({
    url: '/dsm/dsmMdmRel/updateDsmMdmRelDimensionIdUn',
    method: 'put',
    data
  })
}

export function updateDsmMdmRelIndexUn(data) {
  return request({
    url: '/dsm/dsmMdmRel/updateDsmMdmRelIndexUn',
    method: 'put',
    data
  })

}

