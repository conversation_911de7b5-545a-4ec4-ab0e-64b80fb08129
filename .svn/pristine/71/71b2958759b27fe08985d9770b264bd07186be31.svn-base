package com.dqms.mdm.domain;
import com.dqms.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;

import java.util.List;

/**
 * 数据实体历史对象 mdm_data_entity_his
 *
 * <AUTHOR>
 * @date 2021-04-10
 */
public class MdmDataEntityHis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据实体历史表编号 */
    private Long entityId;

    /** 所属元数据注册ID */
    @Excel(name = "所属元数据注册ID")
    private Long registryId;

    /** 表名 */
    @Excel(name = "表名")
    private String tableName;

    /** 表注释 */
    @Excel(name = "表注释")
    private String tableComment;

    /** SCHEMA */
    @Excel(name = "SCHEMA")
    private String tableSchema;

    /** SQL脚本 */
    @Excel(name = "SQL脚本")
    private String sqlScripts;


    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operType;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNo;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long updateId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createId;

    private String dsType;

    private String metaType;

    private String fileName;

    private String isMtc;

    public String getMetaType() {
        return metaType;
    }

    public void setMetaType(String metaType) {
        this.metaType = metaType;
    }

    public String getIsMtc() {
        return isMtc;
    }

    public void setIsMtc(String isMtc) {
        this.isMtc = isMtc;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDsType() {
        return dsType;
    }

    public void setDsType(String dsType) {
        this.dsType = dsType;
    }

    private String modifyFields;

    private String datasourceName;

    private String systemName;

    private String themeName;

    private String layerName;

    private List<MdmDataEntityPropHis> mdmDataEntityPropHis;

    private Boolean isPorpUpdate;

    public Boolean getPorpUpdate() {
        return isPorpUpdate;
    }

    public void setPorpUpdate(Boolean porpUpdate) {
        isPorpUpdate = porpUpdate;
    }

    public List<MdmDataEntityPropHis> getMdmDataEntityPropHis() {
        return mdmDataEntityPropHis;
    }

    public void setMdmDataEntityPropHis(List<MdmDataEntityPropHis> mdmDataEntityPropHis) {
        this.mdmDataEntityPropHis = mdmDataEntityPropHis;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public String getLayerName() {
        return layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    public String getModifyFields() {
        return modifyFields;
    }

    public void setModifyFields(String modifyFields) {
        this.modifyFields = modifyFields;
    }

    public void setEntityId(Long entityId)
    {
        this.entityId = entityId;
    }

    public Long getEntityId()
    {
        return entityId;
    }
    public void setRegistryId(Long registryId)
    {
        this.registryId = registryId;
    }

    public Long getRegistryId()
    {
        return registryId;
    }
    public void setTableName(String tableName)
    {
        this.tableName = tableName;
    }

    public String getTableName()
    {
        return tableName;
    }
    public void setTableComment(String tableComment)
    {
        this.tableComment = tableComment;
    }

    public String getTableComment()
    {
        return tableComment;
    }
    public void setTableSchema(String tableSchema)
    {
        this.tableSchema = tableSchema;
    }

    public String getTableSchema()
    {
        return tableSchema;
    }
    public void setSqlScripts(String sqlScripts)
    {
        this.sqlScripts = sqlScripts;
    }

    public String getSqlScripts()
    {
        return sqlScripts;
    }
    public void setOperType(String operType)
    {
        this.operType = operType;
    }

    public String getOperType()
    {
        return operType;
    }
    public void setVersionNo(String versionNo)
    {
        this.versionNo = versionNo;
    }

    public String getVersionNo()
    {
        return versionNo;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("entityId", getEntityId())
            .append("registryId", getRegistryId())
            .append("tableName", getTableName())
            .append("tableComment", getTableComment())
            .append("tableSchema", getTableSchema())
            .append("sqlScripts", getSqlScripts())
            .append("operType", getOperType())
            .append("versionNo", getVersionNo())
            .append("createBy", getCreateBy())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createId", getCreateId())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
