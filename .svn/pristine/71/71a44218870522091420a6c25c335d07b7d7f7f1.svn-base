package com.dqms.mdm.domain.vo;
import java.util.List;

import com.dqms.common.core.domain.BaseEntity;

/**
 * 数据实体属性对象 mdm_data_entity_prop
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
public class MdmDataEntityPropVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据实体属性编号 */
    private Long propId;

    /** 属性名称 */
    private String key;

    private String type;

    private List<MdmDataEntityShipVo> relation;

	public Long getPropId() {
		return propId;
	}

	public void setPropId(Long propId) {
		this.propId = propId;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public List<MdmDataEntityShipVo> getRelation() {
		return relation;
	}

	public void setRelation(List<MdmDataEntityShipVo> relation) {
		this.relation = relation;
	}
    
}
