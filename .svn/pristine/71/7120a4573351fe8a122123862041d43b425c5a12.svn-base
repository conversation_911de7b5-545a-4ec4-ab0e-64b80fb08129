package com.dqms.task.mapper;

import java.util.List;

import com.dqms.task.domain.EtlTask;
import com.dqms.task.domain.EtlTaskRelation;

/**
 * 任务关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-12
 */
public interface EtlTaskRelationMapper 
{
    /**
     * 查询任务关系
     * 
     * @param taskRelationId 任务关系ID
     * @return 任务关系
     */
    public EtlTaskRelation selectEtlTaskRelationById(Long taskRelationId);

    /**
     * 查询任务关系列表
     * 
     * @param etlTaskRelation 任务关系
     * @return 任务关系集合
     */
    public List<EtlTaskRelation> selectEtlTaskRelationList(EtlTaskRelation etlTaskRelation);
    public List<EtlTaskRelation> exportEtlTaskRelationList(EtlTask etlTask);
    public List<EtlTaskRelation> selectEtlTaskInstanceRelationList(EtlTaskRelation etlTaskRelation);
    public List<EtlTaskRelation> selectEtlTaskInstanceRelationListByPos(EtlTaskRelation etlTaskRelation);
    public List<EtlTaskRelation> selectEtlTaskInstanceRelationListByPre(EtlTaskRelation etlTaskRelation);

    /**
     * 新增任务关系
     * 
     * @param etlTaskRelation 任务关系
     * @return 结果
     */
    public int insertEtlTaskRelation(EtlTaskRelation etlTaskRelation);

    /**
     * 修改任务关系
     * 
     * @param etlTaskRelation 任务关系
     * @return 结果
     */
    public int updateEtlTaskRelation(EtlTaskRelation etlTaskRelation);

    /**
     * 删除任务关系
     * 
     * @param taskRelationId 任务关系ID
     * @return 结果
     */
    public int deleteEtlTaskRelationById(Long taskRelationId);

    /**
     * 批量删除任务关系
     * 
     * @param taskRelationIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEtlTaskRelationByIds(Long[] taskRelationIds);
    
    /**
     * 删除任务关系
     * 
     * @param etlTaskRelation 需要删除的任务关系
     * @return 结果
     */
    public int delTaskRelationByNode(EtlTaskRelation etlTaskRelation);
    /**
     * 清空任务关系
     * 
     * @param etlTaskRelation 需要删除的任务关系
     * @return 结果
     */
    public int delTaskRelationAll();
    
    public int resetTaskCheckAll();
}
