<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsm.mapper.DsmModelEntityShipMapper">
    
    <resultMap type="DsmModelEntityShip" id="DsmModelEntityShipResult">
        <result property="modelEntityShipId"    column="model_entity_ship_id"    />
        <result property="srcModelEntityId"    column="src_model_entity_id"    />
        <result property="srcModelEntityPropId"    column="src_model_entity_prop_id"    />
        <result property="tarModelEntityId"    column="tar_model_entity_id"    />
        <result property="tarModelEntityPropId"    column="tar_model_entity_prop_id"    />
        <result property="versionNo"    column="version_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="name"    column="name"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateExec"    column="update_exec"    />
        <result property="deleteExec"    column="delete_exec"    />
    </resultMap>

    <sql id="selectDsmModelEntityShipVo">
        select model_entity_ship_id, src_model_entity_id, src_model_entity_prop_id, tar_model_entity_id, tar_model_entity_prop_id, version_no, create_by, name, update_by, create_id, update_id, create_time, update_time, update_exec, delete_exec from dsm_model_entity_ship
    </sql>

    <select id="selectDsmModelEntityShipList" parameterType="DsmModelEntityShip" resultMap="DsmModelEntityShipResult">
        <include refid="selectDsmModelEntityShipVo"/>
        <where>  
            <if test="srcModelEntityId != null "> and src_model_entity_id = #{srcModelEntityId}</if>
            <if test="srcModelEntityPropId != null "> and src_model_entity_prop_id = #{srcModelEntityPropId}</if>
            <if test="tarModelEntityId != null "> and tar_model_entity_id = #{tarModelEntityId}</if>
            <if test="tarModelEntityPropId != null "> and tar_model_entity_prop_id = #{tarModelEntityPropId}</if>
            <if test="versionNo != null  and versionNo != ''"> and version_no = #{versionNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="updateExec != null  and updateExec != ''"> and update_exec = #{updateExec}</if>
            <if test="deleteExec != null  and deleteExec != ''"> and delete_exec = #{deleteExec}</if>
        </where>
    </select>
    
    <select id="selectDsmModelEntityShipById" parameterType="Long" resultMap="DsmModelEntityShipResult">
        <include refid="selectDsmModelEntityShipVo"/>
        where model_entity_ship_id = #{modelEntityShipId}
    </select>
        
    <insert id="insertDsmModelEntityShip" parameterType="DsmModelEntityShip" useGeneratedKeys="true" keyProperty="modelEntityShipId">
        insert into dsm_model_entity_ship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="srcModelEntityId != null">src_model_entity_id,</if>
            <if test="srcModelEntityPropId != null">src_model_entity_prop_id,</if>
            <if test="tarModelEntityId != null">tar_model_entity_id,</if>
            <if test="tarModelEntityPropId != null">tar_model_entity_prop_id,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="name != null">name,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateExec != null">update_exec,</if>
            <if test="deleteExec != null">delete_exec,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="srcModelEntityId != null">#{srcModelEntityId},</if>
            <if test="srcModelEntityPropId != null">#{srcModelEntityPropId},</if>
            <if test="tarModelEntityId != null">#{tarModelEntityId},</if>
            <if test="tarModelEntityPropId != null">#{tarModelEntityPropId},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="name != null">#{name},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateExec != null">#{updateExec},</if>
            <if test="deleteExec != null">#{deleteExec},</if>
         </trim>
    </insert>

    <update id="updateDsmModelEntityShip" parameterType="DsmModelEntityShip">
        update dsm_model_entity_ship
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcModelEntityId != null">src_model_entity_id = #{srcModelEntityId},</if>
            <if test="srcModelEntityPropId != null">src_model_entity_prop_id = #{srcModelEntityPropId},</if>
            <if test="tarModelEntityId != null">tar_model_entity_id = #{tarModelEntityId},</if>
            <if test="tarModelEntityPropId != null">tar_model_entity_prop_id = #{tarModelEntityPropId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="name != null">name = #{name},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateExec != null">update_exec = #{updateExec},</if>
            <if test="deleteExec != null">delete_exec = #{deleteExec},</if>
        </trim>
        where model_entity_ship_id = #{modelEntityShipId}
    </update>

    <delete id="deleteDsmModelEntityShipById" parameterType="Long">
        delete from dsm_model_entity_ship where model_entity_ship_id = #{modelEntityShipId}
    </delete>

    <delete id="deleteDsmModelEntityShipByIds" parameterType="String">
        delete from dsm_model_entity_ship where model_entity_ship_id in 
        <foreach item="modelEntityShipId" collection="array" open="(" separator="," close=")">
            #{modelEntityShipId}
        </foreach>
    </delete>
    
    <delete id="deleteDsmModelEntityShipByDsmModelEntityId" parameterType="Long">
        delete from dsm_model_entity_ship where src_model_entity_id = #{modelEntityId}
    </delete>
</mapper>