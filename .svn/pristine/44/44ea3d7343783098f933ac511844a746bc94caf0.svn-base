package com.dqms.dsm.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.ServletUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.dsm.domain.DsmIndexClass;
import com.dqms.dsm.domain.DsmIndexClassTreeSelect;
import com.dqms.dsm.mapper.DsmIndexClassMapper;
import com.dqms.dsm.service.IDsmIndexClassService;
import com.dqms.framework.web.service.TokenService;

import javax.management.RuntimeErrorException;

/**
 * 指标分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Service
public class DsmIndexClassServiceImpl implements IDsmIndexClassService
{
    @Autowired
    private DsmIndexClassMapper dsmIndexClassMapper;

    @Autowired
    private TokenService tokenService;
    /**
     * 查询指标分类
     *
     * @param indexClassId 指标分类ID
     * @return 指标分类
     */
    @Override
    public DsmIndexClass selectDsmIndexClassById(Long indexClassId)
    {
        return dsmIndexClassMapper.selectDsmIndexClassById(indexClassId);
    }

    /**
     * 查询指标分类列表
     *
     * @param dsmIndexClass 指标分类
     * @return 指标分类
     */
    @Override
    public List<DsmIndexClass> selectDsmIndexClassList(DsmIndexClass dsmIndexClass)
    {
        return dsmIndexClassMapper.selectDsmIndexClassList(dsmIndexClass);
    }

    /**
     * 新增指标分类
     *
     * @param dsmIndexClass 指标分类
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDsmIndexClass(DsmIndexClass dsmIndexClass)
    {
    	DsmIndexClass info = dsmIndexClassMapper.selectDsmIndexClassById(dsmIndexClass.getParentId());
    	if(info!=null) {
    		dsmIndexClass.setAncestors(info.getAncestors() + "," + dsmIndexClass.getParentId());
    		dsmIndexClass.setIndexClassNameFull(info.getIndexClassNameFull() + "/" + dsmIndexClass.getIndexClassName());
    	}else {
    		dsmIndexClass.setAncestors("0");
    		dsmIndexClass.setIndexClassNameFull(dsmIndexClass.getIndexClassName());
    	}
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmIndexClass.setCreateTime(DateUtils.getNowDate());
    	dsmIndexClass.setCreateId(loginUser.getUser().getUserId());
    	dsmIndexClass.setCreateBy(loginUser.getUser().getNickName());
    	dsmIndexClass.setUpdateTime(DateUtils.getNowDate());
    	dsmIndexClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmIndexClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmIndexClassMapper.insertDsmIndexClass(dsmIndexClass);
    }

    /**
     * 修改指标分类
     *
     * @param dsmIndexClass 指标分类
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDsmIndexClass(DsmIndexClass dsmIndexClass)
    {
        if(dsmIndexClass.getIndexClassId().equals(dsmIndexClass.getParentId())){
            throw new RuntimeErrorException(null, "父分类不能是本身！");
        }
    	DsmIndexClass newC = dsmIndexClassMapper.selectDsmIndexClassById(dsmIndexClass.getParentId());
    	DsmIndexClass oldC = dsmIndexClassMapper.selectDsmIndexClassById(dsmIndexClass.getIndexClassId());
         if (StringUtils.isNotNull(newC) && StringUtils.isNotNull(oldC))
         {
             String ancestorsFlag = oldC.getAncestors() + "," + dsmIndexClass.getIndexClassId();
             if(newC.getAncestors().contains(ancestorsFlag)){
                 throw new RuntimeErrorException(null, "父分类不能是本身下级！");
             }
             String newAncestors = newC.getAncestors() + "," + dsmIndexClass.getParentId();
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = newC.getIndexClassNameFull() + "/" + dsmIndexClass.getIndexClassName();
             String oldClassNameFull = oldC.getIndexClassNameFull();
             dsmIndexClass.setAncestors(newAncestors);
             dsmIndexClass.setIndexClassNameFull(newClassNameFull);
             updateClassChildren(dsmIndexClass.getIndexClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dsmIndexClass.getIndexClassId(), newClassNameFull, oldClassNameFull);
         }else if(newC==null){
             String newAncestors = "0";
             String oldAncestors = oldC.getAncestors();
             String newClassNameFull = dsmIndexClass.getIndexClassName();
             String oldClassNameFull = oldC.getIndexClassNameFull();
             dsmIndexClass.setAncestors(newAncestors);
             dsmIndexClass.setIndexClassNameFull(newClassNameFull);
             updateClassChildren(dsmIndexClass.getIndexClassId(), newAncestors, oldAncestors);
             updateClassNameFullChildren(dsmIndexClass.getIndexClassId(), newClassNameFull, oldClassNameFull);
         }
    	LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    	dsmIndexClass.setUpdateTime(DateUtils.getNowDate());
    	dsmIndexClass.setUpdateId(loginUser.getUser().getUserId());
    	dsmIndexClass.setUpdateBy(loginUser.getUser().getNickName());
        return dsmIndexClassMapper.updateDsmIndexClass(dsmIndexClass);
    }
    
    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    @Transactional
    public void updateClassChildren(Long taskClassId, String newAncestors, String oldAncestors)
    {
        List<DsmIndexClass> children = dsmIndexClassMapper.selectChildrenClassById(taskClassId);
        for (DsmIndexClass child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
        	dsmIndexClassMapper.updateClassChildren(children);
        }
    }
    @Transactional
    public void updateClassNameFullChildren(Long taskClassId, String newClassNameFull, String oldClassNameFull)
    {
        List<DsmIndexClass> children = dsmIndexClassMapper.selectChildrenClassById(taskClassId);
        for (DsmIndexClass child : children)
        {
            child.setIndexClassNameFull(child.getIndexClassNameFull().replace(oldClassNameFull, newClassNameFull));
        }
        if (children.size() > 0)
        {
        	dsmIndexClassMapper.updateClassNameFullChildren(children);
        }
    }

    /**
     * 批量删除指标分类
     *
     * @param indexClassIds 需要删除的指标分类ID
     * @return 结果
     */
    @Override
    public int deleteDsmIndexClassByIds(Long[] indexClassIds)
    {
        try {
            return dsmIndexClassMapper.deleteDsmIndexClassByIds(indexClassIds);
        }catch (Exception e){
            e.printStackTrace();
            if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("dsm_index_class_FK")){
                throw new RuntimeErrorException(null, "请先删除子分类");
            }else if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("fk_dsm_index_class_id")){
                throw new RuntimeErrorException(null, "分类已被引用，请勿删除");
            }else{
                throw new RuntimeErrorException(null, "删除失败，请与管理员联系："+e.getMessage());
            }
        }
    }

    /**
     * 删除指标分类信息
     *
     * @param indexClassId 指标分类ID
     * @return 结果
     */
    @Override
    public int deleteDsmIndexClassById(Long indexClassId)
    {
        try {
            return dsmIndexClassMapper.deleteDsmIndexClassById(indexClassId);
        }catch (Exception e){
            e.printStackTrace();
            if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("dsm_index_class_FK")){
                throw new RuntimeErrorException(null, "请先删除子分类");
            }else if(e.getMessage().contains("java.sql.SQLIntegrityConstraintViolationException")&&e.getMessage().contains("fk_dsm_index_class_id")){
                throw new RuntimeErrorException(null, "分类已被引用，请勿删除");
            }else{
                throw new RuntimeErrorException(null, "删除失败，请与管理员联系："+e.getMessage());
            }
        }
    }
    
    /**
     * 构建前端所需要下拉树结构
     *
     * @param etlTaskClasss 任务分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<DsmIndexClassTreeSelect> buildDsmIndexClassTreeSelect(List<DsmIndexClass> dsmIndexClasss)
    {
        List<DsmIndexClass> dsmIndexClasssTrees = buildDsmIndexClassTree(dsmIndexClasss);
        return dsmIndexClasssTrees.stream().map(DsmIndexClassTreeSelect::new).collect(Collectors.toList());
    }
    
    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<DsmIndexClass> buildDsmIndexClassTree(List<DsmIndexClass> dsmIndexClasss)
    {
        List<DsmIndexClass> returnList = new ArrayList<DsmIndexClass>();
        List<Long> tempList = new ArrayList<Long>();
        for (DsmIndexClass dsmIndexClass : dsmIndexClasss)
        {
            tempList.add(dsmIndexClass.getIndexClassId());
        }
        for (Iterator<DsmIndexClass> iterator = dsmIndexClasss.iterator(); iterator.hasNext();)
        {
        	DsmIndexClass dsmIndexClass = (DsmIndexClass) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dsmIndexClass.getParentId()))
            {
                recursionFn(dsmIndexClasss, dsmIndexClass);
                returnList.add(dsmIndexClass);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = dsmIndexClasss;
        }
        return returnList;
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<DsmIndexClass> list, DsmIndexClass t)
    {
        // 得到子节点列表
        List<DsmIndexClass> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DsmIndexClass tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<DsmIndexClass> getChildList(List<DsmIndexClass> list, DsmIndexClass t)
    {
        List<DsmIndexClass> tlist = new ArrayList<DsmIndexClass>();
        Iterator<DsmIndexClass> it = list.iterator();
        while (it.hasNext())
        {
        	DsmIndexClass n = (DsmIndexClass) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getIndexClassId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DsmIndexClass> list, DsmIndexClass t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
