package com.dqms.dsm.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dqms.common.annotation.Log;
import com.dqms.common.core.controller.BaseController;
import com.dqms.common.core.domain.AjaxResult;
import com.dqms.common.enums.BusinessType;
import com.dqms.dsm.domain.DsmMasterDataClass;
import com.dqms.dsm.service.IDsmMasterDataClassService;
import com.dqms.common.utils.poi.ExcelUtil;
import com.dqms.common.core.page.TableDataInfo;

/**
 * 主数据分类Controller
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@RestController
@RequestMapping("/dsm/dsmMasterDataClass")
public class DsmMasterDataClassController extends BaseController
{
    @Autowired
    private IDsmMasterDataClassService dsmMasterDataClassService;

    /**
     * 查询主数据分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmMasterDataClass:list')")
    @GetMapping("/list")
    public AjaxResult  list(DsmMasterDataClass dsmMasterDataClass)
    {
        startPage();
        List<DsmMasterDataClass> list = dsmMasterDataClassService.selectDsmMasterDataClassList(dsmMasterDataClass);
        return AjaxResult.success(list);
    }

    /**
     * 导出主数据分类列表
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmMasterDataClass:export')")
    @Log(title = "主数据分类", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DsmMasterDataClass dsmMasterDataClass)
    {
        List<DsmMasterDataClass> list = dsmMasterDataClassService.selectDsmMasterDataClassList(dsmMasterDataClass);
        ExcelUtil<DsmMasterDataClass> util = new ExcelUtil<DsmMasterDataClass>(DsmMasterDataClass.class);
        return util.exportExcel(list, "dsmMasterDataClass");
    }

    /**
     * 获取主数据分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmMasterDataClass:query')")
    @GetMapping(value = "/{masterDataClassId}")
    public AjaxResult getInfo(@PathVariable("masterDataClassId") Long masterDataClassId)
    {
        return AjaxResult.success(dsmMasterDataClassService.selectDsmMasterDataClassById(masterDataClassId));
    }

    /**
     * 新增主数据分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmMasterDataClass:add')")
    @Log(title = "主数据分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DsmMasterDataClass dsmMasterDataClass)
    {
        return toAjax(dsmMasterDataClassService.insertDsmMasterDataClass(dsmMasterDataClass));
    }

    /**
     * 修改主数据分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmMasterDataClass:edit')")
    @Log(title = "主数据分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DsmMasterDataClass dsmMasterDataClass)
    {
        return toAjax(dsmMasterDataClassService.updateDsmMasterDataClass(dsmMasterDataClass));
    }

    /**
     * 删除主数据分类
     */
    @PreAuthorize("@ss.hasPermi('dsm:dsmMasterDataClass:remove')")
    @Log(title = "主数据分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{masterDataClassIds}")
    public AjaxResult remove(@PathVariable Long[] masterDataClassIds)
    {
        return toAjax(dsmMasterDataClassService.deleteDsmMasterDataClassByIds(masterDataClassIds));
    }
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DsmMasterDataClass dsmMasterDataClass)
    {
        List<DsmMasterDataClass> dsmMasterDataClasss = dsmMasterDataClassService.selectDsmMasterDataClassList(dsmMasterDataClass);
        return AjaxResult.success(dsmMasterDataClassService.buildDsmMasterDataClassTreeSelect(dsmMasterDataClasss));
    }
    
    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselectAll")
    public AjaxResult treeselectAll(DsmMasterDataClass dsmMasterDataClass)
    {
        List<DsmMasterDataClass> dsmMasterDataClasss = dsmMasterDataClassService.selectDsmMasterDataClassList(dsmMasterDataClass);
        return AjaxResult.success(dsmMasterDataClassService.buildDsmMasterDataClassTreeSelectAll(dsmMasterDataClasss));
    }
}
