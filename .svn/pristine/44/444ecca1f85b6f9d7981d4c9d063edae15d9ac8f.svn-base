<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dqms.dsc.mapper.DscEncryptionMapper">
    
    <resultMap type="DscEncryption" id="DscEncryptionResult">
        <result property="encryptionId"    column="encryption_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="encryptionType"    column="encryption_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tableId"    column="table_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />         
    </resultMap>

    <resultMap type="DscEncryptionVo" id="DscEncryptionVoResult">
        <result property="encryptionId"    column="encryption_id"    />
        <result property="entityId"    column="entity_id"    />
        <result property="encryptionType"    column="encryption_type"    />
        <result property="encryptionTypeKey"    column="encryption_type_key"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tableId"    column="table_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="datasourceId"    column="datasource_id"    />
        <result property="datasourceName"    column="datasource_name"    />
        <result property="systemId"    column="system_id"    />
        <result property="systemName"    column="system_name"    />
    </resultMap>

    <sql id="selectDscEncryptionVo">
	    SELECT e.entity_id AS table_id ,e.table_name,ds.datasource_id ,ds.name AS datasource_name,ss.system_id ,ss.name AS system_name,
		t.encryption_id, t.entity_id, t.encryption_type, t.create_by, t.update_by, t.create_id, t.update_id, t.create_time, t.update_time
		FROM mdm_data_entity e 
		LEFT JOIN mdm_registry r ON e.registry_id=r.reg_id
		LEFT JOIN sys_datasource ds ON r.datasource_id=ds.datasource_id
		LEFT JOIN sys_system ss ON r.system_id=ss.system_id
		LEFT JOIN dsc_encryption t ON e.entity_id=t.entity_id
    </sql>

    <select id="selectDscEncryptionList" parameterType="DscEncryption" resultMap="DscEncryptionResult">
        <include refid="selectDscEncryptionVo"/>
        <where>
            <if test="entityId != null "> and t.entity_id = #{entityId}</if>
            <if test="encryptionType != null  and encryptionType != ''"> and encryptionTypeKey = #{encryptionType}</if>
            <if test="systemId != null and systemId != ''"> and ss.system_id = #{systemId}</if>
            <if test="datasourceId != null and datasourceId != ''"> and ds.datasource_id = #{datasourceId}</if>
            and r.meta_type IN(1,2)
            and r.del_flag != '2'
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.create_time desc
    </select>

    <select id="getDscEncryptionListVo" parameterType="DscEncryptionVo" resultMap="DscEncryptionVoResult">
        	 SELECT
				e.entity_id AS table_id,
				e.table_name,
				ds.datasource_id,
				ds.NAME AS datasource_name,
				ss.system_id,
				ss.NAME AS system_name,
				t.encryption_id,
				t.entity_id,
				sd.dict_label AS encryption_type,
				t.encryption_type AS encryption_type_key,
				t.create_by,
				t.update_by,
				t.create_id,
				t.update_id,
				t.create_time,
				t.update_time
			FROM
				dsc_encryption t
				LEFT JOIN mdm_registry r ON e.registry_id = r.reg_id
				LEFT JOIN sys_datasource ds ON r.datasource_id = ds.datasource_id
				LEFT JOIN sys_system ss ON r.system_id = ss.system_id
				LEFT JOIN mdm_data_entity e ON e.entity_id = t.entity_id
				LEFT JOIN sys_dict_data sd ON t.encryption_type = sd.dict_value AND sd.dict_type = 'dsc_encryption_type'
        <where>
            r.del_flag != '2'
            <if test="entityId != null "> and t.entity_id = #{entityId}</if>
            <if test="encryptionType != null  and encryptionType != ''"> and encryption_type_key = #{encryptionType}</if>
            <if test="systemId != null and systemId != ''"> and ss.system_id = #{systemId}</if>
            <if test="datasourceId != null and datasourceId != ''"> and ds.datasource_id = #{datasourceId}</if>
            ${params.dataScope}<!-- 数据范围过滤 -->
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectDscEncryptionById" parameterType="Long" resultMap="DscEncryptionResult">
        <include refid="selectDscEncryptionVo"/>
        where t.encryption_id = #{encryptionId}
    </select>
        
    <insert id="insertDscEncryption" parameterType="DscEncryption" useGeneratedKeys="true" keyProperty="encryptionId">
        insert into dsc_encryption
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityId != null">entity_id,</if>
            <if test="encryptionType != null">encryption_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityId != null">#{entityId},</if>
            <if test="encryptionType != null">#{encryptionType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDscEncryption" parameterType="DscEncryption">
        update dsc_encryption
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="encryptionType != null">encryption_type = #{encryptionType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where encryption_id = #{encryptionId}
    </update>

    <delete id="deleteDscEncryptionById" parameterType="Long">
        delete from dsc_encryption where encryption_id = #{encryptionId}
    </delete>

    <delete id="deleteDscEncryptionByIds" parameterType="String">
        delete from dsc_encryption where encryption_id in 
        <foreach item="encryptionId" collection="array" open="(" separator="," close=")">
            #{encryptionId}
        </foreach>
    </delete>

    <select id="callProcedure" statementType="CALLABLE" useCache="false">
    {call select_user_by_id(
        #{encryptionId, mode=IN},
        #{encryptionType, mode=OUT, jdbcType=VARCHAR}

    )}
</select>

</mapper>