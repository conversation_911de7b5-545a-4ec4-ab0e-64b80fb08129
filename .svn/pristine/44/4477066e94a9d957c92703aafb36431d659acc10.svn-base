package com.dqms.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dqms.api.mapper.ApiDefineHisMapper;
import com.dqms.api.domain.ApiDefineHis;
import com.dqms.api.domain.vo.ApiDefineHisVo;
import com.dqms.api.service.IApiDefineHisService;

/**
 * 接口日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-03
 */
@Service
public class ApiDefineHisServiceImpl implements IApiDefineHisService
{
    @Autowired
    private ApiDefineHisMapper apiDefineHisMapper;

    /**
     * 查询接口日志
     *
     * @param defineHisId 接口日志ID
     * @return 接口日志
     */
    @Override
    public ApiDefineHis selectApiDefineHisById(Long defineHisId)
    {
        return apiDefineHisMapper.selectApiDefineHisById(defineHisId);
    }

    /**
     * 查询接口日志列表
     *
     * @param apiDefineHis 接口日志
     * @return 接口日志
     */
    @Override
    public List<ApiDefineHis> selectApiDefineHisList(ApiDefineHis apiDefineHis)
    {
        return apiDefineHisMapper.selectApiDefineHisList(apiDefineHis);
    }

    /**
     * 新增接口日志
     *
     * @param apiDefineHis 接口日志
     * @return 结果
     */
    @Override
    public int insertApiDefineHis(ApiDefineHis apiDefineHis)
    {
        return apiDefineHisMapper.insertApiDefineHis(apiDefineHis);
    }

    /**
     * 修改接口日志
     *
     * @param apiDefineHis 接口日志
     * @return 结果
     */
    @Override
    public int updateApiDefineHis(ApiDefineHis apiDefineHis)
    {
        return apiDefineHisMapper.updateApiDefineHis(apiDefineHis);
    }

    /**
     * 批量删除接口日志
     *
     * @param defineHisIds 需要删除的接口日志ID
     * @return 结果
     */
    @Override
    public int deleteApiDefineHisByIds(Long[] defineHisIds)
    {
        return apiDefineHisMapper.deleteApiDefineHisByIds(defineHisIds);
    }

    /**
     * 删除接口日志信息
     *
     * @param defineHisId 接口日志ID
     * @return 结果
     */
    @Override
    public int deleteApiDefineHisById(Long defineHisId)
    {
        return apiDefineHisMapper.deleteApiDefineHisById(defineHisId);
    }

	@Override
	public List<ApiDefineHisVo> getRunFb() {
		return apiDefineHisMapper.getRunFb();
	}

	@Override
	public List<ApiDefineHisVo> getRunQs() {
		return apiDefineHisMapper.getRunQs();
	}

	@Override
	public List<ApiDefineHisVo> getRunZl() {
		return apiDefineHisMapper.getRunZl();
	}

	@Override
	public List<ApiDefineHisVo> getRunHs() {
		return apiDefineHisMapper.getRunHs();
	}

	@Override
	public List<ApiDefineHis> getRunYc() {
		return apiDefineHisMapper.getRunYc();
	}
}
