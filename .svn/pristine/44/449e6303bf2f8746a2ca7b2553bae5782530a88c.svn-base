package com.dqms.dam.domain.vo;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.dqms.common.annotation.Excel;
import com.dqms.common.core.domain.BaseEntity;
import com.dqms.dam.domain.DamAssetsClass;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 数据资产对象 dam_assets
 *
 * <AUTHOR>
 * @date 2021-06-08
 */
public class DamAssetsVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 资产ID */
    private Long damAssetsId;


    /*@Excel(name = "全路径")*/
    /** 全路径 */
    private String classNameFull;

    /** 资产标签 */
    @Excel(name = "资产标签")
    private String classNames;
    private Long assetsClassId;
    private Long[] assetsClassIds;
    private List<DamAssetsClass> assetsClasss;

    /** 资产名称 */
    @Excel(name = "资产名称")
    private String assetsName;

    /** 资产编码 */
    @Excel(name = "资产编码")
    private String assetsCode;

    /** 资产类型 */

   /* @Excel(name = "资产类型",readConverterExp = "1=自定义,2=基础标准,3=指标标准,4=元数据",combo={"自定义","基础标准","指标标准","元数据"})*/
    @Excel(name = "资产类型",dictType="assets_type",combo={"自定义","基础标准","指标标准","元数据"})
    private String assetsType;
    private String assetsTypeLabel;

    /** 管理系统 */
    @Excel(name = "管理系统")
    private String systemName;
    private Long systemId;

    /** 数据来源 */
    @Excel(name = "数据来源")
    private String datasourceName;
    private Long datasourceId;

    /** 负责人 */
    @Excel(name = "负责人")
    private String personName;
    private String person;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 资产等级 */
    @Excel(name = "资产等级",dictType="sys_important_level",combo={"高","中高","中","中低","低"})
    private String level;
    private String levelLabel;


    /** 关联属性 */
    private Long relId;

    /** 资产状态 */
    @Excel(name = "资产状态",dictType="dsm_standard_status",combo={"执行中","已作废"})
    private String status;
    private String statusLabel;

    /** 附件路径 */
    @Excel(name = "附件路径")
    private String attachment;

    /** 创建人ID */
    private Long createId;

    /** 修改人ID */
    private Long updateId;
    
    private float grade;
    
    private Long assetsSubscribeId;

    public void setDamAssetsId(Long damAssetsId)
    {
        this.damAssetsId = damAssetsId;
    }

    public Long getDamAssetsId()
    {
        return damAssetsId;
    }

    public void setAssetsName(String assetsName)
    {
        this.assetsName = assetsName;
    }

    public String getAssetsName()
    {
        return assetsName;
    }
    public void setAssetsCode(String assetsCode)
    {
        this.assetsCode = assetsCode;
    }

    public String getAssetsCode()
    {
        return assetsCode;
    }
    public void setAssetsType(String assetsType)
    {
        this.assetsType = assetsType;
    }

    public String getAssetsType()
    {
        return assetsType;
    }
    public void setSystemId(Long systemId)
    {
        this.systemId = systemId;
    }

    public Long getSystemId()
    {
        return systemId;
    }
    public void setDatasourceId(Long datasourceId)
    {
        this.datasourceId = datasourceId;
    }

    public Long getDatasourceId()
    {
        return datasourceId;
    }
    public void setPerson(String person)
    {
        this.person = person;
    }

    public String getPerson()
    {
        return person;
    }
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getStartTime()
    {
        return startTime;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setLevel(String level)
    {
        this.level = level;
    }

    public String getLevel()
    {
        return level;
    }
    public void setRelId(Long relId)
    {
        this.relId = relId;
    }

    public Long getRelId()
    {
        return relId;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setAttachment(String attachment)
    {
        this.attachment = attachment;
    }

    public String getAttachment()
    {
        return attachment;
    }
    public void setCreateId(Long createId)
    {
        this.createId = createId;
    }

    public Long getCreateId()
    {
        return createId;
    }
    public void setUpdateId(Long updateId)
    {
        this.updateId = updateId;
    }

    public Long getUpdateId()
    {
        return updateId;
    }

	public String getClassNameFull() {
		return classNameFull;
	}

	public void setClassNameFull(String classNameFull) {
		this.classNameFull = classNameFull;
	}

	public float getGrade() {
		return grade;
	}

	public void setGrade(float grade) {
		this.grade = grade;
	}

	public Long getAssetsSubscribeId() {
		return assetsSubscribeId;
	}

	public void setAssetsSubscribeId(Long assetsSubscribeId) {
		this.assetsSubscribeId = assetsSubscribeId;
	}

	public Long[] getAssetsClassIds() {
		if(assetsClassIds!=null&&assetsClassIds.length>0) {
			return assetsClassIds;
		}else {
			if(assetsClasss!=null&&assetsClasss.size()>0) {
				Long[] ids =new Long[assetsClasss.size()];
				int i=0;
				for(DamAssetsClass t :assetsClasss) {
					ids[i++]=t.getAssetsClassId();
				}
				return ids;
			}
			
		}
		return assetsClassIds;
	}

    public String getClassNames() {
		String names = "";
		if(this.assetsClasss!=null&& this.assetsClasss.size()>0) {
			for(DamAssetsClass t :this.assetsClasss) {
				names=names+","+t.getClassName();
			}
 			return names.replaceFirst(",", "");
		}
		if(names != null && names.length() != 0) {
            return names;
		}
            return this.classNames;
	}

	public void setClassNames(String classNames) {
		this.classNames = classNames;
	}

	public void setAssetsClassIds(Long[] assetsClassIds) {
		this.assetsClassIds = assetsClassIds;
	}

	public List<DamAssetsClass> getAssetsClasss() {
		return assetsClasss;
	}

	public void setAssetsClasss(List<DamAssetsClass> assetsClasss) {
		this.assetsClasss = assetsClasss;
	}

	public Long getAssetsClassId() {
		return assetsClassId;
	}

	public void setAssetsClassId(Long assetsClassId) {
		this.assetsClassId = assetsClassId;
	}

	public String getAssetsTypeLabel() {
		return assetsTypeLabel;
	}

	public void setAssetsTypeLabel(String assetsTypeLabel) {
		this.assetsTypeLabel = assetsTypeLabel;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getDatasourceName() {
		return datasourceName;
	}

	public void setDatasourceName(String datasourceName) {
		this.datasourceName = datasourceName;
	}

	public String getPersonName() {
		return personName;
	}

	public void setPersonName(String personName) {
		this.personName = personName;
	}

	public String getLevelLabel() {
		return levelLabel;
	}

	public void setLevelLabel(String levelLabel) {
		this.levelLabel = levelLabel;
	}

	public String getStatusLabel() {
		return statusLabel;
	}

	public void setStatusLabel(String statusLabel) {
		this.statusLabel = statusLabel;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("damAssetsId", getDamAssetsId())
            .append("assetsName", getAssetsName())
            .append("assetsCode", getAssetsCode())
            .append("assetsType", getAssetsType())
            .append("remark", getRemark())
            .append("systemId", getSystemId())
            .append("datasourceId", getDatasourceId())
            .append("person", getPerson())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("level", getLevel())
            .append("relId", getRelId())
            .append("status", getStatus())
            .append("attachment", getAttachment())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createId", getCreateId())
            .append("updateId", getUpdateId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
