package com.dqms.dsm.mapper;

import java.util.List;
import com.dqms.dsm.domain.DsmModelEntity;

/**
 * 模型实例Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-11-23
 */
public interface DsmModelEntityMapper 
{
    /**
     * 查询模型实例
     * 
     * @param modelEntityId 模型实例ID
     * @return 模型实例
     */
    public DsmModelEntity selectDsmModelEntityById(Long modelEntityId);

    /**
     * 查询模型实例列表
     * 
     * @param dsmModelEntity 模型实例
     * @return 模型实例集合
     */
    public List<DsmModelEntity> selectDsmModelEntityList(DsmModelEntity dsmModelEntity);

    /**
     * 新增模型实例
     * 
     * @param dsmModelEntity 模型实例
     * @return 结果
     */
    public int insertDsmModelEntity(DsmModelEntity dsmModelEntity);

    /**
     * 修改模型实例
     * 
     * @param dsmModelEntity 模型实例
     * @return 结果
     */
    public int updateDsmModelEntity(DsmModelEntity dsmModelEntity);

    /**
     * 删除模型实例
     * 
     * @param modelEntityId 模型实例ID
     * @return 结果
     */
    public int deleteDsmModelEntityById(Long modelEntityId);

    /**
     * 批量删除模型实例
     * 
     * @param modelEntityIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDsmModelEntityByIds(Long[] modelEntityIds);
}
