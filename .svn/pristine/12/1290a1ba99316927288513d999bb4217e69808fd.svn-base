package com.dqms.mdm.mapper;

import java.util.List;
import com.dqms.mdm.domain.MdmCollectHis;

/**
 * 元数据采集历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-20
 */
public interface MdmCollectHisMapper 
{
    /**
     * 查询元数据采集历史
     * 
     * @param regName 元数据采集历史ID
     * @return 元数据采集历史
     */
    public MdmCollectHis selectMdmCollectHisById(String regName);

    /**
     * 查询元数据采集历史列表
     * 
     * @param mdmCollectHis 元数据采集历史
     * @return 元数据采集历史集合
     */
    public List<MdmCollectHis> selectMdmCollectHisList(MdmCollectHis mdmCollectHis);

    /**
     * 新增元数据采集历史
     * 
     * @param mdmCollectHis 元数据采集历史
     * @return 结果
     */
    public int insertMdmCollectHis(MdmCollectHis mdmCollectHis);

    /**
     * 修改元数据采集历史
     * 
     * @param mdmCollectHis 元数据采集历史
     * @return 结果
     */
    public int updateMdmCollectHis(MdmCollectHis mdmCollectHis);

    /**
     * 删除元数据采集历史
     * 
     * @param regName 元数据采集历史ID
     * @return 结果
     */
    public int deleteMdmCollectHisById(String regName);

    /**
     * 批量删除元数据采集历史
     * 
     * @param regNames 需要删除的数据ID
     * @return 结果
     */
    public int deleteMdmCollectHisByIds(String[] regNames);
}
