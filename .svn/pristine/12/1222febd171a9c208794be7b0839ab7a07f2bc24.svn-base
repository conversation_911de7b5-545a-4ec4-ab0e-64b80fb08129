package com.dqms.web.core.client.bo;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.dqms.common.utils.StringUtils;

public class CmdResultsParser {
	private static final Logger log = LoggerFactory.getLogger(CmdResultsParser.class);
	private static String resStart = "<ExecuteResultStart>";
	private static String resEnd = "</ExecuteResultEnd>";
	private static String STATUS_SUCCESS = "SUCCESS";
	private static String STATUS_FAILURE = "FAILURE";
	public static String STATUS_NAME = "status";
	public static String MSG_NAME = "msg";
	
	@SuppressWarnings("unchecked")
	public static Map<String, Object> parse(String results){
		log.info("命令执行返回结果："+results);
		if(StringUtils.isBlank(results)){
			throw new RuntimeException("执行的命令必须有返回值且必须遵循如下指定格式！<ExecuteResultStart>{status:\"success\",msg:\"成功\"}</ExecuteResultEnd>");
		};
		String resStr = results.substring(results.indexOf(resStart)+resStart.length(),results.indexOf(resEnd));
		log.info("命令执行返回结果Json截取："+resStr);
		Map<String,Object> m = (Map<String, Object>) JSON.parse(resStr);
		String status = (String) m.get(STATUS_NAME);
		if(StringUtils.isBlank(status))throw new RuntimeException("返回的命令结果包含状态（status）,且必须是success、failure中的一个值！");
		status = status.toUpperCase();
		if(!STATUS_SUCCESS.equals(status) &&!STATUS_FAILURE.equals(status)  ){
			throw new RuntimeException("状态字段值必须以下值中的一个，success、failure！");
		}
		String errMsg = (String) m.get("msg");
		m.put(STATUS_NAME, STATUS_SUCCESS.equals(status));
		if(STATUS_FAILURE.equals(status) && StringUtils.isBlank(errMsg)){
			throw new RuntimeException("执行失败必须返回错误信息，信息字段为msg");
		}
		return m;
	}
	public static void main(String[] args) {
		String s = "dfdf \ndf <ExecuteResultStart>{status:\"success\",msg:\"成功\"}</ExecuteResultEnd>dfdfdfdf";
		parse(s);
	}
}
