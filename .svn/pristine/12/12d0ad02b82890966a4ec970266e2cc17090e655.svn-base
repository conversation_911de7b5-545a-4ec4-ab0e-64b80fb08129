package com.dqms.needs.service.impl;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.activiti.api.process.model.ProcessInstance;
import org.activiti.api.process.model.builders.ProcessPayloadBuilder;
import org.activiti.api.process.runtime.ProcessRuntime;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dqms.common.core.domain.model.LoginUser;
import com.dqms.common.utils.DateUtils;
import com.dqms.common.utils.SecurityUtils;
import com.dqms.common.utils.StringUtils;
import com.dqms.needs.domain.NesNeeds;
import com.dqms.needs.domain.NesNeedsDept;
import com.dqms.needs.enums.NesConstants;
import com.dqms.needs.mapper.NesNeedsDeptMapper;
import com.dqms.needs.mapper.NesNeedsMapper;
import com.dqms.needs.service.INesNeedsService;
import com.dqms.system.service.ISysConfigService;
import com.dqms.system.service.ISysUserService;
import com.dqms.utils.FileUtils;

/**
 * 需求反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-17
 */
@Service
public class NesNeedsServiceImpl implements INesNeedsService
{
    @Autowired
    private NesNeedsMapper nesNeedsMapper;

    @Autowired
    private ProcessRuntime processRuntime;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private TaskService taskService;
    @Autowired
    public ISysConfigService configService;
    
    @Autowired
    private NesNeedsDeptMapper nesNeedsDeptMapper;
    /**
     * 查询需求反馈
     *
     * @param needsId 需求反馈ID
     * @return 需求反馈
     */
    @Override
    public NesNeeds selectNesNeedsById(Long needsId)
    {
        return nesNeedsMapper.selectNesNeedsById(needsId);
    }

    /**
     * 查询需求反馈列表
     *
     * @param nesNeeds 需求反馈
     * @return 需求反馈
     */
    @Override
    public List<NesNeeds> selectNesNeedsList(NesNeeds nesNeeds)
    {	
    	List<NesNeeds> list = nesNeedsMapper.selectNesNeedsList(nesNeeds);
        List<String> collect = list.parallelStream().map(wl -> wl.getInstanceId()!=null?wl.getInstanceId():"").collect(Collectors.toList());
        if(collect!=null&&!collect.isEmpty()) {
        	System.out.println(SecurityUtils.getLoginUser().getUsername());
            List<Task> tasks = taskService.createTaskQuery().processInstanceIdIn(collect).taskCandidateUser(SecurityUtils.getLoginUser().getUsername()).list();
            list.forEach(
                    wl->{
                        Task task = tasks.parallelStream().filter(t -> t.getProcessInstanceId().equals(wl.getInstanceId())).findAny().orElse(null);
                        if (task != null) {
                        	System.out.println(task.getProcessInstanceId());
                        	System.out.println(wl.getInstanceId());
                        	System.out.println(task.getId());
                            wl.setTaskName(task.getName());
                            wl.setDefinitionKey(task.getTaskDefinitionKey());
                            wl.setBusinessKey("needs");
                            wl.setTaskId(task.getId());
                        }
                    }
            );
        }
        return list;
    }

    /**
     * 新增需求反馈
     *
     * @param nesNeeds 需求反馈
     * @return 结果
     */
    @Override
    @Transactional
    public int insertNesNeeds(NesNeeds nesNeeds)
    {
    	nesNeeds.setStatus(NesConstants.NES_NEEDS_STATUS_READY);
    	nesNeeds.setCreateTime(DateUtils.getNowDate());
    	nesNeeds.setCreateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	nesNeeds.setCreateBy(SecurityUtils.getLoginUser().getUsername());
    	nesNeeds.setUpdateTime(DateUtils.getNowDate());
    	nesNeeds.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	nesNeeds.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        int i=nesNeedsMapper.insertNesNeeds(nesNeeds);
        LoginUser user = SecurityUtils.getLoginUser();
		
		  String join =
		  StringUtils.join(sysUserService.selectUserNameByPostCodeAndDeptId("se",
		  SecurityUtils.getLoginUser().getUser().getDeptId()), ","); 
		  ProcessInstance processInstance = processRuntime.start(ProcessPayloadBuilder .start()
		  .withProcessDefinitionKey("needs") .withName(nesNeeds.getNeedsName())
		  .withBusinessKey(nesNeeds.getNeedsId().toString())
		  .withVariable("deptLeader",join) .build());
		  nesNeeds.setInstanceId(processInstance.getId());
		  nesNeedsMapper.updateNesNeeds(nesNeeds);
		 
        nesNeeds.setInstanceId("");
        if(nesNeeds.getDepts()!=null) {
        	for(Long dept:nesNeeds.getDepts()) {
        		NesNeedsDept nesNeedsDept = new NesNeedsDept();
        		nesNeedsDept.setDeptId(dept);
        		nesNeedsDept.setNeedsId(nesNeeds.getNeedsId());
        		nesNeedsDeptMapper.insertNesNeedsDept(nesNeedsDept);
        	}
        }
        return i;
    }

    /**
     * 修改需求反馈
     *
     * @param nesNeeds 需求反馈
     * @return 结果
     */
    @Override
    @Transactional
    public int updateNesNeeds(NesNeeds nesNeeds)
    {        
    	nesNeeds.setUpdateTime(DateUtils.getNowDate());
    	nesNeeds.setUpdateId(SecurityUtils.getLoginUser().getUser().getUserId());
    	nesNeeds.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        nesNeedsMapper.updateNesNeeds(nesNeeds);
        nesNeedsDeptMapper.deleteNesNeedsDeptById(nesNeeds.getNeedsId());
        if(nesNeeds.getDepts()!=null) {
        	for(Long dept:nesNeeds.getDepts()) {
        		NesNeedsDept nesNeedsDept = new NesNeedsDept();
        		nesNeedsDept.setDeptId(dept);
        		nesNeedsDept.setNeedsId(nesNeeds.getNeedsId());
        		nesNeedsDeptMapper.insertNesNeedsDept(nesNeedsDept);
        	}
        }
        return nesNeedsMapper.updateNesNeeds(nesNeeds);
    }

    /**
     * 批量删除需求反馈
     *
     * @param needsIds 需要删除的需求反馈ID
     * @return 结果
     */
    @Override
    public int deleteNesNeedsByIds(Long[] needsIds)
    {
        return nesNeedsMapper.deleteNesNeedsByIds(needsIds);
    }

    /**
     * 删除需求反馈信息
     *
     * @param needsId 需求反馈ID
     * @return 结果
     */
    @Override
    public int deleteNesNeedsById(Long needsId)
    {
        return nesNeedsMapper.deleteNesNeedsById(needsId);
    }
    
    /**
     * 根据需求ID查询部门树信息
     *
     * @param needId 需求ID
     * @return 选中部门列表
     */
    @Override
    public List<Integer> selectDeptListByNeedId(Long needsId)
    {
        return nesNeedsMapper.selectDeptListByNeedId(needsId);
    }
    
    /**
     * 单个文件下载
     *
     * @param documentId
     * @param response
     * @param request
     */
    public void singleDown(Long needsId, HttpServletResponse response, HttpServletRequest request) {

        String log = "";
        try {
        	NesNeeds nesNeeds = nesNeedsMapper.selectNesNeedsById(needsId);

            String filePath = nesNeeds.getAttachment();
            File file = new File(filePath);
            if (!file.exists() || file.isDirectory()) {
                throw new Exception(StringUtils.format("文件不存在，不允许下载。 ", file));
            }
            if (file.length() > new Long("2147483648")) {
                throw new Exception(StringUtils.format("文件不能超过2G，不允许下载。 ", file));
            }
            String fName = filePath.trim();
            log = "(" + fName + ")";
            String temp[] = fName.split("\\/"); /**split里面必须是正则表达式，"\\"的作用是对字符串转义*/
            String realFileName = temp[temp.length - 1];
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition","attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
