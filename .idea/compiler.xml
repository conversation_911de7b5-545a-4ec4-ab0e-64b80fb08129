<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="dqms-quartz" />
        <module name="dqms-system" />
        <module name="dqms-generator" />
        <module name="dqms-framework" />
        <module name="dqms-admin" />
        <module name="dqms-common" />
      </profile>
    </annotationProcessing>
  </component>
</project>